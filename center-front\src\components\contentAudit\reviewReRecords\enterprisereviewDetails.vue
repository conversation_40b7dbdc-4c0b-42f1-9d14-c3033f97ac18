<template scope="scope">
    <div>
        <div class="user-titler">{{$route.name}}</div>
        <!--企业彩印-->
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="彩印类型">
                    <el-select v-model="searchReq.caiyinType" class="app-input" placeholder="请选择" size="small" clearable>
                        <el-option
                                v-for="item in caiyinType"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="彩印ID">
                    <el-input v-model="searchReq.uuid" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="内容">
                    <el-input v-model="searchReq.content" class="app-input" size="small"></el-input>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="审核人">
                    <el-input v-model="searchReq.reviewer" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="审核意见">
                    <el-select v-model="searchReq.status" class="app-input" placeholder="请选择" size="small" clearable>
                        <el-option
                                v-for="item in status"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="审核时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchReq.timearr1"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="企业名称">
                    <el-input v-model="searchReq.submitter" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="提交时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchReq.timearr"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
                 <el-form-item label="内容编号">
                    <el-input v-model="searchReq.contentID" size="small" class="app-input"></el-input>
                    </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
					<el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    :header-cell-class-name="tableheaderClassName">
                <el-table-column
                        prop="uuid"
                        label="彩印ID"
                        width="140">
                </el-table-column>
              <el-table-column  label="彩印内容" width="200">
                <template slot-scope="scope">
                  <div v-html="scope.row.content"></div>
                </template>
              </el-table-column>
              <el-table-column label="敏感词"
                               width="200">
                <template slot-scope="scope">
                  <div  v-html="scope.row.sensitiveWords"></div>
                </template>
              </el-table-column>
                <el-table-column
                        prop="contentType"
                        label="彩印类型"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="status"
                        label="审核意见"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="telecomReviewOpinion"
                        label="电信审核意见"
                        width="250">
                        <template slot-scope="scope">
                            <div style="white-space:pre-wrap">{{ scope.row.telecomReviewOpinion }}</div>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="unicomReviewOpinion"
                        label="联通审核意见"
                        width="250">
                        <template slot-scope="scope">
                            <div style="white-space:pre-wrap">{{ scope.row.unicomReviewOpinion }}</div>
                        </template>
                </el-table-column>
                <el-table-column
                        prop="reason"
                        label="驳回原因"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="submitter"
                        label="企业名称"
                        width="100">
                </el-table-column>
                <el-table-column label="企业资质" width="100">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)" :style="hasCorpImage(scope.row.corpImage)?'':'color:#808080'">详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="其他资质" width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)" :style="hasOtherImage(scope.row.otherImage)?'':'color: #808080'">详情</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                        prop="submitDate"
                        label="提交时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="contentID"
                        label="内容编号"
                        width="200">
                </el-table-column>
                <el-table-column
                        label="异网审核结果"
                        width="350">
                  <template slot-scope="scope">
                    <div style="white-space:pre-wrap" >{{scope.row.templateIds}}</div>
                  </template>
                </el-table-column>
                <el-table-column
                        prop="province"
                        label="省份"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="city"
                        label="地市"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="reviewer"
                        label="审核人"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="reviewDate"
                        label="审核时间"
                        width="200">
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="block app-pageganit">
                <el-pagination v-show="pageTotal"
                               @size-change="handleSizeChange"
                               @current-change="handleCurrentChange"
                               :current-page="searchReq.pageIndex"
                               :page-sizes="[10, 20, 30, 50]"
                               :page-size="10"
                               layout="total, sizes, prev, pager, next, jumper"
                               :total="pageTotal"  style="text-align: right;">
                </el-pagination>
            </div>
            <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
                <img style="width: 100%;" :src="corpImage" alt>
            </el-dialog>
            <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
              <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
                 <li >
                   <a :href="item" target="_blank">其他资质{{index+1}}</a>
                 </li>
              </ul>
            </el-dialog>
        </div>
      <el-dialog
          @open="exportClick"
          title="导出"
          :visible.sync="propVisible"
          :close-on-click-modal="false"
          width="45%">
        <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
          <el-form-item label="文件名" prop="fileName">
            <el-input v-model="addReq.fileName" type="input" size="small"
                      placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字"
                      style="width: 90%;"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256"
                      style="width: 90%;"></el-input>
          </el-form-item>
        </el-form>
        <div style=" margin-left: 80px; color: red;">
          导出后请到系统管理-导出文件下载对应文件
        </div>

        <div slot="footer" class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="confirmExport">确定</el-button>
          <el-button @click="cancelExport">取消</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>
    import {postDownload} from './../../../servers/httpServer.js';
    import {dowandFile} from './../../../util/core.js';
    import {dealSensitiveWord} from './../../../util/core.js';
    import axios from '../../../../node_modules/axios/dist/axios';

    export default {
        data() {
            return {
              propVisible: false,
              addReq:{
                fileName: '',
                remark: ''
              },
              rules:{
                fileName: [
                  { required: true, message: '请输入文件名', trigger: 'blur' },
                  { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
                  { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
                ],
                remark: [
                  { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
                ]
              },
                tableLoading:false,
                pageTotal: 0,//总条数
                //类型
                Type:[
                    {
                        id:'ALL',
                        name:'全部',
                    },{
                        id:'ENTERPRISE',
                        name:'企业彩印',
                    },
                    {
                        id:'FEINNO',
                        name:'新媒彩印',
                    }
                ],
                corpImageVisible: false,
                corpImage: "", //企业资质
                otherImageVisible: false,
                otherImage: [], //其他资质
                caiyinType:[
                    {
                        id:0,
                            name:'主叫彩印',
                    },
                    {
                        id:1,
                            name:'被叫彩印',
                    },
                    {
                        id:2,
                            name:'热线彩印',
                    },
                    {
                        id:3,
                            name:'挂机短信',
                    },
                    {
                        id:4,
                            name:'挂机彩信',
                    },
                    // {
                    //     id:5,
                    //         name:'群发短信',
                    // },
                    {
                        id:7,
                            name:'数智反诈'
                    },
                    {
                        id:8,
                            name:'主叫挂机'
                    },
                    {
                        id: 9,
                        name: '行业挂机短信',
                    }
                ],
                //审核意见
                status:[
                    {
                        id:1,
                        name:'通过'
                    },
                    {
                        id:2,
                        name:'驳回'
                    }
                    // ,
                    // {
                    //     id:3,
                    //     name:'已撤销'
                    // }
                ],
                //查询条件
                searchReq: {
                    corpId:'',
                    pageSize:10,
                    pageIndex:1,
                    reviewer:"",//审核人
                    uuid:"",//内容Id
                    content:"",//彩印的内容
                    contentType:"ALL",
                    status:"",//审核的状态（意见）
                    submitStartDate:"",//提交开始时间   yyyyMMddHHmmss
                    submitEndDate:"",//结束时间   yyyyMMddHHmmss
                    timearr:[],//提交时间
                    timearr1:[],//审核时间
                    reviewStartDate:'',//审核时间
                    reviewEndDate:'',
                    submitter:'',//提交人
                    caiyinType:'',
                    contentID:"",//内容编号
                    isReAudit:"1"
                },
                //数据表
                tableData: [],
                //操作列表
                rowData: "",
                checked: [],
                passVisible: false,
                //撤销单或批量
                rejectType:0,
                multipleSelection: [],
                //撤销请求参数
                rejectReq: {
                    corpInfoId:'',//id
                    corpInfoIds:[],//多id
                    undoReason:'',
                    reviewer:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName//操作者
                },
            };
        },
        created(){
            this.search();
        },
        methods: {
          exportClick(){
            this.$refs.addReqForm.resetFields();
          },
          confirmExport() {
            this.$refs.addReqForm.validate(valid => {
              if (valid) {
                this.propVisible = !this.propVisible;

                if (this.searchReq.timearr) {
                  this.searchReq.submitStartDate = this.searchReq.timearr[0];
                  this.searchReq.submitEndDate = this.searchReq.timearr[1];
                } else {
                  this.searchReq.submitStartDate = '';
                  this.searchReq.submitEndDate = '';
                }
                //审核时间
                if (this.searchReq.timearr1) {
                  this.searchReq.reviewStartDate = this.searchReq.timearr1[0];
                  this.searchReq.reviewEndDate = this.searchReq.timearr1[1];
                } else {
                  this.searchReq.reviewStartDate = '';
                  this.searchReq.reviewEndDate = '';
                }

                const vm = this;
                var req = {
                  fileName: this.addReq.fileName,
                  remark: this.addReq.remark,
                  taskType: 9,
                  params: JSON.stringify(this.searchReq)
                }
                axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then(function (res) {

                  let data = res.data;
                  if (data.code == 0) {
                    vm.$message.success("系统将生成文件名为" + vm.addReq.fileName + "的文件");
                  } else {
                    vm.$message.error(data.msg);
                  }
                });
              } else {
                return false;
              }
            });
          },
          cancelExport() {
            this.propVisible = !this.propVisible;
            this.$refs.addReqForm.resetFields();
          },
            showCorpImage(corpImage) {
                console.log(corpImage);
                this.corpImage = corpImage;
                this.corpImageVisible = true;
            },
            showOtherImage(otherImage) {
              this.otherImage = otherImage;
              this.otherImageVisible = true;
            },
            // 判断企业资质和其他资质是否有值，没有则字体颜色变成灰色
            hasCorpImage(corpImage){
                return !(corpImage == null || corpImage == "");
            },
            hasOtherImage(otherImage){
                return !(otherImage == null || otherImage.length == 0);
            },
            searchBtn() {
              this.searchReq.pageIndex = 1;
              this.search()
            },
            //查询请求
            search: function() {
                this.tableLoading = true;
                //提交时间
                if(this.searchReq.timearr){
                    this.searchReq.submitStartDate = this.searchReq.timearr[0];
                    this.searchReq.submitEndDate = this.searchReq.timearr[1];
                }else{
                    this.searchReq.submitStartDate='';
                    this.searchReq.submitEndDate='';
                }
                //审核时间
                if(this.searchReq.timearr1){
                    this.searchReq.reviewStartDate = this.searchReq.timearr1[0];
                    this.searchReq.reviewEndDate = this.searchReq.timearr1[1];
                }else{
                    this.searchReq.reviewStartDate='';
                    this.searchReq.reviewEndDate='';
                }
                this.$http.post(`${this.proxyUrl}/entContent/audit/query`, JSON.stringify(this.searchReq), {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "contentType": "application/json",
                        "charset":"utf-8"
                    },
                    emulateJSON: true,
                    timeout: 5000,
                })
                    .then(res => {
                        this.tableLoading = false;
                        var res = res.data;
                        if(res.code==0){
                            this.tableData = res.data;
                            this.tableData.forEach(val => {
                                switch(val.contentType) {
                                    case 'ENTERPRISE':
                                        if(val.caiyinType==0){
                                            val.contentType = '主叫彩印';
                                        }else if(val.caiyinType==1){
                                            val.contentType = '被叫彩印';
                                        }else if(val.caiyinType==2){
                                            val.contentType = '热线彩印';
                                        }else if(val.caiyinType==3){
                                            val.contentType = '挂机短信';
                                        }else if(val.caiyinType==4){
                                            val.contentType = '挂机彩信';
                                        }else if(val.caiyinType==5){
                                            val.contentType = '群发短信';
                                        }else if(val.caiyinType==7){
                                            val.contentType = '数智反诈';
                                        }else if(val.caiyinType==8){
                                            val.contentType = '主叫挂机';
                                        }else if(val.caiyinType==9){
                                            val.contentType = '行业挂机短信';
                                        }
                                        else {
                                            val.contentType = '企业彩印'
                                        }
                                        break;
                                    case 'FEINNO':
                                        val.contentType = '新媒彩印'
                                        break;
                                    default:
                                        break;
                                };
                                switch(val.status) {
                                    case 1:
                                        val.status = '通过'
                                        break;
                                    case 2:
                                        val.status = '驳回'
                                        break;
                                    case 3:
                                        val.status = '已撤销'
                                        break;
                                    case 0:
                                        val.status = '待审核'
                                        break;
                                    case 4:
                                        val.status = '初审通过'
                                        break;
                                    case 5:
                                        val.status = '初审驳回'
                                        break;
                                    default:
                                        break;
                                }
                              let sensitiveWords = "";
                              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"03");
                              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"02");
                              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"01");
                              val.sensitiveWords = sensitiveWords;
                            })
                            this.pageTotal = res.totalCount;
                        }
                    });
            },
            handleSizeChange(val) {
                this.searchReq.pageIndex = 1;
                //每页条数
                this.searchReq.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                //当前页
                this.searchReq.pageIndex = val;
                this.search();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
        }
    };
</script>
<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .content-title{
        margin-top: 20px;
        margin-left: 20px;
        background-color: white;
    }
    .content-line{
        margin-top: 20px;
    }
    .el-table {
        margin-left: 3%;
        margin-top: 3%;
        border: 1px solid #ecebe9;
    }
</style>
<style>
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
