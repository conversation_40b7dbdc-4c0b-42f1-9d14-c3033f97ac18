<template>
  <div>
    <div class="user-tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="用户信息" name="first">用户信息</el-tab-pane>
        <el-tab-pane label="个人彩印" name="second">个人彩印</el-tab-pane>
        <el-tab-pane label="新媒彩印" name="third">新媒彩印</el-tab-pane>
        <el-tab-pane label="防诈骗号码来电提示服务" name="fourth">防诈骗号码来电提示服务</el-tab-pane>
        <el-tab-pane label="企业彩印" name="fiveth">企业彩印</el-tab-pane>
        <el-tab-pane label="黑白名单管理" name="sixth">黑白名单管理</el-tab-pane>
        <el-tab-pane label="内容设置历史" name="seventh">内容设置历史</el-tab-pane>
        <el-tab-pane label="套餐包订购历史" name="eightth">套餐包订购历史</el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'UserList',
    data() {
      return {
        tableData: []
      }
    },
    methods: {
        slideToggle:function(){

        }
    },
    created() {
    },
    components: {}
  }


</script>
<style scoped>
  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
  .user-tabs{
  width:94%;
  margin:0 auto;
  margin-top: 3%;
  }

</style>
