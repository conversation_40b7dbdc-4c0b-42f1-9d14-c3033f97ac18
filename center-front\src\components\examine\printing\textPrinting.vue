<template>
  <div>
    <h1 class="user-title">个人彩印</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="彩印ID">
          <el-input v-model="print.svNumber"  placeholder="彩印ID" size="small" :maxlength="32" style="width:170px;" clearable></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input v-model="print.svCard" placeholder="彩印内容" size="small" :maxlength="50" style="width:170px;" clearable></el-input>
        </el-form-item>
          <el-form-item label="提交时间">
              <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px" size="small"
                    />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit(print)" size="small">查询</el-button>
          </el-form-item>
      </el-form>
      </div>
<div>
<el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        tooltip-effect="dark"
        class="app-tab"
        :header-cell-class-name="tableheaderClassName">
        <el-table-column
          prop="svNumber"
          label="彩印ID"
          width="240">
        </el-table-column>
        <el-table-column
          prop="svCard"
          label="彩印内容"
          width="400"
          :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="groupName"
          label="内容分类"
          width="200">
        </el-table-column>
        <el-table-column
          prop="labelName"
          label="内容标签"
          width="200"
          :show-overflow-tooltip="true">
        </el-table-column>

        <el-table-column
          prop="submitTime"
          label="提交时间"
          width="200">
        </el-table-column>
        <el-table-column
          prop="svStatusName"
          label="审核意见"
          width="120">
        </el-table-column>
        <el-table-column
          prop="svAssessor"
          label="审核人"
          width="200">
        </el-table-column>
        <el-table-column
          prop="svCause"
          label="驳回原因"
          width="200"
          :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="svSssesstime"
          label="审核时间"
          width="200">
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120">
          <template slot-scope="scope">
            <el-button @click="openModify(scope.row)" type="text" >编辑</el-button>
            <el-button @click="delVisible=true;delRequest.svId=scope.row.svId;" type="text" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog
          width="30%"
          title="删除"
          :visible.sync="delVisible"
          :close-on-click-modal="false"
          append-to-body>
        <span style="font-size:20px;">确认删除彩印？</span>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="delVisible = false" size="small">取 消</el-button>
          <el-button type="primary" @click="delVisible = false;deltr();" size="small">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tableData.pageNum"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageTotal"  style="text-align: right;">
        </el-pagination>
        </div>
    
        <!-- 编辑 -->
            <el-dialog  title="编辑彩印内容" :visible.sync="updateVisible" :close-on-click-modal="false">
             <el-form :inline="true" class="demo-form-inline">
                <el-form-item label=" 彩印ID" :label-width="formLabelWidth">
                  {{updateRequest.svNumber}}
                </el-form-item>

                <el-form-item label="内容分类" :label-width="formLabelWidth">
                    <el-select v-model="updateRequest.groupId" placeholder="请选择" size="small">
                    <el-option
                        v-for="item in groupData"
                        :key="item.groupId"
                        :label="item.groupName"
                        :value="item.groupId">
                    </el-option>
                    </el-select>
                </el-form-item>


                <el-form-item label="内容标签" :label-width="formLabelWidth">
                    <el-button type="info" @click="visible = true" size="small">添加标签</el-button>
                    &nbsp;{{ckLabelNames}}
                     
                    <el-dialog title="添加标签" :visible.sync="visible" :close-on-click-modal="false"  append-to-body>
                      <div style="height:300px;overflow:auto;">
                        <el-form class="demo-form-inline" label-width="160px" justify="center">
                          <el-form-item>
                            <el-checkbox-group v-model="ckLabelIdArray">
                              <el-checkbox  @change="labelChange(item.liName)" v-for="item in labelData" :label="item.liId" :key="item.liName" style="display:inline-block;margin-left:30px;">{{item.liName}} </el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>
                        </el-form>
                      </div>
                      <div slot="footer" class="dialog-footer" style="text-align: right;">
                        <span>{{"已选"+ckLabelIdArray.length+"个标签"}}</span>
                        <el-button @click="visible = false" size="small">取消</el-button>
                        <el-button type="primary" @click="subCheckLabel();visible = false" size="small">确认</el-button>
                      </div>
                    </el-dialog>   
                </el-form-item>


                <el-form-item label="彩印内容" :label-width="formLabelWidth">
                    <el-input type="textarea" autosize v-model="updateRequest.svCard" clearable :maxlength="50" style="width:210px;"></el-input>
                </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer" style="text-align: right;">
                <el-button @click="updateVisible = false" size="small">关闭</el-button>
                <el-button @click="updatetr();" size="small" type="primary">保存</el-button>
                
            </div>
          </el-dialog>
  </div>
  </div>

</template>
<script>
import {formDate} from './../../../util/core.js'
  export default {
    name: 'UserList',
    data() {
      return {
        tableLoading: false,
        delVisible: false,
        pageTotal:0,
        tableData:[],
        updateVisible: false,
        
        tableData:[],
        value6: '',
        dateTime:[],
        print:{
            svNumber:"",
            svCard:"",
            startTime:"",
            endTime:"",
            pageNum:1,
            pageSize:10
        },
        formLabelWidth:'200px',
        ckLabelNames:'',
        ckLabelNameArray: [],
        ckLabelIdArray:[],
        groupData: [], //内容分类变量
        labelData: [], //内容标签变量
        visible: false,
        request: {
          csGroupName: "",
          csLabelName: ""
         },
        updateRequest: {
          svId: "",
          svNumber:"",
          svCard:"",
          groupId:"",
          labelId:""
        },
        delRequest: {
          svId: ""
        }
      }
    },
    mounted() {
      this.groupDatas();
      this.labelDatas();
    },
    methods: {
      //内容分类选项请求
    groupDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.groupData = res.data;
        });
    },
    //内容标签选项请求
    labelDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.labelData = res.data;
        });
    },

    labelChange: function(labelName) {
      let checkedBoolean=false;
      for(let i=0;i<this.ckLabelNameArray.length;i++){
        if(labelName===this.ckLabelNameArray[i]){
          this.ckLabelNameArray.splice(i,1);
          checkedBoolean=true;
        }
      }
      if(!checkedBoolean){
        this.ckLabelNameArray.push(labelName);
      }
    },
    subCheckLabel:function(){
      this.updateRequest.labelId = this.ckLabelIdArray.join(',');
      this.ckLabelNames = this.ckLabelNameArray.join(',');
    },

    //编辑
    openModify: function(row) {
        this.updateVisible = true;
        this.updateRequest.svId = row.svId;
        this.updateRequest.svNumber = row.svNumber;
        this.updateRequest.svCard = row.svCard;
        this.updateRequest.groupId = row.groupId;
        this.updateRequest.labelId = row.labelId;
        this.ckLabelNames = row.labelName;
        this.ckLabelNameArray = row.labelName.split(",");
        this.ckLabelIdArray = row.labelId.split(",");
        for (let i=0;i<this.ckLabelIdArray.length;i++){
            this.ckLabelIdArray[i] = parseInt(this.ckLabelIdArray[i]);
        }
    },
    handleSizeChange(val) {
      this.print.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyTaskCs`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
    handleCurrentChange(val) {
      this.print.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyTaskCs`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
      onSubmit(val) {
        const h = this.$createElement;
        if(this.dateTime && this.dateTime.length>0){
          val.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          val.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          val.startTime='';
          val.endTime='';
        }
        this.tableLoading=true;
        this.$http.post(`${this.proxyUrl}/content/auditBox/getMyTaskCs`, val).then((response) => {
              this.tableData=response.data.datas;
              this.pageTotal=response.data.pageTotal;
              this.tableLoading=false;
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '查询异常'
              });
               this.tableLoading=false;
          });
      },
      updatetr: function() {
        if(!this.updateRequest.groupId){
          this.$message("内容分类不能为空");
          return;
        }
        if(!this.updateRequest.labelId){
            this.$message("内容标签不能为空");
            return;
        }
        if(!this.updateRequest.svCard || !this.updateRequest.svCard.trim()){
            this.$message('内容不能为空');
            return;
        }
        this.updateRequest.svCard=this.updateRequest.svCard.trim();
        this.updateVisible = false;
        this.$http
          .post(`${this.proxyUrl}/content/auditBox/modMyAuditCs`, this.updateRequest, {
            emulateJSON: true
          })
          .then(function(res) {
            if (res.data.resStatus == "0") {
              this.onSubmit(this.print);
              this.$message.success('修改成功');
            } else if (res.data.resStatus == "1") {
              this.$message.error("修改失败 "+res.data.resText);
            }
        });
      },
      deltr: function() {
        console.log(this.delRequest.svId);
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/delMyAuditCs`, this.delRequest, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.onSubmit(this.print);
            this.$message.success('删除成功');
          } else if (res.data.resStatus == "1") {
            this.$message.error("删除失败 "+res.data.resText);
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
        return "table-head-th";
    },
  },
    created() {
    },
    components: {}
  }


</script>
<style>


  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
    margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }
  .el-pagination{
    margin-left:270px;
  }
.el-table {
  margin: 0 auto;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
    background-color: #F5F5F5;
}

</style>
