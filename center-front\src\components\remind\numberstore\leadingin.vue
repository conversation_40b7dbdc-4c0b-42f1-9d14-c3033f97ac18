<template>
    <div class="leadingin">
        <div class="content" style="width: 50%;margin-left: 20%;">
            <el-upload
                    class="upload-demo"
                    ref="upload"
                    action=""
                    :auto-upload='false'
                    :limit="1"
                    :on-change="handleChange"
                    :on-remove="handleRemove"
                    accept=".xls, .xlsx">
                <el-button size="small" type="primary">上传excel表</el-button>
            </el-upload>
            <div>
                <el-button type="text" size="small" @click="downloadYellowNumExcel">下载黄页模版文件</el-button>
                <br>
                <el-button type="text" size="small" @click="downloadDefraudExcel">下载诈骗模版文件</el-button>
                <br>
                <el-button type="text" size="small" @click="downloadSignExcel">下载标记模版文件</el-button>
                <br>
            </div>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="submit1" size="small">取 消</el-button>
            <el-button type="primary" @click="submit" size="small">确 定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'leadingin',
        data(){
            return{
                file:"",//上传文件流
                fileName:'',//文件名
            }
        },
        components: {},
        props:['alladdVisible'],
        watch:{
            'alladdVisible':function () {
                if(!this.alladdVisible){
                    this.calcelbutton();
                }
            }
        },
        methods:{
            //获取文件信息
            handleChange(file,fileList){
                if(this.file){
                    this.$message.warning('只能上传一个文件');
                    fileList.length = 1;
                    return;
                }
                this.file=file.raw;
                this.fileName=file.name;
            },
            handleRemove(file,fileList){
                this.file='';
                this.fileName='';
            },
            submit(){
                if(!this.file){
                    this.$message({
                        message:'请上传excel文件',
                        type:'warning'
                    })
                    return;
                }
                let formData=new FormData();
                formData.append('file',this.file);
                if(this.fileName=='黄页模版.xlsx'){
                    //上传黄页
                    postHeader('uploadYellowNumExcel',formData).then(res=>{
                        if(res.data.code == "0"){
                            this.$message.success("导入成功");
                        }else{
                            this.$message.error("导入失败");
                        }
                    })
                }else if(this.fileName=='诈骗模版.xlsx'){
                    //上传诈骗
                    postHeader('uploadDefraudExcel',formData).then(res=>{
                        if(res.data.code == "0"){
                            this.$message.success("导入成功");
                        }else{
                            this.$message.error("导入失败");
                        }
                    })
                }else if(this.fileName=='标记模版.xlsx'){
                    //上传标记
                    postHeader('uploadSignExcel',formData).then(res=>{
                        if(res.data.code == "0"){
                            this.$message.success("导入成功");
                        }else{
                            this.$message.error("导入失败");
                        }
                    })
                }else{
                    this.$message.error("请选择固定模版导入");
                    return;
                }
                this.$emit('addall');
            },
            //下载黄页模版
            downloadYellowNumExcel(){
                postDownloadHeader('downloadYellowNumExcel').then(res=>{
                    dowandFile(res.data,'黄页模版.xlsx');
                })
            },
            //下载诈骗模版
            downloadDefraudExcel(){
                postDownloadHeader('downloadDefraudExcel').then(res=>{
                    dowandFile(res.data,'诈骗模版.xlsx');
                })
            },
            //下载标记模版
            downloadSignExcel(){
                postDownloadHeader('downloadSignExcel').then(res=>{
                    dowandFile(res.data,'标记模版.xlsx');
                })
            },
            calcelbutton(){
                this.file='';
                this.fileName='';
                if(this.$refs.upload){
                    this.$refs.upload.clearFiles();
                }
            },
            submit1(){
                this.$emit('addall');
            }
        }
    }
</script>

<style scoped>
    .content{
        width: 640px;
    }
</style>
