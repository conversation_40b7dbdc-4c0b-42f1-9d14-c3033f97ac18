<template>
    <div class="exporteffect">
        <div class="content">
            <el-form :model="addForm" ref="addForm" class="demo-form-inline app-form-item" size="small" label-width="35%" style="width: 100%;">
                <el-form-item label="分类：" prop="provinceCode">
                    <el-select v-model="addForm.categoryId" placeholder="请选择" @change="qusourceList">
                        <el-option
                                v-for="item in classify"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!--<el-form-item label="标准类型：" prop="standardTypeId">-->
                    <!--<el-select v-model="addForm.standardTypeId" placeholder="请选择">-->
                        <!--<el-option-->
                                <!--v-for="item in StandardType"-->
                                <!--:key="item.standardTypeId"-->
                                <!--:label="item.standardTypeName"-->
                                <!--:value="item.standardTypeId">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                <!--</el-form-item>-->
                <el-form-item label="号码来源：" prop="categoryid">
                    <el-checkbox-group v-model="addForm.sourceIds">
                        <el-checkbox v-for="item in sourceList" :label="item.sourceId" :key="item.sourceId">{{item.sourceName}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="被删除时间：">
                    <el-date-picker
                            v-model="addForm.startTime"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
        </div>
        <div class="content1">
            <el-button type="primary" @click="exportexcle" size="small">导出excel</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'name',
        data(){
            return{
                sourceList :[],//来源
                addForm:{
                    numType:2,//号码库
                    categoryId:1,//分类
                    phoneNumber:'',//号码
                    standardTypeId:'',//标准类型
                    sourceIds:[],//来源id
                    markTypeIds:[],//标记类型数组
                    startTime:[],
                    startDate:'',//开始时间
                    endDate:'',//结束时间
                },
                //分类
                classify:[
                    {
                        id:1,
                        name:'诈骗'
                    },
                    {
                        id:2,
                        name:'黄页'
                    },
                    {
                        id:3,
                        name:'标记'
                    }
                ],
                searchForm:{
                    pageSize:10000,
                    pageNo:1
                },
                //标准类型
                StandardType:[]
            }
        },
        created(){
            this.searchStand();
            this.qusourceList();
        },
        methods:{
            //导出
            exportexcle(){
                if(this.addForm.startTime){
                    this.addForm.startDate = this.addForm.startTime[0];
                    this.addForm.endDate = this.addForm.startTime[1];
                }else{
                    this.addForm.startDate = '';
                    this.addForm.endDate = '';
                }
                postDownloadHeader('exportDeletedExcel',JSON.stringify(this.addForm)).then(res=>{
                    dowandFile(res.data,'已删除.xlsx');
                })
            },
            //查看号码来源
            qusourceList(){
                let vm = this;
                postHeader('querySource',JSON.stringify({categoryId:vm.addForm.categoryId})).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.sourceList = data.data.sourceList;
                }
            })
            },
            //查询标准类型
            searchStand() {
                let vm = this;
                postHeader('queryStandardType',JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.StandardType = data.data.standardTypeList;
                }
            })
            },
            handleCheckAllChange(list) {
                let a = [];
                list.markList.forEach(item => {
                    a.push(item.markTypeId)
                })
                list.checkedCities = list.checkAll ? a : [];
                list.isIndeterminate = false;
            },
            handleCheckedCitiesChange(list) {
                let checkedCount = list.checkedCities.length;
                list.checkAll = checkedCount === list.markList.length;
                list.isIndeterminate = checkedCount > 0 && checkedCount < list.markList.length;
            }
        },
        components: {}
    }
</script>

<style scoped>
    .content{
        width: 640px;
    }
    .content1{
        margin-top: 50px;
        text-align: center;
    }
    .listleft{
        width: 20%;
        float: left;
        margin-right: 0;
    }
    .listright{
        width: 70%;
        display: inline-block;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px !important;
        margin-right: 20px;
    }
</style>
