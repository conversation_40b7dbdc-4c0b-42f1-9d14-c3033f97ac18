package com.cs.param.controller;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.http.HttpServletResponse;

import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.cs.param.common.ParRedListCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.dao.ParRedListMapper;
import com.cs.param.execl.PhoneData;
import com.cs.param.services.RedisService;
import com.cs.param.services.SysTaskService;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;
import com.cy.jwt.JwtUtil.JWTHelper;
import com.cy.model.SysTaskModel;
import com.github.crab2died.ExcelUtils;

/**
 * 
 * 系统红名单Controller
 *
 */
@RequestMapping("/redList")
@RestController
public class RedListController {

	private static final Logger log = LoggerFactory.getLogger(RedListController.class);

	@Autowired
	private ParRedListMapper parRedListMapper;

	@Autowired
	private SysTaskService sysTaskService;

	@Autowired
	private RedisService redisService;

	/**
	 * 
	 * 获取系统红名单列表
	 *
	 */
	@RequestMapping(value = "getRedListPage")
	@CySysLog(methodName = "获取系统红名单列表", modularName = "公参模块", optContent = "获取系统红名单列表")
	public ResultListCommon getRedListPage(@ModelAttribute("common") ParRedListCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getRedListPage", "获取系统红名单列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parRedListMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parRedListMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getRedListPage", "获取系统红名单列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 新增系统红名单
	 *
	 */
	@RequestMapping(value = "addRedList")
	@CySysLog(methodName = "新增系统红名单", modularName = "公参模块", optContent = "新增系统红名单")
	public ResultCommon addRedList(@ModelAttribute("common") ParRedListCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "addRedList", "新增系统红名单", common);
		ResultCommon result = new ResultCommon();
		common.setSysUserName(JWTHelper.getSysUser().getSysUserName());
		result.setResStatus(1);
		try {
			if (parRedListMapper.insertParRedList(common) > 0) {
				result.setResStatus(0);

			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addRedList", "新增系统红名单出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 修改系统红名单
	 *
	 */
	@RequestMapping(value = "updateRedList")
	// @CySysLog(methodName = "修改系统红名单", modularName = "公参模块", optContent =
	// "修改系统红名单")
	public ResultCommon updateRedList(@ModelAttribute("common") ParRedListCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateRedList", "修改系统红名单", common);
		ResultCommon result = new ResultCommon();
		common.setSysUserName(JWTHelper.getSysUser().getSysUserName());
		result.setResStatus(1);
		try {
			// if (parRedListMapper.updateParRedListByPK(common) > 0) {
			// result.setResStatus(0);
			// // 更新缓存
			// ParRedListModel model = new ParRedListModel();
			// model.setPhone(common.getPhone());
			// redisService.redNumerToRedis(model);
			// }
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getRedListPage", "获取系统红名单列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除系统红名单，软删除
	 *
	 */
	@RequestMapping(value = "deleteRedList")
	@CySysLog(methodName = "删除系统红名单", modularName = "公参模块", optContent = "删除系统红名单")
	public ResultCommon deleteRedList(@ModelAttribute("common") ParRedListCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteRedList", "删除系统红名单", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parRedListMapper.deleteParRedListByPK(common) > 0) {
				result.setResStatus(0);
				redisService.deleteRedNumerFromRedis(common.getPhone());
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getRedListPage", "获取系统红名单列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 批量删除系统红名单，软删除
	 *
	 */
	@RequestMapping(value = "deleteParRedListBatch")
	@CySysLog(methodName = "批量删除系统红名单", modularName = "公参模块", optContent = "批量删除系统红名单")
	public ResultCommon deleteParRedListBatch(@RequestBody ParRedListCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParRedListBatch", "批量删除系统红名单", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parRedListMapper.deleteParRedListBatch(common.getPhones()) > 0) {
				result.setResStatus(0);
				redisService.deleteRedNumerFromRedis(common.getPhones());
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getRedListPage", "获取系统红名单列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 批量导入系统红名单
	 *
	 */
	@RequestMapping(value = "batchInsert")
	// @CySysLog(methodName = "批量导入系统红名单", modularName = "公参模块", optContent =
	// "批量导入系统红名单")
	public ResultCommon batchInsert(MultipartHttpServletRequest multipartRequest, HttpServletResponse response)
			throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "batchInsert", "批量导入系统红名单");
		ResultCommon result = new ResultCommon();
		SysTaskModel task = new SysTaskModel();
		task.setSysTaskName("批量导入系统红名单");
		task.setSysTriggerPeople(JWTHelper.getSysUser().getSysUserName());
		task.setSysTaskDesc("批量导入系统红名单");
		Integer sysId = null;
		try {
			sysId = sysTaskService.startUpload(task);
			task.setSysTaskId(sysId);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "SysTaskService.startUpload", e.getMessage());
		}
		try {
			String username = JWTHelper.getSysUser().getSysUserName();
			List<ParRedListCommon> redList = new ArrayList<>();
			String key = multipartRequest.getFileNames().next();
			MultipartFile file = multipartRequest.getFile(key);
			List<PhoneData> list = ExcelUtils.getInstance().readExcel2Objects(file.getInputStream(), PhoneData.class, 0,
					Integer.MAX_VALUE, 0);
			for (PhoneData phoneData : list) {
				ParRedListCommon model = new ParRedListCommon();
				// model.setIsBusiness(isBusiness);
				// model.setIsRemind(isRemind);
				// model.setIsPersonal(isPersonal);
				// model.setIsNewMedia(isNewMedia);
				model.setPhone(phoneData.getPhoneNo());
				model.setSysUserName(username);
				redList.add(model);
			}
			if (redList.size() > 0) {
				parRedListMapper.insertBatch(redList);
				result.setResStatus(0);
				// 更新缓存
				redisService.redNumerToRedis(redList);
			}
			if (sysId != null) {
				sysTaskService.endUpload(task);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "batchInsert", "批量导入系统红名单出错！", e);
			if (sysId != null) {
				sysTaskService.failUpload(task);
			}
			result.setResStatus(1);
		}
		return result;
	}

	@RequestMapping(value = "downloadTemplate")
	public void downloadTemplate(HttpServletResponse response) {
		LogUtil.info(log, LogUtil.BIZ, "getTemplate", "下载红名单模板");
		response.setCharacterEncoding("utf-8");
		response.setContentType("application/octet-stream");
		InputStream ins = null;
		try {
			Resource resource = new ClassPathResource("redlist_template.xlsx");
			ins = resource.getInputStream();
			response.setHeader("Content-Disposition", "attachment; filename=redlist_template.xlsx");
			IOUtils.copy(ins, response.getOutputStream());
			response.flushBuffer();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getTemplate", "下载红名单模板出错！", e);
		} finally {
			if (ins != null) {
				try {
					ins.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}
}
