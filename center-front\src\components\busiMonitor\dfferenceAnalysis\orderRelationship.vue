<template>
<div class="fun_page">
    <h1 class="user-title">订购关系</h1>
    <div class="user-line"></div>
    <div class="app-search">
    <!--图表信息-->
    <el-radio-group v-model="searchForm.dataType" size="small" class="tab_radio">
        <el-radio-button label="differenceNum">订购关系差异数量</el-radio-button>
    </el-radio-group>
    <div class="chartArea">
        <el-form :inline="true" :model="searchForm" size="small">
            <el-row>
                <el-col :span="24">
                    <el-form-item>
                        <el-radio-group v-model="searchForm.date1" size="small" @change="searchForm.startDate='';searchForm.endDate='';">
                            <el-radio-button label="yesterday">昨天</el-radio-button>
                            <el-radio-button label="7">7天</el-radio-button>
                            <el-radio-button label="30">30天</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="时间：">
                        <el-select v-model="dateType" style="width: 60px" @change="searchForm.date1=''">
                            <el-option label="天" value="date"></el-option>
                            <el-option label="月" value="month"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker placeholder="开始日期" v-model="searchForm.startDate" @change="searchForm.date1=''"
                                            style="width: 140px" :type="dateType"/>
                            至
                            <el-date-picker placeholder="结束日期" v-model="searchForm.endDate" @change="searchForm.date1=''"
                                            style="width: 140px"  :type="dateType"/>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" class="app-bnt" @click="searchQuery(1)">查询</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <div id="myChart" style="width:100%;height:300px;"></div>
    </div>

    <!--数据信息-->
    <el-table :data="tableData" border style="width: 100%" max-height=500 v-loading="tableLoading">
        <el-table-column prop="statsDate" label="时间"></el-table-column>
        <el-table-column prop="comparePlatform" label="对比平台"></el-table-column>
        <el-table-column prop="compareTotalCount" label="对比数据总量"></el-table-column>
        <el-table-column prop="sameCount" label="相同数据量"></el-table-column>
        <el-table-column prop="diffCount" label="差异数量"></el-table-column>
        <el-table-column prop="diffRadio" label="差异比例"></el-table-column>
        <el-table-column prop="csTextInfoNo" label="导出">
            <template slot-scope="scope">
                <el-button type="text" size="small" @click="orderDiffExportFile(scope.row.orderdiffId)">差异清单</el-button>
            </template>
        </el-table-column>
    </el-table>
    <div class="block app-pageganit" v-if="searchForm.total>0">
        <el-pagination  @size-change="handleSizeChange" @current-change="searchQuery" :current-page="searchForm.pageNo"
                            :page-sizes="[10,20,30,50,100]" :page-size="searchForm.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="searchForm.total">
        </el-pagination>
    </div>
</div></div>
</template>

<script>
    import {orderDiffExport,orderRelationQuery} from './dfferenceAnalysisServer.js';
    import {dowandFile,formDate} from './../../../util/core.js';
    // 引入 ECharts 主模块
    var echarts = require('echarts/lib/echarts');
    // 引入柱状图
    require('echarts/lib/chart/bar');
    // 引入提示框和标题组件
    require('echarts/lib/component/tooltip');
    require('echarts/lib/component/title');
    import 'echarts/lib/component/legendScroll';
    export default {
        name: 'orderRelationship',
        data() {
            return {
              data:new Array(),//对比数据总量
              differenceCount:new Array(),//差异量
              differencProportion:new Array(),//差异比例
              dataName:new Array(),
              dateType:'date',
              searchForm:{
                dataType:'differenceNum',
                date1:'7',
                pariodDate:[],
                startDate:'',
                endDate:'',
                pageNo:1,
                pageSize:10,
                total:0
              },
              pickerOptions:{
                disabledDate:function (today) {
                  return today.getTime()>Date.now();
                }
              },
              tableLoading:false,
              tableData:[]
            }
        },
        mounted(){
          this.searchQuery(1);
        },
        methods: {
            draw(){
                var myChart = echarts.init(document.getElementById('myChart'));
                var option = {
                    color: ['#0099FF','#CCCCCC','#FF00CC'],
                    tooltip : {
                        trigger: 'axis',
                        axisPointer : {          
                            type : 'shadow'
                        }
                    },
                    legend: {
                        data: ['对比数据总量', '差异数量', '差异比例'],
                    },
                    xAxis:[
                        {
                            type: 'category',
                            axisTick: {show: false},
                            data: this.dataName
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            splitNumber:10
                        }
                    ],
                    series: [
                        {
                            name: '对比数据总量',
                            type: 'bar',
                            data: this.data
                        },
                        {
                            name: '差异数量',
                            type: 'bar',
                            data: this.differenceCount
                        },
                        {
                            name: '差异比例',
                            type: 'bar',
                            data: this.differencProportion
                        }
                    ]
                };
                myChart.setOption(option);
            },
            searchQuery(pageNo){
                let params={};
                this.searchForm.pageNo=pageNo;
                if(this.searchForm.date1==7){
                    params={
                        type:1,
                        pageNo:this.searchForm.pageNo,
                        pageSize:this.searchForm.pageSize,
                        endDate:formDate(new Date(),'yyyy-MM-dd'),
                        startDate:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),'yyyy-MM-dd'),
                    }
                }else if(this.searchForm.date1=='yesterday'){
                    params={
                        type:1,
                        pageNo:this.searchForm.pageNo,
                        pageSize:this.searchForm.pageSize,
                        startDate:formDate(new Date(new Date().getTime() - 3600 * 1000 * 24),'yyyy-MM-dd'),
                        endDate:formDate(new Date(new Date().getTime() - 3600 * 1000 * 24),'yyyy-MM-dd'),
                    }
                }else if(this.searchForm.date1==30){
                    params={
                        type:1,
                        pageNo:this.searchForm.pageNo,
                        pageSize:this.searchForm.pageSize,
                        endDate:formDate(new Date(),'yyyy-MM-dd'),
                        startDate:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-30),'yyyy-MM-dd'),
                    }
                }else{
                    if(this.searchForm.startDate===''){
                        this.$message('请选择开始时间');
                        return;
                    }else if(this.searchForm.endDate===''){
                        this.$message('请选择结束时间');
                        return;
                    }else if(new Date(this.searchForm.startDate).getTime()>new Date(this.searchForm.endDate).getTime()){
                        this.$message('开始时间不得小于结束时间');
                        return;
                    }
                    let type=1;
                    if(this.dateType==='date'){
                        type=1;
                    }else{
                        type=2;
                    }
                    params={
                        type:type,
                        pageNo:this.searchForm.pageNo,
                        pageSize:this.searchForm.pageSize,
                        startDate:formDate(new Date(this.searchForm.startDate),'yyyy-MM-dd'),
                        endDate:formDate(new Date(this.searchForm.endDate),'yyyy-MM-dd'),
                    }
                }
                this.tableLoading=true;
                orderRelationQuery('queryOrderDiffByTime',params).then(res=>{
                    if(res.code===0){
                        this.tableLoading=false;
                        this.data=new Array();
                        this.dataName=new Array();
                        this.differenceCount=new Array();
                        this.differencProportion=new Array();
                        this.tableData=new Array();
                        if(res.data.orderDiffList){
                             this.tableData=res.data.orderDiffList;
                            this.searchForm.total=res.data.total;
                            for(let i=0;i<this.tableData.length;i++){
                                this.data.push(this.tableData[i].compareTotalCount);
                                this.dataName.push(this.tableData[i].comparePlatform);
                                this.differenceCount.push(this.tableData[i].diffCount);
                                this.differencProportion.push(this.tableData[i].diffRadio);
                            }

                        }
                        this.draw();
                    }
                })
            },
            //分页查询
            handleSizeChange(pageSize){
                this.searchForm.pageSize=pageSize;
                this.searchQuery(1);
            },
            //导出差异清单
            orderDiffExportFile(id){
                orderDiffExport('exportOrderDiffDetail',{orderDiffId:id}).then(res=>{
                     dowandFile(res,'订购关系差异清单.xlsx')
                })
            }
        }
    }
</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
    .el-radio-button:first-child .el-radio-button__inner,.el-radio-button:last-child .el-radio-button__inner{
        border-radius:0;
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #434343;
        border-color:#DDDFE6;
        box-shadow:-1px 0 0 0 #DDDFE6;
        color:#fff;
    }
    .chartArea{
        border:1px solid #DDDFE6;
        margin-top:-1px;
    }
    .chartArea form{
        margin:20px 0 0 20px;
    }
    .chartArea .el-radio-button__inner{
        background:transparent;
        border: 1px solid transparent;
    }
    .chartArea .el-radio-button__orig-radio:checked+.el-radio-button__inner{
        background-color:#3B9ED8;
    }
</style>
