package com.cy.user.model;

import java.io.Serializable;

public class ParPackage implements Serializable {
    private Integer Id;
    private String productMark;
    private String chargeCode;
    private String companyCode;
    private String businessCode;
    private String setMealName;
    private String setMealType;
    private String setMealMarkCode;
    private String sendMark;
    private String setMealFunction;
    private String setDealDesc;

    public Integer getId() {
        return Id;
    }

    public void setId(Integer id) {
        Id = id;
    }

    public String getProductMark() {
        return productMark;
    }

    public void setProductMark(String productMark) {
        this.productMark = productMark;
    }

    public String getChargeCode() {
        return chargeCode;
    }

    public void setChargeCode(String chargeCode) {
        this.chargeCode = chargeCode;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getSetMealName() {
        return setMealName;
    }

    public void setSetMealName(String setMealName) {
        this.setMealName = setMealName;
    }

    public String getSetMealType() {
        return setMealType;
    }

    public void setSetMealType(String setMealType) {
        this.setMealType = setMealType;
    }

    public String getSetMealMarkCode() {
        return setMealMarkCode;
    }

    public void setSetMealMarkCode(String setMealMarkCode) {
        this.setMealMarkCode = setMealMarkCode;
    }

    public String getSendMark() {
        return sendMark;
    }

    public void setSendMark(String sendMark) {
        this.sendMark = sendMark;
    }

    public String getSetMealFunction() {
        return setMealFunction;
    }

    public void setSetMealFunction(String setMealFunction) {
        this.setMealFunction = setMealFunction;
    }

    public String getSetDealDesc() {
        return setDealDesc;
    }

    public void setSetDealDesc(String setDealDesc) {
        this.setDealDesc = setDealDesc;
    }
}
