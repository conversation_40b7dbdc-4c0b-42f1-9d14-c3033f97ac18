package com.cy.user.model;


import java.util.Date;

public class CsSensitiveLogsRecordModel {
    private Integer id;
    private String msisdn;
    private String name;
    private String result;
    private String operType;
    private String callResult;
    private Date createTime;
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getOperType() {
        return operType;
    }

    public void setOperType(String operType) {
        this.operType = operType;
    }

    public String getCallResult() {
        return callResult;
    }

    public void setCallResult(String callResult) {
        this.callResult = callResult;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CsSensitiveLogsRecordModel{" +
                "id=" + id +
                ", msisdn='" + msisdn + '\'' +
                ", name='" + name + '\'' +
                ", result='" + result + '\'' +
                ", operType='" + operType + '\'' +
                ", callResult='" + callResult + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }

    public CsSensitiveLogsRecordModel() {
    }

}
