<template>
    <div class="editstandard">
        <div class="content">
            <el-form :inline="true" class="demo-form-inline" size="small">
                <el-form-item>
                    <span>标准类型名称:</span>
                </el-form-item>
                <el-form-item>
                    <el-input placeholder="类型名称" v-model="addfrom.standardType.standardTypeName" style="width:250px;" ></el-input>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                    <span>标记类型信息:</span>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline" size="small">
                <el-form-item v-for="item in tableList" :label="item.sourceName+':'" label-width="100px" class="standlist">
                    <el-checkbox-group  v-model="checkList" style="width: 500px;">
                        <el-checkbox @change="changebox(val)" v-for="val in item.markList" :label="val.markTypeId" :key="val.markTypeId">{{val.markType}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
        </div>
        <div class="content1">
            <el-button type="primary" @click="submit" size="small">提交</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js'
    export default {
        name: 'editstandard',
        data(){
            return{
                checkList: [],//选中项
                tableList:[],
                //编辑传参
                addfrom:{
                    type:2,//编辑
                    standardType:{
                        standardTypeId:18,//标准类型id
                        standardTypeName:""//编辑的标准类型的名称
                    },
                    standardTypeDetailList:[

                    ]
                },
                alllistck:[]
            }
        },
        components: {},
        props:['standardTypeId'],
        created(){
            this.details();
        },
        watch:{
            standardTypeId(){
                this.details();
            }
        },
        methods:{
            submit(){
                //编辑
                postHeader('addOrUpdateStandardType',JSON.stringify(this.addfrom)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("编辑成功");
                    }else{
                        vm.$message.error("编辑失败");
                    }
                })
                this.$emit('editStandard');
            },
            //查询类型信息
            details(){
                let vm = this;
                postHeader('queryNumSourceDetail').then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        let list = data.data.sourceDetailList;
                        vm.alllistck = data.data.sourceDetailList;
                        let listtype = []
                        list.forEach(item => {
                            let onoff = true;
                            listtype.forEach(val => {
                                if(item.sourceName == val.sourceName){
                                    onoff = false;
                                    val.markList.push(item)
                                }
                            })
                            if(onoff){
                                listtype.push({'sourceName':`${item.sourceName}`,'sourceId':`${item.sourceId}`,'markList':[item]});
                            }
                        })
                        vm.tableList = listtype;
                        //查询详情
                        postHeader('queryMarkTypeByConditions',JSON.stringify({standardTypeId:this.standardTypeId})).then(res=>{
                            let data = res.data;
                            if(data.code==0){

                            }
                        })
                    }
                })
            },
            //选中项
            changebox(val){
                let vm = this;
                this.checkList.forEach(item=>{
                    this.alllistck.forEach(zit=>{
                        if(item==zit.markTypeId){
                            vm.addfrom.standardTypeDetailList.push(zit);
                        }
                    })
                })
            },
        }
    }
</script>

<style scoped>
    .addtitle{
        font-size: 18px;
        margin-left: 20px;
        margin-top: -20px;
    }
    .content{
        width: 640px;
        margin: 50px auto;
    }
    .standlist{
        margin-left: 40px;
        width: 640px;
        border-bottom: 1px dashed #ccc;
    }
    .content1{
        text-align: center;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px;
        margin-right: 20px;
    }
</style>
