
package com.cs.param.model;

public class ParSmsForModel {
	private Integer id;
	private String orderNo;
	private String order;
	private String partition;// 判断用户是否属于分区
	private String continued;// 是否继续进行指令处理
	private String partitionCode;
	private String partitionName;
	private String partitionType;
	private String status;
	private String content;
	private String remarks;
	private String isDelete;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getPartition() {
		return partition;
	}

	public void setPartition(String partition) {
		this.partition = partition;
	}

	public String getContinued() {
		return continued;
	}

	public void setContinued(String continued) {
		this.continued = continued;
	}

	public String getPartitionCode() {
		return partitionCode;
	}

	public void setPartitionCode(String partitionCode) {
		this.partitionCode = partitionCode;
	}

	public String getPartitionName() {
		return partitionName;
	}

	public void setPartitionName(String partitionName) {
		this.partitionName = partitionName;
	}

	public String getPartitionType() {
		return partitionType;
	}

	public void setPartitionType(String partitionType) {
		this.partitionType = partitionType;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

}
