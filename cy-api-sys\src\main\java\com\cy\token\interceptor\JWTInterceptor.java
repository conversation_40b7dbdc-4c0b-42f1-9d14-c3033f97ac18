package com.cy.token.interceptor;

import jakarta.servlet.http.HttpServletRequest;

import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import com.cy.common.JSONUtil;
import com.cy.jwt.JwtUtil.JWTHelper;
import com.cy.jwt.JwtUtil.JWTUtils;
import com.cy.model.SysUserModel;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;

public class JWTInterceptor implements HandlerInterceptor {

	// jwt的私钥
	@Value("${jwt.key:471C1F3FC1DD7BB8CD0341B03E4BE59E}")
	private String key;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {

		String token = request.getHeader("token");
		if(StringUtils.isNotBlank(token)){
		    
    		Jws<Claims> parseClaimsJws = JWTUtils.parseToken(token, key);
    		if (parseClaimsJws == null) {
    			response.setStatus(401);
    			response.getWriter().write("{\"result\":\"token is expired!\"}");
    			return false;
    		}
    
    		JWTHelper.TOKEN.set(token);
    		Object object = parseClaimsJws.getBody().get(JWTUtils.JWT_KEY_USER_INFO);
    		SysUserModel model = (SysUserModel) JSONUtil.JSONToObj(object.toString(), SysUserModel.class);
    		JWTHelper.CURRENT_USER.set(model);
		} else {
			JWTHelper.TOKEN.set(null);
		}
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {

	}

}
