import {post} from './../servers/httpServer.js';

const state={
   userInfo:{},//保存用户信息
};
const mutations={
  //保存用户信息
  USER_INFO(state,value){
    state.userInfo=value;
  }
};
const actions={
  //菜单项查询
  menuListQuery:async({},params)=>{
    let result=await post('/sys/sysRole/getRoleResources',params);
    return result.data;
  },
  //新增系统红名单
  addSysRed:async({},params)=>{
    let result=await post('/param/redList/batchInsert',params);
    return result.data;
  },
  //新增系统黑名单
  addSysBlack:async({},params)=>{
    let result=await post('/param/blackList/batchInsert',params);
    return result.data;
  },
  refuseListQuery:async({},params)=>{
    let result=await post('/sys/sysUser/sysReject/getList',params);
    return result.data;
  },

};
const coreModule={
  namespaced:true,
  state:state,
  mutations:mutations,
  actions:actions
};
export default coreModule;