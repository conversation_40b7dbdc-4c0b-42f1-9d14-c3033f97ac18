package com.cy.user.vo;

import java.util.List;

public class CustomerRuleVo {
	private String ruleId;
	private String gourpId;
	private String phone;
	/**
	 * 0正常1删除2已过期
	 */
	private String ruleStatus;
	/**
	 * 0主叫1被叫
	 */
	private String sendType;
	/**
	 * //0：所有1：号码分组2：指定号码当彩印显示类型为他显时生效优先级从上往下递增
	 */
	private String recType;

	private String recTypeName;

	private String recPhone;
	private List<String> recPhones;
	/**
	 * 0：默认 1：USSD短信发送 2：免提短信(闪信)发送 3：普通短信发送
	 */
	private String sendMode;
	/**
	 * 1文本彩印，2彩印盒，3图片彩印，4视频彩印
	 */
	private String csType;
	private String sendTime;
	private String updateTime;
	private String subService;
	private String crEndDate;

	public String getCrEndDate() {
		return crEndDate;
	}

	public void setCrEndDate(String crEndDate) {
		this.crEndDate = crEndDate;
	}

	public List<String> getRecPhones() {
		return recPhones;
	}

	public void setRecPhones(List<String> recPhones) {
		this.recPhones = recPhones;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getRuleStatus() {
		return ruleStatus;
	}

	public void setRuleStatus(String ruleStatus) {
		this.ruleStatus = ruleStatus;
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public String getSendTime() {
		return sendTime;
	}

	public String getRecType() {
		return recType;
	}

	public void setRecType(String recType) {
		this.recType = recType;
	}

	public String getRecPhone() {
		return recPhone;
	}

	public void setRecPhone(String recPhone) {
		this.recPhone = recPhone;
	}

	public void setSendTime(String sendTime) {
		this.sendTime = sendTime;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

	public String getSubService() {
		return subService;
	}

	public void setSubService(String subService) {
		this.subService = subService;
	}

	public String getSendMode() {
		return sendMode;
	}

	public void setSendMode(String sendMode) {
		this.sendMode = sendMode;
	}

	public String getCsType() {
		return csType;
	}

	public void setCsType(String csType) {
		this.csType = csType;
	}

	public String getGourpId() {
		return gourpId;
	}

	public void setGourpId(String gourpId) {
		this.gourpId = gourpId;
	}

	public String getRecTypeName() {
		return recTypeName;
	}

	public void setRecTypeName(String recTypeName) {
		this.recTypeName = recTypeName;
	}

}
