<template>
<div>
    <h1 class="user-title">携转号码导入</h1>
    <div class="user-line"></div>
    <div style="margin:10px 0 0 24px;">
      <el-form style="margin-top:15px;" >
          <el-form-item label="通过文件导入用户号码:">
            <el-upload
                    class="upload-demo"
                    ref="upload"
                    action=""
                    :auto-upload='false'
                    :limit="1"
                    :on-remove='fileRemove'
                    :on-change="handleChange"
                    accept=".xls, .xlsx">
              <el-button size="mini" type="primary" style="font-size:14px;">上传excel表</el-button>
              <div slot="tip" class="el-upload__tip"></div>
            </el-upload>
            <a :href="`${this.proxyUrl}/user/batchCarryTurn/getTemplate`">下载模板</a><span style="color: red;margin-left: 15px">注:每次批设号码不能超过10万</span>
          </el-form-item>
        </el-form>
      <el-button type="primary" :disabled="disabled" style="margin-top:15px;width:106px;height:28px;" @click="submitForm()" size="small" class="app-bnt">提 交</el-button>
    </div>
</div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
export default {
  data() {
    return {
      request:{},
      disabled:false,
      file:'',
    };
  },
    mounted(){
    //this.queryPkgContend();
    // this.list = this.states.map(item => {
    //     return { value: item, label: item };
    //   });
  },
  methods: {

    //删除上传的文件
    fileRemove(file){

    },
    //上传文件
    handleChange(file,fileList){
      this.file=file.raw;
    },
    downloadTemplate:function(){
        window.location.href=`${this.proxyUrl}/user/batchCarryTurn/getTemplate`;
    },

    //提交批量用户信息
    clear(){
      this.$refs.upload.clearFiles();
      this.file = '';
    },
    submitForm(){
        this.disabled = true;
      if(!this.file){
        this.$message({
          message:'请上传excel文件',
          type:'warning'
        })
        this.disabled = false;
        return false;
      }
      this.disabled = true;
      let formData=new FormData();
      formData.append('file',this.file);
      post('/user/batchCarryTurn/batchCommit',formData).then(res=>{
        if(res.data.status== 0){
          this.clear();
          this.$message({ message:'提交成功',type:'success'});
        }else{
            this.$message({ message:res.data.resText,type:'error'});
        }
      })
        this.disabled = false;
    }
  }
};
</script>
<style scoped>
  label{
    font-weight: bold;
    font-size:16px !important;
  }
.fl {
  float: left;
}
.user-title {
     padding: 10px 0px 0px 0px;
}
.user-line {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border-bottom: 1px solid #439ae6;
}
.user-search2 {
  width: 40%;
  margin-left: 0;
}
/* 弹窗checkbox样式 */
.el-form-item__content{
  margin:0 !important;
}
</style>
