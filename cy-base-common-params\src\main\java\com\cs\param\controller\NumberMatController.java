package com.cs.param.controller;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.cs.param.common.ParNumberCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.dao.ParBrandMapper;
import com.cs.param.dao.ParNetworkMapper;
import com.cs.param.dao.ParNumberMapper;
import com.cs.param.dao.ParTerminalMapper;
import com.cs.param.execl.SectionData;
import com.cs.param.model.ParBrandModel;
import com.cs.param.model.ParNetworkModel;
import com.cs.param.model.ParTerminalModel;
import com.cs.param.model.SectionModel;
import com.cs.param.services.SysTaskService;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;
import com.cy.jwt.JwtUtil.JWTHelper;
import com.cy.model.SysTaskModel;
import com.github.crab2died.ExcelUtils;

/**
 * 
 * 号段管理Controller
 *
 */
@RequestMapping("/numberMat")
@RestController
public class NumberMatController {

	private static final Logger log = LoggerFactory.getLogger(NumberMatController.class);

	@Autowired
	private ParBrandMapper parBrandMapper;

	@Autowired
	private ParTerminalMapper parTerminalMapper;

	@Autowired
	private ParNetworkMapper parNetworkMapper;

	@Autowired
	private ParNumberMapper parNumberMapper;

	@Autowired
	private SysTaskService sysTaskService;

	@Autowired
	private StringRedisTemplate redisTemplate;

	/**
	 * 
	 * 获取品牌列表
	 *
	 */
	@RequestMapping(value = "getAllBrand")
	public List<ParBrandModel> getAllBrand() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "获取品牌列表");
		try {

			return parBrandMapper.getAllParBrand();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "获取所有品牌列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 获取终端列表
	 *
	 */
	@RequestMapping(value = "getAllTerminal")
	public List<ParTerminalModel> getAllTerminal() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "获取终端列表");
		try {

			return parTerminalMapper.getAllParTerminal();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "获取终端列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 获取所在网络列表
	 *
	 */
	@RequestMapping(value = "getAllNetwork")
	public List<ParNetworkModel> getAllNetwork() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "获取所在网络列表");
		try {

			return parNetworkMapper.getAllParNetwork();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "获取所在网络列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 新增号段
	 *
	 */
	@RequestMapping(value = "addNumber")
	@CySysLog(methodName = "新增号段", modularName = "公参模块", optContent = "新增号段")
	public ResultCommon addNumber(@ModelAttribute("common") ParNumberCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "新增号段", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parNumberMapper.insertParNumber(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "新增号段出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 修改号段
	 *
	 */
	@RequestMapping(value = "updateNumber")
	@CySysLog(methodName = "修改号段", modularName = "公参模块", optContent = "修改号段")
	public ResultCommon updateNumber(@ModelAttribute("common") ParNumberCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "修改号段", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parNumberMapper.updateParNumberByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "修改号段出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取号段列表
	 *
	 */
	@RequestMapping(value = "getNumberPage")
	@CySysLog(methodName = "获取号段列表", modularName = "公参模块", optContent = "查询号段")
	public ResultListCommon getNumberPage(@ModelAttribute("common") ParNumberCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "获取号段列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parNumberMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parNumberMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "获取号段列表！", e);
		}
		return result;
	}

	@RequestMapping(value = "deleteNumber")
	@CySysLog(methodName = "删除号段", modularName = "公参模块", optContent = "删除号段")
	public ResultCommon deleteNumber(@ModelAttribute("common") ParNumberCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "", "删除号段", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parNumberMapper.deleteByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "删除号段出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 批量导入号段信息
	 *
	 */
	@RequestMapping(value = "batchInsert")
	public ResultCommon batchInsert(MultipartHttpServletRequest multipartRequest, HttpServletResponse response)
			throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "batchInsert", "批量导入号段信息");
		ResultCommon result = new ResultCommon();
		result.setResStatus(0);
		SysTaskModel task = new SysTaskModel();
		task.setSysTaskName("批量导入号段信息");
		task.setSysTriggerPeople(JWTHelper.getSysUser().getSysUserName());
		task.setSysTaskDesc("批量导入号段信息");
		Integer sysId = null;
		try {
			sysId = sysTaskService.startUpload(task);
			task.setSysTaskId(sysId);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "SysTaskService.startUpload", e.getMessage());
		}
		try {
			List<ParNumberCommon> numberlist = new ArrayList<>();
			String key = multipartRequest.getFileNames().next();
			MultipartFile file = multipartRequest.getFile(key);
			List<SectionData> list = ExcelUtils.getInstance().readExcel2Objects(file.getInputStream(),
					SectionData.class, 0, Integer.MAX_VALUE, 0);
			for (SectionData sectionData : list) {
				ParNumberCommon common = new ParNumberCommon();
				if (sectionData.getPhoneSection() != null && !"".equals(sectionData.getPhoneSection())) {
					List<SectionModel> sectionModels = parNumberMapper.queryIsDouble(sectionData.getPhoneSection());
					if(!CollectionUtils.isEmpty(sectionModels)){
						continue;
					}
					common.setPhoneSection(sectionData.getPhoneSection());
					common.setOptCode(sectionData.getOptCode());
					common.setNetCode(Util.getPrefix(sectionData.getNetCode()));
					common.setProvinceCode(Util.getPrefix(sectionData.getProvinceCode()));
					common.setRegionCode(Util.getPrefix(sectionData.getRegionCode()));
					common.setHlrAddr(sectionData.getHlrAddr());
					common.setTerminalCode(Util.getPrefix(sectionData.getTerminalCode()));
					numberlist.add(common);
				}
			}
			if (numberlist.size() > 0) {
				parNumberMapper.insertBatch(numberlist);
			}
			if (sysId != null) {
				sysTaskService.endUpload(task);
			}
		} catch (InvalidFormatException e) {
			LogUtil.error(log, LogUtil.BIZ, "batchInsert", "批量导入号段信息！", e);
			if (sysId != null) {
				sysTaskService.failUpload(task);
			}
			result.setResStatus(2);
			result.setResText("模板数据格式错误，请重试");
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "batchInsert", "批量导入号段信息！", e);
			if (sysId != null) {
				sysTaskService.failUpload(task);
			}
			result.setResStatus(1);
			result.setResText("上传处理失败，请重试");
		}
		return result;
	}

	/**
	 * 
	 * 号段导出Execl
	 *
	 */
	@RequestMapping(value = "exportExecl")
	@ResponseBody
	public ResultCommon exportExecl(@RequestBody ParNumberCommon common, HttpServletResponse response)
			throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "exportExecl", "号段导出Execl");
		ResultCommon result = new ResultCommon();
		result.setResStatus(0);
		try {
			response.setContentType("application/binary;charset=UTF-8");
			ServletOutputStream out = response.getOutputStream();
			List<SectionData> list = parNumberMapper.queryDataForExecl(common);
			ExcelUtils.getInstance().exportObjects2Excel(list, SectionData.class, true, null, true, out);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "exportExecl", "号段导出Execl失败", e);
			result.setResStatus(1);
			result.setResText("号段导出Execl失败，请重试");
		}
		return result;
	}

	/**
	 * 下载号段模板
	 * 
	 */
	@RequestMapping(value = "downloadTemplate")
	public void downloadTemplate(HttpServletResponse response) {
		LogUtil.info(log, LogUtil.BIZ, "getTemplate", "下载号段模板");
		response.setCharacterEncoding("utf-8");
		response.setContentType("application/octet-stream");
		InputStream ins = null;
		try {
			Resource resource = new ClassPathResource("section_template.xlsx");
			ins = resource.getInputStream();
			response.setHeader("Content-Disposition", "attachment; filename=section_template.xlsx");
			IOUtils.copy(ins, response.getOutputStream());
			response.flushBuffer();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getTemplate", "下载号段模板出错！", e);
		} finally {
			if (ins != null) {
				try {
					ins.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 
	 * 获取所在网络列表
	 *
	 */
	@RequestMapping(value = "initSectionToRedis")
	public String initSectionToRedis() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "initSectionToRedis", "初始化数据号段数据到redis！");
		try {
			boolean isContinue = true;
			ParNumberCommon number = new ParNumberCommon();
			int start = 0;
			int pageSize = 10000;
			number.setPageSize(pageSize);
			while (isContinue) {
				number.setPageNum(start * pageSize);
				List<SectionModel> list = parNumberMapper.initRedis(number);
				String key = "SectionForRule:";
				if (list != null) {
					for (SectionModel sectionModel : list) {
						redisTemplate.opsForValue().set(key + sectionModel.getSection(),
								sectionModel.getPartitionCode() + "," + sectionModel.getProvinceCode());
					}
					start++;
					if (list.size() < pageSize) {
						isContinue = false;
					}
				} else {
					isContinue = false;
				}

			}
			return "SUCCESS";
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "初始化数据号段数据到redis出错！", e);
			return "FAIL";
		}
	}

}
