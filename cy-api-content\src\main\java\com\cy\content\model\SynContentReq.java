package com.cy.content.model;

import lombok.Data;

@Data
public class SynContentReq {
    /**
     * 企业ID
     */
    private String enterpriseID;

    /**
     * 企业名称
     */
    private String enterpriseName;


    /**
     * 模板内容，模板中的变量符使用#*#来标识，可修改，新增必填
     */
    private String content;

    /**
     * 行业类型，可修改，新增必填
     */
    private String industrytype;

    /**
     * 内容支持的运营商:1移动2联通3电信
     * 多个时用,分割，默认支持移动。可修改
     */
    private String operator;
    /**
     * 资质图片，异网必填，base64转码后的文件，多个用|分割，单个资质图片格式 ：key文件名称 : Base64编码后的文件（当前支持png|jpeg|jpg 的文件格式），最多5个。可修改
     */
    private String qualificationsFiles;
    /**
     * 签名，当支持异网时必传，传入后，投递文本会拼接成【{sign}】{CYContent}；
     */
    private String sign;
    /**
     * 投递类型：1.闪信，2.短信，新增必填
     */
    private String deliveryType;
    /**
     * 彩印ID，修改、删除时必填
     */
    private String cyID;

    private String operType;
}
