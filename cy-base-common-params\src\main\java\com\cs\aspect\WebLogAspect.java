package com.cs.aspect;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cs.param.constants.CommonResultCode;
import com.cs.param.constants.Constants;
import com.cs.param.constants.LogConstant;
import com.cs.param.utils.ManualLogUtils;
import com.cs.param.utils.ResultObject;
import com.cs.param.utils.SafeHttpServletRequestWrapper;
import com.cs.param.utils.ToolUtil;
import com.cs.param.utils.WebLogUtil;
import com.cs.param.utils.WriteThreadContextUtil;

/**
 * 
 * Web日志切面
 * 
 * <AUTHOR>
 * @version [版本号, 2022年5月11日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Aspect
@Component
public class WebLogAspect
{
    private static String localIp;
    
    static org.slf4j.Logger logger = LoggerFactory.getLogger(WebLogAspect.class);
    
    static
    {
        try
        {
            localIp = ToolUtil.getLocalHostAddress().getHostAddress();
        }
        catch (Exception e)
        {
            localIp = "unknown";
        }
    }
    
    /**
     * 存放初始时间
     */
    private ThreadLocal<Long> startTime = new ThreadLocal<Long>();
    
    /**
     * 检查点配置 resdcm接口
     * 
     * <AUTHOR> 2018年5月16日
     * @see [类、类#方法、类#成员]
     */
    @Pointcut("execution(public * com.cs.param.api.controller.CommonParamApiController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.getBlackListPage(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.addBlackList(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.delBlackList(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.updateBlackList(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.downloadTemplate(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.batchInsert(..))"
    		+ "|| execution(public * com.cs.param.controller.BlackListController.delBatch(..))"
    		+ "|| execution(public * com.cs.param.controller.CarryTurnController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.NumberMatController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.PackageController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.PushRulesController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.RedListController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getAllParDefperContant(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getAllParDefperBox(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getDefperBoxById(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getThreConfPage(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getParThreConfDetail(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.updateParThreConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.addParThreConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.deleteParThreConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getOtherConfPage(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.getParOtherConfDetail(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.updateParOtherConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.deleteParOtherConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegConfController.addParOtherConf(..))"
    		+ "|| execution(public * com.cs.param.controller.RegionMatController.*(..))"
    		+ "|| execution(public * com.cs.param.controller.SmsMatController.*(..))"
    		)
    public void webLog()
    {
    }
    
    @Around("webLog()")
    public Object process(final ProceedingJoinPoint point) throws Throwable
    {
    	Object obj = doBefore(point);
    	return obj;
    }
    
    
    /**
     * 拦截前操作
     */
    public Object doBefore(ProceedingJoinPoint point)
    {
    	ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    	//方法名
    	Signature signature = point.getSignature();
    	String businessName = signature.getName();
        String path = null;
        //消息头链路ID
        String linkID = "";
        String ipAdrress = "";
        if (null != attributes)
        {
            HttpServletRequest httpServletRequest = attributes.getRequest();
            ipAdrress = ToolUtil.getIpAdrress(httpServletRequest);
            linkID = httpServletRequest.getHeader("linkID_");
            if(StringUtils.isEmpty(linkID)){
                linkID = httpServletRequest.getHeader("linkID_".toLowerCase());
            }
            path = getPath(attributes);
            String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
            if(StringUtils.isNotEmpty(callNum))
            {
            	ThreadContext.put(path, String.valueOf(Integer.parseInt(callNum) + 1));
            }
            else
            {
            	ThreadContext.put(path, "1");
            }
        }
        if(StringUtils.isEmpty(linkID)){
        	linkID = ThreadContext.get("linkID");
        	if(StringUtils.isEmpty(linkID)){
        		linkID = UUID.randomUUID().toString();
        	}
        }
        //调用公共方法线程信息缓存（writeThreadContext），其中businessName取接口名，linkID取链路ID
        WriteThreadContextUtil.writeThreadContext(businessName, linkID);
        // 记录下请求内容
        StringBuffer logMsgBuffer = new StringBuffer();
        //唯一流水号
        String serialID = UUID.randomUUID().toString();
        ThreadContext.put("webLog_serialID_" + businessName, serialID);
        //唯一流水号
        logMsgBuffer.append("serialID_"+serialID + Constants.Split.EUROPE_THREE);
        //事务ID
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //链路ID
        logMsgBuffer.append("linkID_"+linkID + Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_REQ + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        //接口名
        logMsgBuffer.append(StringUtils.isEmpty(businessName) ? "" : businessName).append(Constants.Split.EUROPE_THREE);
        // 源IP
        logMsgBuffer.append(ipAdrress + Constants.Split.EUROPE_THREE);
        // 目标IP
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        //错误描述
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //请求编码
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //请求时长
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        // 请求报文
        Object [] args = point.getArgs();
        List<Object> list = new ArrayList<Object>();
        for (int i = 0; i < args.length; i++)
        {
            Object arg = args[i];
            String argStr = "";
            if (arg instanceof BeanPropertyBindingResult)
            {
                continue;
            }
            else if (arg instanceof MultipartHttpServletRequest)
            {
            	list.add(arg);
            	continue;
            }
            else if (arg instanceof HttpServletRequest)
            {
            	String str = "";
            	SafeHttpServletRequestWrapper requestWrapper = null;
        		try {
    	            HttpServletRequest httpServletRequest = attributes.getRequest();
    	            requestWrapper = new SafeHttpServletRequestWrapper(httpServletRequest);
        			InputStream inStream = requestWrapper.getInputStream();
        			BufferedReader br = new BufferedReader(new InputStreamReader(inStream, "UTF-8"));
        			StringBuffer sb = new StringBuffer();
        			while ((str = br.readLine()) != null) {
        				sb.append(str);
        			}
        			str = sb.toString();
        		} catch (Exception e) {
        		}
        		logMsgBuffer.append("arg " + i + " : " + str.replace('\n', ' ').replace('\r', ' '));
    			list.add(requestWrapper);
                continue;
            }
            else if (arg instanceof HttpServletResponse)
            {
            	list.add(arg);
            	logMsgBuffer.append("arg " + i + " : " + argStr.replace('\n', ' ').replace('\r', ' '));
            	continue;
            }
            else
            {
            	if (null != arg)
                {
                	if(arg instanceof String)
                	{
                		argStr = arg.toString();
                	}
                	else if(arg instanceof Object)
                	{
                        if(arg instanceof org.springframework.web.multipart.MultipartFile ||
                                arg.getClass().getName().startsWith("org.springframework.web.multipart")) {
                            argStr = arg.getClass().getSimpleName();
                        }
                        else
                        {
                            argStr = JSON.toJSONString(arg);
                        }
                	}
                }
            	list.add(arg);
            	logMsgBuffer.append("arg " + i + " : " + argStr.replace('\n', ' ').replace('\r', ' '));
            }
        }
        startTime.set(System.currentTimeMillis());
        String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
        if(StringUtils.isEmpty(callNum) || "1".equals(callNum))
        {
        	WebLogUtil.getInstance().printWebLog(logMsgBuffer.toString(), Boolean.FALSE);
        }
        Object obj = null;
        try {
			if(CollectionUtils.isNotEmpty(list))
			{
				obj = point.proceed(list.toArray());
			} else {
				obj = point.proceed();
			}
			doAfterReturning(point, obj);
        } catch (NullPointerException e) {
			doAfterError(point, e, true);
		} catch (Throwable e) {
			doAfterError(point, e, false);
		}
        return obj;
    }
    
    
    /**
     * 拦截后的操作
     * 
     */
    public void doAfterReturning(ProceedingJoinPoint point, Object ret)
    {
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        String ipAdrress = "";
        String path = null;
        if (null != attributes)
        {
            HttpServletRequest httpServletRequest = attributes.getRequest();
            ipAdrress = ToolUtil.getIpAdrress(httpServletRequest);
            path = getPath(attributes);
            String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
            if(StringUtils.isNotEmpty(callNum))
            {
        		ThreadContext.put(path, String.valueOf(Integer.parseInt(callNum) - 1));
            }
        }
        Signature signature = point.getSignature();
        //接口名
        String methodsName = signature.getName();
        // 记录下响应内容
        StringBuffer logMsgBuffer = new StringBuffer();
        
        // serialID_+获取到的流水号
        logMsgBuffer.append("serialID_"+ThreadContext.get("webLog_serialID_" + methodsName) + Constants.Split.EUROPE_THREE);
        // 事务ID
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = ThreadContext.get("linkID");
        if(StringUtils.isEmpty(linkID)){
    		linkID = UUID.randomUUID().toString();
        }
        logMsgBuffer.append("linkID_"+ linkID + Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_RSP + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 接口名
        logMsgBuffer.append(StringUtils.isEmpty(methodsName) ? "" : methodsName).append(Constants.Split.EUROPE_THREE);
        // 源IP
        logMsgBuffer.append(ipAdrress + Constants.Split.EUROPE_THREE);
        // 目标IP
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        // 取映射后的响应编码
        String resultMsg = "";
        String resultCode = "";
        if (null != ret)
        {
        	MethodSignature methodSignature = (MethodSignature)signature;
        	Class clazz = methodSignature.getReturnType();
        	if("java.lang.Boolean".equals(clazz.getName())) {
        		resultCode = CommonResultCode.FINAL_SUCCESS;
        		resultMsg = ret.toString();
        	} else if ("java.lang.Integer".equals(clazz.getName()) || "int".equals(clazz.getName())) {
        		resultCode = CommonResultCode.FINAL_SUCCESS;
        		resultMsg = ret.toString();
        	} else {
        		if("java.lang.String".equals(clazz.getName())) {
        			resultMsg = ret.toString();
        		} else {
        			resultMsg = JSON.toJSONString(ret);
        		}
                resultCode = getResultCode(resultMsg, methodsName);
                if(StringUtils.isEmpty(resultCode))
                {
                	if(StringUtils.isNotEmpty(resultMsg))
                	{
                		resultCode = CommonResultCode.FINAL_SUCCESS;
                	} else {
                		resultCode = CommonResultCode.FINAL_SERVICE_ERROR; 
                	}
                }
        	}
        }
        else
        {
            resultCode = CommonResultCode.FINAL_SUCCESS;  
        }
        String errorMsg = "";
        if(!CommonResultCode.FINAL_SUCCESS.equals(resultCode))
        {
        	errorMsg = getErrorMsg(resultMsg);
        }
        // 错误描述
        logMsgBuffer.append(errorMsg + Constants.Split.EUROPE_THREE);
        logMsgBuffer.append(resultCode + Constants.Split.EUROPE_THREE);
        //映射后的响应编码加入到响应信息的头消息rspCode_中
        if (null != attributes)
        {
            HttpServletResponse response = attributes.getResponse();
            response.setHeader("rspCode_", resultCode);
        }
        
        // 响应时间-请求时间
        Long cost = System.currentTimeMillis() - startTime.get();
        logMsgBuffer.append(cost + Constants.Split.EUROPE_THREE);
        // 响应报文
        logMsgBuffer.append(StringUtils.isEmpty(resultMsg) ? "":resultMsg.replace('\n', ' ').replace('\r', ' '));
        String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
        if(StringUtils.isEmpty(callNum) || "0".equals(callNum))
    	{
        	WebLogUtil.getInstance().printWebLog(logMsgBuffer.toString(), Boolean.FALSE);
    		if (StringUtils.isNotEmpty(path))
            {
                ThreadContext.remove(path);
            }
    		ThreadContext.remove("linkID");
    	}
        
        //清除缓存
        ThreadContext.remove("webLog_serialID_" + methodsName);
    }
    
    private String getErrorMsg(String jsonResp)
    {
    	String errorMsg = "";
    	jsonResp = jsonResp.replace("\\", "");
    	ResultObject resultObject = null;
    	try {
			resultObject = JSONObject.parseObject(jsonResp, ResultObject.class);
		} catch (Exception e) {
		}
    	if(resultObject != null)
    	{
    		if(StringUtils.isNotEmpty(resultObject.getErrorMsg()))
    		{
    			return resultObject.getErrorMsg();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getErrDesc()))
    		{
    			return resultObject.getErrDesc();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getText()))
    		{
    			return resultObject.getText();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getMessage()))
    		{
    			return resultObject.getMessage();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getMsg()))
    		{
    			return resultObject.getMsg();
    		}
    	}
    	return errorMsg;
    }
    
    private String getResultCode(String resultMsg, String businessName)
    {
    	String jsonResp = resultMsg.replace("\\", "");
    	String resultCode = null;
    	if("virtualProdSubscribe".equals(businessName))
    	{
    		Pattern pattern1 = Pattern.compile("\"resultCode\":\"00000000\"");
    		Matcher matcher1 = pattern1.matcher(jsonResp);
    		resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
    		while(matcher1.find())
            {
    			resultCode = CommonResultCode.FINAL_SUCCESS;
            }
    	}
    	else if("notify".equals(businessName) || "contractSync".equals(businessName)|| "contentAuth".equals(businessName))
    	{
    		Pattern pattern1 = Pattern.compile("\"resultCode\":\"1\"");
    		Matcher matcher1 = pattern1.matcher(jsonResp);
    		resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
    		while(matcher1.find())
            {
    			resultCode = CommonResultCode.FINAL_SUCCESS;
            }
    	}
    	else if("query".equals(businessName))
    	{
    		Pattern pattern1 = Pattern.compile("\"resultCode\":\"8000000000\"");
    		Matcher matcher1 = pattern1.matcher(jsonResp);
    		resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
    		while(matcher1.find())
            {
    			resultCode = CommonResultCode.FINAL_SUCCESS;
            }
    	}
    	else if("createUnSubscribe".equals(businessName))
    	{
    		Pattern pattern = Pattern.compile("\"code\":\"1\"|\"code\":\"2\"|\"code\":1|\"code\":2");
            Matcher matcher = pattern.matcher(jsonResp);
            resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            while(matcher.find())
            {
            	resultCode = CommonResultCode.FINAL_SUCCESS;
            }
    	}
    	else
    	{
        	ResultObject resultObject = null;
        	try {
				resultObject = JSONObject.parseObject(jsonResp, ResultObject.class);
			} catch (Exception e) {
			}
        	if(resultObject != null)
        	{
        		if(StringUtils.isNotEmpty(resultObject.getStatus()))
        		{
        			resultCode = resultObject.getStatus();
        		}
        		if(StringUtils.isNotEmpty(resultObject.getCode()))
        		{
        			resultCode = resultObject.getCode();
        		}
        		if(StringUtils.isNotEmpty(resultObject.getResultCode()))
        		{
        			resultCode = resultObject.getResultCode();
        		}
        		if(StringUtils.isNotEmpty(resultCode))
        		{
        			if("0".equals(resultCode) || "200".equals(resultCode))
            		{
            			resultCode = CommonResultCode.FINAL_SUCCESS;
            		} 
            		else
            		{
            			resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            		}
        		}
        	}
    	}
        return resultCode;
    }
    
    /**
     * 报错后的操作
     */
    public void doAfterError(ProceedingJoinPoint point, Throwable e, boolean isNullPointerException)
    {
        ServletRequestAttributes attributes = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        String ipAdrress = "";
        String path = null;
        if (null != attributes)
        {
            HttpServletRequest httpServletRequest = attributes.getRequest();
            ipAdrress = ToolUtil.getIpAdrress(httpServletRequest);
            path = getPath(attributes);
            String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
            if(StringUtils.isNotEmpty(callNum))
            {
        		ThreadContext.put(path, String.valueOf(Integer.parseInt(callNum) - 1));
            }
        }
        //接口名
        Signature signature = point.getSignature();
        String businessName = signature.getName();
        // 记录下响应内容
        StringBuffer logMsgBuffer = new StringBuffer();
        
        // serialID_+获取到的流水号
        logMsgBuffer.append("serialID_"+ThreadContext.get("webLog_serialID_" + businessName) + Constants.Split.EUROPE_THREE);
        // 事务ID
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = ThreadContext.get("linkID");
        if(StringUtils.isEmpty(linkID)){
    		linkID = UUID.randomUUID().toString();
        }
        logMsgBuffer.append("linkID_"+ linkID + Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_RSP + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 接口名
        logMsgBuffer.append(StringUtils.isEmpty(businessName) ? "" : businessName).append(Constants.Split.EUROPE_THREE);
        // 源IP
        logMsgBuffer.append(ipAdrress + Constants.Split.EUROPE_THREE);
        // 目标IP
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        String resultMessage = e.getMessage();
        resultMessage = StringUtils.isEmpty(resultMessage) ? "":resultMessage;
        //错误描述
        logMsgBuffer.append((isNullPointerException ? "NullPointerException" : resultMessage) + Constants.Split.EUROPE_THREE);
        // 返回码
        String resultCode = CommonResultCode.FINAL_SYSTEM_ERROR;
        ManualLogUtils.errorLog("system error!", e);
        logMsgBuffer.append(resultCode + Constants.Split.EUROPE_THREE);
        //映射后的响应编码加入到响应信息的头消息rspCode_中
        if (null != attributes)
        {
            HttpServletResponse response = attributes.getResponse();
            response.setHeader("rspCode_", resultCode);
        }
        // 响应时间-请求时间
        Long cost = System.currentTimeMillis() - startTime.get();
        logMsgBuffer.append(cost + Constants.Split.EUROPE_THREE);
        // 响应报文
        logMsgBuffer.append(resultMessage);
        String callNum = null;
        if (StringUtils.isNotEmpty(path))
        {
            callNum = ThreadContext.get(path);

        }
        if(StringUtils.isEmpty(callNum) || "0".equals(callNum))
    	{
        	WebLogUtil.getInstance().printWebLog(logMsgBuffer.toString(), Boolean.TRUE);
    		if (StringUtils.isNotEmpty(path))
            {
                ThreadContext.remove(path);
            }
    		ThreadContext.remove("linkID");
    	}
        //清除缓存
        ThreadContext.remove("webLog_serialID_" + businessName);
    }
    
    private String getPath(ServletRequestAttributes attributes)
    {
    	String path = null;
    	if (null != attributes)
        {
            HttpServletRequest httpServletRequest = attributes.getRequest();
            if(httpServletRequest != null)
        	{
            	String url = httpServletRequest.getRequestURL().toString();
                if(StringUtils.isNotEmpty(url))
                {
                	path = url.substring(url.lastIndexOf("/") +1 , url.length());
                }
        	}
        }
    	return path;
    }
}