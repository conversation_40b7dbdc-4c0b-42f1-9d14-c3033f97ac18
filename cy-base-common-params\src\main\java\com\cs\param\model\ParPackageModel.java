
package com.cs.param.model;

import java.util.Date;

public class ParPackageModel{
    private static final long serialVersionUID = 1L;
    private Integer id;
    private String companyCode;
    private String businessCode;
    private String chargeCode;
    private String setMealName;
    private String setMealType;
    private String productMark;
    private String setMealMarkCode;
    private String setMealMark;
    private String sendMark;
    private String setMealFunction;
    private String setMealDesc;

    public Integer getId (){
        return id;
    }

    public void setId (Integer id){
        this.id = id;
    }

    public String getCompanyCode (){
        return companyCode;
    }

    public void setCompanyCode (String companyCode){
        this.companyCode = companyCode;
    }

    public String getBusinessCode (){
        return businessCode;
    }

    public void setBusinessCode (String businessCode){
        this.businessCode = businessCode;
    }

    public String getChargeCode (){
        return chargeCode;
    }

    public void setChargeCode (String chargeCode){
        this.chargeCode = chargeCode;
    }

    public String getSetMealName (){
        return setMealName;
    }

    public void setSetMealName (String setMealName){
        this.setMealName = setMealName;
    }

    public String getSetMealType (){
        return setMealType;
    }

    public void setSetMealType (String setMealType){
        this.setMealType = setMealType;
    }

    public String getProductMark (){
        return productMark;
    }

    public void setProductMark (String productMark){
        this.productMark = productMark;
    }

    public String getSetMealMarkCode (){
        return setMealMarkCode;
    }

    public void setSetMealMarkCode (String setMealMarkCode){
        this.setMealMarkCode = setMealMarkCode;
    }

    public String getSetMealMark (){
        return setMealMark;
    }

    public void setSetMealMark (String setMealMark){
        this.setMealMark = setMealMark;
    }

    public String getSendMark (){
        return sendMark;
    }

    public void setSendMark (String sendMark){
        this.sendMark = sendMark;
    }

    public String getSetMealFunction (){
        return setMealFunction;
    }

    public void setSetMealFunction (String setMealFunction){
        this.setMealFunction = setMealFunction;
    }

    public String getSetMealDesc (){
        return setMealDesc;
    }

    public void setSetMealDesc (String setMealDesc){
        this.setMealDesc = setMealDesc;
    }

}
