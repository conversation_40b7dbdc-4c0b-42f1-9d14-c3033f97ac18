
package com.cs.param.common;

public class ParRedListCommon {
	private Integer id; // id
	private String phone; // 用户号码
	private String sysUserName; // 操作人，系统用户id
	private String isPersonal;// 是否接受个人彩印",//0：否、1：是
	private String isNewMedia;// 是否接受新媒彩印",//0：否、1：是
	private String isRemind;// 是否接受提醒彩印",//0：否、1：是
	private String isBusiness;// 是否接受企业彩印"//0：否、1：是
	private String isDelete;// 标识是否为删除数据：0否，1是
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private Integer[] ids;// id数组
	private String[] phones;// id数组

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getIsPersonal() {
		return isPersonal;
	}

	public void setIsPersonal(String isPersonal) {
		this.isPersonal = isPersonal;
	}

	public String getIsNewMedia() {
		return isNewMedia;
	}

	public void setIsNewMedia(String isNewMedia) {
		this.isNewMedia = isNewMedia;
	}

	public String getIsRemind() {
		return isRemind;
	}

	public void setIsRemind(String isRemind) {
		this.isRemind = isRemind;
	}

	public String getIsBusiness() {
		return isBusiness;
	}

	public void setIsBusiness(String isBusiness) {
		this.isBusiness = isBusiness;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getSysUserName() {
		return sysUserName;
	}

	public void setSysUserName(String sysUserName) {
		this.sysUserName = sysUserName;
	}

	public Integer[] getIds() {
		return ids;
	}

	public void setIds(Integer[] ids) {
		this.ids = ids;
	}

	public String[] getPhones() {
		return phones;
	}

	public void setPhones(String[] phones) {
		this.phones = phones;
	}

}
