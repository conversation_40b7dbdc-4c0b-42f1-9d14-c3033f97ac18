package com.cs.param.controller;

import com.cs.param.api.dao.NumberMapper;
import com.cs.param.config.CarryTurnConfig;
import com.cs.param.model.request.QueryNpInfoReq;
import com.cs.param.model.request.QueryNpTraceReq;
import com.cs.param.model.request.RequestHeader;
import com.cs.param.model.response.QueryNpInfoResp;
import com.cs.param.model.response.QueryNpTraceResp;
import com.cs.param.services.CarryTurnApi;
import com.cs.param.services.CarryTurnService;
import com.cs.param.utils.DateUtil;
import com.cs.param.utils.LogUtil;
import com.cy.model.NumberModel;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 携号转网Controller
 */
@RequestMapping("/carryTurn")
@RestController
public class CarryTurnController implements CarryTurnApi {

    private static final Logger log = LoggerFactory.getLogger(CarryTurnController.class);

    @Autowired
    private CarryTurnConfig carryTurnConfig;

    @Autowired
    private CarryTurnService carryTurnService;

    @Autowired
    private NumberMapper numberMapper;

    /**
     * 携转用户信息查询npInfo
     */
    @RequestMapping(value = "/query/npInfo")
    @ResponseBody
    public QueryNpInfoResp queryNpInfo(@RequestBody QueryNpInfoReq queryNpInfoReq) {
        LogUtil.info(log, LogUtil.BIZ, "", "携转用户信息查询");
        try {
            RequestHeader requestHeader = new RequestHeader();
            BeanUtils.copyProperties(carryTurnConfig, requestHeader);
            requestHeader.setTimeStamp(DateUtil.parseDate(new Date(), DateUtil.DEFAULT_TIMESTAMP));
            requestHeader.setAuthenticatorSource(DigestUtils.sha256Hex(carryTurnConfig.getSourceDeviceCode() + carryTurnConfig.getSharedSecret() + requestHeader.getTimeStamp()));
            queryNpInfoReq.setRequestHeader(requestHeader);
            queryNpInfoReq.setShopCode(carryTurnConfig.getShopCode());
            LogUtil.info(log, LogUtil.BIZ, "", "queryNpInfoReq={}", queryNpInfoReq);
            return carryTurnService.queryNpInfo(queryNpInfoReq);
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "", "携转用户信息查询出错！", e);
            return null;
        }
    }

    @Override
    public String getAreaByCarrayTurnNum(@RequestBody String phoneNo) {
        String area =null;
        NumberModel model=null;
        QueryNpInfoReq queryNpInfoReq = new QueryNpInfoReq();
        List<String> serviceNumberList = new ArrayList<String>();
        serviceNumberList.add(phoneNo);
        queryNpInfoReq.setServiceNumberList(serviceNumberList);
        QueryNpInfoResp queryNpInfoResp = queryNpInfo(queryNpInfoReq);
        LogUtil.info(log, LogUtil.BIZ, phoneNo, "携转查询结果：", queryNpInfoResp);
        if (!CollectionUtils.isEmpty(queryNpInfoResp.getNpInfoResultList())) {
            String pro = queryNpInfoResp.getNpInfoResultList().get(0).getDetailNpInfo().getProvince();
            model= numberMapper.getSectionByCarrayTurnProCode(pro);
            area = model.getPartitionCode();
        }
        LogUtil.info(log, LogUtil.BIZ, phoneNo, "获取携转省份信息：", model);
        return area;
    }

    /**
     * 携转用户轨迹信息查询npTrace
     */
    @RequestMapping(value = "/query/npTrace")
    public QueryNpTraceResp queryNpTrace(@RequestBody QueryNpTraceReq queryNpTraceReq) throws Exception {
        LogUtil.info(log, LogUtil.BIZ, "", "携转用户轨迹信息查询");
        try {
            RequestHeader requestHeader = new RequestHeader();
            BeanUtils.copyProperties(carryTurnConfig, requestHeader);
            requestHeader.setTimeStamp(DateUtil.parseDate(new Date(), DateUtil.DEFAULT_TIMESTAMP));
            requestHeader.setAuthenticatorSource(DigestUtils.sha256Hex(carryTurnConfig.getSourceDeviceCode() + carryTurnConfig.getSharedSecret() + requestHeader.getTimeStamp()));
            queryNpTraceReq.setRequestHeader(requestHeader);
            queryNpTraceReq.setShopCode(carryTurnConfig.getShopCode());
            LogUtil.info(log, LogUtil.BIZ, "", "queryNpTraceReq={}", queryNpTraceReq);
            return carryTurnService.queryNpTrace(queryNpTraceReq);
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "", "携转用户轨迹信息查询出错！", e);
            return null;
        }
    }
}
