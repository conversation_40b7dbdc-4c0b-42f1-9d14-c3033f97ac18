<template>
    <div>
        <h1 class="user-title">修改文本彩印</h1>
        <div class="user-line"></div>
        <div class="user-search2">
          <el-form>
            <!-- 类别 -->
            <el-form-item label="内容分类">
                <el-select v-model="addReq.csGroupId" placeholder="请选择" style="margin-left:14px;" size="small">
                <el-option
                    v-for="item in contentData"
                    :key="item.groupId"
                    :label="item.groupName"
                    :value="item.groupId">
                </el-option>
                </el-select>
            </el-form-item>

           <el-form-item label="内容标签">
              <el-button type="info" @click="visible = true" size="small" style="margin-left:28px;">添加标签</el-button>
              <span v-for="item in checkedLabel" :key="item">{{' '+item}} ;</span>
              <el-dialog title="添加标签" :visible.sync="visible" :close-on-click-modal="false">
                <el-form class="demo-form-inline" label-width="160px" justify="center">
                  <el-form-item>
                    <el-checkbox-group v-model="addReq.csLabelId">
                      <el-checkbox  @change="labelChange(item.liName)" v-for="item in labelData" :label="item.liId" :key="item.liName" style="display:inline-block;margin-left:30px;">{{item.liName}} </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                  <span>{{"已选"+addReq.csLabelId.length+"个标签"}}</span>
                  <el-button type="primary" @click="visible = false">确认</el-button>
                </div>
              </el-dialog>
            </el-form-item>



            <el-form-item label="彩印内容">
              <el-input type="textarea" v-model="addReq.csContent" :maxlength="50"></el-input>
            </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item>
                  <el-button type="primary" @click="submit">提交</el-button>
              </el-form-item>
            </el-form>
        </div>

    </div>
</template>
<script>
export default {
  name: "creatMaterial",
  data() {
    return {
      category: [],
      CStype: [],
      checkedLabel: [],
      contentData: [], //内容分类变量
      labelData: [], //内容标签变量
      visible: false,
      //添加文本彩印
      addReq: {
        csContent: "",
        csGroupId: "",
        csLabelId: []
      },
      //标签
      request: {
        csGroupName: "",
        csLabelName: ""
      }
    };
  },
  mounted() {
    this.contentDatas();
    this.labelDatas();
  },
  methods: {
  //   check(vm){
  //     if(!(this.addReq.csGroupId)){
  //       vm.$message.error("请选择内容分类选项");
  //       return false;
  //     }
  //     if(this.addReq.csLabelIds.length==0){
  //       vm.$message.error("请添加标签");
  //       return false;
  //     }
  //     if(!(this.addReq.csContent)){
  //       vm.$message.error("新增彩印内容不能为空");
  //       return false;
  //     }
  //     return true;
  //   },
    //显示选中的标签名
    labelChange: function(labelName) {
      let checkedBoolean=false;
      for(let i=0;i<this.checkedLabel.length;i++){
        if(labelName===this.checkedLabel[i]){
          this.checkedLabel.splice(i,1);
          checkedBoolean=true;
        }
      }
      if(!checkedBoolean){
        this.checkedLabel.push(labelName);
      }
    },
    //内容分类选项请求
    contentDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.contentData = res.data;
        });
    },
    //内容标签选项请求
    labelDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.labelData = res.data;
        });
    },
    //添加文本彩印
    submit: function() {
      // if(!this.check(this)){
      //   return false;
      // }
      this.addReq.csLabelId = this.addReq.csLabelId.join(',');
      this.$http
        .post(`${this.proxyUrl}/content/csText/addCsText`, this.addReq, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
           this.$message.success('添加内容成功');
           this.$router.push({url:'/textCS',name: 'textCS'});
          } else if (res.data.resStatus == "1") {
           this.$message('添加内容失败');
          }
        });
    },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search2 {
  width: 40%;
  margin: 0 auto;
  margin-top: 3%;
}
/* 弹窗checkbox样式 */
.el-form-item__content {
  margin: 0 !important;
}
</style>
