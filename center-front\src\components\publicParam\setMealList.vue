<template>
    <div>
        <h1 class="user-title">彩印套餐</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="产品标识">
                    <el-input  v-model="searchForm.productMark" placeholder=""  size="small"></el-input>
                </el-form-item>
                <el-form-item label="套餐名称">
                    <el-input v-model="searchForm.setMealName" placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="套餐类型">
                    <el-select v-model="searchForm.setMealType" clearable placeholder="请选择" size="small">
                        <el-option
                                v-for="item in setMealTypeList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                  :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="companyCode" label="企业代码" width="100"/>
            <el-table-column prop="businessCode" label="业务代码" width="126"/>
            <el-table-column prop="productMark" label="产品标识" width="165"/>
            <el-table-column prop="setMealName" label="套餐包名称" width="220"/>
            <el-table-column prop="chargeCode" label="业务资费(分)" width="130"/>
            <el-table-column prop="setMealType" label="套餐类型" :formatter="formatterType"  width="100"/>
            <el-table-column prop="setMealMark" label="套餐标识"  width="100"/>
            <el-table-column prop="setMealFunction" label="套餐功能" width="200" :formatter="formatterFunction" :show-overflow-tooltip="true"/>
            <el-table-column prop="setMealDesc" label="描述" width="150" :show-overflow-tooltip="true"/>
            <el-table-column prop="sendMark" label="发送标识" :formatter="formatterSendMark"  width="100"/>
        </el-table>
        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                //查询form对象定义
                searchForm: {
                    productMark:'',
                    setMealName:'',
                    setMealType:'',
                    pageSize:10,// 每页显示条数
                    pageNum:1,

                },
                tableData:[],
                setMealTypeList:[
                    {
                        key:'1',
                        value:'企业彩印'
                    },
                    {
                        key:'2',
                        value:'新媒彩印'
                    },{
                        key:'3',
                        value:'提醒彩印'
                    },{
                        key:'4',
                        value:'个人彩印'
                    }
                ],
                currentPage: 1,
                total:0,
            }
        },

        mounted(){
            this.search();
            // this.queryMealMarkList();
//            this.slideData(this.proxyUrl);
        },
        methods: {
            formatterFunction(row, column){
                let sb = '';
                let arr = row.setMealFunction.split('');
                if(arr[0]==='1'){
                    sb+='个人彩印-默认被叫彩印;';
                }
                if(arr[1]==='1'){
                    sb+='个人彩印-默认主叫彩印;';
                }
                if(arr[2]==='1'){
                    sb+='个人彩印-DIY被叫彩印;';
                }
                if(arr[3]==='1'){
                    sb+='个人彩印-DIY主叫彩印;';
                }
                if(arr[4]==='1'){
                    sb+='个人彩印-情景彩印;';
                }
                if(arr[5]==='1'){
                    sb+='个人彩印-动态彩印;';
                }
                if(arr[6]==='1'){
                    sb+='提醒彩印;';
                }
                return sb;
            },
            formatterSendMark(row, column) {
                switch(row.sendMark){
                    case '1':
                        return '主叫';
                        break;
                    case '2':
                        return '被叫';
                        break;
                    case '3':
                        return '主被叫';
                        break;
                }
            },
            formatterType(row, column) {
                switch(row.setMealType){
                    case '1':
                        return '企业彩印';
                        break;
                    case '2':
                        return '新媒彩印';
                        break;
                    case '3':
                        return '提醒彩印';
                        break;
                    case '4':
                        return '个人彩印';
                        break;
                }
            },
            queryMealMarkList(){
                this.$http.get(`${this.proxyUrl}/param/packageFunction/getPackageMark`).then(function(res){
                            this.setMealMarkList=res.data;
                        })
            },

            //查询请求
            search(searchForm){
                this.$http.post(`${this.proxyUrl}/param/packageFunction/getPackagePage`,searchForm,{emulateJSON:true})
                        .then(function(res){
                            this.currentPage=res.data.pageNum;
                            this.total=res.data.pageTotal;
                            this.tableData=res.data.datas;;

                        })
            },
            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
