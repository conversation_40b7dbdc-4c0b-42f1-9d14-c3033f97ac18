<template>
    <div class="apilist">
        <div class="user-titler">{{$route.name}}</div>
        <div class="contentbox">
            <!--表格-->
            <div style="margin-top: 20px;width: 50%">
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                    <el-table-column prop="apiName" label="API接口名称" />
                    <el-table-column prop="category" label="号码库" />
                    <el-table-column prop="createTime" label="创建时间" />
                    <el-table-column prop="apiQueryCount" label="被查询次数" />
                </el-table>
            </div>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'apilist',
        data(){
            return{
                tableData:[],
                currentPage: 1,
                total:0
            }
        },
        components: {

        },
        created(){
            this.search();
        },
        methods:{
            //查询请求
            search() {
                let vm = this;
                postHeader('queryApiManageList', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.apiHistoryList;
                    }
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .contentbox{
        margin:0 15px;
    }
    .el-table{
        margin-left: 0;
        margin-top: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
