<template>
  <div class="addprovinces">
    <h1 class="user-title">修改省市提醒模板</h1>
    <hr class="user-line"/>
    <div class="content">
      <el-form :model="addForm" ref="addForm" class="demo-form-inline" label-width="35%"  style="width: 80%">
        <el-form-item label="提醒模板编号：" prop="sysUserName">
          <el-input v-model="addForm.sysUserName"></el-input>
        </el-form-item>
        <el-form-item label="模板名称：" prop="sysUserName">
          <el-input v-model="addForm.sysUserName"></el-input>
        </el-form-item>
        <el-form-item label="分类：" prop="sysStaffName">
          <el-select v-model="addForm.provinceCode" placeholder="请选择">
            <el-option
                    v-for="item in provinceList"
                    :key="item.provinceCode"
                    :label="item.provinceName"
                    :value="item.provinceCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="省份：" prop="provinceCode">
          <el-select v-model="addForm.provinceCode" placeholder="请选择">
            <el-option
                    v-for="item in provinceList"
                    :key="item.provinceCode"
                    :label="item.provinceName"
                    :value="item.provinceCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="地市：" prop="sysMobileNumber">
          <el-select v-model="addForm.provinceCode" placeholder="请选择">
            <el-option
                    v-for="item in provinceList"
                    :key="item.provinceCode"
                    :label="item.provinceName"
                    :value="item.provinceCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="拨打号码提醒内容：">
          <el-input
                  type="textarea"
                  :rows="2"
                  placeholder=""
                  v-model="addForm.textarea">
          </el-input>
        </el-form-item>
        <el-form-item label="接听号码提醒内容：">
          <el-input
                  type="textarea"
                  :rows="2"
                  placeholder=""
                  v-model="addForm.textarea">
          </el-input>
        </el-form-item>
        <el-form-item label="描述：">
          <el-input
                  type="textarea"
                  :rows="2"
                  placeholder=""
                  v-model="addForm.textarea">
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="content1">
      <el-button type="primary" @click="submit">提交</el-button>
    </div>
  </div>
</template>

<script>
    export default {
        name: 'addprovinces',
        data(){
            return{
                name:'',
                addForm:{}
            }
        },
        components: {},
        props:['provinceList'],
        methods:{
            submit(){
                this.$emit('upList');
            }
        }
    }
</script>

<style scoped>
  .addtitle{
    font-size: 18px;
    margin-left: 20px;
  }
  .content{
    width: 640px;
    margin: 50px auto;
  }
  .content1{
    text-align: center;
  }
</style>
