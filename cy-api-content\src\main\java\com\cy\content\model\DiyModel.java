package com.cy.content.model;

import java.io.Serializable;

public class DiyModel  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String dsId;
	
	private String dsSeriId;
	/**
	 * 彩印内容
	 */
	private String dsCard;
	/**
	 * 彩印ID，后面5位从00001-10000，每个用户最大1W
	 */
	private String dsSerialNumber;
	/**
	 * 彩印提交时间，精确到秒
	 */
	private String dsSubmitTime;
	/**
	 * 彩印提交人，就是用户的手机号码
	 */
	private String dsSubmitUser;
	/**
	 * 彩印来源
	 * 0：其他 1：WEB 2：WAP 3：客户端 4：微信
	 */
	private String dsSubmitType;
	/**
	 * 内容说明
	 */
	private String dsRemark;
	/**
	 * 1：上架 2：待上架 3：删除 4：下架'
	 */
	private String dsStatus;
	private String dsStatusName;
	/**
	 * 审核状态
	 * 1：待审批
	 * 2：审批不通过
	 * 3：审核通过
	 *4：失效（提交新纪录，或彩印被删除）
	 */
	private int svStatus;
	/**
	 * 审核状态名称
	 * 1：待审批
	 * 2：审批不通过
	 * 3：审核通过
	 *4：失效（提交新纪录，或彩印被删除）
	 */
	private String svStatusName;
	/**
	 * 审核时间，精确到秒
	 */
	private String svVerifyTime;
	/**
	 * 审核人
	 */
	private String auditor;
	/**
	 * 驳回原因
	 */
	private String auditCause;
	
	private String dsProId;
	
	private String dsProName;
	
	private String startTime;
	private String endTime;
	private int pageNum=1;// 查询的页码

	private int pageSize=0;// 每页显示条数
	private int startNum;//起始查询行
    private String reciver; //专属号码
	private int svRuleStatus;//1：不生成规则；0生成规则

	private boolean isOne;// false批量下架 true单个下架
	private  String serviceId;//专属号码


	private String weekFlag;

	private String ruleStartTime;

	private String ruleEndTime;

	private String contactGroupId;

	public String getWeekFlag() {
		return weekFlag;
	}

	public void setWeekFlag(String weekFlag) {
		this.weekFlag = weekFlag;
	}

	public String getRuleStartTime() {
		return ruleStartTime;
	}

	public void setRuleStartTime(String ruleStartTime) {
		this.ruleStartTime = ruleStartTime;
	}

	public String getRuleEndTime() {
		return ruleEndTime;
	}

	public void setRuleEndTime(String ruleEndTime) {
		this.ruleEndTime = ruleEndTime;
	}

	public String getContactGroupId() {
		return contactGroupId;
	}

	public void setContactGroupId(String contactGroupId) {
		this.contactGroupId = contactGroupId;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}
	public Integer getContentType() {
		return contentType;
	}

	public void setContentType(Integer contentType) {
		this.contentType = contentType;
	}

	private Integer contentType;

	public int getSvRuleStatus() {
		return svRuleStatus;
	}

	public void setSvRuleStatus(int svRuleStatus) {
		this.svRuleStatus = svRuleStatus;
	}
    public String getReciver() {
        return reciver;
    }

    public void setReciver(String reciver) {
        this.reciver = reciver;
    }

	public void setStartPage(){
	    int startIndex = (pageNum-1)*pageSize;
	    if(startIndex<0)
		startIndex = 0;
	    this.startNum = startIndex;
	}
	public int getPageNum() {
	    return pageNum;
	}
	public void setPageNum(int pageNum) {
	    this.pageNum = pageNum;
	}
	public int getPageSize() {
	    return pageSize;
	}
	public void setPageSize(int pageSize) {
	    this.pageSize = pageSize;
	}
	public int getStartNum() {
	    return startNum;
	}
	public void setStartNum(int startNum) {
	    this.startNum = startNum;
	}
	public String getStartTime() {
	    return startTime;
	}
	public void setStartTime(String startTime) {
	    this.startTime = startTime;
	}
	public String getEndTime() {
	    return endTime;
	}
	public void setEndTime(String endTime) {
	    this.endTime = endTime;
	}
	public String getDsStatusName() {
	    return dsStatusName;
	}
	public void setDsStatusName(String dsStatusName) {
	    this.dsStatusName = dsStatusName;
	}
	public String getDsSeriId() {
	    return dsSeriId;
	}
	public void setDsSeriId(String dsSeriId) {
	    this.dsSeriId = dsSeriId;
	}
	public String getAuditor() {
	    return auditor;
	}
	public void setAuditor(String auditor) {
	    this.auditor = auditor;
	}
	public String getAuditCause() {
	    return auditCause;
	}
	public void setAuditCause(String auditCause) {
	    this.auditCause = auditCause;
	}
	public String getDsStatus() {
	    return dsStatus;
	}
	public void setDsStatus(String dsStatus) {
	    this.dsStatus = dsStatus;
	}
	public String getDsId() {
		return dsId;
	}
	public void setDsId(String dsId) {
		this.dsId = dsId;
	}
	public String getDsCard() {
		return dsCard;
	}
	public void setDsCard(String dsCard) {
		this.dsCard = dsCard;
	}
	public String getDsSerialNumber() {
		return dsSerialNumber;
	}
	public void setDsSerialNumber(String dsSerialNumber) {
		this.dsSerialNumber = dsSerialNumber;
	}
	public String getDsSubmitTime() {
		return dsSubmitTime;
	}
	public void setDsSubmitTime(String dsSubmitTime) {
		this.dsSubmitTime = dsSubmitTime;
	}
	public String getDsSubmitUser() {
		return dsSubmitUser;
	}
	public void setDsSubmitUser(String dsSubmitUser) {
		this.dsSubmitUser = dsSubmitUser;
	}
	public String getDsSubmitType() {
		return dsSubmitType;
	}
	public void setDsSubmitType(String dsSubmitType) {
		this.dsSubmitType = dsSubmitType;
	}
	public String getDsRemark() {
		return dsRemark;
	}
	public void setDsRemark(String dsRemark) {
		this.dsRemark = dsRemark;
	}
	public String getSvVerifyTime() {
		return svVerifyTime;
	}
	public void setSvVerifyTime(String svVerifyTime) {
		this.svVerifyTime = svVerifyTime;
	}
	public int getSvStatus() {
		return svStatus;
	}
	public void setSvStatus(int svStatus) {
		this.svStatus = svStatus;
	}
	public String getSvStatusName() {
		return svStatusName;
	}
	public void setSvStatusName(String svStatusName) {
		this.svStatusName = svStatusName;
	}
	public String getDsProId() {
	    return dsProId;
	}
	public void setDsProId(String dsProId) {
	    this.dsProId = dsProId;
	}
	public String getDsProName() {
	    return dsProName;
	}
	public void setDsProName(String dsProName) {
	    this.dsProName = dsProName;
	}

	public boolean isOne() {
		return isOne;
	}

	public void setOne(boolean one) {
		isOne = one;
	}
}
