package com.cs.param.constants;

/**
 * 
 * <一句话功能简述>系统公共常量<功能详细描述>
 * 
 * <AUTHOR>
 * @version [版本号, 2018年5月19日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class Constants
{
    /**
     * http
     */
    public static final String HTTP = "http://";
    
    /**
     * https
     */
    public static final String HTTPS = "https://";
    
    /**
     * 冒号：
     */
    public static final String COLON = ":";
    
    /**
     * 用用设置状态的常量
     */
    public static final int STATUS_0 = 0;
    
    /**
     * 用用设置状态的常量
     */
    public static final Integer STATUS_0_INTEGER = 0;
    
    /**
     * 用用设置状态的常量
     */
    public static final int STATUS_1 = 1;
    
    /**
     * long 类型的0 的常量
     */
    public static final Long LONG_STATUS_0 = 0L;
    
    /**
     * 2007版本Excel的二进制文件前两个字节字母PK
     */
    public static final String EXCELFILE_VERSION_IDENYIFY_2007 = "PK";
    
    /**
     * 工作密钥存放节点
     */
    public static final String SOFTCARE_WORKING_KEY = "softCare_working_aes_encrypt/encrypt_key";
    
    /**
     * 数字1
     */
    public static final Integer NUM_NEGATIVE_1 = 1;
    
    /**
     * 数字-5
     */
    public static final Integer NUM_NEGATIVE_5 = -5;
    
    /**
     * 数字0
     */
    public static final Integer NUM_0 = 0;
    
    /**
     * 数字1
     */
    public static final Integer NUM_1 = 1;
    
    /**
     * 数字2
     */
    public static final Integer NUM_2 = 2;
    
    /**
     * 数字3
     */
    public static final Integer NUM_3 = 3;
    
    /**
     * 数字4/
     */
    public static final Integer NUM_4 = 4;
    
    /**
     * 数字5
     */
    public static final Integer NUM_5 = 5;
    
    /**
     * 数字6
     */
    public static final Integer NUM_6 = 6;
    
    /**
     * 数字7
     */
    public static final Integer NUM_7 = 7;
    
    /**
     * 数字8
     */
    public static final Integer NUM_8 = 8;
    
    /**
     * 数字9
     */
    public static final Integer NUM_9 = 9;
    
    /**
     * 数字10
     */
    public static final Integer NUM_10 = 10;
    
    /**
     * 数字11
     */
    public static final Integer NUM_11 = 11;
    
    /**
     * 数字12
     */
    public static final Integer NUM_12 = 12;
    
    /**
     * 数字13
     */
    public static final Integer NUM_13 = 13;
    
    /**
     * 数字14
     */
    public static final Integer NUM_14 = 14;
    
    /**
     * 数字15
     */
    public static final Integer NUM_15 = 15;
    
    /**
     * 数字16
     */
    public static final Integer NUM_16 = 16;
    
    /**
     * 数字17
     */
    public static final Integer NUM_17 = 17;
    
    /**
     * 数字18
     */
    public static final Integer NUM_18 = 18;
    
    /**
     * 数字19
     */
    public static final Integer NUM_19 = 19;
    
    /**
     * 数字20
     */
    public static final Integer NUM_20 = 20;
    
    /**
     * 数字21
     */
    public static final Integer NUM_21 = 21;
    
    /**
     * 数字22
     */
    public static final Integer NUM_22 = 22;
    
    /**
     * 数字23
     */
    public static final Integer NUM_23 = 23;
    
    /**
     * 数字24
     */
    public static final Integer NUM_24 = 24;
    
    /**
     * 数字25
     */
    public static final Integer NUM_25 = 25;
    
    /**
     * 数字26
     */
    public static final Integer NUM_26 = 26;
    
    /**
     * 数字27
     */
    public static final Integer NUM_27 = 27;
    
    /**
     * 数字28
     */
    public static final Integer NUM_28 = 28;
    
    /**
     * 数字29
     */
    public static final Integer NUM_29 = 29;
    
    /**
     * 数字30
     */
    public static final Integer NUM_30 = 30;
    
    /**
     * 数字37
     */
    public static final Integer NUM_37 = 37;
    
    /**
     * 数字38
     */
    public static final Integer NUM_38 = 38;
    
    /**
     * 数字39
     */
    public static final Integer NUM_39 = 39;
    
    /**
     * 数字40
     */
    public static final Integer NUM_40 = 40;
    
    /**
     * 数字50
     */
    public static final Integer NUM_50 = 50;
    
    /**
     * 数字51
     */
    public static final Integer NUM_51 = 51;
    
    /**
     * 数字52
     */
    public static final Integer NUM_52 = 52;
    
    /**
     * 数字59
     */
    public static final Integer NUM_59 = 59;
    
    /**
     * 数字60
     */
    public static final Integer NUM_60 = 60;
    
    /**
     * 数字61
     */
    public static final Integer NUM_61 = 61;
    
    /**
     * 数字62
     */
    public static final int NUM_62 = 62;
    
    /**
     * 数字63
     */
    public static final int NUM_63 = 63;
    
    /**
     * 数字65
     */
    public static final Integer NUM_65 = 65;
    
    /**
     * 数字70
     */
    public static final Integer NUM_70 = 70;
    
    /**
     * 数字70
     */
    public static final Integer NUM_80 = 80;
    
    /**
     * 数字1
     */
    public static final short SHORT_1 = 1;
    
    /**
     * 数字2
     */
    public static final short SHORT_2 = 2;
    
    /**
     * 数字3
     */
    public static final short SHORT_3 = 3;
    
    /**
     * 数字4
     */
    public static final short SHORT_4 = 4;
    
    /**
     * 数字5
     */
    public static final short SHORT_5 = 5;
    
    /**
     * 数字8
     */
    public static final short SHORT_8 = 8;
    
    /**
     * 数字9
     */
    public static final short SHORT_9 = 9;
    
    /**
     * 数字10
     */
    public static final short SHORT_10 = 10;
    
    /**
     * 数字12
     */
    public static final short SHORT_12 = 12;
    
    /**
     * 数字15
     */
    public static final short SHORT_15 = 15;
    
    /**
     * 数字20
     */
    public static final short SHORT_20 = 20;
    
    /**
     * 数字31
     */
    public static final short SHORT_31 = 31;
    
    /**
     * 数字42
     */
    public static final short SHORT_42 = 42;
    
    /**
     * 数字47
     */
    public static final short SHORT_47 = 47;
    
    /**
     * UTF-8编码格式
     */
    public static final String UTF = "UTF-8";
    
    /**
     * 
     * <一句话功能简述>地址新增，修改等操作符常量 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月19日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public static interface AddressOpreate
    {
        /**
         * 地址修改
         */
        public static final String ADDRESS_UPDATE = "2";
        
        /**
         * 地址新增
         */
        public static final String ADDRESS_ADD = "1";
    }
    
    /**
     * 
     * <一句话功能简述>订单状态<功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月19日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public static interface OrderStatus
    {
        /**
         * 取消订单(订单中心的订单取消)
         */
        public static final String ORDER_CANCEL = "4";
        
        /**
         * 一级能力平台已支付
         */
        public static final String ORDER_PAYED = "2";
        
        /**
         * 一级能力平台已履约
         */
        public static final String ONE_CENTER_CONFIRM = "3";
        
        /**
         * 订单中心已支付
         */
        public static final String ORDER_CENTER_PAYED = "3";
        
        /**
         * 订单中心已完成
         */
        public static final String ORDER_CENTER_SUCCESS = "5";
    }
    
    /**
     * 
     * <一句话功能简述>字符串分割符号 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月19日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public static interface Split
    {
        /**
         * 字符串默认分割符 ","
         */
        public static final String DEFAULT_REGEX = ",";
        
        /**
         * 空格分隔符 " "
         */
        public static final String SPACE_REGEX = " ";
        
        /**
         * 分号分隔符 ";"
         */
        public static final String SEMICOLON_REGEX = ";";
        
        /**
         * 冒号分隔符 ":"
         */
        public static final String COLON_REGEX = ":";
        
        /**
         * 点号分割符"."
         */
        public static final String DOT_REGEX = ".";
        
        /**
         * 点号分割符 "\\."
         */
        public static final String DOT_SPLIT_REGEX = "\\.";
        
        /**
         * 斜线分割符 "/"
         */
        public static final String SOLIDUS_REGEX = "/";
        
        /**
         * 单引号分割符 "'"
         */
        public static final String SINGLEQUOTES_REGEX = "'";
        
        /**
         * 横条分割符 "-"
         */
        public static final String CROSSBAND_REGEX = "-";
        
        /**
         * 横条分割符 "="
         */
        public static final String AMOUNT_REGEX = "=";
        
        /**
         * 下划线分割符 "_"
         */
        public static final String UNDERLINE_REGEX = "_";
        
        /**
         * 竖线分割符 "\\|"
         */
        public static final String VERTICAL_REGEX = "\\|";
        
        /**
         * 双引号分割符 "\""
         */
        public static final String DOUBLEQUOTES_REGEX = "\"";
        
        /**
         * 竖线分割符 "|"
         */
        public static final String UPRIGHT_REGEX = "|";
        
        /**
         * 空字符串 ""
         */
        public static final String NULL_REGEX = "";
        
        /**
         * 左括号 (
         */
        public static final String PARENTHESES_LEFT = "(";
        
        /**
         * 右括号 )
         */
        public static final String PARENTHESES_RIGHT = ")";
        
        /**
         * 问号 ?
         */
        public static final String QUESTION_REGEX = "?";
        
        /**
         * 井号 #
         */
        public static final String HASHTAG_REGEX = "#";
        
        /**
         * 百分号 %
         */
        public static final String PERCENTSIGN_REGEX = "%";
        
        /**
         * 星号 *
         */
        public static final String EOS_NOTNULL_FLAG = "*";
        
        /**
         * 地址符 &
         */
        public static final String ADDRESS_CHARACTER_REGEX = "&";
        
        /**
         * 大括号
         */
        char CURLY_BRACES = '}';
        
        /**
         * $符号
         */
        char DOLLAR_MARK = '$';
        
        /**
         * =符号
         */
        char EQUAL_MARK = '=';
        
        /**
         * 中文句号
         */
        char ZH_END = '。';
        
        /**
         * 欧元符号
         */
        char EUROPE = '€';
        
        /**
         * 日志分隔符:三个欧元符号
         */
        public static final String EUROPE_THREE = "€€€";
        
        /**
         * 日志url和报文之间分隔符:::
         */
        public static final String EUROPE_THREES = ":::";
    }
    
    /**
     * 
     * <一句话功能简述>true和false对应的字符串 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月29日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface BooleanStr
    {
        /**
         * ture 对应的字符串
         */
        String TRUE_STATUS_STR = "0";
        
        /**
         * false 对应的字符串
         */
        String FALSE_STATUS_STR = "1";
    }
    
    public static final String MEMEBERPRICE = "memberPrice";
    
    /**
     * 归属类型: 3 IP
     */
    public static final int RELATIONTYPE_IP = 3;
    
    /**
     * 日期格式--yyyyMMddhhmmss
     */
    public static final String DATE_FORMAL_YYYYMMDDHHMMSS = "yyyyMMddhhmmss";
    
    /**
     * 订单返回的objectType类型，3表示运费
     */
    public static final Integer TRANSPORT_FEE_TYPE = 3;
    
    /**
     * objectType类型，4表示圈圈卡
     */
    public static final Integer CARD_CONSUMPTION_TYPE = 4;
    
    /**
     * objectType类型，2表示普通商品
     */
    public static final Integer GENNERAL_OBJECT_TYPE = 2;
    
    /**
     * objectType类型，5表示蛋糕商品
     */
    public static final Integer CAKE_OBJECT_TYPE = 5;
    
    /**
     * objectType类型，6表示米卡商品商品
     */
    public static final Integer MIKA_OBJECT_TYPE = 6;
    
    public static final String MESSAGE_PRE = "密码为：";
    
    /**
     * 
     * <一句话功能简述> 员工会员的状态 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月29日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface UserEffectiveType
    {
        /**
         * 员工有效状态
         */
        final String userEffectiveType = "1";
        
        /**
         * 员工失效状态/或者是订单的状态
         */
        final String userLoseType = "2";
    }
    
    /**
     * 
     * <一句话功能简述>httpClient连接的工具类常量 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年5月29日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface HttpClient
    {
        /**
         * json的编码格式;
         */
        final String CHAR_SET = "UTF-8";
        
        /**
         * 最大连接数400
         */
        int MAX_CONNECTION_NUM = 400;
        
        /**
         * 单路由最大连接数80
         */
        int MAX_PER_ROUTE = 80;
        
        /**
         * 向服务端请求超时时间设置(单位:毫秒)
         */
        int SERVER_REQUEST_TIME_OUT = 5000;
        
        /**
         * 服务端响应超时时间设置(单位:毫秒)
         */
        int SERVER_RESPONSE_TIME_OUT = 5000;
    }
    
    public interface WareServiceConstant
    {
        /**
         * realtiontype - 商品
         */
        Integer RELATION_TYPE_WARE = 1;
        
        /**
         * realtiontype - 商品规格
         */
        Integer RELATION_TYPE_WARE_FORMAT = 2;
    }
    
    /**
     * 
     * <一句话功能简述>订单的常量接口 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月8日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface Order
    {
        /**
         * 支付类型1000,圈圈卡支付
         */
        Integer CHANNEL_TYPE_OR_PAY_TYPE = 1000;
        
        /**
         * orderType为1：购物车
         */
        String ORDER_TYPE_1 = "1";
        
        /**
         * orderType为2：订单
         */
        String ORDER_TYPE_2 = "2";
        
        /**
         * 获取圈圈卡的折扣信息
         */
        String ORDER_CRICLE_CARD_DISCOUNT = "comicCardPay";
        
        /**
         * 计算圈圈卡的折扣
         */
        Double ORDER_CALCULATE_DISCOUNT = 100D;
        
        Double ORDER_ROUND_NUMBER = 0.5D;
        
        /**
         * 支付类型1007,兑换卡支付
         */
        Integer CHANNEL_TYPE_OR_PAY_TYPE_CASH_CARD = 1007;
        
    }
    
    public interface datePattern
    {
        String YYYY_MM_DD = "yyyyMMdd";
    }
    
    /**
     * 
     * <一句话功能简述>PAE请求头信息常量 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月7日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface HeaderConstant
    {
        /**
         * 浏览器可以接受的媒体类型
         */
        String ACCEPT = "*/*";
        
        /**
         * 浏览器申明自己接收的语言
         */
        String ACCEPT_LANGUAGE = "zh-cn,en;q=0.5";
        
        /**
         * 浏览器申明自己接收的编码方法，通常指定压缩方法
         */
        String ACCEPT_ENCODING = "gzip, deflate";
        
        /**
         * 表示是否需要持久连接
         */
        String CONNECTION = "Keep-Alive";
        
        /**
         * 版本号
         */
        String VERSION = "1.0";
        
        /**
         * 接入账号,统一由PAE配置，一个账号对应一种门户类型或者平台
         */
        String SOURCEDEVICECODE = "1404230";
        
        /**
         * 接入账号对应的密码，统一由PAE配置
         */
        String SHARED_SECRET = "35345";
        
        /**
         * 请求头的行数
         */
        Integer HEADER_LENGTH = 9;
        
        /**
         * 服务提供商合作伙伴ID [样例] 000201
         * 
         */
        String SPId = "35000001";
        
        /**
         * 业务id
         *
         */
        String SERVICEID = "35000001000001";
        
    }
    
    /**
     * 
     * <一句话功能简述>调用产品中心使用的常量 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月8日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface ProductCenter
    {
        /**
         * 圈圈卡的objectType
         */
        Integer OBJECT_TYPE = 4;
        
        /**
         * 运费的objectType
         */
        Integer EMAIL_TYPE = 3;
    }
    
    /**
     * 
     * <一句话功能简述>支付类型 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月8日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface PayType
    {
        /**
         * 圈圈卡的payType = 1000
         */
        Integer QUANQUAN_CARD_PAY_TYPE = 1000;
        /**
         * 和包的payType = 1005
         */
        Integer HEBAO_PAY_TYPE = 1005;
        
        /**
         * 兑换卡支付
         */
        Integer CASH_CARD_PAY_TYPE = 1007;
        
        /**
         * 一级支付—微信支付
         */
        Integer YIJI_WX_PAY_TYPE = 1008;
        
        /**
         * 一级支付—支付宝支付
         */
        Integer YIJI_ALI_PAY_TYPE = 1009;
        
        /**
         * 一级支付—咪咕币支付
         */
        Integer YIJI_MIGUPAY_PAY_TYPE = 1011;
        
        /**
         * 一级支付—和包支付
         */
        Integer YIJI_HE_PAY_TYPE = 1010;
    }
    
    /**
     * 
     * <一句话功能简述>用户标识类型 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月11日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface IdType
    {
        String Mobile = "0";//移动手机号
        String USER_ID = "2";
    }
    
    /**
     * 
     * <一句话功能简述>短信类型字段:smsType 1:购买卡密下发 ,2:赠送卡密下发 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月11日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface MessageType
    {
        /**
         * 购买卡密下发
         */
        String PURCHASE_TYPE = "1";
        
        /**
         * 增送卡密下发
         */
        String FREE_TYPE = "2";
        /**
         * 自动绑定短信下发
         */
        String AUTO_TYPE = "3";
    }
    
    /**
     * 
     * <一句话功能简述>一级能力平台消息头 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月12日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface OneAbilityHeader
    {
        /**
         * 协议版本
         */
        String ONE_ABILITY_VERSION = "1.0";
        
        /**
         * 产品线编码
         */
        String ONE_ABILITY_PRODUCTLINE = "03";
        
        /**
         * 平台类型
         */
        String ONE_ABILITY_PLATFORM = "91";
        
        /**
         * 厂商ID
         */
        String ONE_ABILITY_COMPANYId = "01";
        
        /**
         * IMEI
         */
        String ONE_ABILITY_IMEI = "555";
        
        /**
         * imsi
         */
        String ONE_ABILITY_IMSI = "666";
        
        /**
         * 客户端版本号
         */
        String ONE_ABILITY_CLIENTVER = "777";
        
        /**
         * 应用id
         */
        String ONE_ABILITY_APPID = "888";
        
        /**
         * 应用名称
         */
        String ONE_ABILITY_APPNAME = "999";
        
        /**
         * 用户访问线索，依据产品侧格式填写
         */
        String ONE_ABILITY_ACCESSINFO = "000";
        
        /**
         * 灰度使用
         */
        String ONE_ABILITY_EXTENTION = "XXX";
        
        /**
         * 平台密码
         */
        String ONE_ABILITY_SHAREDSECRET = "xxxxx";
        
        /**
         * 渠道id
         */
        String ONE_ABILITY_CHANNELID = "1x1x1x";
        
        /**
         * 接入账号
         */
        String ONE_ABILITY_SOURCEDEVICECODE = "shop";
        
        /**
         * 接入类型编码
         */
        String ONE_ABILITY_PORTALTYPE = "05";
        
        /**
         * 时间戳
         */
        String TIMESTAMP = "20180605170139";
        
    }
    
    /**
     * 
     * <一句话功能简述>获取t_cfginfo中的系统配置信息 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月13日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface CfgInfo
    {
        /**
         * 获取圈圈卡每日最大的消费额
         */
        String MAX_CARD_CONSUMPTION = "maxCardConsumption";
        
        /**
         * 导出审核历史记录IP
         */
        String TASK_EXPORT_APPROVERECORD_IP_CONFIG = "10010056";

        /**
         * 异步导出根目录
         */
        String CPID_INFORMATION = "10040004";
        
        /**
         * 导出内容记录IP
         */
        String TASK_EXPORT_CONTENT_IP_CONFIG = "10010057";
    }
    
    /**
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月13日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface CopyRightCfgInfo
    {
        /**
         * 版权上线定时任务分流IP
         */
        String TASK_COPYRIGHT_ONLIE_IP_CONFIG = "99999009";
        
        /**
         * 版权上线定时任务分流IP
         */
        String TASK_COPYRIGHT_OFFLIE_IP_CONFIG = "99999010";

    }
    
    /**
     * 同步失败重发最大失败次数配置
     */
    public interface ResysncMaxFailCount
    {
        String NOTIFY_FAIL_MAX = "10080004";
    }
    
    /**
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月13日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface ComicAutoCreatCfgInfo
    {
        /**
         * 版权上线定时任务分流IP
         */
        String TASK_COMIC_AUTOCREAT_IP_CONFIG = "99999011";
        
    }
    
    /**
     * 调用cmi地址
     * <一句话功能简述>
     * <功能详细描述>
     * 
     * <AUTHOR>
     * @version  [版本号, 2019年1月5日]
     * @see  [相关类/方法]
     * @since  [产品/模块版本]
     */
    public interface CmiCfgInfo
    {
        /**
         * 调用cmi发送短信消息url
         */
        String CMI_SENDMQMESSAGE_URL_CONFIG = "********";
        
    }
    
    /**
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月13日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface MsgCfgInfo
    {
        /**
         * triggerType在数据库配置表中的objectid
         */
        String MSG_TRIGGERTYPE_CONFIG_VALUE = "********";
        
        /**
         * fromAccount在数据库配置表中的objectid
         */
        String MSG_FROMACCOUNT_CONFIG_VALUE = "********";
        
        /**
         * userName在数据库配置表中的objectid
         */
        String MSG_USERNAME_CONFIG_VALUE = "********";
        
        /**
         * passwordTemp在数据库配置表中的objectid
         */
        String MSG_PASSWORDTEMP_CONFIG_VALUE = "********";
        
        /**
         * spId在数据库配置表中的objectid
         */
        String MSG_SPID_CONFIG_VALUE = "********";
        
        /**
         * serviceId在数据库配置表中的objectid
         */
        String MSG_SERVICEID_CONFIG_VALUE = "********";
        
        /**
         * senderName在数据库配置表中的objectid
         */
        String MSG_SENDERNAME_CONFIG_VALUE = "********";
        
        /**
         * paeUrl在数据库配置表中的objectid
         */
        String MSG_PAEURL_CONFIG_VALUE = "********";

    }
    
    /**
     * 
     * <一句话功能简述>是否使用数据库配置的一级能力中心的秘钥 <功能详细描述>
     * 
     * <AUTHOR>
     * @version [版本号, 2018年6月20日]
     * @see [相关类/方法]
     * @since [产品/模块版本]
     */
    public interface OneLevelSwitch
    {
        /**
         * 使用数据库中配置的秘钥
         */
        String OPEN = "1";
        
        /**
         * 使用默认配置
         */
        String CLOSE = "0";
    }
    /**
     * 
     * 
     * 
     * <AUTHOR>
     * @version  [版本号, 2018年8月3日]
     * @see  [相关类/方法]
     * @since  [产品/模块版本]
     */
    public interface CampaignSync
    {
        /**
         * 优惠券金额
         */
        String PAYMENTBILLS = "paymentBills";
        /**
         * 订单失效时间类型
         */
        String EXPIRETIMETYPE = "expireTimeType";
        /**
         * 订单指定失效时间
         */
        String SPECIFYEXPIRETIME = "specifyExpireTime";
        /**
         * 相对时间
         */
        String EXPIRETIMETYPE_1 = "1";
        /**
         * 指定时间
         */
        String EXPIRETIMETYPE_2 = "2";
    }
    
    /**
     * 
     * 任务表任务类型枚举
     * 
     * <AUTHOR>
     * @version  [版本号, 2018年12月26日]
     */
    public interface TaskType
    {
        /**
         * 审核记录导出任务枚举
         */
        int TASKTYPE_EXPORT_APPROVERECORD = 5;
        
        /**
         * 内容导出枚举
         */
        int TASKTYPE_EXPORT_CONTENT = 6;
        
        /**
         * 导出版权信息枚举
         */
        int TASKTYPE_EXPORT_COPYINFO = 8;
        
        /**
         * 导出版权信息枚举
         */
        int TASKTYPE_EXPORT_BLACKCONTENT = 12;
        
        /**
         * 导出媒资信息枚举
         */
        int TASKTYPE_EXPORT_MEDIACONT = 35;
        
        /**
         * 导出版权信息枚举
         */
        int TASKTYPE_EXPORT_SPEQUAAPPROVERECORD = 25;
        
        /**
         * 11、举报内容数据导出
         */
        String EXPORT_COMPLAINCONTENT = "11";
    }
    
}