<template>
  <div>
    <h1 class="user-title">彩印专题标签</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="标签名称">
          <el-input v-model="searchReq.labelName" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="pageQuery()" size="small" class="app-bnt">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" size="small" @click="add">新建标签</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-table :data="tableData" border class="app-tab">
      <el-table-column type="index" width="150" label="序号"></el-table-column>
      <el-table-column prop="labelName" width="310" label="标签名称"></el-table-column>
      <el-table-column width="120" label="标签图片">
        <template slot-scope="scope">
          <el-button
            @click="labelPhotoVisible=true;labelPhotoUrl=scope.row.labelPhotoUrl"
            type="text"
            size="small"
            style="text-align: center"
          >详情</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" width="200" label="提交时间"></el-table-column>
      <el-table-column prop="dsSubmitTime" label="操作">
        <template slot-scope="scope">
          <el-button @click="editItem(scope.row)" type="text" size="small">编辑</el-button>
          <el-button @click="deleteItem(scope.row);" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      width="30%"
      title="标签图片"
      :close-on-click-modal="false"
      :visible.sync="labelPhotoVisible"
      append-to-body
    >
      <img style="display: block;width: 80%; margin: 0 auto;" :src="labelPhotoUrl" alt />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="labelPhotoVisible=false">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 分页 -->
    <div class="block app-pageganit">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableData.pageIndex"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageTotal"
        style="text-align: right;"
      ></el-pagination>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  name: "userPush",
  data() {
    return {
      propMsg: "",
      row: {},
      labelPhotoVisible: false,
      labelPhotoUrl: "",
      delVisible: false,
      pageTotal: 0,
      tableData: [],
      searchReq: {
        labelName: "",
        pageIndex: 1,
        pageSize: 10
      }
    };
  },
  mounted() {
    this.pageQuery();
  },
  methods: {
    //查询请求
    pageQuery: function() {
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectLabelList`,
          JSON.stringify(this.searchReq)
        )
        .then(function(res) {
          if (res.data.code == 0) {
            this.tableData = res.data.data;
            this.pageTotal = res.data.totalCount;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
            this.$message.error(res.data.message);
          }
        });
    },
    add() {
      sessionStorage.removeItem('subjectLabelHandleType')
      this.$router.push('/newSubjectLabel')
    },
    editItem(row) {
      sessionStorage.setItem('subjectLabelHandleType', 'edit')
      sessionStorage.setItem('subjectLabelContent', JSON.stringify(row))
      this.$router.push('/newSubjectLabel')
    },
    //删除
    deleteItem(row) {
      this.$confirm("确认删除此标签?").then(_ => {
        this.$http
          .post(`${this.proxyUrl}/cySubject/delSubjectLabel`, JSON.stringify({
            id: row.id
          }))
          .then(res => {
            if (res.data.code == 0) {
              this.$message.success("删除成功!");
              this.pageQuery();
            } else {
              this.$message.error(res.data.message);
              this.pageQuery();
            }
          });
      }, () => {});
    },
    //分页
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      this.pageQuery();
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageIndex = val;
      this.pageQuery();
    },
  },
};
</script>
<style scoped>
.user-title {
  padding: 10px 0px 0px 0px;
}
.user-line {
  width: 100%;
  margin: 0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #dddfe6;
}
.user-search {
  width: 100%;
}
.user-push-serch {
  margin-top: 10px;
  padding-left: 24px !important;
}
.el-table {
  border: 1px solid #ecebe9;
  margin: 0 auto;
  width: 99%;
}
.el-th {
  border-right: 1px solid #dddfe6 !important;
}
</style>
