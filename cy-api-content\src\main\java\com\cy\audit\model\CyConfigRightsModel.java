package com.cy.audit.model;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-10-16 10:54
 */

public class CyConfigRightsModel implements Serializable {
    private Integer id;
    private String provinceCode;
    private String packageCode;
    private String cost;
    private Integer hasDiyRights;
    private Integer hasTextRights;
    private Integer hasGrgcRights;
    private String packageName;
    private List<String> packageCodeList;
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public List<String> getPackageCodeList() {
        return packageCodeList;
    }

    public void setPackageCodeList(List<String> packageCodeList) {
        this.packageCodeList = packageCodeList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPackageCode() {
        return packageCode;
    }

    public void setPackageCode(String packageCode) {
        this.packageCode = packageCode;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public Integer getHasDiyRights() {
        return hasDiyRights;
    }

    public void setHasDiyRights(Integer hasDiyRights) {
        this.hasDiyRights = hasDiyRights;
    }

    public Integer getHasTextRights() {
        return hasTextRights;
    }

    public void setHasTextRights(Integer hasTextRights) {
        this.hasTextRights = hasTextRights;
    }

    public Integer getHasGrgcRights() {
        return hasGrgcRights;
    }

    public void setHasGrgcRights(Integer hasGrgcRights) {
        this.hasGrgcRights = hasGrgcRights;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
}