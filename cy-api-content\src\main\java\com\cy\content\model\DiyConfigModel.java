package com.cy.content.model;

import java.io.Serializable;

public class DiyConfigModel implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    String id;

    String provinceCode;
    String packageCode;
    String cost;
    String hasDiyRights;
    String hasTextRights;
    String hasGrgcRights;
    String packageName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}


	public String getPackageCode() {
		return packageCode;
	}

	public void setPackageCode(String packageCode) {
		this.packageCode = packageCode;
	}

	public String getCost() {
		return cost;
	}

	public void setCost(String cost) {
		this.cost = cost;
	}

	public String getHasDiyRights() {
		return hasDiyRights;
	}

	public void setHasDiyRights(String hasDiyRights) {
		this.hasDiyRights = hasDiyRights;
	}

	public String getHasTextRights() {
		return hasTextRights;
	}

	public void setHasTextRights(String hasTextRights) {
		this.hasTextRights = hasTextRights;
	}

	public String getHasGrgcRights() {
		return hasGrgcRights;
	}

	public void setHasGrgcRights(String hasGrgcRights) {
		this.hasGrgcRights = hasGrgcRights;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}



	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	@Override
	public String toString() {
		return "DiyConfigModel{" +
				"id='" + id + '\'' +
				", provinceCode='" + provinceCode + '\'' +
				", packageCode='" + packageCode + '\'' +
				", cost='" + cost + '\'' +
				", hasDiyRights='" + hasDiyRights + '\'' +
				", hasTextRights='" + hasTextRights + '\'' +
				", hasGrgcRights='" + hasGrgcRights + '\'' +
				", packageName='" + packageName + '\'' +
				'}';
	}
}
