package com.cs.param.controller;

import com.cs.param.common.ParBlackListCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.execl.PhoneData;
import com.cs.param.services.ParBlackListService;
import com.cs.param.services.SysTaskService;
import com.cs.param.utils.LogUtil;
import com.cy.jwt.JwtUtil.JWTHelper;
import com.cy.model.SysTaskModel;
import com.github.crab2died.ExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;


/**
 * 系统黑名单管理控制层
 *
 * <AUTHOR> Zhu
 * @date 2021/8/2 14:32
 */
@RestController
@RequestMapping("blackList")
@Slf4j
public class BlackListController {


    @Autowired
    private SysTaskService sysTaskService;
    @Autowired
    private ParBlackListService parBlackListService;
    // 线程池
    private final static ExecutorService executorService = Executors.newFixedThreadPool(12);


    /**
     * 查询黑名单
     *
     * @param common
     * @return
     */
    @PostMapping("getBlackListPage")
    public ResultListCommon getBlackListPage(@RequestBody ParBlackListCommon common) {
        LogUtil.info(log, LogUtil.BIZ, "getBlackListPage", "获取系统黑名单列表", common);
        ResultListCommon result = new ResultListCommon();
        try {
            result.setPageNum(common.getPageNum());
            result.setPageTotal(parBlackListService.queryCount(common));
            result.setDatas(parBlackListService.queryPageInfos(common));
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "getBlackListPage", "获取系统黑名单列表异常", e);
        }
        LogUtil.info(log, LogUtil.BIZ, "getBlackListPage", "获取系统黑名单列表", result);
        return result;
    }

    /**
     * 新增黑名单
     *
     * @param common
     * @return
     */
    @PostMapping("addBlackList")
    public ResultCommon addBlackList(@RequestBody ParBlackListCommon common) {
        LogUtil.info(log, LogUtil.BIZ, "addBlackList", "新增系统黑名单", common);
        ResultCommon result = new ResultCommon();
        result.setResStatus(1);
        try {
            String newPhone = common.getPhone();
            String checkResult = checkPhone(newPhone);
            if (checkResult != null) {
                result.setResText(checkResult);
                return result;
            }
            common.setSysUserName(JWTHelper.getSysUser().getSysUserName());
            if (parBlackListService.insert(common) > 0) {
                result.setResStatus(0);
            }
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "addBlackList", "新增系统黑名单异常", e);
            result.setResText("新增系统黑名单异常");
        }
        return result;
    }

    /**
     * 删除黑名单
     *
     * @param common
     * @return
     */
    @PostMapping("delBlackList")
    public ResultCommon delBlackList(@RequestBody ParBlackListCommon common) {
        LogUtil.info(log, LogUtil.BIZ, "delBlackList", "删除系统黑名单", common);
        ResultCommon result = new ResultCommon();
        result.setResStatus(1);
        try {
            if (parBlackListService.delBlackList(common) > 0) {
                result.setResStatus(0);
            }
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "delBlackList", "删除系统黑名单异常", e);
            result.setResText("删除系统黑名单异常");
        }
        return result;
    }

    /**
     * 更新黑名单
     *
     * @param common
     * @return
     */
    @PostMapping("updateBlackList")
    public ResultCommon updateBlackList(@RequestBody ParBlackListCommon common) {
        LogUtil.info(log, LogUtil.BIZ, "updateBlackList", "更新系统黑名单", common);
        ResultCommon result = new ResultCommon();
        result.setResStatus(1);
        try {
            String newPhone = common.getNewPhone();
            String checkResult = checkPhone(newPhone);
            if (checkResult != null) {
                result.setResText(checkResult);
                return result;
            }
            common.setSysUserName(JWTHelper.getSysUser().getSysUserName());
            if (parBlackListService.updateBlackList(common) > 0) {
                result.setResStatus(0);
            }
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "updateBlackList", "更新系统黑名单异常", common);
            result.setResText("更新系统黑名单异常");
        }
        return result;
    }

    /**
     * 下载黑名单模板
     *
     * @param response
     */
    @RequestMapping("downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        LogUtil.info(log, LogUtil.BIZ, "downloadTemplate", "下载黑名单模板");
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/octet-stream");
        InputStream ins = null;
        try {
            Resource resource = new ClassPathResource("redlist_template.xlsx");
            ins = resource.getInputStream();
            response.setHeader("Content-Disposition", "attachment; filename=blacklist_template.xlsx");
            IOUtils.copy(ins, response.getOutputStream());
            response.flushBuffer();
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "downloadTemplate", "下载黑名单模板异常", e);
        } finally {
            if (ins != null) {
                try {
                    ins.close();
                } catch (IOException e) {
                    LogUtil.error(log, LogUtil.BIZ, "downloadTemplate", "下载黑名单模板关闭流异常", e);
                }
            }
        }
    }

    /**
     * 批量导入系统黑名单
     *
     * @param multipartRequest
     * @param response
     * @return
     */
    @PostMapping("batchInsert")
    public ResultCommon batchInsert(MultipartHttpServletRequest multipartRequest, HttpServletResponse response) {
        LogUtil.info(log, LogUtil.BIZ, "batchInsert", "批量导入系统黑名单");
        Integer taskId = null;
        SysTaskModel task = new SysTaskModel();
        try {
            String sysUserName = JWTHelper.getSysUser().getSysUserName();
            String fileName = multipartRequest.getFileNames().next();
            MultipartFile file = multipartRequest.getFile(fileName);
            task.setSysTaskName("批量导入系统黑名单");
            task.setSysTaskDesc("批量导入系统黑名单");
            task.setSysTriggerPeople(sysUserName);
            task.setSysFileName(new String(file.getOriginalFilename().getBytes("UTF-8"), "UTF-8"));
            taskId = sysTaskService.startUpload(task);
            task.setSysTaskId(taskId);
            List<PhoneData> phoneList = ExcelUtils.getInstance().readExcel2Objects(file.getInputStream(), PhoneData.class, 0, Integer.MAX_VALUE, 0);
            executorService.submit(() -> parBlackListService.insertBatch(phoneList, sysUserName));
            if (taskId != null) sysTaskService.endUpload(task);
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "batchInsert", "批量入库黑名单异常", e);
            if (taskId != null) sysTaskService.failUpload(task);
            return ResultCommon.getInstance(1, "批量入库黑名单失败");
        }
        return ResultCommon.getInstance(0, null);
    }

    /**
     * 批量删除黑名单
     *
     * @param common
     * @return
     */
    @PostMapping("delBatch")
    public ResultCommon delBatch(@RequestBody ParBlackListCommon common) {
        LogUtil.info(log, LogUtil.BIZ, "delBatch", "批量删除黑名单", common);
        Integer delResult = 0;
        try {
            if (common == null || common.getPhones().length == 0) {
                return ResultCommon.getInstance(1, "号码不得为空");
            }
            delResult = parBlackListService.delBatch(common);
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.BIZ, "delBatch", "批量删除黑名单异常", e);
            return ResultCommon.getInstance(1, "批量删除黑名单异常");
        }
        return ResultCommon.getInstance(0, delResult.toString());
    }

    /**
     * 新增或更新时检查手机格式以及是否已经在表中存在
     *
     * @param newPhone  手机号
     * @return
     */
    private String checkPhone(String newPhone) {
        String result = null;
        if (StringUtils.isBlank(newPhone)) {
            return result = "新号码不能为空";
        }
        // 号码规则有时间再优化，目前只校验长度
        if (newPhone.length() != 11) {
            return result = "新号码必须为11位手机号码";
        }
        if (parBlackListService.isExisted(newPhone)) {
            return result = "新号码在黑名单中已经存在";
        }
        return result;
    }
}
