package com.cy.user.model;

import java.util.List;

public class PersonInfoModel {

	private String userId;
	/**
	 * 个人彩印状态 0：删除(未开通) 1：正常(已开通)
	 * 
	 */
	private int personStatus;
	/**
	 * 企业代码
	 */
	private String bossId;
	/**
	 * 业务代码
	 */
	private String serviceId;
	/**
	 * 产品代码
	 */
	private String productId;
	/**
	 * 套餐包名称
	 */
	private String packageName;
	/**
	 * 业务资费
	 */
	private String serviceCost;
	/**
	 * 开通渠道
	 */
	private String serviceChannel;
	/**
	 * 订购时间
	 */
	private String submitTime;

	private List<PersonCsSetModel> csList;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public int getPersonStatus() {
		return personStatus;
	}

	public void setPersonStatus(int personStatus) {
		this.personStatus = personStatus;
	}

	public String getBossId() {
		return bossId;
	}

	public void setBossId(String bossId) {
		this.bossId = bossId;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getServiceCost() {
		return serviceCost;
	}

	public void setServiceCost(String serviceCost) {
		this.serviceCost = serviceCost;
	}

	public String getServiceChannel() {
		return serviceChannel;
	}

	public void setServiceChannel(String serviceChannel) {
		this.serviceChannel = serviceChannel;
	}

	public List<PersonCsSetModel> getCsList() {
		return csList;
	}

	public void setCsList(List<PersonCsSetModel> csList) {
		this.csList = csList;
	}

	public String getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(String submitTime) {
		this.submitTime = submitTime;
	}

}
