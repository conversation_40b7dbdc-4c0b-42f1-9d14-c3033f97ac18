<template>
    <div class="addwhite">
        <div class="content">
            <el-form :model="addForm" ref="addForm" :rules="rules" class="demo-form-inline app-form-item" size="small" label-width="35%"  style="width: 80%">
                <el-form-item label="号码：" prop="phoneNumber">
                    <el-input v-model="addForm.phoneNumber" style="width: 80%;"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="content1" style="text-align: right;">
            <el-button @click="cancle" size="small">取消</el-button>
            <el-button type="primary" @click="submit" size="small">确定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'addwhite',
        data(){
            return{
                name:'',
                addForm:{
                    type:2,//1.生效号码 2.白名单
                    phoneNumber:'',//号码
                    provinceId:'',//省份ID
                    provinceName:'',//省份名称
                    countyId:'',//地区ID
                    countyName:'',//地区名称
                    categoryId:1,//分类（1.诈骗 2黄页 3标记)
                    standardTypeId:'',//标准类型id
                    markTypeName:[],//标记类型名称
                    markTypeIds:[],//标记类型id
                    sourceId:'',//来源
                    markTimes:'',//标记次数
                    classBtype:'',//号码描述
                    operatorName:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//操作者姓名
                },
                haveNumber:false,
                rules: {
                    phoneNumber: [
                        {required: true, message: '请输入号码', trigger: 'blur' },
                    ],
                }
            }
        },
        props:['addVisible'],
        components: {},
        watch:{
            'addForm.phoneNumber':function () {
                if(this.addForm.phoneNumber.length==11){
                    this.queryNumIsExist();
                }
            },
            'addVisible':function () {
                if(!this.addVisible){
                    this.resetForm('addForm');
                }
            }
        },
        created(){

        },
        methods:{
            //查询号码是否存在
            queryNumIsExist(){
                let vm = this;
                vm.haveNumber = false;
                postHeader('queryNumIsExist',JSON.stringify(vm.addForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        if(data.data.queryNumInfoList){
                            vm.$message.error("新增号码已存在，不允许重复新增");
                            vm.haveNumber = true;
                        }
                    }
                })
            },
            submit(){
                let vm = this;
                this.$refs['addForm'].validate((valid) => {
                    if(valid){
                        if(!this.haveNumber){//如果不存在
                            postHeader('addNumber', JSON.stringify(vm.addForm)).then(res=>{
                                let data = res.data;
                                if(data.code==0){
                                    vm.$message.success("新增成功");
                                }else{
                                    vm.$message.error("新增失败");
                                }
                            })
                        }else{
                            vm.$message.error("新增号码已存在，不允许重复新增");
                        }
                        this.$emit('addList');
                    }
                })
            },
            cancle(){
                this.$emit('addList');
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]) {
                    this.$refs[formName].resetFields();
                }
            }
        },

    }
</script>

<style scoped>
    .content{
        width: 640px;
    }
    .content1{
        text-align: center;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px;
        margin-right: 20px;
    }
</style>
