<template>
    <div class="fun_page">
        <h1 class="user-title">内容设置统计</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <el-form :inline="true" :model="searchForm1" size="small">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="时间">
                            <el-select v-model="searchForm1.type" style="width: 60px">
                                <el-option label="天" value="date"></el-option>
                                <el-option label="月" value="month"></el-option>
                            </el-select>
                        </el-form-item>
                         <el-form-item>
                            <el-date-picker placeholder="开始日期" v-model="searchForm1.startDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="searchForm1.type"/>
                            至
                            <el-date-picker placeholder="结束日期" v-model="searchForm1.endDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="searchForm1.type"/>
                        </el-form-item>
                        <el-form-item label="省份：">
                            <el-select v-model="selectProvinceIds" multiple collapse-tags clearable style="width: 150px" @change="selectAll2($event,provinceList)">
                                <el-option  value="全选" label="全选" style="font-weight:bold;"></el-option>
                                <el-option v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="城市">
                            <el-select v-model="searchForm1.showCounty" clearable  style="width: 120px">
                                <el-option label="展示" :value="1"></el-option>
                                <el-option label="不展示" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search" v-bind:disabled="btnhide">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain @click="download" v-bind:disabled="btnhide">导出excel</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <!--数据信息-->
            <div style="margin-top: 30px;">
                <el-table :data="tableData" border style="width: 100%" max-height=500 v-loading="tableLoading">
                    <el-table-column prop="statsDate" label="时间"/>
                    <el-table-column prop="provinceName" label="省份"/>
                    <el-table-column prop="countyName" label="地市"/>
                    <el-table-column label="内容修改用户数">
                        <el-table-column prop="systemCyUser" label="用户设置"/>
                        <el-table-column prop="diyCyUser" label="用户编辑"/>
						<el-table-column prop="defaultCyUser" label="默认彩印盒配置"/>
					    <el-table-column prop="modifiedCyUser" label="主动修改"/>
                    </el-table-column>
                    <el-table-column label="内容修改次数">
                        <el-table-column prop="updateSystemCyCount" label="用户设置"/>
                        <el-table-column prop="updateDiyCyCount" label="用户编辑"/>
						<el-table-column prop="updateDefaultCyCount" label="默认彩印盒配置"/>
						<el-table-column prop="updateModifiedCyCount" label="主动修改"/>
                    </el-table-column>
                </el-table>
                <div class="block app-pageganit">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="searchForm1.page.pageNo"
                                :page-sizes="[100, 200, 300, 400]" :page-size="searchForm1.page.pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="searchForm1.page.total">
                    </el-pagination>
                </div>
            </div>
        </div>
        <jkAuth menuCode="CPUserList" @buttonDisable="setButton"></jkAuth>
    </div>
</template>

<script>
    import Vue from 'vue'
    import VeLine from 'v-charts/lib/line'
    import axios from '../../../node_modules/axios/dist/axios'
    import {dowandFile,formDate} from './../../util/core.js';
    import jkAuth from '@/components/common/jkAuth';

    Vue.component(VeLine.name, VeLine)

    export default {
        components:{
            jkAuth
        },
        name: 'procMoni',
        data() {
            return {
                btnhide:true,
                functionCode: '030304',
                operationCode: '3004',
                searchForm1:{
                    dataType:'cyUserTotalCount',
                    date1:'last_seven_days',
                    date2:[],
                    type:'date',
                    startDate:'',
                    endDate:'',
                    provinceIds:new Array(),
                    showCounty:1,
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                searchForm2:{
                    date:'',
                    startDate:'',
                    endDate:'',
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                //省份列表
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                regionList:new Array(),//城市列表
                chartData:{},
                chartLoading:false,
                chartSettings:{},
                tableData: [],
                tableLoading:false,
                pickerOptions:{
                    disabledDate:function (today) {
                        return today.getTime()>Date.now();
                    }
                },
                oldOptions:[],
                selectProvinceIds:[],
            }
        },
        created: function () {
            this.chartColors=["#5ab1ef"];
            this.legend_visible=false;
            this.chartSettings = {
                area: true
            };
        },
        watch: {
            'searchForm1.dataType':function (n, o) {this.search();},
            'searchForm1.date1':function (n, o) {
                if(n!=''){
                    this.searchForm1.date2='';
                    this.searchForm1.date2='';
                }
            },
            'searchForm1.date2':function (n, o) {
                if(n!=''){
                    this.searchForm1.date1='';
                }
            },
        },
        methods: {
            setButton(data){
                this.btnhide = data;
            },
            check(vm){
                if (vm.searchForm1.provinceIds.length===0) {
                    vm.$message("请选择省份");
                    return false;
                }
                return true;
            },
            search() {
                const vm = this;
                if(!vm.check(vm)){
                    return false;
                }
                var params = {
                    startDate:'',
                    endDate:'',
                    showType:this.searchForm1.type==='date'?1:2,
                    provinceIds:vm.searchForm1.provinceIds.join(','),
                    showCounty:vm.searchForm1.showCounty,
                    pageNo:vm.searchForm1.page.pageNo,
                    pageSize:vm.searchForm1.page.pageSize,
                }
                if(!this.searchForm1.startDate){
                    this.$message('请输入开始时间');
                    return;
                }
                if(!this.searchForm1.endDate){
                    this.$message('请输入结束时间');
                    return;
                }
                if(new Date(this.searchForm1.startDate).getTime()>new Date(this.searchForm1.endDate).getTime()){
                    this.$message('结束时间不得小于开始时间');
                    return
                }
                if(this.searchForm1.type=='month'){
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM');
                }else{
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM-dd');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM-dd');
                }
                vm.tableLoading=true;
                axios.post(`${this.proxyUrl}/oper/sop`,params,{
                        
                            headers: {
                                'CY-operation': 'contentStats'
                            }
                        }).then(function(response) {
                            
                            vm.tableData = response.data.data.contentStatsListByTime;
                            vm.searchForm1.page.total = response.data.data.total;
                            vm.chartData= {
                                columns: ['statsDate',vm.searchForm1.dataType],
                                rows: response.data.data.contentStatsListByDay
                            };
                            vm.chartLoading=false;
                            vm.tableLoading=false;
                        })
                        .catch(function (error) {
                            console.log(error);
                        }).finally(function () {
                            vm.tableLoading=false;
                        });
            },
            download() {
                 const vm = this;
                if(!vm.check(vm)){
                    return false;
                }
                var params = {
                    showType:this.searchForm1.type==='date'?1:2,
                    provinceIds:vm.searchForm1.provinceIds.join(','),
                    showCounty:vm.searchForm1.showCounty,
                }
                if(!this.searchForm1.startDate){
                    this.$message('请输入开始时间');
                    return;
                }
                if(!this.searchForm1.endDate){
                    this.$message('请输入结束时间');
                    return;
                }
                if(new Date(this.searchForm1.startDate).getTime()>new Date(this.searchForm1.endDate).getTime()){
                    this.$message('结束时间不得小于开始时间');
                    return
                }
                if(this.searchForm1.type=='month'){
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM');
                }else{
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM-dd');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM-dd');
                }
                //发送下载请求
                axios.post(`${this.proxyUrl}/oper/sop`,params,{
                    headers: {
                        'CY-operation': 'exportContentStats'
                    },
                    responseType:'blob'
                })
                .then(function(response) {
                    dowandFile(response.data,'内容设置统计数据.xlsx');
                })
                .catch(function (error) {
                    console.log(error);
                })
            },
            handleSizeChange(val) {
                this.searchForm1.page.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm1.page.pageNo=val;
                this.search();
            },
            // //根据省份查询城市列表
            // selectProvince(){
            //     this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.searchForm1.provinceId},{emulateJSON:true})
            //                 .then((res)=>{
            //                     this.regionList=res.data;
            //     })
            // }
            selectAll2(val,optionList) {
                let allCodeName='全选';
                let allValues = ["全选"]
                //保留所有值
                for (let item of optionList) {
                    allValues.push(item.provinceCode)
                } 
                // 若是全部选择
                if (val.includes(allCodeName)) this.selectProvinceIds = allValues;


                 // 取消全部选中  上次有 当前没有 表示取消全选
                if (this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) this.selectProvinceIds = []

                // 点击非全部选中  需要排除全部选中 以及 当前点击的选项 
                // 新老数据都有全部选中 
                if (this.oldOptions.includes(allCodeName) && val.includes(allCodeName)) {
                    const index = val.indexOf(allCodeName)
                    val.splice(index, 1) // 排除全选选项
                    this.selectProvinceIds = val
                }

                //全选未选 但是其他选项全部选上 则全选选上 上次和当前 都没有全选
                if (!this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) {
                    if (val.length === allValues.length - 1) this.selectProvinceIds = [allCodeName].concat(val)
                }

                //储存当前最后的结果 作为下次的老数据 
                this.oldOptions = this.selectProvinceIds;

                //设置到form表单
                this.searchForm1.provinceIds = [];
                for (let provinceCode of this.selectProvinceIds) {
                    if(provinceCode != allCodeName){
                        this.searchForm1.provinceIds.push(provinceCode)
                    }
                    
                }
            }
        }
    }
</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
    .el-radio-button:first-child .el-radio-button__inner,.el-radio-button:last-child .el-radio-button__inner{
        border-radius:0;
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #434343;
        border-color:#DDDFE6;
        box-shadow:-1px 0 0 0 #DDDFE6;
        color:#fff;
    }
    .chartArea{
        border:1px solid #DDDFE6;
        margin-top:-1px;
        background-color: #F2F2F2;
    }
    .chartArea form{
        margin:20px 0 0 20px;
    }
    .chartArea .el-radio-button__inner{
        background:transparent;
        border: 1px solid transparent;
    }
    .chartArea .el-radio-button__orig-radio:checked+.el-radio-button__inner{
        background-color:#3B9ED8;
    }
</style>
