package com.cy.user.api;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.cy.user.common.CyUserCommon;
import com.cy.user.model.PersonInfoModel;

public interface CyPersonApi {
	
	@RequestMapping(value = "/cypersonservice/getPersonInfo")
	List<PersonInfoModel> getPersonInfo(CyUserCommon common);
	
	@RequestMapping(value = "/cypersonservice/countPersonCs")
	int countPersonCs(CyUserCommon common);

}
