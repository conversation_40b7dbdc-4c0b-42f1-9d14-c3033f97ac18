import Select from './src/select';
import Option from './src/option';

const components = {
  Select,Option
}
const install = function(Vue, opts = {}) {
  components.map(component => {
    Vue.component(component.name, component);
  });
}
/* istanbul ignore if */
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue);
}

module.exports = {
  Select,Option
}

module.exports.default = module.exports;
