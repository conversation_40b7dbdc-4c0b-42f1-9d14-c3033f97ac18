<template scope="scope">
  <div>
    <!--中央平台审核分类优化——修改增彩内容为内容-->
    <div class="user-titler">内容</div>
    <!--广告彩印-->
    <div class="wrap">
      <div class="itemWrap">
        <div class="item itemLeft">彩印ID：</div>
        <div class="item itemRight">{{massDetail.uuid}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">主题名称：</div>
        <div class="item itemRight">{{massDetail.subject}}</div>
      </div>
      <div class="itemWrap">
        <!--中央平台审核分类优化——修改增彩内容为内容-->
        <div class="item itemLeft">内容：</div>
      </div>
      <div class="itemWrap" v-for="(item, index) in frames" :key="index">
        <div class="item itemImg itemRight">
          <div class="itemImgWrap" v-if="item.text && (item.text != null)">
            <p class="hangupText" style="color: #606266" v-html="item.text"></p>
          </div>
          <div class="itemImgWrap" v-if="item.imageLocalPath && (item.imageLocalPath != null)">
            <div class="minImgBox"style="margin: 2px 0;">
              <img
                      :src="item.imageLocalPath"
                      alt
                      class="minImg"
                      @click="_showBigImg(item.imageLocalPath)"
              >
            </div>
          </div>
          <div class="itemImgWrap" v-if="item.videoLocalPath && (item.videoLocalPath != null)">
            <div class="minImgBox" style="margin: 2px 0;">
              <video :src="item.videoLocalPath" controls="controls" class="minVideo"></video>
              <div class="videoShade" @click="_showVideo(item.videoLocalPath)"/>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="itemWrap">
        <div class="item itemLeft">彩印视频：</div>
        <div class="item itemImg">
          <el-row>
            <el-col v-for="(item) in frames" :key="item.id">
              <div class="minImgBox">
                <img
                  v-if="item.imageLocalPath && (item.imageLocalPath != null)"
                  :src="item.imageLocalPath"
                  alt
                  class="minImg"
                  @click="_showVideo(item.videoLocalPath)"
                >
              </div>
            </el-col>
          </el-row>
        </div>
      </div>-->
    </div>
    <div class="bottom">
      <el-button size="small" type="primary" @click="back">返回</el-button>
    </div>

    <div v-show="showBigImg" class="dialog-bg" style="showBigImg" @click="_hideBigImg">
      <img :src="bigImg">
    </div>

    <div v-show="showVideo" class="dialog-bg" style="showBigImg" @click="_hideVideo">
      <video :src="videoSrc" controls="controls"></video>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      massDetail: {},
      frames: [],
      bannerImages: [],
      showBigImg: false,
      bigImg: "",
      showVideo: false,
      videoSrc: ""
    };
  },
  created() {
    this.massDetail = JSON.parse(sessionStorage.getItem("massDetail"));
    console.log(this.massDetail);
    this.frames = this.massDetail.frames || [];
    this.bannerImages = this.massDetail.bannerImages || [];
  },
  watch: {},
  methods: {
    back() {
      window.history.back();
    },
    _showBigImg(src) {
      this.showBigImg = true;
      this.bigImg = src;
    },
    _hideBigImg() {
      this.showBigImg = false;
      this.bigImg = "";
    },
    _showVideo(src) {
      this.showVideo = true;
      this.videoSrc = src;
    },
    _hideVideo() {
      this.showVideo = false;
      this.videoSrc = "";
    }
  }
};
</script>

<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.wrap {
  padding: 24px;
}

.itemWrap {
  margin: 6px 0;
  font-size: 0;
}

.itemWrap >>> .el-col {
  width: 200px;
}

.itemLeft {
  width: 120px;
  float: left;
  /* text-align: right; */
}

.itemRight {
  margin-left: 120px;
}

.item {
  font-size: 16px;
  min-height: 21px;
}

.itemImg {
  vertical-align: top;
}

.minImgBox {
  width: 500px;
  height: 100px;
  position: relative;
  border: 1px solid #ccc;
}

.minImg {
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  cursor: pointer;
}

.videoShade {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  cursor: pointer;
}

.minVideo {
  width: 100%;
  height: 100%;
}

.bottom {
  border-top: 1px solid #d9d9d9;
  line-height: 56px;
  padding-left: 120px;
}

.dialog-bg {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
  cursor: pointer;
}

.dialog-bg img,
.dialog-bg video {
  height: 80%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  cursor: pointer;
}

.itemImgWrap {
  display: inline-block;
}

.itemImgWrap > .minImgBox {
  vertical-align: top;
  display: inline-block;
}

.hangupText {
  display: inline-block;
  vertical-align: top;
  width: 500px;
  height: 100px;
  border: 1px solid #ccc;
  overflow-y: scroll;
  word-wrap:break-word;
}
</style>
