<template>
  <div>
    <h1 class="user-title">导入专题素材</h1>
    <div class="user-line"></div>
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
      style="margin-top: 22px;"
    >
      <el-form-item label="素材文件" prop="name">
        <div class="fileName" :title="fileName">{{fileName}}</div>
        <div>
          <input type="file" ref="uploadInput" style="display: none;" @change="changeFile">
          <span class="uploadBtn" @click="uploadBtn">上传excel表</span>
          <span class="downTemplate">下载模版文件</span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: "userPush",
  data() {
    return {
      fileName: "从本地电脑上传excel表",
      ruleForm: {
        name: "",
        desc: ""
      },
      rules: {
        name: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
          { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }
        ],
        desc: [{ required: true, message: "请填写活动形式", trigger: "blur" }]
      }
    };
  },
  methods: {
    uploadBtn() {
      this.$refs.uploadInput.click();
    },
    changeFile() {
      var file = this.$refs.uploadInput.files[0];
      console.log(file.name)
      this.fileName = file.name
      // if (window.FileReader) {
      //   var fr = new FileReader();
      //   fr.onloadend = e => {
      //     console.log(e)
      //   };
      //   fr.readAsDataURL(file);
      // }
    },
    ubmitUpload() {
      this.$refs.upload.submit();
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    }
  }
};
</script>

<style scoped>
.user-title {
  padding: 10px 0px 0px 0px;
}
.user-line {
  width: 100%;
  margin: 0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #dddfe6;
}
.el-form-item {
  margin-bottom: 22px !important;
}
.fileName {
  text-indent: 10px;
  border: 1px dashed #b9b9b9;
  line-height: 30px;
  color: #b9b9b9;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.uploadBtn {
  display: inline-block;
  color: #ffffff;
  background: #409eff;
  line-height: 20px;
  padding: 0 20px;
  border-radius: 4px;
  cursor: pointer;
}
.downTemplate {
  color: #04c;
  text-decoration: underline;
  cursor: pointer;
}
</style>
