<template>
  <div v-loading="loading">
    <h1 class="user-title">新建文本彩印</h1>
    <div class="user-line"></div>
    <div class="user-search2" style="width: 60%">
      <el-form label-position="left" label-width="100px">
        <el-form-item label="业务类型">
          <el-select
            v-model="addReq.serviceType"
            placeholder="请选择"
            size="small"
            @change="changeServiceType"
          >
            <el-option label="个人彩印" :value="1"></el-option>
            <el-option label="名片号" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <!-- 类别 -->
        <el-form-item label="内容分类">
          <el-select
            v-model="addReq.csGroupId"
            placeholder="请选择"
            size="small"
          >
            <el-option
              v-for="item in contentData"
              :key="item.groupId"
              :label="item.groupName"
              :value="item.groupId"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容类型">
          <el-select
            v-model="addReq.csType"
            placeholder="请选择"
            size="small"
            @change="changeCsType"
          >
            <el-option
              v-for="item in contentTypeData"
              :key="item.cyType"
              :label="item.name"
              :value="item.cyType"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容标签">
          <el-button type="info" @click="visible = true" size="small"
            >添加标签</el-button
          >
          &nbsp;{{ checkedLabelNames }}
          <el-dialog
            title="添加标签"
            :visible.sync="visible"
            :close-on-click-modal="false"
          >
            <div style="height: 300px; overflow: auto">
              <el-form
                class="demo-form-inline"
                label-width="160px"
                justify="center"
              >
                <el-form-item >
                  <el-checkbox-group v-model="checkedLabelId">
                    <el-checkbox
                      @change="labelChange(item.liName)"
                      v-for="(item, index) in labelData"
                      :label="item.liId"
                      :key="`${item.liName}_${index}`"
                      style="display: inline-block; margin-left: 30px"
                      >{{ item.liName }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-form>
            </div>
            <div slot="footer" class="dialog-footer" style="text-align: right">
              <span>{{ "已选" + checkedLabelId.length + "个标签" }}</span>
              <el-button @click="visible = false" size="small">取消</el-button>
              <el-button type="primary" @click="checkLabel()" size="small"
                >确认</el-button
              >
            </div>
          </el-dialog>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input
            type="textarea"
            placeholder="文本彩印中如需带变量，请使用{}进行标识，如：“你好，{}给您来电！”"
            style="width: 75%"
            v-model="addReq.csContent"
            :maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="运营商">
          <el-checkbox-group v-model="addReq.operator">
            <el-checkbox
              :label="item.value"
              v-for="item in operatorList"
              :key="`${item.value}_operator`"
              :disabled="item.disabled"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="资质上传">
          <el-upload
            ref="upload"
            action=""
            :auto-upload="false"
            :on-change="handleChange"
            :on-remove="handleRemove"
            accept="image/jpeg,image/jpg,image/png,image/bmp"
            list-type="picture-card"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="tips-wrap">
            <p class="grey">
              最多只能上传6张图片，仅支持jpg、bmp、png、jpeg格式
            </p>
          </div>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" @click="submit" size="small"
          style="margin-left: 100px"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
export default {
  name: "creatMaterial",
  data () {
    return {
      loading: false,
      category: [],
      checkedLabelNames: '',
      checkedLabelId: [],
      checkedLabelName: [],
      contentData: [], //内容分类变量
      contentTypeData: [
        { "name": "文本彩印", "cyType": "2", },
      ], //内容分类变量
      labelData: [], //内容标签变量
      visible: false,
      //添加文本彩印
      addReq: {
        csContent: "",
        csGroupId: "",
        csLabelId: [],
        serviceType: 1, // 1:个人彩印 2:名片号
        operator: [1],// 1:移动 2:联通 3:电信
        qualificationsFiles: "", //资质文件
        csType: "", //内容类型
      },
      imgList: [], //资质文件
      //标签
      request: {
        csGroupName: "",
        csLabelName: "",
        serviceType: 1, // -1:全部 1:个人彩印 2:名片号
      },
      operatorList: [
        {
          label: "移动",
          value: 1,
          disabled: true
        },
        {
          label: "联通",
          value: 2,
          disabled: false
        },
        {
          label: "电信",
          value: 3,
          disabled: false
        }
      ]
    };
  },
  mounted () {
    this.contentDatas();
    this.labelDatas();
  },
  methods: {
    changeCsType (e) {
      this.operatorList[0].disabled = true;
      if (e == 8 || e == 9) { // 主叫/被叫文本彩印
        this.operatorList[1].disabled = false;
        this.operatorList[2].disabled = false;
      } else { // 文本彩印
        this.operatorList[1].disabled = true;
        this.operatorList[2].disabled = true;
        this.addReq.operator = [1]; // 移动
      }
    },
    changeServiceType (e) {
      this.request.serviceType = e;
      // 清空已选标签
      this.checkedLabelId = [];
      this.checkedLabelName = [];
      this.checkedLabelNames = "";
      // 清空内容分类
      this.addReq.csGroupId = "";
      this.contentDatas();
      this.labelDatas();
      this.addReq.csType = "";
      // 修改内容类型选项
      if (e == 1) {  // 个人彩印
        this.contentTypeData = [
          { "name": "文本彩印", "cyType": "2" },
        ];
      } else if (e == 2) {  // 名片号
        this.addReq.csType = "";
        this.contentTypeData = [
          { "name": "主叫文本彩印", "cyType": "8" },
          { "name": "被叫文本彩印", "cyType": "9" }
        ];
        this.changeCsType(8); // 主叫文本彩印
      }
    },
    handleChange (file, fileList) {
      console.log(file, "文件");
      // 检测文件类型
      var typeArr = ["image/jpeg", "image/jpg", "image/png", "image/bmp"];
      if (!typeArr.includes(file.raw.type)) {
        this.$message.error("请上传jpg、png、jpeg、bmp格式图片");
        fileList.pop();
        return;
      }
      if (fileList.length > 6) {
        this.$message.error("最多只能上传6张图片");
        fileList.pop();
        return;
      }
      // 限制所有图片加起来不能超过10M
      var totalSize = 0;
      for (var i = 0; i < fileList.length; i++) {
        totalSize += fileList[i].size;
      }
      if (totalSize > 10 * 1024 * 1024) {
        this.$message.error("图片总大小不能超过10M");
        fileList.pop();
        return;
      }
      // blob转base64
      var reader = new FileReader();
      reader.readAsDataURL(file.raw);
      reader.onload = function (e) {
        var base64 = e.target.result;
        this.imgList.push({
          fileName: file.name,
          fileBase64: base64,
          ...file
        });
        console.log(this.imgList, "图片列表");
      }.bind(this);
    },
    handleRemove (file, fileList) {
      // 删除对应的图片
      var index = this.imgList.findIndex(
        item => item.uid === file.uid
      );
      this.imgList.splice(index, 1);
    },
    checkLabel: function () {
      this.visible = false;
      this.checkedLabelNames = this.checkedLabelName.join(',');
      this.addReq.csLabelId = this.checkedLabelId.join(',');
    },
    //显示选中的标签名
    labelChange: function (labelName) {
      let checkedBoolean = false;
      for (let i = 0; i < this.checkedLabelName.length; i++) {
        if (labelName === this.checkedLabelName[i]) {
          this.checkedLabelName.splice(i, 1);
          checkedBoolean = true;
        }
      }
      if (!checkedBoolean) {
        this.checkedLabelName.push(labelName);
      }
    },
    //内容分类选项请求
    contentDatas: function () {
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
          emulateJSON: true
        })
        .then(function (res) {
          this.contentData = res.data;
        });
    },
    //内容标签选项请求
    labelDatas: function () {
      this.$http
        .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
          emulateJSON: true
        })
        .then(function (res) {
          this.labelData = res.data;
        });
    },
    //添加文本彩印
    submit: function () {
      // if(!this.check(this)){
      //   return false;
      // }
      if (this.addReq.csGroupId == "" || this.addReq.csGroupId == null) {
        this.$message('内容分类不能为空');
        return;
      }
      if (this.addReq.csLabelId == "" || this.addReq.csLabelId == null) {
        this.$message('内容标签不能为空');
        return;
      }
      if (this.addReq.csContent == null || this.addReq.csContent.trim() == "") {
        this.$message('内容不能为空');
        return;
      }
      if (this.addReq.csType == null || this.addReq.csType.trim() == "") {
        this.$message('内容类型不能为空');
        return;
      }

      // 有联通或电信运营商时，资质文件不能为空
      if (
        this.addReq.operator.includes(2) ||
        this.addReq.operator.includes(3)
      ) {
        if (this.imgList.length == 0) {
          this.$message('请上传资质文件');
          return;
        }
      }

      this.addReq.csContent = this.addReq.csContent.trim();
      // 资质文件 "fileNames:fileBase64|fileNames:fileBase64"
      this.addReq.qualificationsFiles = this.imgList.map(item =>
        `${item.fileName}:${item.fileBase64}`
      ).join("|");
      const operator = this.addReq.operator.join(",");
      console.log({ ...this.addReq, operator }, "添加文本彩印请求参数");
      this.loading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csText/addCsText`, {
          ...this.addReq,
          operator
        }, {
          emulateJSON: true
        })
        .then(function (res) {
          if (res.data.resStatus == "0") {
            this.$message.success('添加内容成功');
            this.loading = false;
            this.$router.push({ url: '/textCS', name: 'textCS' });
          } else if (res.data.resStatus == "1") {
            this.$message.error('添加内容失败');
            this.loading = false;
          }
        });
    },
    //路由跳转
    locationHref (href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
  margin-top: 3%;
  background-color: blue;
}
.user-search2 {
  width: 40%;
  margin: 0 auto;
  margin-top: 3%;
}
/* 弹窗checkbox样式 */
.demo-form-inline> .el-form-item >.el-form-item__content {
  margin: 0 !important;
}
</style>
