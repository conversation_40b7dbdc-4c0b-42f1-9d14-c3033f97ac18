
package com.cs.param.common;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ParBlackListCommon {
	private Integer id; //  id
	private String phone; // 用户号码
	private String sysUserName; // 操作人，系统用户id
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private Integer[] ids;// id数组
	private String[] phones;// id数组
	private String newPhone; // 新手机号
}
