<template scope="scope">
  <div class="cardPrint">
    <h1 class="user-title">彩印专题审核</h1>
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="专题标签">
              <el-select
                v-model="searchReq.subjectLabelId"
                clearable
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in labelList"
                  :key="item.id"
                  :label="item.labelName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专题ID">
              <el-input v-model="searchReq.id" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专题内容">
              <el-input v-model="searchReq.subjectContent" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="提交人">
              <el-input v-model="searchReq.commitBy" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-form-item label="提交时间">
            <div class="block">
              <el-date-picker
                v-model="searchReq.timearr"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                size="small"
              ></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
          </el-form-item>
        </el-row>
      </el-form>

      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="passVisible=true;passType=2;"
          >批量通过</el-button>
          <el-button
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="rejectVisible=true;rejectType=2;"
          >批量驳回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="专题ID" width="200"></el-table-column>
        <el-table-column prop="subjectContent" label="专题内容" width="240"></el-table-column>
        <el-table-column prop="labelName" label="专题标签" width="240"></el-table-column>
        <el-table-column label="专题图片" width="100">
          <template slot-scope="scope">
            <el-button
              @click="labelPhotoVisible=true;subjectLabelUrl=scope.row.subjectLabelUrl"
              type="text"
              size="small"
              style="text-align: center"
            >详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="commitBy" label="提交人" width="200"></el-table-column>
        <el-table-column prop="commitNum" label="提交次数" width="200"></el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="200"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="passVisible=true;rowData=scope.row;passType=1;"
            >通过</el-button>
            <el-button
              @click="rejectVisible=true;rowData=scope.row;rejectReq.svCause='';rejectType=1;"
              type="text"
              size="small"
            >驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
      <el-dialog
        width="30%"
        title="标签图片"
        :close-on-click-modal="false"
        :visible.sync="labelPhotoVisible"
        append-to-body
      >
        <img style="display: block;width: 80%; margin: 0 auto;" :src="subjectLabelUrl" alt />
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="labelPhotoVisible=false">确 定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        width="30%"
        title="通过"
        :visible.sync="passVisible"
        :close-on-click-modal="false"
        append-to-body
      >
        <div>是否通过该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        width="30%"
        title="驳回"
        :visible.sync="rejectVisible"
        :close-on-click-modal="false"
        append-to-body
      >
        <el-form label-width="80px" justify="center">
          <el-form-item label="驳回原因">
            <el-radio v-model="radio" label="1">手动输入</el-radio>
            <el-radio v-model="radio" label="2">系统预设</el-radio>
          </el-form-item>
          <el-form-item label v-show="radio==1">
            <el-input
              v-model="rejectReq.auditCause"
              type="textarea"
              :rows="2"
              placeholder
              style="width: 200px;"
            ></el-input>
          </el-form-item>
          <el-form-item label v-show="radio==2">
            <el-select v-model="rejectReq.auditCause" clearable placeholder="请选择" size="small">
              <el-option
                v-for="item in refuse"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false" size="small">取 消</el-button>
          <el-button
            type="primary"
            @click="rejectVisible = false;rejectCheck(rowData)"
            size="small"
          >确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="user-line"></div>
  </div>
</template>
<script>
import moment from "moment";

export default {
  data() {
    return {
      labelPhotoVisible: false,
      subjectLabelUrl: "",
      tableLoading: false,
      searchReq: {
        subjectLabelId: "",
        id: "",
        subjectContent: "",
        commitBy: "",
        timearr: [],
        beginTime: "",
        endTime: "",
        pageSize: 10,
        pageIndex: 1,
        auditStatusArray: [0]
      },
      typeoff: "info",
      clickoff: true,
      //数据表
      tableData: [],
      //操作列表
      rowData: "",
      pageTotal: 0, //总条数
      passVisible: false,
      rejectVisible: false,
      radio: "1",
      //通过请求参数
      passReq: {
        contentIdArray: [],
        auditStatus: 2,
        auditBy:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName
      },
      //驳回请求参数
      rejectReq: {
        contentIdArray: [],
        auditStatus: 1,
        auditCause: "",
        auditBy:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        auditOpinion: ""
      },
      //系统驳回原因
      refuse: [],
      labelList: []
    };
  },
  created() {
    // this.formatData();
    this.search();
    this.initLabelList();
    this.refuse=JSON.parse(sessionStorage.getItem("refuseList"))
  },
  watch: {
    "rejectReq.contentIdArray"() {
      if (this.rejectReq.contentIdArray.length) {
        this.clickoff = false;
        this.typeoff = "primary";
      } else {
        this.clickoff = true;
        this.typeoff = "info";
      }
    },
    radio() {
      if (this.radio == 2 || this.radio == 1) {
        this.rejectReq.auditCause = "";
      }
    },
    rejectVisible() {
      if (!this.rejectVisible) {
        this.rejectReq.auditCause = "";
      }
    }
  },
  methods: {
    initLabelList() {
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectLabelList`,
          JSON.stringify({
          labelName: ""
          }),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(function(res) {
          this.labelList = res.data.data;
        });
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr) {
        this.searchReq.beginTime = this.searchReq.timearr[0]
          ? moment(new Date(this.searchReq.timearr[0])).format("YYYY-MM-DD")
          : "";
        this.searchReq.endTime = this.searchReq.timearr[1]
          ? moment(new Date(this.searchReq.timearr[1])).format("YYYY-MM-DD")
          : "";
      } else {
        this.searchReq.beginTime = "";
        this.searchReq.endTime = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectAuditList`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data || [];
            this.pageTotal = res.totalCount;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    //多选框
    handleSelectionChange(val) {
      this.rejectReq.contentIdArray = [];
      this.passReq.contentIdArray = [];
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.contentIdArray.push(val[i].id);
        this.passReq.contentIdArray.push(val[i].id);
      }
    },
    handleSizeChange(val) {
      this.searchReq.pageIndex = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.pageIndex = val;
      this.search();
    },
    //判断是批量还是单操作，发相应请求
    passCheck(val) {
      //1为单，2为多
      if (this.passType == 1) {
        this.pass(val);
      } else if (this.passType == 2) {
        this.passlist();
      }
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val) {
      //1为单，2为多
      if (this.rejectType == 1) {
        this.reject(val);
      } else if (this.rejectType == 2) {
        this.rejectlist();
      }
    },
    //通过请求---单
    pass: function(val) {
      this.passReq.contentIdArray = [val.id];
      this.$http
        .post(`${this.proxyUrl}/cySubject/doSubjectAudit`, JSON.stringify(this.passReq))
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("通过成功");
            this.search();
            this.passReq.contentIdArray = [];
          } else {
            this.$message("通过失败");
            this.passReq.contentIdArray = [];
          }
        });
    },
    //通过请求---多
    passlist: function() {
      if (!this.passReq.contentIdArray.length > 0) {
        this.$message.error("请选择批量通过的内容");
        return false;
      }
      this.$http
        .post(`${this.proxyUrl}/cySubject/doSubjectAudit`, JSON.stringify(this.passReq))
        .then(function(res) {
          if (res.data.code == "0") {
            // let reslist = res.data.data;
            // let count = 0;
            // reslist.forEach(list => {
            //   if (list.success) {
            //     count++;
            //   }
            // });
            // this.$message.success(
            //   `审核通过成功记录${count}条，失败记录${reslist.length -
            //     count}条，详细情况请查询企业明细`
            // );
            this.$message.success("通过成功");
            this.search();
            this.passReq.contentIdArray = [];
          } else {
            this.$message("通过失败");
            this.passReq.contentIdArray = [];
          }
        });
    },
    //驳回请求---单
    reject: function(val) {
      if (!this.rejectReq.auditCause) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        return false;
      }
      this.rejectReq.contentIdArray = [val.id];
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/doSubjectAudit`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("驳回成功");
            this.rejectReq.contentIdArray = [];
            this.search();
          } else {
            this.$message("驳回失败");
            this.rejectReq.contentIdArray = [];
          }
        });
    },
    //驳回请求---多
    rejectlist: function() {
      if (!this.rejectReq.auditCause) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        return false;
      }
      if (!this.rejectReq.contentIdArray.length > 0) {
        this.$message.error("请选择批量驳回的内容");
        return false;
      }
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/doSubjectAudit`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          if (res.data.code == "0") {
            // let reslist = res.data.data;
            // let count = 0;
            // reslist.forEach(list => {
            //   if (list.success) {
            //     count++;
            //   }
            // });
            // this.$message.success(
            //   `驳回成功记录${count}条，失败记录${reslist.length -
            //     count}条，详细情况请查询企业明细`
            // );
            this.$message.success("驳回成功");
            this.search();
            this.rejectReq.contentIdArray = [];
          } else {
            this.$message("驳回失败");
            this.rejectReq.contentIdArray = [];
          }
        });
    },
  }
};
</script>
<style scoped>
.cardPrint >>> .el-tabs__header {
  margin-left: 24px;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
  margin-top: 3%;
  background-color: blue;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th {
  background-color: #f5f5f5;
}

.zzItemWrap {
  margin: 8px 0;
}

.zzItemLeft {
  float: left;
  width: 70px;
}

.zzItemRight >>> .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.carouselIndex {
  margin-left: 70px;
  text-align: center;
}

.zzWrap >>> .el-dialog__body {
  text-align: center;
}
</style>
