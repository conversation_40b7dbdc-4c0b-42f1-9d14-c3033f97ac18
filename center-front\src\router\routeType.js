/**
 * @param resolve
 * 按需引入组件
 */
  //内容
export const Pos = resolve => require(['@/components/page/Pos'], resolve)

//登录
export const noPermission = resolve => require(['@/components/noPermission/noPermission.vue'], resolve)

//登录
export const login = resolve => require(['@/components/login/login.vue'], resolve)

//登录
export const forgetPwd = resolve => require(['@/components/forgetPwd/forgetPwd.vue'], resolve)
//主页
export const main=resolve=>require(['@/components/main.vue'], resolve);
//首页
export const home=resolve=>require(['@/components/home/<USER>'], resolve);
//用户管理
//user
export const user = resolve => require(['@/components/user/user'], resolve)

//UserList
export const userList = resolve => require(['@/components/user/UserList'], resolve)

//userDetail
export const userDetail = resolve => require(['@/components/user/userDetail'], resolve)

//userPrint
export const userPrint = resolve => require(['@/components/user/userPrint'], resolve)

//newmediaPrint
export const newmediaPrint = resolve => require(['@/components/user/newmediaPrint'], resolve)

//swindleNumber
export const swindleNumber = resolve => require(['@/components/user/swindleNumber'], resolve)

//enterprisePrint
export const enterprisePrint = resolve => require(['@/components/user/enterprisePrint'], resolve)

//userBlackWhite
export const userBlackWhite = resolve => require(['@/components/user/userBlackWhite'], resolve)

//contentHistory
export const contentHistory = resolve => require(['@/components/user/contentHistory'], resolve)

//customerHisPkg
export const customerHisPkg = resolve => require(['@/components/user/customerHisPkg'], resolve)

//userPush
export const userPush = resolve => require(['@/components/user/userPush'], resolve)

//batchMod
export const batchMod = resolve => require(['@/components/user/batchMod'], resolve)

//batchContent
export const batchContent = resolve => require(['@/components/user/batchContent'], resolve)

//默认批量设置
export const batchSetting = resolve => require(['@/components/user/batchSetting'], resolve)

//switchAccount
export const switchAccount = resolve => require(['@/components/user/switchAccount'], resolve)

//delAccount
export const delAccount = resolve => require(['@/components/user/delAccount'], resolve)

// 个人彩印内容
// 专题标签
export const subjectLabel = resolve => require(['@/components/subject/subjectLabel'], resolve)
// 专题彩印
export const subjectPrint = resolve => require(['@/components/subject/subjectPrint'], resolve)
// 新建或编辑专题标签
export const newSubjectLabel = resolve => require(['@/components/subject/newSubjectLabel'], resolve)
// 新建或编辑专题彩印
export const newSubjectPrint = resolve => require(['@/components/subject/newSubjectPrint'], resolve)
// 导入专题彩印
export const uploadSubjectPrint = resolve => require(['@/components/subject/uploadSubjectPrint'], resolve)

//运营
//业务发展监控
export const bm_procMoni = resolve => require(['@/components/busiMonitor/procMoni/procMoni.vue'], resolve)
//业务预警
export const bm_warnMoni = resolve => require(['@/components/busiMonitor/warnMoniMain.vue'], resolve)
//开销户预警
export const warnMoniNewUser = resolve => require(['@/components/busiMonitor/warnMoniNewUser/warnMoniNewUser.vue'], resolve)
//推送量预警
export const warnMoniPush = resolve => require(['@/components/busiMonitor/warnMoniPush/warnMoniPush.vue'], resolve)
//推送量预警
export const warnMoniTest = resolve => require(['@/components/busiMonitor/warnMoniTest/warnMoniTest.vue'], resolve)
//告警人员管理
export const warnMoniWarnUser = resolve => require(['@/components/busiMonitor/warnMoniWarnUser/warnMoniWarnUser.vue'], resolve)
//订购关系
export const orderRelationship = resolve => require(['@/components/busiMonitor/dfferenceAnalysis/orderRelationship.vue'], resolve)
//拒接彩印操作记录
export const rejection_record = resolve => require(['@/components/user/rejectionRecord.vue'], resolve)
//携转号码导入
export const carray_turn = resolve => require(['@/components/user/batchCarrayTurn.vue'], resolve)
//携转号码列表
export const carray_turn_list = resolve => require(['@/components/user/carrayTurnList.vue'], resolve)
//推送规则
export const pushRule = resolve => require(['@/components/busiMonitor/dfferenceAnalysis/pushRule.vue'], resolve)
//用户统计
export const stat_userStat = resolve => require(['@/components/statistic/userStat.vue'], resolve)
//呼叫推送统计
export const stat_pushStat = resolve => require(['@/components/statistic/pushStat.vue'], resolve)
//活跃量统计
export const stat_activityStat = resolve => require(['@/components/statistic/activityStat.vue'], resolve)
//内容设置统计
export const stat_contentStat = resolve => require(['@/components/statistic/contentStat.vue'], resolve)
//数据统计
export const remind_dataStat = resolve => require(['@/components/remind_statistic/dataStat.vue'], resolve)
//热线彩印固定模板统计
export const stat_hotAdFixedTemplateStat = resolve => require(['@/components/statistic/hotAdFixTemplateStat'], resolve)
//号码维度分析
export const remind_numberDimension = resolve => require(['@/components/remind_statistic/numberDimension.vue'], resolve)
//用户维度分析
export const remind_userDimension = resolve => require(['@/components/remind_statistic/userDimension.vue'], resolve)
//提醒号码清单
export const remind_remindList = resolve => require(['@/components/remind_statistic/remindList.vue'], resolve)

//内容管理
//userContentDIY  用户DIY内容
export const userContentDIY = resolve => require(['@/components/contentCtrl/userContentDIY'], resolve)

//contentCategory  内容分类
export const contentCategory = resolve => require(['@/components/contentCtrl/recContents/contentCategory'], resolve)

//contentLabel  内容标签
export const contentLabel = resolve => require(['@/components/contentCtrl/recContents/contentLabel'], resolve)

//textCS  文本彩印
export const textCS = resolve => require(['@/components/contentCtrl/recContents/textCS/textCS'], resolve)

//creatMaterial  文本彩印-新建标签
export const creatMaterial = resolve => require(['@/components/contentCtrl/recContents/textCS/creatMaterial'], resolve)

//importText  文本彩印-导入文本彩印
export const importText = resolve => require(['@/components/contentCtrl/recContents/textCS/importText'], resolve)

//CSbox  彩印盒
export const CSbox = resolve => require(['@/components/contentCtrl/recContents/CSbox/CSbox'], resolve)

//creatBox  彩印盒-新建彩印盒
export const creatBox = resolve => require(['@/components/contentCtrl/recContents/CSbox/creatBox'], resolve)

//importBox  彩印盒-导入彩印盒
export const importBox = resolve => require(['@/components/contentCtrl/recContents/CSbox/importBox'], resolve)

//batchoffSale  批量下架内容
export const batchoffSale = resolve => require(['@/components/contentCtrl/batchoffsale'], resolve)

//batchoffSale  下架内容
export const offSale = resolve => require(['@/components/contentCtrl/offsale'], resolve)
export const hotContent = resolve => require(['@/components/contentCtrl/hotContent'], resolve)
//内部审核
//contentAudit  内容审核-待审核
export const contentAudit = resolve => require(['@/components/contentAudit/toBeAudited/contentAudit'], resolve)

//contentAudit/miguCard 内容审核-待审核-咪咕名片
export const miguCard = resolve => require(['@/components/contentAudit/toBeAudited/miguCard'], resolve)

//contentAudit/miguCardDetails 内容审核-待审核-咪咕名片详情
export const miguCardDetails = resolve => require(['@/components/contentAudit/toBeAudited/miguCardDetails'], resolve)

//contentAudit/personalPrint 内容审核-待审核-文本彩印
export const personalPrint = resolve => require(['@/components/contentAudit/toBeAudited/personalPrint'], resolve)

//contentAudit/printBox 内容审核-待审核-彩印盒
export const printBox = resolve => require(['@/components/contentAudit/toBeAudited/printBox'], resolve)

//contentAudit/companyPrint 内容审核-待审核-企业彩印
export const companyPrint = resolve => require(['@/components/contentAudit/toBeAudited/companyPrint'], resolve)

//contentAudit/mediaPrint 内容审核-待审核-新媒彩印
export const mediaPrint = resolve => require(['@/components/contentAudit/toBeAudited/mediaPrint'], resolve)

//contentAudit/cardPrint 内容审核-待审核-名片彩印
export const cardPrint = resolve => require(['@/components/contentAudit/toBeAudited/cardPrint'], resolve)

//contentAudit/hotlinePrint 内容审核-待审核-热线彩印
export const hotlinePrint = resolve => require(['@/components/contentAudit/toBeAudited/hotlinePrint'], resolve)

//contentAudit/hotlinePrint 内容审核-待审核-热线彩印固定模板
export const hotlineGdTemplatePrint = resolve => require(['@/components/contentAudit/toBeAudited/hotlineGdTemplatePrint'], resolve)

//contentAudit/adPrint 内容审核-待审核-广告彩印
export const adPrint = resolve => require(['@/components/contentAudit/toBeAudited/adPrint'], resolve)

//contentAudit/screenShow 内容审核-待审核-广告彩印-屏显
export const screenShow = resolve => require(['@/components/contentAudit/toBeAudited/screenShow'], resolve)

//contentAudit/hangUp 内容审核-待审核-广告彩印-挂机
export const hangUp = resolve => require(['@/components/contentAudit/toBeAudited/hangUp'], resolve)

//contentAudit/colourPrint 内容审核-待审核-彩印专题审核
export const colourPrint = resolve => require(['@/components/contentAudit/toBeAudited/colourPrint'], resolve)

//contentAudit/colourPrint 内容审核-待审核-彩印专题审核记录
export const colourPrintviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/colourPrintviewDetails'], resolve)

//contentAudit/massDetail 内容审核-待审核-增强群发彩印详情
export const massDetail = resolve => require(['@/components/contentAudit/toBeAudited/massDetail'], resolve)

//contentAudit/provTemplate 内容审核-待审核-省市提醒模板
export const provTemplate = resolve => require(['@/components/contentAudit/toBeAudited/provTemplate'], resolve)

//contentAudit/numTemplate 内容审核-待审核-特定号码提醒模板
export const numTemplate = resolve => require(['@/components/contentAudit/toBeAudited/numTemplate'], resolve)
// 内容审核-待审核-行业名片号-热线彩印
export const professionHotLinePrint = resolve => require(['@/components/contentAudit/toBeAudited/professionHotLinePrint'], resolve)
// 内容审核-待审核-行业名片号-名片号彩印
// export const professionCardPrint = resolve => require(['@/components/contentAudit/toBeAudited/professionCardPrint'], resolve)

//passRecords  内容审核-审核记录-已通过审核记录
export const passRecords = resolve => require(['@/components/contentAudit/reviewRecords/passRecords'], resolve)

// contentAudit/enhanceMassPrint 内容审核-待审核-增强群发彩印
export const enhanceMassPrint = resolve => require(['@/components/contentAudit/toBeAudited/enhanceMassPrint'], resolve)

// contentAudit/miguCardviewDetails 内容审核-审核记录-咪咕名片
export const miguCardviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/miguCardviewDetails'], resolve)

// contentAudit/miguCardviewVerifyDetails 内容审核-审核记录-咪咕名片详情
export const miguCardviewVerifyDetails = resolve => require(['@/components/contentAudit/reviewRecords/miguCardviewVerifyDetails'], resolve)

// contentAudit/enhanceMassReviewList 内容审核-审核记录-增彩群发审核明细列表
export const enhanceMassReviewList = resolve => require(['@/components/contentAudit/reviewRecords/enhanceMassReviewList'], resolve)

//passRecords  内容审核-审核记录-企业已通过审核记录
export const enterprisepassRecords = resolve => require(['@/components/contentAudit/reviewRecords/enterprisepassRecords'], resolve)

//passRecords  内容审核-审核记录-省市已通过审核记录
export const provTemplatepassRecords = resolve => require(['@/components/contentAudit/reviewRecords/provTemplatepassRecords'], resolve)

//reviewDetails  内容审核-审核记录-审核明细
export const reviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/reviewDetails'], resolve)

//reviewDetails  内容审核-审核记录-企业审核明细
export const enterprisereviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/enterprisereviewDetails'], resolve)

//reviewDetails  内容审核-审核记录-省市审核明细
export const provTemplatereviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/provTemplatereviewDetails'], resolve)

//cardprintviewDetails  内容审核-复审记录-名片挂彩审核记录
export const cardprintviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/cardprintviewDetails'], resolve)

//hotlineprintviewDetails  内容审核-审核记录-企业彩印-热线彩印已审核明细
export const hotlineprintviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/hotlineprintviewDetails'], resolve)

//hotlineGdTemplatePrintViewDetails  内容审核-审核记录-热线彩印固定模板已审核明细
export const hotlineGdTemplatePrintViewDetails = resolve => require(['@/components/contentAudit/reviewRecords/hotlineGdTemplatePrintViewDetails'], resolve)

//adprintviewDetails  内容审核-审核记录-广告彩印已审核明细
export const adprintviewDetails = resolve => require(['@/components/contentAudit/reviewRecords/adprintviewDetails'], resolve)

//  内容审核-初审审核记录-行业名片号-热线彩印审核明细
export const professionHotLinePrintViewDetails = resolve => require(['@/components/contentAudit/reviewRecords/professionHotLinePrintViewDetails'], resolve)

//审核箱
//textPrinting //审核箱-个人文本彩印
export const textPrinting = resolve => require(['@/components/examine/printing/textPrinting'], resolve)

//textPrinting //审核箱-修改个人文本彩印
export const upPrinting = resolve => require(['@/components/examine/printing/upPrinting'], resolve)

//textPrinting //审核箱-修改个人彩印盒
export const upPrintingH = resolve => require(['@/components/examine/printing/upPrintingH'], resolve)

//textPrinting //审核箱-企业彩印
export const companyPrinting = resolve => require(['@/components/examine/companyPrinting/companyPrinting'], resolve)

//textPrinting //审核箱-修改企业彩印
export const upcompanyPrinting = resolve => require(['@/components/examine/companyPrinting/upcompanyPrinting'], resolve)

//textPrinting //审核箱-修改企业彩印盒
export const upcompanyPrintingH = resolve => require(['@/components/examine/companyPrinting/upcompanyPrintingH'], resolve)

//textPrinting //审核箱-新媒彩印
export const mediaPrinting = resolve => require(['@/components/examine/mediaprinting/mediaPrinting'], resolve)

//textPrinting //审核箱-修改新媒彩印
export const upmediaPrinting = resolve => require(['@/components/examine/mediaprinting/upmediaPrinting'], resolve)

//textPrinting //审核箱-省市地区提醒模版
export const remindPrinting = resolve => require(['@/components/examine/remindprinting/remindPrinting'], resolve)

//textPrinting //审核箱-修改省市地区提醒模版
export const upremindPrinting = resolve => require(['@/components/examine/remindprinting/upremindPrinting'], resolve)

//textPrinting //审核箱-特定号码提醒模版
export const givenremindPrinting = resolve => require(['@/components/examine/givenremindprinting/givenremindPrinting'], resolve)

//textPrinting //审核箱-修改省市地区提醒模版
export const upgivenremindPrinting = resolve => require(['@/components/examine/givenremindprinting/upgivenremindPrinting'], resolve)



//textPrinting //审核箱-我的文本彩印
export const myPrinting = resolve => require(['@/components/examine/myprinting/myPrinting'], resolve)

//textPrinting //审核箱-我的企业彩印
export const mycompanyPrinting = resolve => require(['@/components/examine/mycompanyPrinting/mycompanyPrinting'], resolve)

//textPrinting //审核箱-我的新媒彩印
export const mymediaPrinting = resolve => require(['@/components/examine/mymediaprinting/mymediaPrinting'], resolve)

//textPrinting //审核箱-我的省市地区提醒模版
export const myremindPrinting = resolve => require(['@/components/examine/myremindprinting/myremindPrinting'], resolve)

//textPrinting //审核箱-我的特定号码提醒模版
export const mygivenremindPrinting = resolve => require(['@/components/examine/mygivenremindprinting/mygivenremindPrinting'], resolve)

//推送规则同步
export const cdpRuleList = resolve => require(['@/components/publicParam/cdpRuleList'], resolve)

//系统红名单
export const sysRedList = resolve => require(['@/components/publicParam/sysRedList'], resolve)
//系统黑名单
export const sysBlackList = resolve => require(['@/components/publicParam/sysBlackList'], resolve)

//短信指令模板
export const smsCommand  = resolve => require(['@/components/publicParam/smsCommand'], resolve)
export const smsForwardCommand = resolve => require(['@/components/publicParam/smsForwardCommand'], resolve)

//省号和区号
export const proAndRegionList = resolve => require(['@/components/publicParam/proAndRegionList'], resolve)

// 号段管理
export const sectionManagerList = resolve => require(['@/components/publicParam/sectionManagerList'], resolve)

// 彩印套餐功能配置
export const setMealList = resolve => require(['@/components/publicParam/setMealList'], resolve)
// 白名单有效期
export const setWhiteEffDate = resolve => require(['@/components/publicParam/setWhiteEffDate'], resolve)

// 系统运营短信
export const smsSysDef = resolve => require(['@/components/publicParam/smsSysDef'], resolve)
// 告警短信
export const smsAlarm = resolve => require(['@/components/publicParam/smsAlarm'], resolve)

// 阈值配置
export const thresholdConf = resolve => require(['@/components/publicParam/thresholdConf'], resolve)
// 其他配置
export const otherConf = resolve => require(['@/components/publicParam/otherConf'], resolve)

// 角色管理
export const roleList = resolve => require(['@/components/sysManager/roleList'], resolve)

// 系统用户管理
export const sysUserList = resolve => require(['@/components/sysManager/sysUserList'], resolve)

// 日志管理
export const logList = resolve => require(['@/components/sysManager/logList'], resolve)

// 异步任务管理
export const asynTaskList = resolve => require(['@/components/sysManager/asynTaskList'], resolve)

// 导出文件下载
export const fileDownLoad = resolve => require(['@/components/sysManager/fileDownLoad'], resolve)


/*
* 提醒彩印
* */
export const remind = resolve => require(['@/components/remind/remind'], resolve);
//分类设置
export const classifyset = resolve => require(['@/components/remind/classifyset'], resolve);
//分类
export const classify = resolve => require(['@/components/remind/classifyset/classify'], resolve);
//标准类型
export const standard = resolve => require(['@/components/remind/classifyset/standard'], resolve);
//提醒模版
export const masterplate = resolve => require(['@/components/remind/masterplate'], resolve);
//省市提醒模版
export const provinces = resolve => require(['@/components/remind/masterplate/provinces'],resolve);
//特定号码模版
export const specific = resolve => require(['@/components/remind/masterplate/specific'], resolve);
//号码库管理
export const effect = resolve => require(['@/components/remind/numberstore/effect'], resolve);
//API能力开放
export const apimanage = resolve => require(['@/components/remind/apimanage'], resolve);
//API管理列表
export const apilist = resolve => require(['@/components/remind/apimanage/apilist'], resolve);
//API企业接入管理
export const enterprise = resolve => require(['@/components/remind/apimanage/enterprise'], resolve);
//企业查询记录
export const inquiry = resolve => require(['@/components/remind/apimanage/inquiry'], resolve);
//离线包能力开放
export const offline = resolve => require(['@/components/remind/offline'], resolve);
//离线包管理
export const manage = resolve => require(['@/components/remind/offline/manage'], resolve);
//离线包规则管理
export const rule = resolve => require(['@/components/remind/offline/rule'], resolve);
//企业下载记录
export const download = resolve => require(['@/components/remind/offline/download'], resolve);
//分省规则管理
export const provincial = resolve => require(['@/components/remind/provincial'], resolve);
//活动管理
export const activityManager = resolve => require(['@/components/activityManager/activityAll'], resolve);
//监控指标
export const monitorList = resolve => require(['@/components/businessMonitor/monitorList'], resolve)
//告警配置
export const setAlarm = resolve => require(['@/components/businessMonitor/setAlarm'], resolve)

//404
export const errorFour = resolve => require(['@/components/errorPage/404.vue'], resolve);
//500
export const errorFive = resolve => require(['@/components/errorPage/500.vue'], resolve);

export const reAuditCompanyPrint = resolve =>require(['@/components/contentAudit/reAudit/companyPrint'], resolve);
export const reAuditCardPrint = resolve => require(['@/components/contentAudit/reAudit/cardPrint'], resolve);
export const reAuditHotlinePrint = resolve => require(['@/components/contentAudit/reAudit/hotlinePrint'], resolve);
export const reAuditEnhanceMassPrint = resolve => require(['@/components/contentAudit/reAudit/enhanceMassPrint'], resolve);
export const reAuditAdPrint = resolve => require(['@/components/contentAudit/reAudit/adPrint'], resolve);
export const reAuditHotlineGdTemplatePrint = resolve => require(['@/components/contentAudit/reAudit/hotlineGdTemplatePrint'], resolve);
export const reAuditEnterprisereviewDetails = resolve => require(['@/components/contentAudit/reviewReRecords/enterprisereviewDetails'], resolve);
export const reAuditCardprintviewDetails = resolve => require(['@/components/contentAudit/reviewReRecords/cardprintviewDetails'], resolve);
export const reAuditHotlineprintviewDetails = resolve =>  require(['@/components/contentAudit/reviewReRecords/hotlineprintviewDetails'], resolve);
export const reAuditEnhanceMassReviewList = resolve => require(['@/components/contentAudit/reviewReRecords/enhanceMassReviewList'], resolve);
export const reAuditAdprintviewDetails = resolve => require(['@/components/contentAudit/reviewReRecords/adprintviewDetails'], resolve);
export const reAuditHotlineGdTemplatePrintViewDetails = resolve => require(['@/components/contentAudit/reviewReRecords/hotlineGdTemplatePrintViewDetails'], resolve)

export const deliveryWay = resolve => require(['@/components/publicParam/deliveryWay'], resolve)


