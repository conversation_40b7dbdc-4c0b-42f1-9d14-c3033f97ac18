<template>
    <div class="inquiry">
        <div class="user-titler">{{$route.name}}</div>
        <div class="contentbox" style="margin-top: 15px;">
            <!--查询条件-->
            <div>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small">
                    <el-form-item label="企业名称">
                        <el-input  v-model="searchForm.companyName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-date-picker
                                v-model="searchForm.startTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search">查询</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                <el-table-column prop="companyName" label="企业名称" />
                <el-table-column prop="companyNo" label="企业编号" />
                <el-table-column prop="apiName" label="API接口名称" />
                <el-table-column prop="lastSearchTime" label="时间" />
                <el-table-column prop="apiQueryCount" label="查询次数" />
            </el-table>
            <!--分页-->
            <div class="block app-pageganit" v-show="total">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'inquiry',
        data(){
            return{
                //查询form对象定义
                searchForm: {
                    companyName:'', //名称
                    startDate: '', //开始时间
                    endDate:'', //结束时间
                    startTime:[],//时间
                    pageSize:10,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },
        components: {

        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            //查询请求
            search() {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                postHeader('queryCompanyHistory', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.companyHistoryList;
                        vm.total = data.data.total;
                    }
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .contentbox{
        margin:0 15px;
    }
    .el-table{
        margin-left: 0;
        margin-top: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
