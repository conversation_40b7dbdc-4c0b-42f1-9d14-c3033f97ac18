<template>
	 <div>
	 	<el-dialog
            width="40%"
            title="金库认证"
            :visible.sync="authenVisible"
            :close-on-click-modal="false"
            append-to-body>
             <el-form :model="cbReq" class="demo-form-inline" label-width="35%" >

                <el-form-item label="审批人">
                    <el-select v-model="cbReq.approverUser" value-key="userId" size="small" placeholder="请选择审批人">
                            <el-option
                                    v-for="item in approverUserList"
                                    :label="item.userName"
                                    :value="item">
                                    <span style="float: left">{{ item.userId }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 10px">{{ item.userName }}</span>
                            </el-option>
                        </el-select>
                </el-form-item>
                 <el-form-item label="申请理由">
                     <el-input v-model="cbReq.applyReason" type="textarea" :rows="2" :maxlength="100" style="width:215px;"></el-input>
                 </el-form-item>
                 <el-form-item label="口令验证码">
                     <el-input v-model="cbReq.passCode" :maxlength="50" placeholder="口令验证码" style="width:215px;" size="small" ></el-input>
                     <el-button type="primary"  size="small" @click="doCreateAppRequestAuth()" v-if="cbReq.requestId == ''">申请口令</el-button>
                     <el-button type="primary"  size="small" @click="doReSendJKPassAuth()" v-else>口令重发</el-button>
                 </el-form-item>

                <el-form-item style="margin-top:10px;">
                    <el-button type="primary" size="small" @click="doRemoteAuthAuth()" v-if="cbReq.requestId !== ''">认证</el-button>
                    <el-button type="primary" size="small" @click="doQueryJKStatusByIDAuth()" v-if="cbReq.requestId !== ''">查看审批状态</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog
            width="40%"
            title="金库认证"
            :visible.sync="authenAginVisible"
            :close-on-click-modal="false"
            append-to-body>
             <el-form :model="cbReq" class="demo-form-inline" label-width="35%" >
                <el-form-item label="审批人">
                    {{approverUserList.length > 0 ? approverUserList[0].userName : ''}}
                </el-form-item>
                <el-form-item label="审批人手机号码">
                    {{approverUserList.length > 0 ? approverUserList[0].mobile : ''}}
                </el-form-item>
                <el-form-item label="口令验证码" >
                    <el-input v-model="cbReq.passCode" :maxlength="50" placeholder="口令验证码" style="width:215px;" size="small"></el-input>
                    <el-button type="primary"  size="small" @click="doCreateAppRequestAuth()" v-if="cbReq.requestId == ''">申请口令</el-button>
                    <el-button type="primary"  size="small" @click="doReSendJKPassAuth()" v-else>口令重发</el-button>
                </el-form-item>
                 <el-form-item style="color:#ff1109">
                     *输入3次错误，则验证码失效
                 </el-form-item>
                <el-form-item style="margin-top:10px;">
                    <el-button type="primary" size="small" @click="doRemoteAuthAuth()">认证</el-button>
                    <el-button type="primary" size="small" @click="doQueryJKStatusByIDAuth()">查看审批状态</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
	 </div>
</template>
<script>
export default {
	name: 'jkAuth',
	props:['menuCode'],
	data() {
        return {
            authenVisible:false,
            authenAginVisible:false,
            cbReq:{
                applyReason:'',
                approverUser:{userId:'',userName:'',mobile:''},
                passCode:'',
                keyOpCode:this.menuCode,
                requestId:'',
                functionCode: this.$parent.functionCode,
                operationCode: this.$parent.operationCode,
                selectedApproverList:[]
            },
            approverUserList:[]
        }
    },
    methods: {
        openAuthenDailog:function(){
            this.$http
                .post(`${this.proxyUrl}/sys/4aAuth/JKService/queryAppOperJKStatus`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.result == '-1'){
                        this.authenVisible = false;
                    }else if (res.data.result == '-2'){
                        this.authenVisible = false;
                        this.$message.error(res.data.message);
                    }else if(res.data.result == '0'){
                        this.cbReq.requestId = res.data.data.requestId;
                        this.approverUserList = res.data.data.approverUserList;
                        if(this.cbReq.requestId !== ''){
                            this.authenVisible = false;
                            this.authenAginVisible = true;
                        }else{
                            this.authenVisible = true;
                        }
                    }else if(res.data.result == '00000'){
                        this.authenVisible = false;
                        this.authenAginVisible = false;
                        this.$emit('buttonDisable', false);
                    }else if(res.data.result == '-120'){
                        this.authenVisible = false;
                        this.authenAginVisible = false;
                        this.$message.warning(res.data.message);
                    }else{
                        this.$message.error(res.data.message);
                    }
             })
        },
        doCreateAppRequestAuth:function(){
            if(!this.cbReq.approverUser || !this.cbReq.approverUser.userId.trim()){
                this.$message.warning("审批人不能为空");
                return;
            }
            if(!this.cbReq.applyReason || !this.cbReq.applyReason.trim()){
                this.$message.warning("申请理由不能为空");
                return;
            }
            this.cbReq.selectedApproverList.push(this.cbReq.approverUser.userId);
            this.$http
                .post(`${this.proxyUrl}/sys/4aAuth/JKService/createAppRequest`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.result == '-1'){
                        this.authenVisible = false;
                    }else if(res.data.result == '0'){
                        this.cbReq.requestId = res.data.data.requestId;
                        this.approverUserList = res.data.data.approverUserList;
                        this.authenVisible = false;
                        this.authenAginVisible = true;
                    }else{
                        this.$message.error(res.data.message);
                    }
                })
        },
        doReSendJKPassAuth:function(){
            this.$http
                .post(`${this.proxyUrl}/sys/4aAuth/JKService/reSendJKPass`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.result == '-1'){
                        this.authenVisible = false;
                    }else if(res.data.result == '0'){
                        this.cbReq.requestId = res.data.data.requestId;
                        this.authenVisible = false;
                        this.authenAginVisible = true;
                        this.$message.info(res.data.message);
                    }else{
                        this.$message.error(res.data.message);
                    }
                })
        },
        doQueryJKStatusByIDAuth:function(){
            this.$http
                .post(`${this.proxyUrl}/sys/4aAuth/JKService/queryJKStatusByID`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.result == '-1'){
                        this.authenVisible = false;
                    }else if(res.data.result == '0'){
                        this.cbReq.requestId = res.data.data.requestId;
                        this.authenVisible = false;
                        this.authenAginVisible = true;
                        this.$message.info(res.data.message);
                    }else{
                        this.$message.error(res.data.message);
                    }
                })
        },
        doRemoteAuthAuth:function(){
            if(!this.cbReq.passCode || !this.cbReq.passCode.trim()){
                this.$message.warning("口令验证码不能为空");
                return;
            }
            this.$http
                .post(`${this.proxyUrl}/sys/4aAuth/JKService/remoteAuth`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.result == '-1'){
                        this.authenVisible = false;
                    }else if(res.data.result == '0'){
                        this.authenVisible = false;
                        this.authenAginVisible = false;
                        this.$message.info(res.data.message);
                        this.$emit('buttonDisable', false);
                    }else if(res.data.result == '-2'){
                        this.authenVisible = false;
                        this.authenAginVisible = true;
                        this.$message.error(res.data.message);
                    }else{
                        this.$message.error(res.data.message);
                    }
                })
        }

    },
    mounted() {
        this.openAuthenDailog();
    }
}
</script>