
package com.cs.param.common;

public class ParOtherConfCommon {
	private Integer id; // 阈值配置id
	private String provinceCode;// 省份code
	private String regionCode;// 地区code
	private String createTime;// 创建时间
	private String effectTime;// 生效时间
	private String sendType;// 彩印发送方式，0：USSD、1：闪信、2：短信、3：彩漫、默认的发送方式；业务地区配置可以为空，为空表示取上级配置
	private String domainSelection;// 是否开启域选开关，0关闭，1开启
	private String isCronSend;// 是否正式向用户发送定时短信通知，0否，1是
	private String isFirst;// 首次接收彩印是否短信通知，0否，1是
	private String sendOptionCode;// 彩印发送选项code
	private String sendOptionName;// 彩印发送选项名
	private String defPerContantId;// 默认个人彩印内容id
	private String defPerContant;// 默认个人彩印内容id
	private String defPerContantSt;// 默认个人彩印内容生效时间
	private String defPerContantEt;// 默认个人彩印内容失效时间
	private String defPerBoxId;// 默认个人彩印盒
	private String defPerBoxName;// 默认个人彩印盒
	private String defPerBoxSt;// 默认个人彩印盒生效时间
	private String defPerBoxEt;// 默认个人彩印盒失效时间
	private String suffixVariable;// 短信模板后缀变量内容
	private String isDelete;// 标识是否为删除数据：0否，1是 默认为0
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public String getDomainSelection() {
		return domainSelection;
	}

	public void setDomainSelection(String domainSelection) {
		this.domainSelection = domainSelection;
	}

	public String getIsCronSend() {
		return isCronSend;
	}

	public void setIsCronSend(String isCronSend) {
		this.isCronSend = isCronSend;
	}

	public String getIsFirst() {
		return isFirst;
	}

	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}

	public String getSendOptionCode() {
		return sendOptionCode;
	}

	public void setSendOptionCode(String sendOptionCode) {
		this.sendOptionCode = sendOptionCode;
	}

	public String getSendOptionName() {
		return sendOptionName;
	}

	public void setSendOptionName(String sendOptionName) {
		this.sendOptionName = sendOptionName;
	}

	public String getDefPerContantSt() {
		return defPerContantSt;
	}

	public void setDefPerContantSt(String defPerContantSt) {
		this.defPerContantSt = defPerContantSt;
	}

	public String getDefPerContantEt() {
		return defPerContantEt;
	}

	public void setDefPerContantEt(String defPerContantEt) {
		this.defPerContantEt = defPerContantEt;
	}

	public String getDefPerContantId() {
		return defPerContantId;
	}

	public void setDefPerContantId(String defPerContantId) {
		this.defPerContantId = defPerContantId;
	}

	public String getDefPerBoxId() {
		return defPerBoxId;
	}

	public void setDefPerBoxId(String defPerBoxId) {
		this.defPerBoxId = defPerBoxId;
	}

	public String getDefPerBoxSt() {
		return defPerBoxSt;
	}

	public void setDefPerBoxSt(String defPerBoxSt) {
		this.defPerBoxSt = defPerBoxSt;
	}

	public String getDefPerBoxEt() {
		return defPerBoxEt;
	}

	public void setDefPerBoxEt(String defPerBoxEt) {
		this.defPerBoxEt = defPerBoxEt;
	}

	public String getSuffixVariable() {
		return suffixVariable;
	}

	public void setSuffixVariable(String suffixVariable) {
		this.suffixVariable = suffixVariable;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getEffectTime() {
		return effectTime;
	}

	public void setEffectTime(String effectTime) {
		this.effectTime = effectTime;
	}

	public String getDefPerContant() {
		return defPerContant;
	}

	public void setDefPerContant(String defPerContant) {
		this.defPerContant = defPerContant;
	}

	public String getDefPerBoxName() {
		return defPerBoxName;
	}

	public void setDefPerBoxName(String defPerBoxName) {
		this.defPerBoxName = defPerBoxName;
	}

}
