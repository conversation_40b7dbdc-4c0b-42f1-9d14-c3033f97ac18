<template>
  <div>
    <h1 class="user-title">行业名片号-热线彩印</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="彩印ID">
          <el-input
            v-model="searchReq.hcNumber"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input
            v-model="searchReq.content"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印类型">
          <el-select v-model="searchReq.deliveryType" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="热线彩印闪信" value="1"></el-option>
            <el-option label="热线彩印挂机短信" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model="dateTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 355px"
            size="small"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="small" @click="handleCurrentChange(1)"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="user-table">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
      >
        <el-table-column
          prop="hcNumber"
          label="彩印ID"
          width="240"
        ></el-table-column>
        <el-table-column
          prop="content"
          label="彩印内容"
          width="240"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div v-html="highLight(scope.row)"></div>
          </template>
        </el-table-column>
        <el-table-column label="热线号码">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showPhoneDetail(scope.row.hotLineNoList)"
              >详情</el-button
            >
          </template>
        </el-table-column>
        <el-table-column label="彩印类型" width="200">
          <template slot-scope="scope">
            {{
              scope.row.deliveryType == 1 ? "热线彩印闪信" : "热线彩印挂机短信"
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="企业ID"
          prop="enterpriseId"
          width="200"
        ></el-table-column>
        <el-table-column
          label="企业名称"
          prop="enterpriseName"
          width="200"
        ></el-table-column>
        <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showCorpImage(scope.row.certificateUrl)"
              :style="
                !scope.row.certificateUrl ? 'color:#808080' : 'color: #409EFF'
              "
              >详情</el-button
            >
          </template>
        </el-table-column>
        <el-table-column label="提交时间" prop="createTime" width="200">
          <template slot-scope="scope">
            {{
              scope.row.createTime
                ? moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                : ""
            }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right"
        ></el-pagination>
      </div>
    </div>
    <!-- 弹窗-热线号码详情 -->
    <el-dialog title="热线号码" width="30%" :visible.sync="phonesVisible">
      <el-table :data="phonesData" :max-height="450">
        <el-table-column
          type="index"
          label="序号"
          width="120"
        ></el-table-column>
        <el-table-column prop="phone" label="热线号码"></el-table-column>
      </el-table>
    </el-dialog>
    <!-- 弹窗-企业资质 -->
    <el-dialog
      title="企业资质"
      class="zzWrap"
      width="30%"
      :visible.sync="corpImageVisible"
    >
      <ul
        class="contentlist"
        v-for="(corpImage, index) in corpImageList"
        :key="`${index}_${{ corpImage }}`"
      >
        <li>
          <a :href="corpImage" target="_blank">资质{{ index + 1 }}</a>
        </li>
      </ul>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
export default {
  data () {
    return {
      tableLoading: false,
      tableData: [], // 表格数据
      // 搜索条件
      searchReq: {
        hcNumber: '',
        content: '',
        approveStatus: "2",
        deliveryType: '',
        createTimeStart: '',
        createTimeEnd: '',
        pageNum: 1,
        pageSize: 10
      },
      dateTime: [], // 时间范围
      pageTotal: 0, // 总页数
      phonesData: [], // 热线号码详情数据
      phonesVisible: false, // 热线号码详情弹窗
      corpImageVisible: false, // 企业资质弹窗
      corpImageList: [], // 企业资质图片
    }
  },
  created () {
    this.search();
    this.refuse = JSON.parse(sessionStorage.getItem("refuseList"))
  },
  methods: {
    moment,
    highLight (row) {
      return row.sign ? `【${row.sign}】${row.content}` : row.content;
    },
    handleSizeChange (val) {
      this.searchReq.pageNum = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange (val) {
      //当前页
      this.searchReq.pageNum = val;
      this.search();
    },
    search () {
      this.tableLoading = true;
      if (this.dateTime) {
        this.searchReq.createTimeStart = this.dateTime[0]
          ? moment(new Date(this.dateTime[0])).format("YYYYMMDD")
          : "";
        this.searchReq.createTimeEnd = this.dateTime[1]
          ? moment(new Date(this.dateTime[1])).format("YYYYMMDD")
          : "";
      } else {
        this.searchReq.createTimeStart = "";
        this.searchReq.createTimeEnd = "";
      }
      console.log(this.searchReq);
      this.$http
        .post(
          `${this.proxyUrl}/content/hotSend/content/queryHotContent`,
          JSON.stringify(this.searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              "Content-Type": "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.resStatus == 0) {
            this.tableData = res.datas || [];
            this.pageTotal = res.pageTotal;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    // 表头样式
    tableheaderClassName ({ row, rowIndex }) {
      return "table-head-th";
    },
    // 热线号码-详情
    showPhoneDetail (phones) {
      this.phonesData = (phones || []).map(item => ({ phone: item }));
      this.phonesVisible = true;
    },
    // 企业资质-详情
    showCorpImage (corpImage) {
      this.corpImageList = corpImage ? corpImage.split(";") : [];
      this.corpImageVisible = true;
    },
  }
}
</script>
<style scoped>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
