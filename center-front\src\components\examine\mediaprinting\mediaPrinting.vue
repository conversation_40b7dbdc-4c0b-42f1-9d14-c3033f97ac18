<template>
  <div>
    <h1 class="user-title">我的代办-新媒彩印</h1>
    <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="企业编号">
        <el-input  v-model="print.companyId" placeholder="企业编号"></el-input>
      </el-form-item>
      <el-form-item label="企业名称">
        <el-input v-model="print.companyName"  placeholder="企业名称"></el-input>
      </el-form-item>
      <el-form-item label="提交时间">
        <el-date-picker
      type="daterange"
      v-model="print.subTime"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期">
    </el-date-picker>
      </el-form-item>
      <el-form-item>
    <el-button type="primary" @click="onSubmit(print)">查询</el-button>
  </el-form-item>
    </el-form>
  </div>
  <div id="printingtable">
    <el-table
    :data="tableData"
    border
    style="width: 100%">
    <el-table-column
      fixed
      prop="date"
      label="彩印ID"
      width="150">
    </el-table-column>
    <el-table-column
      prop="companyId"
      label="企业编号"
      width="120">
    </el-table-column>
    <el-table-column
      prop="companyName"
      label="企业名称"
      width="120">
    </el-table-column>
    <el-table-column
      prop="mediaShow"
      label="新媒屏显"
      width="120">
    </el-table-column>
    <el-table-column
      prop="mediaHang"
      label="新媒挂机"
      width="300">
    </el-table-column>
    <el-table-column
      prop="subtime"
      label="提交时间"
      width="120">
    </el-table-column>
    <el-table-column
      prop="reason"
      label="驳回原因"
      width="120">
    </el-table-column>
    <el-table-column
      prop="auditor"
      label="审核人"
      width="120">
    </el-table-column>
    <el-table-column
      prop="auditorTime"
      label="审核时间"
      width="120">
    </el-table-column>
    <el-table-column
      fixed="right"
      label="操作"
      width="100">
      <template slot-scope="scope">
        <el-button type="text" size="small" @click="locationHref(scope.row)">编辑</el-button>
        <el-button @click="handleClick(scope.row)" type="text" size="small">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
  </div>
  <div class="block app-pageganit">
  <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="print.currentPage"
      :page-sizes="[100, 200, 300, 400]"
      :page-size="print.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="print.total">
    </el-pagination>  
  </div>
  </div>
</template>
<script>
  export default {
    name: 'UserList',
    data() {
      return {
      print:{
            printId:"",
            printContent:"",
            subTime:"",
            currentPage:4,
            province:"",
            region:"",
            companyName:"",
            companyId:"",
            total:400,
            pageSize:100
        },
        tableData: [{
          date: '文本彩印',
          name: '王小虎',
          province: '上海',
          city: '普陀区',
          address: '上海市普陀区金沙江路 1518 弄',
          zip: 200333
        }, {
          date: '彩印盒',
          name: '王小虎',
          province: '上海',
          city: '普陀区',
          address: '上海市普陀区金沙江路 1518 弄',
          zip: 200333
        }, {
          date: '文本彩印',
          name: '王小虎',
          province: '上海',
          city: '普陀区',
          address: '上海市普陀区金沙江路 1518 弄',
          zip: 200333
        }, {
          date: '彩印盒',
          name: '王小虎',
          province: '上海',
          city: '普陀区',
          address: '上海市普陀区金沙江路 1518 弄',
          zip: 200333
        }],
        value6: ''

      }
    },
    methods: {
      locationHref(href){
          let vm=this;
          if(href.date=="文本彩印"){
              vm.$router.push({name:"upmediaPrinting",path:"/upmediaPrinting",params:href})
          }
      },
      handleSizeChange(val) {
        this.print.pageSize=val;
        this.onSubmit(this.print);
        console.log(`每页 ${val} 条`);
      },
      handleCurrentChange(val) {
        this.print.currentPage=val;
        this.onSubmit(this.print);
        console.log(`当前页: ${val}`);
      },
      onSubmit(val) {
        console.log(val.companyName);
        this.$http.post('http://localhost:8088/post', val).then((response) => {
              console.log(response.data);
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '查询异常'
              });
          });
      }
    },
    created() {
    },
    components: {}
  }


</script>
<style>


  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }
.el-pagination{
    margin-left:270px;
  }
</style>
