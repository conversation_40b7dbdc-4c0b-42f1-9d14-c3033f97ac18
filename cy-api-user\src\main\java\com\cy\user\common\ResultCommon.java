package com.cy.user.common;

import java.util.List;

public class ResultCommon {
	// 0成功，1失败
	private int status;

	private String resText;
	// 数据集
	private List<?> datas;
	// 数据集2
	private List<?> datas2;
	// 总条数
	private int pageTotal;
	// 查询页码
	private int pageNum;
	// //2：黑名单分组;3：白名单分组 ;
	private String  userBwType;

	private String personStatus;

	private String mediaStatus;

	private String remindStatus;

	private String cdpStatus;

	private static ResultCommon res = new ResultCommon();

	public static ResultCommon getInstance(int resStatus, String resText) {
		res.setStatus(resStatus);
		res.setResText(resText);
		return res;
	}

	public static ResultCommon getInstance(List<?> datas, int resStatus, String resText) {
		res.setDatas(datas);
		res.setStatus(resStatus);
		res.setResText(resText);
		return res;
	}

	public static ResultCommon getInstance(List<?> datas, int pageTotal, int pageNum) {
		res.setDatas(datas);
		res.setPageTotal(pageTotal);
		res.setPageNum(pageNum);
		return res;
	}

	public static ResultCommon getInstance(List<?> datas, List<?> datas2, int pageTotal, int pageNum) {
		res.setDatas(datas);
		res.setDatas2(datas2);
		res.setPageTotal(pageTotal);
		res.setPageNum(pageNum);
		return res;
	}

	public String getUserBwType() {
		return userBwType;
	}

	public void setUserBwType(String userBwType) {
		this.userBwType = userBwType;
	}

	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	public String getResText() {
		return resText;
	}

	public void setResText(String resText) {
		this.resText = resText;
	}

	public List<?> getDatas() {
		return datas;
	}

	public void setDatas(List<?> datas) {
		this.datas = datas;
	}

	public int getPageTotal() {
		return pageTotal;
	}

	public void setPageTotal(int pageTotal) {
		this.pageTotal = pageTotal;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public static ResultCommon getRes() {
		return res;
	}

	public static void setRes(ResultCommon res) {
		ResultCommon.res = res;
	}

	public List<?> getDatas2() {
		return datas2;
	}

	public void setDatas2(List<?> datas2) {
		this.datas2 = datas2;
	}

	public String getPersonStatus() {
		return personStatus;
	}

	public void setPersonStatus(String personStatus) {
		this.personStatus = personStatus;
	}

	public String getMediaStatus() {
		return mediaStatus;
	}

	public void setMediaStatus(String mediaStatus) {
		this.mediaStatus = mediaStatus;
	}

	public String getRemindStatus() {
		return remindStatus;
	}

	public void setRemindStatus(String remindStatus) {
		this.remindStatus = remindStatus;
	}

	public String getCdpStatus() {
		return cdpStatus;
	}

	public void setCdpStatus(String cdpStatus) {
		this.cdpStatus = cdpStatus;
	}

}
