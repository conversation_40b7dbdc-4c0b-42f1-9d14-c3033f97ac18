<template>
  <div class="pos">
    <div>
      <el-row  id="order-list">
        <el-col :span='7'>
          <el-tabs>
            <el-tab-pane label="用户">
              <el-table :data="tableData" border show-summary style="width: 100%" >
                <el-table-column prop="goodsName" label="企业"  ></el-table-column>
                <el-table-column prop="count" label="量" width="50"></el-table-column>
                <el-table-column prop="totalPrice" label="其他" width="70"></el-table-column>
                <el-table-column  label="操作" width="100" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="delSingleGoods(scope.row)">删除</el-button>
                    <el-button type="text" size="small" @click="addOrderList(scope.row)">增加</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="挂单">
            </el-tab-pane>
            <el-tab-pane label="订购">
            </el-tab-pane>
          </el-tabs>
          <br>
          <div style="text-align: center;">
            <el-button type="warning" >订购</el-button>
            <el-button type="danger" @click="delAllGoods">删除</el-button>
            <el-button type="success" @click="checkout">结算</el-button>
          </div>
        </el-col>
        <!--商品展示-->
        <el-col :span="17">
          <div class="often-goods">
            <div class="title">常用</div>
            <div class="often-goods-list">
              <ul style="overflow: hidden;">
                <li v-for="item in oftenGoods" @click="addOrderList(item)">
                  <span>{{item.goodsName}}</span>
                  <span class="o-price">￥{{item.price}}元</span>
                </li>
              </ul>
            </div>
          </div>
          <div class="goods-type">
            <el-tabs>
              <el-tab-pane label="汉堡">
                <ul class='cookList' style="overflow: hidden;">
                  <li v-for="item in type0Goods" @click="addOrderList(item)">
                    <span class="foodImg"><img :src="item.goodsImg" width="100%"></span>
                    <span class="foodName">{{item.goodsName}}</span>
                    <span class="foodPrice">￥{{item.price}}元</span>
                  </li>
                </ul>
              </el-tab-pane>
              <el-tab-pane label="小食">
                小食
              </el-tab-pane>
              <el-tab-pane label="饮料">
                饮料
              </el-tab-pane>
              <el-tab-pane label="套餐">
                套餐
              </el-tab-pane>

            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'Pos',
    data () {
      return {
        tableData: [],
        oftenGoods:[],
        type0Goods:[],
      }
    },
    created(){
      var vm = this;
      //初始化
      vm.init();
      //读取分类商品列表
      vm.getInit();
    },
    methods:{
      //初始化
      init(){
        var vm = this;
        vm.$http({
          method: 'get',
          url: vm.baseUrl + '/DemoApi/oftenGoods.php',
        }).then(
          res => {
            vm.oftenGoods=res.data;
          },
          _ => {
            console.log(res);
          }
        )
      },
      //读取分类商品列表
      getInit(){
        var vm = this;
        vm.$http({
          method: 'get',
          url: vm.baseUrl + '/DemoApi/typeGoods.php',
        }).then(
          res => {

              vm.type0Goods=res.data[0];

            },
            _ => {
              console.log(res);
            }
        )
      },
      //添加订单列表的方法
      addOrderList(goods){
        let isHave=false;
        //判断是否这个商品已经存在于订单列表
        for (let i=0; i<this.tableData.length;i++){
          console.log(this.tableData[i].goodsId);
          if(this.tableData[i].goodsId==goods.goodsId){
            isHave=true; //存在
          }
        }
        //根据isHave的值判断订单列表中是否已经有此商品
        if(isHave){
          //存在就进行数量添加
          let arr = this.tableData.filter(o =>o.goodsId == goods.goodsId);
          arr[0].count++;
          arr[0].totalPrice=arr[0].count*arr[0].price;
          //console.log(arr);
        }else{
          //不存在就推入数组
          let newGoods={goodsId:goods.goodsId,goodsName:goods.goodsName,price:goods.price,totalPrice:goods.price,count:1};
          this.tableData.push(newGoods);

        }
      },
      //删除单个商品
      delSingleGoods(goods){
        console.log(goods);
        this.tableData=this.tableData.filter(o => o.goodsId !=goods.goodsId);
      },
      //删除所有商品
      delAllGoods() {
        this.tableData = [];
      },
      checkout() {
        if (this.tableData.length!=0) {
          this.tableData = [];
          this.$message({
            message: '结账成功，感谢你又为店里出了一份力!',
            type: 'success'
          });
        }else{
          this.$message.error('不能空结。老板了解你急切的心情！');
        }
      }
    },
    mounted:function(){
      var orderHeight=document.body.clientHeight;
      document.getElementById("order-list").style.height=orderHeight+'px';
    },
  }
</script>
<style scoped>
  .title{
    height: 20px;
    border-bottom:1px solid #D3DCE6;
    background-color: #F9FAFC;
    padding:10px;
  }
  .often-goods-list ul li{
    list-style: none;
    float:left;
    border:1px solid #E5E9F2;
    padding:10px;
    margin:5px;
    background-color:#fff;
    cursor: pointer;
  }
  .o-price{
    color:#58B7FF;
  }
  .cookList li{
    list-style: none;
    width:23%;
    border:1px solid #E5E9F2;
    height: auto;
    overflow: hidden;
    background-color:#fff;
    padding: 2px;
    float:left;
    margin: 2px;
    cursor: pointer;
  }
  .cookList li span{

    display: block;
    float:left;
  }
  .foodImg{
    width: 40%;
  }
  .foodName{
    font-size: 18px;
    padding-left: 10px;
    color:brown;

  }
  .foodPrice{
    font-size: 16px;
    padding-left: 10px;
    padding-top:10px;
  }
</style>
