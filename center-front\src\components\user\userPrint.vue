<template>
    <div>
        <div>
            <el-row style="width: 94%;margin: 10px auto">
                <span style="font-weight:bold">个人彩印状态:</span>
                <span>{{getPersonStatus}}</span>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <div style="font-weight:bold;margin-bottom: 10px">套餐包订购列表:</div>
                <el-table :data="tableData.datas" border  style="margin:0 auto;width: 100%;">
                    <el-table-column prop="bossId" label="企业代码"  width="160">
                    </el-table-column>
                    <el-table-column prop="serviceId" label="业务代码"  width="160">
                    </el-table-column>
                    <el-table-column prop="productId" label="产品代码"  width="180">
                    </el-table-column>
                    <el-table-column prop="packageName" label="套餐包名称" width="180">
                    </el-table-column>
                    <el-table-column prop="serviceCost" label="业务资费"  width="100">
                    </el-table-column>
                    <el-table-column prop="serviceChannel" label="开户渠道"  width="180">
                    </el-table-column>
                    <el-table-column prop="submitTime" label="订购时间"  width="180">
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <div style="font-weight:bold;margin-bottom: 10px">彩印内容设置列表:</div>
                <el-table :data="tableData.datas2" border  style="margin:0 auto;width: 100%;">
                    <!-- <template slot-scope="scope">
                        <span>{{labels.setCsType[scope.row.setCsType]}}</span>
                    </template> -->
                    <el-table-column prop="setCsTypeName" label="设置类型" width="200">
                        <!-- <template slot-scope="scope">
                            <span>{{labels.setCsType[scope.row.setCsType]}}</span>
                        </template> -->
                    </el-table-column>
                     <el-table-column prop="setCsContentType" label="内容类型" width="200">
                    </el-table-column>
                    <el-table-column prop="setCsContent" label="设置内容" width="160" :show-tooltip-when-overflow="true">
                    </el-table-column>
                    <el-table-column prop="setRecTypeName"  width="160" label="接收方类型">
                        <!-- <template slot-scope="scope">
                            <span>{{labels.setRecType[scope.row.setRecType]}}</span>
                        </template> -->
                    </el-table-column>
                    <el-table-column prop="setRecPhone" label="接收号码"  width="160" :show-tooltip-when-overflow="true">
                        <template slot-scope="scope">
                            <span style="margin-left: 10px" v-if="scope.row.setRecType==1">
                                <el-button @click="openNums(scope.row)" type="text" size="small">号码详情</el-button>
                            </span>
                            <span style="margin-left: 10px" v-else-if="scope.row.setRecType==0">
                            全部
                            </span>
                            <span style="margin-left: 10px" v-else-if="scope.row.setRecType==2">
                                {{scope.row.setRecPhone}}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column label="详情" width="80">
                        <template slot-scope="scope">
                            <el-button @click="openPersonCsDetail(scope.row)" type="text" size="small">查看详情</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column prop="setTime" label="设置时间" width="180">
                    </el-table-column>
                     <el-table-column prop="crEndDate" label="结束时间" width="180">
                    </el-table-column>
                    <el-table-column label="操作" fixed="right">
                        <template slot-scope="scope">
                            <el-button v-show="( scope.row.setRecType==0 && scope.row.serviceCode=='03002')" @click="openPersonCsModify(scope.row)" type="text" size="small">编辑</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="app-pageganit">
            <el-pagination v-if="page.pageTotal>0"  class="user-page" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="page.pageNum" :page-sizes="[10,20,30,50]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="page.pageTotal">
            </el-pagination>
            </div>
        </div>
        <div>
            <el-dialog width="400px" title="号码详情" v-if="modals.visible === 'setRecPhone'" :visible.sync="modals.visible === 'setRecPhone'" :show-close="false" :close-on-click-modal="false">
                <div style="height:150px;overflow-y:auto;overflow-x:hidden;">
                    <el-row :gutter="15">
                        <el-col :span="8" v-for="(phone, key, index) in modals.phones" :key="index" style="margin-bottom:10px;text-align:center">
                            {{phone}}
                        </el-col>
                    </el-row>
                </div>
                <div slot="footer" class="dialog-footer" style="text-align: center;">
                    <el-button @click="modals.visible = ''">关闭</el-button>
                </div>
            </el-dialog>
            <!-- 详情 -->
            <el-dialog title="详情" v-if="modals.visible === 'csDetail'" :visible.sync="modals.visible === 'csDetail'" :show-close="false">
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置类型:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{modals.csDetail.setCsTypeName}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">{{modals.csDetail.setCsType != 2 ? '彩印ID':'彩印盒ID'}}：</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{modals.csDetail.setCsTypeId}}</div>
                    </el-col>
                </el-row>
                <!--详情 文本彩印 -->
                <el-row :gutter="15" v-if="modals.csDetail.setCsType==0 || modals.csDetail.setCsType==1">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">个人彩印内容:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{modals.csDetail.setCsContent}}</div>
                    </el-col>
                </el-row>
                <!--详情 彩印盒 -->
                <el-row :gutter="15" v-else-if="modals.csDetail.setCsType==2">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">彩印盒内容:</div>
                    </el-col>
                    <el-col :span="15">
                        <div class="grid-content bg-purple">
                            <el-table :data="csHisList.list" border style="width: 400px;">
                                <el-table-column property="id" label="彩印ID"style="width: 100px;"></el-table-column>
                                <el-table-column property="content"  label="内容" style="width: 300px;"></el-table-column>
                            </el-table>
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">接收方类型:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{modals.csDetail.setRecTypeName}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15" v-if="modals.csDetail.setRecType !== 2">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">接收方号码:</div>
                    </el-col>
                    <el-col :span="18" v-if="modals.csDetail.setRecType !== 2">
                        <div class="grid-content bg-purple">{{modals.csDetail.setRecPhone}}</div>
                    </el-col>
                    <el-col :span="18" v-if="modals.csDetail.setRecType == 1">
                        <!-- <div class="grid-content bg-purple">{{modals.csDetail.setRecPhones}}</div> -->
                        <el-row :gutter="15">
                            <el-col :span="5" v-for="(phone, key, index) in modals.csDetail.setRecPhones" :key="index" style="margin-bottom:10px;text-align:center">
                                {{phone}}
                            </el-col>
                        </el-row>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置时间:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{modals.csDetail.setTime}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置来源:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{labels.channel[modals.csDetail.channel]}}</div>
                    </el-col>
                </el-row>
                <div slot="footer" class="dialog-footer" style="text-align: center;">
                    <el-button @click="modals.visible = ''">关闭</el-button>
                </div>
            </el-dialog>
            <!-- 编辑 -->
            <el-dialog title="编辑个人彩印内容" v-if="modals.visible === 'csModify'" :visible.sync="modals.visible === 'csModify'"  :show-close="false">
                <div style="height:250px;overflow-y:auto;overflow-x:hidden;">
                    <el-row :gutter="15" style="margin:10px">
                        <el-col :span="6">
                            <div class="grid-content bg-purple" style="text-align:right">用户号码:</div>
                        </el-col>
                        <el-col :span="18">
                            <div class="grid-content bg-purple">{{page.phone}}</div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="15" style="margin:10px">
                        <el-col :span="6">
                            <div class="grid-content bg-purple" style="text-align:right">用户彩印内容:</div>
                        </el-col>
                        <el-col :span="18">
                            <el-radio-group v-model="modals.setCsType">
                                <el-radio :label="1">文本彩印</el-radio>
                                <el-radio :label="2">彩印盒</el-radio>
                            </el-radio-group>
                        </el-col>
                    </el-row>
                    <el-row v-if="modals.setCsType==0 || modals.setCsType==1" style="margin:10px">
                        <el-col :span="6">&nbsp;</el-col>
                        <el-col :span="12">
                            <el-input type="textarea" :rows="5" placeholder="文本彩印内容" v-model="modals.setCsContent" clearable></el-input>
                        </el-col>
                    </el-row>
                <el-row v-if="modals.setCsType == 2" tyle="margin:10px">
                    <el-col :span="3">&nbsp;</el-col>
                    <el-col :span="15">
                      <el-select
                        v-model="value9"
                        filterable
                        remote
                        reserve-keyword
                        placeholder="请输入彩印盒名称"
                        :remote-method="remoteMethod"
                        :loading="loading"
                        size='50'>
                        <el-option
                          v-for="item in options4"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                          <span style="float: left">{{ item.name }}</span>
                          <span style="float: right; color: #8492a6; font-size: 10px">{{ item.id }}</span>
                        </el-option>
                      </el-select>
                    </el-col>
                </el-row>

                </div>
                <div slot="footer" class="dialog-footer" style="text-align: right;">

                    <el-button @click="modals.visible = ''" size="small">关闭</el-button>
                    <el-button type="primary"  size="small" @click="updateCsContent()" >确定</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
    export default {
        name: 'UserList',
        data() {
            return {
                options4: [],
                value9: [],
                list: [],
                loading: false,
                request:{},

                statusData:new Array(),
                csHisList:{},
                box:"",
                boxId:'',
                totalCount: 0,
                labels: {
                    setCsType: [],
                    setRecType: [],
                    csIdLabel: [],
                    channel: {}
                },
                page: {
                    cyType:'4',
                    phone: '',
                    pageNum: 1,
                    pageSize: 10
                },
                tableData: {},
                modals: {}
            }
        },
        methods: {
            remoteMethod(query) {
                if (query !== '') {
                   this.loading = true;
                   this.request.pkgId=query;//pkgName
                  post('/user/batchMod/getPkgContent',this.request,{ emulateJSON: true}).then(res=>{
                     if(res.status===200){
                       this.options4=res.data;
                     }
                     this.loading = false;
                  });
                } else {
                  this.loading = false;
                  this.options4 = [];
                }
              },
            pageQuery: function(param) {
                this.$http
                    .post(`${this.proxyUrl}/user/personInfo/getPersonInfo`, param, {
                        emulateJSON: true
                    })
                    .then(function(res) {
                        this.tableData = res.data;
                        this.$set(this.page,'pageTotal',res.data.pageTotal);
                    })
            },
            handleSizeChange: function(size) {
                this.$set(this.page, 'pageSize', size);
                this.$set(this.page, 'pageNum', 1);
                this.pageQuery(this.page);
            },
            handleCurrentChange: function(currentPage) {
                this.$set(this.page, 'pageNum', currentPage);
                this.pageQuery(this.page);
            },
            openNums: function(row) {
                this.$set(this.modals, 'visible', 'setRecPhone');
                this.$set(this.modals, 'phones', row.setRecPhones);
            },
            //查看详情
            openPersonCsDetail: function(row) {
                this.$set(this.modals, 'csDetail', row);
                this.$set(this.modals, 'visible', 'csDetail');
                console.log(row.setCsType);
                if(row.setCsType==2){
                    post('/user/personInfo/getHisCustomerCsBoxContent',{pkgId:row.setCsTypeId}).then(res=>{
                        if(res.status===200){
                            this.csHisList=res.data;
                        }
                    });

                }
            },
            //编辑
            openPersonCsModify: function(row) {
                this.$http
                    .post(`${this.proxyUrl}/user/personInfo/getPersonCsDetail`, {
                        phone: this.page.phone,
                        ruleId: row.ruleId,
                        csNumber: row.csNumber
                    }, {
                        emulateJSON: true
                    }).then(function(res) {
                    this.$set(this.modals, 'setCsType', row.setCsType !=2 ? 1:2);
                    this.$set(this.modals, 'visible', 'csModify');
                    this.$set(this.modals, 'info', res.data);
                    this.$set(this.modals, 'setCsContent', res.data.setCsContent);
                })
            },
            //编辑提交
            updateCsContent: function() {
                this.boxId=this.value9;
                if(this.boxId == '' && this.modals.setCsType == 2){
                    this.$message({ message:'请选择彩印盒内容', type:'error' });
                    return;
                }
                let param = {
                    phone: this.page.phone,
                    ruleId:this.modals.info.ruleId,
                    csNumber: this.modals.info.csNumber,
                    setCsType: this.modals.setCsType,
                    csContent: this.modals.setCsContent,
                    pkgId : this.boxId

                }
                this.$http
                    .post(`${this.proxyUrl}/user/personInfo/modPersonCs`, param, {
                        emulateJSON: true
                    }).then(function(res){
                        if(res.data.status == 0){
                            this.$message.success("保存成功");
                        }else{
                            this.$message.error("保存失败");
                        }
                    this.pageQuery(this.page);
                });
                this.$set(this.modals, 'visible', '');
            },
            //查询彩印盒列表数据
            queryPkgContend(){
                post('/user/personInfo/getAllBoxContent',{}).then(res=>{
                    if(res.status===200){
                    this.statusData=res.data;
                    }
                });
            },
            //获取选中的值
            selectClickStatu(list){
                this.box=list.name;
                this.boxId=list.id;
            }
        },
        mounted() {
            this.$set(this.page, 'phone', sessionStorage.getItem('pkCurUserid'));
            this.pageQuery(this.page);
            //this.queryPkgContend();
        },
        computed: {
            getPersonStatus: function() {
                if(this.tableData.personStatus==0){
                    return '未订购';
                }
                if(this.tableData.personStatus==1){
                    return '已开通';
                }
                if(this.tableData.personStatus==2){
                    return '已退订';
                }
            }
        },
        components: {}
    }
</script>
<style>
</style>