<template>
    <div>
        <h1 class="user-title">系统用户管理</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="状态" style="margin-left:15px;">
                    <el-select v-model="searchForm.isDelete" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in statusList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>

                </el-form-item>
                <el-form-item label="角色">
                    <el-select v-model="searchForm.sysRoleId" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option v-if="roleList.length>0"
                                   v-for="item in roleList"
                                   :key="item.sysRoleId"
                                   :label="item.sysRoleName"
                                   :value="item.sysRoleId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="负责省份">
                    <el-select v-model="searchForm.provinceCode" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>

            </el-form>
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="用户名">
                    <el-input  v-model="searchForm.sysUserName" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="公司">
                    <el-input  v-model="searchForm.sysUserCompany" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                            v-model="searchForm.endTime"
                            type="date"
                            placeholder="创建时间" size="small">
                    </el-date-picker>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="showAddUser()" size="small">新增系统用户</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
                  class="app-tab" :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="sysUserName" label="用户名" width="120"/>
            <el-table-column prop="sysStaffName" label="姓名" width="120"/>
            <el-table-column prop="sysRoleName" label="角色" width="160"/>
            <el-table-column prop="sysUserCompany" label="公司" width="160"/>
            <el-table-column prop="provinceName" label="负责省份" width="200" :show-overflow-tooltip="true"/>
            <el-table-column prop="sysMobileNumber" label="联系电话" width="160"/>
            <el-table-column prop="sysUserEmail" label="Email" width="200"/>
            <el-table-column prop="sysCreateTime" label="创建时间" width="180" />
            <el-table-column prop="sysStateName" label="状态" width="100"/>
            <el-table-column prop="oper" label="操作" width="200px" fixed="right">
                <template slot-scope="scope" >
                    <el-button  v-show="scope.row.isDelete != '2'" type="text" size="small" @click="resetPwd(scope.row)" >重置密码</el-button>
                    <el-button  v-show="scope.row.isDelete != '2'" type="text" size="small" @click="showEditUser(scope.row)" >编辑</el-button>
                    <el-button   v-show="scope.row.isDelete != '2'" type="text" size="small" @click="delUser(scope.row)" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

        <div>
            <el-dialog title="新增系统用户" :visible.sync="addVisible"   :close-on-click-modal="false">
                <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline app-form-item" label-width="25%"  style="width: 80%">
                    <el-form-item label="用户名" prop="sysUserName">
                        <el-input v-model="addForm.sysUserName" size="small" :maxlength="40"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名" prop="sysStaffName">
                        <el-input v-model="addForm.sysStaffName" size="small" :maxlength="40"></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="sysUserCompany">
                        <el-input v-model="addForm.sysUserCompany" size="small" :maxlength="100"></el-input>
                    </el-form-item>
                    <el-form-item label="角色" prop="sysRoleId">
                        <el-select v-model="addForm.sysRoleId" placeholder="请选择" size="small">
                            <el-option v-if="roleList.length>0"
                                       v-for="item in roleList"
                                       :key="item.sysRoleId"
                                       :label="item.sysRoleName"
                                       :value="item.sysRoleId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="负责省份" prop="provinceCode">
                        <el-select v-model="addForm.provinceCodeArr" multiple collapse-tags placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="isDelete">
                        <el-select v-model="addForm.isDelete" placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in statusListNew"
                                    :key="item.key"
                                    :label="item.value"
                                    :value="item.key">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="联系电话" prop="sysMobileNumber">
                        <el-input v-model="addForm.sysMobileNumber" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="Email" prop="sysUserEmail">
                        <el-input v-model="addForm.sysUserEmail" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addVisible = false" size="small">取 消</el-button>
                    <el-button type="primary" @click="addUser('addForm')" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <div>
            <el-dialog title="重置密码" :visible.sync="resetVisible"  :close-on-click-modal="false">
                <el-form :model="resetPwdForm"  ref="resetPwdForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                    <el-form-item label="用户名" prop="sysUserName">
                        <el-input v-model="resetPwdForm.sysUserName" disabled="disabled" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="密码" prop="sysUserPassword">
                        <el-input v-model="resetPwdForm.sysUserPassword" type="password" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="确认密码" prop="confirmPassword">
                        <el-input v-model="resetPwdForm.confirmPassword" type="password" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="resetVisible = false" size="small">取 消</el-button>
                    <el-button type="primary" @click="resetPwd('resetPwdForm')" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <div>
            <el-dialog title="编辑系统用户" :visible.sync="editVisible" :close-on-click-modal="false">
                <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                    <el-form-item label="用户名" prop="sysUserName">
                        <el-input v-model="editForm.sysUserName" :disabled="true" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名" prop="sysStaffName">
                        <el-input v-model="editForm.sysStaffName" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="sysUserCompany">
                        <el-input v-model="editForm.sysUserCompany" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="角色" prop="sysRoleId">
                        <el-select v-model="editForm.sysRoleId" placeholder="请选择" size="small">
                            <el-option v-if="roleList.length>0"
                                       v-for="item in roleList"
                                       :key="item.sysRoleId"
                                       :label="item.sysRoleName"
                                       :value="item.sysRoleId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="负责省份" prop="provinceCodeArr">
                        <el-select v-model="editForm.provinceCodeArr"  multiple collapse-tags  placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="isDelete">
                        <el-select v-model="editForm.isDelete" placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in statusListNew"
                                    :key="item.key"
                                    :label="item.value"
                                    :value="item.key">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="联系电话" prop="sysMobileNumber">
                        <el-input v-model="editForm.sysMobileNumber" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="Email" prop="sysUserEmail">
                        <el-input v-model="editForm.sysUserEmail" size="small"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="editVisible = false" size="small">取 消</el-button>
                    <el-button type="primary" @click="editUser('editForm')" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>

    </div>

</template>

<script >
    import {isMobile,isCombination,isComb} from './../../util/tools.js';
    import md5 from 'blueimp-md5'



    export default {
        data() {

            let validPhone=(rule, value,callback)=>{
                if (!isMobile(value)){
                    callback(new Error('请输入正确的11位手机号码'))
                }else {
                    callback()
                }
            };
            let validUserName=(rule, value,callback)=>{
                if (!isCombination(value)){
                    callback(new Error('必须以字母开头，由字母、数字或下划线组成'))
                }else {
                    callback()
                }

            };
            let validIsExistUserName=(rule, value,callback)=>{
                this.addForm.sysUserName = value;
                console.info(this.addForm);
                this.$http.post(`${this.proxyUrl}/sys/sysUser/isExistUserName`,JSON.stringify(this.addForm)).then(function(res){
                    if(res.data == 1 && this.addVisible){
                        callback(new Error('用户名已存在!'))
                    }else{
                        callback()
                    }
                });

            };
            let validComb=(rule, value,callback)=>{
                if (!isComb(value)){
                    callback(new Error('必须以字母或汉字组成'))
                }else {
                    callback()
                }
            };
            return {
                addVisible: false,
                resetVisible:false,
                editVisible: false,
                propVisible:false,
                row:{},
                propMsg:'',
                //查询form对象定义
                searchForm: {
                    isDelete:'', //状态
                    sysRoleId:'',//角色id,
                    sysUserName:'', //模糊查询
                    sysUserCompany: '', //模糊查询
                    provinceCode: '',
                    startTime:'',//创建时间
                    endTime:'',//创建时间
                    pageSize:10,// 每页显示条数
                    pageNum:1 // 查询的页码
                },
                //新增form对象定义
                addForm: {
                    sysUserName:'',
                    sysUserPassword:'',
                    confirmPassword:'',
                    sysStaffName:'',
                    sysRoleId:'',//角色id
                    sysUserCompany:'',
                    provinceCode: '',
                    provinceCodeArr:[],
                    sysMobileNumber:'',
                    sysUserEmail:'',
                    isDelete:''
                },
                resetPwdForm:{
                    sysUserId:'',
                    sysUserName:'',
                    sysUserPassword:'',
                    confirmPassword:''
                },
                //编辑form对象定义
                editForm: {
                    sysUserId:'',
                    sysUserName:'',
                    sysStaffName:'',
                    sysRoleId:12,//角色id
                    sysUserCompany:'',
                    provinceCode:'',
                    provinceCodeArr:[],
                    sysMobileNumber:'',
                    sysUserEmail:'',
                    isDelete:''
                },
                //查询或删除form
                queryOrDelForm:{
                    sysUserId:''
                },
                rules: {
                    sysUserName: [
                        { required: true, message: '请输入用户名称', trigger: 'blur' },
                        { min: 4, max: 40, message: '长度在 4 到 40 个字符', trigger: 'blur' },
                        { trigger: 'blur',validator: validUserName},
                        { trigger: 'blur',validator: validIsExistUserName}
                    ],
                    sysStaffName: [
                        { required: true, message: '请输入姓名', trigger: 'blur' },
                        { max: 40, message: '长度不超过 40 个字符', trigger: 'blur' },
                        { trigger: 'blur',validator: validComb}
                    ],
                    sysRoleId: [
                        { required: true, message: '请选择角色', trigger: 'change' }
                    ],
                    sysUserCompany: [
                        { required: true, message: '请输入公司名称', trigger: 'blur' },
                        { max: 100, message: '长度不超过 100 个字符', trigger: 'blur' },
                        { trigger: 'blur',validator: validComb}
                    ],
                    sysUserEmail: [
                        { required: true, message: '请输入邮箱', trigger: 'blur' },
                        { max: 40, message: '长度不超过 40 个字符', trigger: 'blur' },
                        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
                    ],
                    sysMobileNumber: [
                        { required: true, message: '请输入联系电话', trigger: 'blur'},
                        { max: 11, message: '长度不超过 11 个字符', trigger: 'blur' },
                        { type: 'mobile', message: '请输入正确的联系电话', trigger: 'blur',validator: validPhone}
                    ],
                    isDelete : [
                        { required: true, message: '请选择状态', trigger: 'blur' }
                    ]

//
                },
                reSetRules:{
                },
                statusList:[ {key:'0',value:'激活'},{key:'1',value:'锁定'},{key:'2',value:'删除'}],
                statusListNew:[ {key:'0',value:'激活'},{key:'1',value:'锁定'}],
                roleList:[],
                provinceList:[],

                tableData:[],
                currentPage: 1,
                total:0

            }
        },
        mounted(){
//            this.slideData(this.proxyUrl);
            //获取省份list
            this.getProvinceList();
            //获取角色list
            this.getRoleList();
            //查询列表请求
            this.search(this.searchForm);
        },
        methods: {

            //获取省份list
            getProvinceList:function(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getProvince`).then(function(res){
                    //console.log(res);
                    this.provinceList.push({
                        provinceName:'全国',
                        provinceCode:'0'
                    })
                    this.provinceList.push(...res.data);
                })
            },
            //获取角色list
            getRoleList:function(){
                this.$http.get(`${this.proxyUrl}/sys/sysRole/getSysAllRoleList`).then(function(res){
                    this.roleList=res.data;
                    //this.roleList.unshift([{sysRoleId:5,sysRoleName:'全部'}]);
                })
            },

            //查询列表请求
            search:function(searchForm){
                //console.log(searchForm);
                this.$http.post(`${this.proxyUrl}/sys/sysUser/getSysUserPage`,JSON.stringify(searchForm)).then(function(res){
                    console.log(res);
                    if(res.data.resStatus == 1){
                        setTimeout(
                            function() {
                                this.$router.push("/noPermission");
                            }.bind(this),
                            50
                        );
                    } else {
                        this.currentPage=res.data.pageNum;
                        this.total=res.data.pageTotal;
                        this.tableData=res.data.datas;
                    }
                })
            },
            // 弹出修改框
            showEditUser(editForm){
                this.editForm = Object.assign({},editForm);
                // console.info(this.editForm.provinceCode);
                // if(this.editForm.provinceCode != null){
                //     this.editForm.provinceCodeArr=this.editForm.provinceCode.split(',');
                // }
                // console.info(this.editForm.provinceCodeArr);
                this.editVisible = true;
                // this.resetForm('editForm');
            },
            showAddUser(){
                this.addForm = {
                    sysUserName:'',
                    sysUserPassword:'',
                    confirmPassword:'',
                    sysStaffName:'',
                    sysRoleId:'',//角色id
                    sysUserCompany:'',
                    provinceCodeArr:[],
                    sysMobileNumber:'',
                    sysUserEmail:'',
                    isDelete:''
                };
                this.addVisible = true;
                this.resetForm('addForm');

            },
            // 弹出密码重置框
            showResetPwd(resetPwdForm){
                this.resetPwdForm = Object.assign({},resetPwdForm);
                this.resetPwdForm.sysUserPassword = '';
                this.resetVisible = true;
            },
            //修改请求
            editUser(editForm){
                this.$refs[editForm].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/sys/sysUser/updateSysUser`,JSON.stringify(this.editForm)).then(function(res){
                            // console.log(res);
                            if(res.data.resStatus == 0){
                                this.$message.success("修改成功!");
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.$message.error('修改失败!'+ res.data.resText);
                            }
                        })
                    } else {
                        return false;
                    }
                });
            },
            isPwd(val){
                let reg = /^[a-zA-Z]\w{6,16}$/;
                return reg.test(val);
            },
            resetPwd(row){
                this.resetPwdForm.sysUserId = row.sysUserId;
                this.$confirm('确定重置密码?')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/sys/sysUser/resetPwd`,JSON.stringify(this.resetPwdForm)).then(function(res){
                            // console.log(res);
                            if(res.data.resStatus == 0){
                                this.$message.success(res.data.resText);
                                this.resetVisible = false;
                            }else{
                                this.$message.error('重置失败!'+ res.data.resText);
                            }
                        })
                    })
            },
            // 新增
            addUser(formName) {
                this.$refs[formName].validate((valid) => {
                    if (valid) {

                        this.$http.post(`${this.proxyUrl}/sys/sysUser/addSysUser`,JSON.stringify(this.addForm))
                            .then(function(res){
                                // console.log(res);
                                if(res.data.resStatus == 0){
                                    this.$message.success("新增成功!");
                                    this.addVisible = false;
                                    this.search(this.searchForm);
                                }else if (res.data.resStatus == 2){
                                    this.$message.error("用户名已存在!");
                                }else{
                                    this.$message.error('新增失败!'+ res.data.resText);
                                }
                                this.addForm = {};
                            })
                    } else {
                        return false;
                    }
                });
            },
            //删除请求
            delUser(role){
                if(role.sysUserName  == 'admin'){
                    this.$message.error('系统管理员不可删除!');
                    return;
                }
                this.queryOrDelForm.sysUserId = role.sysUserId;
                this.$confirm('确定删除此用户?')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/sys/sysUser/deleteSysUser`,JSON.stringify(this.queryOrDelForm))
                            .then(function(res){
                                // console.log(res)
                                if(res.data.resStatus == 0){
                                    this.$message.success("删除成功!");
                                    this.search(this.searchForm);
                                }else{
                                    this.$message.error('删除失败!'+ res.data.resText);
                                }
                            })
                    })

            },
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },

            //清空提示信息
            resetForm(formName){
                if (this.$refs[formName]!==undefined) {
                    this.$refs[formName].resetFields();
                }
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
