package com.cy.user.model;

public class CyUserPlayModel {

	private Integer id;
	private String msisdn; // 用户手机号码或固话号码
	private String contentCode;// 内容编码
	private String channelId;// 产品推广渠道
	private String cpCode;// 合作伙伴
	private String payType;// 支付方式
	private String validDate;// 订购关系生效时间YYYYMMDDHH24MISS
	private String expireDate;// 订购关系失效时间YYYYMMDDHH24MISS
	private String createDate;// 订购关系创建时间
	private String accessInfo;// 访问信息
	private Integer fee;// 内容价格
	private String currency;// 货币类型：0人民币 1：美元 2：咪咕币 3：咪咕券 4：点数
	private String oldExpireDate; // BOSS生效时间
	private String operTime; // 彩印系统操作时间
	private String contractProdCode;// 消耗配额所对应的合约产品
	private String quota;// 本次消费配额 默认1

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getMsisdn() {
		return msisdn;
	}

	public void setMsisdn(String msisdn) {
		this.msisdn = msisdn;
	}

	public String getContentCode() {
		return contentCode;
	}

	public void setContentCode(String contentCode) {
		this.contentCode = contentCode;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public String getCpCode() {
		return cpCode;
	}

	public void setCpCode(String cpCode) {
		this.cpCode = cpCode;
	}

	public String getPayType() {
		return payType;
	}

	public void setPayType(String payType) {
		this.payType = payType;
	}

	public String getValidDate() {
		return validDate;
	}

	public void setValidDate(String validDate) {
		this.validDate = validDate;
	}

	public String getExpireDate() {
		return expireDate;
	}

	public void setExpireDate(String expireDate) {
		this.expireDate = expireDate;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getAccessInfo() {
		return accessInfo;
	}

	public void setAccessInfo(String accessInfo) {
		this.accessInfo = accessInfo;
	}

	public Integer getFee() {
		return fee;
	}

	public void setFee(Integer fee) {
		this.fee = fee;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getOldExpireDate() {
		return oldExpireDate;
	}

	public void setOldExpireDate(String oldExpireDate) {
		this.oldExpireDate = oldExpireDate;
	}

	public String getOperTime() {
		return operTime;
	}

	public void setOperTime(String operTime) {
		this.operTime = operTime;
	}

	public String getContractProdCode() {
		return contractProdCode;
	}

	public void setContractProdCode(String contractProdCode) {
		this.contractProdCode = contractProdCode;
	}

	public String getQuota() {
		return quota;
	}

	public void setQuota(String quota) {
		this.quota = quota;
	}

}
