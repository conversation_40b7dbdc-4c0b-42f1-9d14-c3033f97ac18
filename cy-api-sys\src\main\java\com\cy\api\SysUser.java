package com.cy.api;

import com.cy.common.ResultCommon;
import com.cy.common.ResultListCommon;
import com.cy.common.SysUserCommon;
import com.cy.model.RejectPresupposition;
import com.cy.model.SysUserModel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.SQLException;
import java.util.List;

public interface SysUser {

	/**
	 * 查询单个系统用户
	 */
	@RequestMapping(value = "/sysUserCore/getSysUser")
	SysUserModel getSysUser(@RequestBody SysUserCommon common);

	@RequestMapping(value = "/sysUserCore/getSmsVerifyCode")
	ResultCommon getSmsVerifyCode(@RequestBody SysUserCommon common);

	@RequestMapping(value = "/sysUserCore/validSmsVerifyCode")
	ResultCommon validSmsVerifyCode(@RequestBody SysUserCommon common);

	/**
	 * 分页查询系统用户列表
	 */
	@RequestMapping(value = "/sysUserCore/getSysUserPage")
	ResultListCommon getSysUserPage(@RequestBody SysUserCommon common);

	/**
	 * 分页查询系统用户列表所有
	 */
	@RequestMapping(value = "/sysUserCore/getSysUserPageList")
	List<SysUserModel> getSysUserPageList(@RequestBody SysUserCommon common) throws SQLException;

	/**
	 * 修改用户
	 */
	@RequestMapping(value = "/sysUserCore/updateSysUser")
	ResultCommon updateSysUser(@RequestBody SysUserCommon common);
	
	
	/**
	 * 
	 * 重置密码
	 *
	 */
	@RequestMapping(value = "/sysUserCore/resetPwd")
	ResultCommon resetPwd(@RequestBody SysUserCommon common);

	/**
	 * 
	 * 删除系统用户，软删除
	 *
	 */
	@RequestMapping(value = "/sysUserCore/deleteSysUser")
	ResultCommon deleteSysUser(@RequestBody SysUserCommon common);

	/**
	 * 
	 * 新增系统用户
	 *
	 */
	@RequestMapping(value = "/sysUserCore/addSysUser")
	ResultCommon addSysUser(@RequestBody SysUserCommon common);

	/**
	 * 
	 * 用户登录
	 *
	 */
	@RequestMapping(value = "/sysUserCore/SysUserLog")
	ResultCommon SysUserLog(SysUserCommon common);
	
	
	/**
	 * 
	 * 当前用户修改密码
	 *
	 */
	@RequestMapping(value = "/sysUserCore/sysUserMod")
	ResultCommon sysUserMod(@RequestBody SysUserCommon common);

	@RequestMapping(value = "/sysUserCore/forgetPwd")
	ResultCommon forgetPwd(@RequestBody SysUserCommon common);

	@RequestMapping(value = "/sysUserCore/isExistUserName")
	int isExistUserName(@RequestBody SysUserCommon common);

	@RequestMapping(value = "/sysUserCore/isExistByUserName")
	SysUserModel isExistByUserName(@RequestBody SysUserCommon common);


	@RequestMapping(value = "/sysUserCore/findUser", method = RequestMethod.GET)
	public SysUserModel findSystemUser(@RequestParam("name") String name, @RequestParam("password") String password);


	@RequestMapping(value = "/sysUserCore/overdueUserPwd")
	boolean overdueUserPwd(@RequestParam("userId") Integer userId);

	@RequestMapping(value = "/sysUserCore/getList")
	List<RejectPresupposition> getListRejectPresupposition();


}
