<template>
  <div>
    <h1 class="user-title">个人用户开户</h1>
    <div class="user-line"></div>
    <div class="user-search2">
      <el-form>
        <el-form-item label="用户号码:"><br>
          <el-input v-model="addReq.phone" style="" size="small" class="app-input-long" :maxlength="11"></el-input>
        </el-form-item>
        <el-form-item label="产品代码:"><br>
          <el-select v-model="addReq.proId"  placeholder="请选择" clearable size="small" @change="changeValue()" class="app-input-long">
            <el-option
                v-for="item in pkgData"
                :key="item.businessCode"
                :label="item.productMark + ' ' + item.setMealName"
                :value="item.productMark">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="套餐包">
         {{mealName}}
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="addAccount" size="small" class="app-bnt">提 交</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
            title="提示"
            :visible.sync="propVisible"
            width="30%"
            :close-on-click-modal="false">
      <span>{{propMsg}}</span>
      <span slot="footer" class="dialog-footer">
    <el-button @click="propVisible = false">取 消</el-button>
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
    </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
    import {isMobile,isCombination,isComb} from './../../util/tools.js';
export default {
  data() {
      let validPhone=(rule, value,callback)=>{
          if (!isMobile(value)){
              callback(new Error('请输入正确的11位手机号码'))
          }else {
              callback()
          }
      };

    return {
      pkgData:new Array(),
      mealName:'',
      contentRadio:'1',
      province:[],
      location:[],
      product:[],
      propVisible:false,
      propMsg:'',
      switchReq:{
        phone:''
      },
      addReq:{
        phone:'',
        proId:'',
        locationId:'',
        pkgId:'',
        csContent:'',
        sendSM:'1'      
        }
    };
  },
  mounted(){
    this.getProduct();
  },
  methods: {
    //获取产品代码
      getProduct(){
        this.$http
            .post(`${this.proxyUrl}/user/switchAccount/getProduct`,this.addReq)
            .then((res)=>{
              this.pkgData=res.data.datas;
            })
      },

      changeValue(value){
          let obj = {};
          obj = this.pkgData.filter(item=>{
            return item.productMark===this.addReq.proId
          })
          this.mealName=obj[0].setMealName;
      },
      addAccount(){
        if(this.addReq.phone == '' || this.addReq.phone == null){
            this.$message.error('请输入用户号码');
            return false;
        }
          if (!isMobile(this.addReq.phone)){
              this.$message.error('请输入正确的11位手机号码');
              return false;
          }

          if(this.addReq.proId == '' || this.addReq.proId == null){
              this.$message.error('请选择产品代码');
              return false;
          }
        let vm=this;
        this.$http
            .post(`${this.proxyUrl}/user/switchAccount/addAccount`,this.addReq ,{ emulateJSON: true })
            .then((res)=>{
              if (res.data.status == 0) {
                  this.addReq.phone = '';
                  this.addReq.proId = '';
                  this.mealName = '';
              this.$message.success(res.data.resText);
              //vm.$router.push({path: '/list'});
            } else {
              this.$message.error(res.data.resText);
            }
            })
    },
    handleCloseConfirm(value){
      console.log(value);
    }
  }
};
</script>
<style scoped>
.user-title {
     padding: 10px 0px 0px 0px;
}
  label{
    font-size:16px !important;
  }
.fl {
  float: left;
}
.user-search2 {
  width: 40%;
  margin-left: 3%;
  margin-top: 3%;
}
</style>
