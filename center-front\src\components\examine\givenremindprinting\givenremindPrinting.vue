<template>
  <div>
    <div v-show="list">
      <h1 class="user-title">特定号码提醒模板</h1>
      <div class="user-line"></div>
      <div class="app-search">
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="模版ID">
            <el-input v-model="print.companyId"  placeholder="企业编号"></el-input>
          </el-form-item>
          <el-form-item label="模版名称">
            <el-input v-model="print.companyName" placeholder="模版名称"></el-input>
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="print.provinceCode" placeholder="请选择">
              <el-option
                      v-for="item in provinceList"
                      :key="item.provinceCode"
                      :label="item.provinceName"
                      :value="item.provinceCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-form :inline="true" class="demo-form-inline">
          <el-form-item label="省份">
            <el-select v-model="print.provinceCode" placeholder="请选择">
              <el-option
                      v-for="item in provinceList"
                      :key="item.provinceCode"
                      :label="item.provinceName"
                      :value="item.provinceCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="地市">
            <el-select v-model="print.provinceCode" placeholder="请选择">
              <el-option
                      v-for="item in provinceList"
                      :key="item.provinceCode"
                      :label="item.provinceName"
                      :value="item.provinceCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="审核时间">
            <el-date-picker
                    type="daterange"
                    v-model="print.subTime"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit(print)" class="app-btn">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div id="printingtable">
        <el-table
                :data="tableData"
                border tooltip-effect="dark"
                style="width: 98%;margin-left: 10px;">
          <el-table-column
                  prop="svNumber"
                  label="模板ID">
          </el-table-column>
          <el-table-column
                  prop="svName"
                  label="模板名称">
          </el-table-column>
          <el-table-column
                  prop="svName"
                  label="分类">
          </el-table-column>
          <el-table-column
                  prop="svName"
                  label="省份">
          </el-table-column>
          <el-table-column
                  prop="svName"
                  label="地市">
          </el-table-column>
          <el-table-column
                  label="模板内容">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="dialogVisible=true;rowData=scope.row;">内容详情</el-button>
            </template>
          </el-table-column>
          <el-table-column
                  prop="groupName"
                  label="描述">
          </el-table-column>
          <el-table-column
                  prop="labelName"
                  label="提交时间">
          </el-table-column>
          <el-table-column
                  prop="submitTime"
                  label="审核意见">
          </el-table-column>
          <el-table-column
                  prop="assessor"
                  label="审核人">
          </el-table-column>
          <el-table-column
                  prop="svCause"
                  label="驳回原因">
          </el-table-column>
          <el-table-column
                  prop="svSssesstime"
                  label="审核时间">
          </el-table-column>
          <el-table-column
                  label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="upList">编辑</el-button>
              <el-button type="text" size="small" @click="deletebtn(scope.row)" >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="block app-pageganit">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="tableData.pageNum"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pageTotal"  style="text-align: right;">
        </el-pagination>
      </div>
    </div>
    <!--编辑-->
    <div v-show="!list">
      <upgivenremindPrinting :provinceList="provinceList" @upList="upList"></upgivenremindPrinting>
    </div>
    <!--查看详情-->
    <el-dialog
            title="模版详情"
            :visible.sync="dialogVisible"
            width="40%">
      <ul class="detbox">
        <li>
          <p>拨打号码短信提醒内容:</p>
          <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
        </li>
        <li>
          <p>拨打号码短信提醒内容:</p>
          <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
        </li>
        <li>
          <p>拨打号码短信提醒内容:</p>
          <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
        </li>
      </ul>
    </el-dialog>
  </div>
</template>
<script>
    import upgivenremindPrinting from './upgivenremindPrinting'
    export default {
        name: 'UserList',
        data() {
            return {
                list:true,
                dialogVisible:false,
                pageTotal:1,
                provinceList:[{ provinceCode: '', provinceName: '全部'},
                    { provinceCode: '01', provinceName: '北京'},
                    { provinceCode: '02', provinceName: '天津'},
                    { provinceCode: '03', provinceName: '河北'},
                    { provinceCode: '04', provinceName: '山西'},
                    { provinceCode: '05', provinceName: '内蒙古'},
                    { provinceCode: '06', provinceName: '辽宁'},
                    { provinceCode: '07', provinceName: '吉林'},
                    { provinceCode: '08', provinceName: '黑龙江'},
                    { provinceCode: '09', provinceName: '上海'},
                    { provinceCode: '10', provinceName: '江苏'},
                    { provinceCode: '11', provinceName: '浙江'},
                    { provinceCode: '12', provinceName: '安徽'},
                    { provinceCode: '13', provinceName: '福建'},
                    { provinceCode: '14', provinceName: '江西'},
                    { provinceCode: '15', provinceName: '山东'},
                    { provinceCode: '16', provinceName: '河南'},
                    { provinceCode: '17', provinceName: '湖北'},
                    { provinceCode: '18', provinceName: '湖南'},
                    { provinceCode: '19', provinceName: '广东'},
                    { provinceCode: '20', provinceName: '海南'},
                    { provinceCode: '21', provinceName: '广西'},
                    { provinceCode: '22', provinceName: '重庆'},
                    { provinceCode: '23', provinceName: '四川'},
                    { provinceCode: '24', provinceName: '贵州'},
                    { provinceCode: '25', provinceName: '云南'},
                    { provinceCode: '26', provinceName: '陕西'},
                    { provinceCode: '27', provinceName: '甘肃'},
                    { provinceCode: '28', provinceName: '青海'},
                    { provinceCode: '29', provinceName: '宁夏'},
                    { provinceCode: '30', provinceName: '新疆'},
                    { provinceCode: '31', provinceName: '西藏'}
                ],
                print:{
                    printId:"",
                    printContent:"",
                    subTime:"",
                    currentPage:4,
                    province:"",
                    region:"",
                    companyName:"",
                    companyId:"",
                    total:400,
                    pageSize:100
                },
                tableData: [
                    {
                        date: '文本彩印',
                        name: '王小虎',
                        province: '上海',
                        city: '普陀区',
                        address: '上海市普陀区金沙江路 1518 弄',
                        zip: 200333
                    }
                ],
                value6: ''

            }
        },
        methods: {
            upList(){
                let vm = this;
                vm.list = !vm.list;
            },
            //删除
            deletebtn() {
                this.$confirm('确定删除此项？', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                }).then(() => {
                    this.$message({
                    type: 'success',
                    message: '删除成功!'
                });
            }).catch(() => {
                    this.$message({
                    type: 'info',
                    message: '已取消删除'
                });
            });
            },
            handleSizeChange(val) {
                this.print.pageSize=val;
                this.onSubmit(this.print);
                console.log(`每页 ${val} 条`);
            },
            handleCurrentChange(val) {
                this.print.currentPage=val;
                this.onSubmit(this.print);
                console.log(`当前页: ${val}`);
            },
            onSubmit(val) {
                console.log(val.companyName);
                this.$http.post('/caiyin/examine/remindPrinting/remindPrintingQuery', val).then((response) => {
                    console.log(response.data);
            }, (response) => {
                    this.$notify.error({
                        title: '错误',
                        message: '查询异常'
                    });
                });
            },
            delRemindPrinting(val){
                console.log(val);
                this.$http.post('/caiyin/examine/remindPrinting/delRemindPrinting', val).then((response) => {
                    console.log(response.data);
                this.$notify({
                    title: '成功',
                    message: '删除成功',
                    type: 'success'
                });
            }, (response) => {
                    this.$notify.error({
                        title: '错误',
                        message: '删除失败'
                    });
                });
            }
        },
        created() {
        },
        components: {
            upgivenremindPrinting
        }
    }


</script>
<style>


  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
  .user-line{
    margin-top: 3%;
    background-color: blue;;
  }

  .user-search{
    width: 100%;
    margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }

</style>
