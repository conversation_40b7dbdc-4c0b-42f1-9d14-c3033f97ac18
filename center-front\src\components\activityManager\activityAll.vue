<template>
    <div>
        <h1 class="user-title">活动统计</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="活动ID">
                    <el-input v-model="searchReq.activityId" size="small"></el-input>
                </el-form-item>
                <el-form-item label="活动名称">
                    <el-input v-model="searchReq.activityName" size="small"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="pageQuery()" size="small" class="app-bnt">查询</el-button>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                    <el-button type="primary" @click="propVisible=true" size="small" class="app-bnt">新建活动</el-button>
                    <el-button size="small" type="primary" @click="exportex" plain>导出excel表</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table
                :data="tableData"
                border class="app-tab">
            <el-table-column
                    width="150"
                    prop="activityId"
                    label="活动ID">
            </el-table-column>
            <el-table-column
                    prop="activityName"
                    width="150"
                    label="活动名称">
            </el-table-column>
            <el-table-column
                    prop="createTime"
                    width="180"
                    label="创建时间">
            </el-table-column>
            <el-table-column label="订购用户数">
                <el-table-column
                        prop="packageName"
                        label="套餐名称"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="packageUserCount"
                        label="用户数"
                        width="100">
                </el-table-column>
            </el-table-column>
            <el-table-column label="设置彩印用户数">
                <el-table-column
                        prop="txtUserCount"
                        label="系统文本"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="diyUserCount"
                        label="DIY彩印"
                        width="100">
                </el-table-column>
            </el-table-column>
            <el-table-column
                    prop="browerCount"
                    width="100"
                    label="浏览次数">
            </el-table-column>
            <el-table-column
                    width="100"
                    prop="loginUserCount"
                    label="登录用户数">
            </el-table-column>
            <el-table-column
                    prop="shareCount"
                    width="100"
                    label="分享次数">
            </el-table-column>
            <el-table-column
                    fixed="right"
                    label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="ceratactive(scope.row)" size="small">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.pageNum"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageTotal"  style="text-align: right;">
            </el-pagination>
        </div>

        <el-dialog
                title="新建活动"
                :visible.sync="propVisible"
                :close-on-click-modal="false"
                width="30%">
            <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReq">
                <el-form-item label="活动名称" prop="activityName">
                    <el-input v-model="addReq.activityName"
                              type="input" size="small"
                              placeholder="" style="width: 90%;"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer" style="text-align: center;">
                <el-button type="primary" size="small" @click="newactivity">提交</el-button>
            </div>
        </el-dialog>
        <el-dialog
                title="编辑活动"
                :visible.sync="editVisible"
                :close-on-click-modal="false"
                width="30%">
            <el-form label-width="80px" justify="center" :model="editReq" :rules="rules" ref="editReq">
                <el-form-item label="活动ID">
                    {{editReq.activityId}}
                </el-form-item>
                <el-form-item label="活动名称" prop="activityName">
                    <el-input v-model="editReq.activityName"
                              type="input" size="small"
                              placeholder="" style="width: 90%;"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer" style="text-align: center;">
                <el-button type="primary" size="small" @click="editctivity">提交</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script type="text/ecmascript-6">
    import {dowandFile} from '@/util/core.js';
    import {postDownload} from '@/servers/httpServer.js';
    export default {
        name: 'userPush',
        data() {
            return {
                propVisible:false,
                editVisible:false,
                pageTotal:0,
                tableData:[],
                searchReq:{
                    activityId:'',
                    activityName:'',
                    pageNum:1,
                    pageSize:10
                },
                addReq:{
                    activityName:''
                },
                rules:{
                    activityName: [
                        { required: true, message: '请输入活动名称', trigger: 'blur' },
                    ]
                },
                editReq:{
                    activityId:'',
                    activityName:''
                }
            }
        },
        created(){
            this.pageQuery();
        },
        watch:{
            propVisible(){
                if(!this.propVisible){
                    this.addReq.activityName = '';
                    this.resetForm('addReq');
                }
            },
            editVisible(){
                if(!this.editVisible){
                    this.editReq.activityName = '';
                    this.resetForm('editReq');
                }
            }
        },
        methods:{
            //查询请求
            pageQuery(){
                this.$http
                    .post(`${this.proxyUrl}/content/act/query`,JSON.stringify(this.searchReq),{emulateJSON:true})
                    .then(function(res){
                        let data = res.data;
                        if(data.resStatus==0){
                            this.tableData=data.datas;
                            this.tableData.forEach(item=>{
                                item.createTime = this.formatDateTime(item.createTime)
                            })
                            this.pageTotal=data.pageTotal;
                        }
                    })
            },
            //新建活动
            newactivity(){
                let vm = this;
                this.$refs['addReq'].validate((valid) => {
                    if (valid) {
                        this.propVisible = !this.propVisible;
                        this.$http.post(`${this.proxyUrl}/content/act/create`,JSON.stringify(this.addReq),{emulateJSON:true}).then(function (res) {
                            let data = res.data;
                            if(data.resStatus==0){
                                vm.$message.success(data.resText);
                                this.pageQuery();
                            }else{
                                vm.$message.error("新增失败");
                            }
                        })
                    } else {
                        return false;
                    }
                })
            },
            ceratactive(row){
                this.editVisible = !this.editVisible;
                this.editReq={
                    activityId:row.activityId,
                    activityName:row.activityName
                };
            },
            //编辑活动
            editctivity(){
                let vm = this;
                this.$refs['editReq'].validate((valid) => {
                    if (valid) {
                        this.editVisible = !this.editVisible;
                        this.$http.post(`${this.proxyUrl}/content/act/mod`,JSON.stringify(this.editReq),{emulateJSON:true}).then(function (res) {
                            let data = res.data;
                            if(data.resStatus==0){
                                vm.$message.success(data.resText);
                                this.pageQuery();
                            }else{
                                vm.$message.error("编辑失败");
                            }
                        })
                    } else {
                        return false;
                    }
                })

            },
            //导出
            exportex(){
                postDownload('/content/act/export',JSON.stringify(this.searchReq))
                    .then(function(res){
                        dowandFile(res.data,'活动统计.xlsx');
                    })
            },
            //时间戳转换
            formatDateTime(timeStamp) {
                var date = new Date();
                date.setTime(timeStamp);
                var y = date.getFullYear();
                var m = date.getMonth() + 1;
                m = m < 10 ? ('0' + m) : m;
                var d = date.getDate();
                d = d < 10 ? ('0' + d) : d;
                var h = date.getHours();
                h = h < 10 ? ('0' + h) : h;
                var minute = date.getMinutes();
                var second = date.getSeconds();
                minute = minute < 10 ? ('0' + minute) : minute;
                second = second < 10 ? ('0' + second) : second;
                return y + '-' + m + '-' + d+' '+h+':'+minute+':'+second;
            },
            //分页
            handleSizeChange(val) {
                this.searchReq.pageSize=val;
                this.pageQuery();
            },
            handleCurrentChange(val) {
                this.searchReq.pageNum=val;
                this.pageQuery();
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        },
        mounted() {
            //this.pageQuery();
        },
    }


</script>
<style scoped>
    .user-title{
        padding: 10px 0px 0px 0px;
    }
    .user-line{
        width:100%;
        margin:0 auto;
        margin-top: 3%;
        border-bottom: 1px solid #DDDFE6;
    }
    .user-search{
        width: 100%;
    }
    .user-push-serch {
        margin-top: 10px;
        padding-left:24px !important;
    }
    .el-table{
        border:1px solid #ECEBE9;
        margin: 0 auto;
        width:99%;
    }
    .el-th {
        border-right: 1px solid #DDDFE6 !important;
    }
</style>
