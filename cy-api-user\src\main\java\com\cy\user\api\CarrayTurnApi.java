package com.cy.user.api;

import com.cy.user.model.CsCarryTurnModel;
import com.cy.user.model.CsCarryTurnRecord;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

public interface CarrayTurnApi {


	@RequestMapping(value = "/save/CarrayTurnInfos")
	int insertCarrayTurnInfos(@RequestBody CsCarryTurnModel model);

	@RequestMapping(value = "/get/CarrayTurnInfos")
	List<CsCarryTurnRecord> getCarrayTurnInfos(@RequestBody CsCarryTurnModel model);

	@RequestMapping(value = "/get/CarrayTurnCount")
	int carrayTurnCount(@RequestBody CsCarryTurnModel model);

}
