package com.cy.api;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.cy.common.ResultCommon;
import com.cy.common.ResultListCommon;
import com.cy.common.SysRoleCommon;
import com.cy.model.SysResourcesModel;
import com.cy.model.SysRoleModel;

public interface SysRole {

	@RequestMapping(value = "/sysRoleCore/getSysRoleDetail")
	SysRoleModel getSysRoleDetail(SysRoleCommon common);

	/**
	 * 获取角色列表
	 */
	@RequestMapping(value = "/sysRoleCore/getSysRoleList")
	ResultListCommon getSysRoleList(SysRoleCommon common);

	
	/**
	 * 获取角色列表
	 */
	@RequestMapping(value = "/sysRoleCore/getSysAllRoleList")
	List<SysRoleModel> getSysAllRoleList();
	
	
	/**
	 * 角色修改
	 */
	@RequestMapping(value = "/sysRoleCore/updateSysRole")
	ResultCommon updateSysRole(SysRoleCommon common);

	/**
	 * 删除角色,角色表软删除，关系表真删除
	 */
	@RequestMapping(value = "/sysRoleCore/deleteSysRole")
	ResultCommon deleteSysRole(SysRoleCommon common);

	/**
	 * 角色添加
	 */
	@RequestMapping(value = "/sysRoleCore/addSysRole")
	ResultCommon addSysRole(SysRoleCommon common);

	/**
	 * 获取资源列表
	 */
	@RequestMapping(value = "/sysRoleCore/getAllSysResources")
	SysResourcesModel getAllSysResources();
	
	
	@RequestMapping(value = "/sysRoleCore/findRoleResources", method = RequestMethod.GET)
	public List<SysRoleModel> findRoleResources(@RequestParam("id") int id);
	
	@RequestMapping(value = "/sysRoleCore/isExistRoleName")
	public int isExistRoleName(SysRoleCommon common);
	

}
