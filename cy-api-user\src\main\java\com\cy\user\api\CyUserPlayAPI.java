package com.cy.user.api;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cy.user.model.CyUserPlayModel;

public interface CyUserPlayAPI {

	@RequestMapping(value = "/cyplayservice/getCyUserPlaysByMsisdn")
	List<CyUserPlayModel> getCyUserPlaysByMsisdn(@RequestParam("msisdn") String msisdn,
			@RequestParam("pageNum") Integer pageNum, @RequestParam("pageSize") Integer pageSize);

	@RequestMapping(value = "/cyplayservice/getCyUserPlayByContentCode")
	CyUserPlayModel getCyUserPlayByContentCode(@RequestParam("userId") String userId,
			@RequestParam("contentCode") String contentCode);

}
