package com.cs.param.constants;

/**
 * 
 * PAE公共结果码接口定义类
 * 
 * <AUTHOR>
 * @version [版本号, 2016年4月5日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public interface CommonResultCode
{
    /*-----------------公共返回码  Begin --------------------*/
    // 成功
    public static final String SUCCESS = "00000000";
    
    
    // 版本不一致
    public static final String PAE_VERSION_UNMATCH = "********";
    
    // 未授权的接口调用
    public static final String UNAUTHORIZATION_ACCESS = "********";
    
    // 认证码错误
    public static final String AUTHENTICATORSOURCE_ERROR = "********";
    
    // Timestamp时间戳已经过期
    public static final String TIMESTAMP_EXPIRED = "********";
    
    // 非法的连接源IP地址
    public static final String ILLEGAL_SOURCE = "********";
    
    // 接入账号已经失效
    public static final String ACCESSACCOUNT_EXPIRED = "********";
    
    // 该接口的业务请求已超SLA请求速率上限
    public static final String INTERFACE_SLA_LIMIT = "********";
    
    // 该账号对该接口的业务请求已超SLA请求速率上限
    public static final String ACCOUNT_INTERFACE_SLA_LIMIT = "********";
    
    // 该账号的业务请求已超SLA请求速率上限
    public static final String ACCOUNT_SLA_LIMIT = "********";
    
    // 该接口的业务请求已超每日调用最大次数
    public static final String INTERFACE_COUNT_LIMIT = "********";
    
    // 该账号的业务请求已超每日调用最大次数
    public static final String ACCOUNT_COUNT_LIMIT = "********";
    
    // 请求消息格式错误
    public static final String PARAMETER_ERROR = "********";
    
    // 系统内部处理异常
    public static final String SYSTEM_ERROR = "********";
    
    // 系统内部处理异常for PAE迁移接口
    public static final String SYSTEM_ERROR_PAE = "***********";
    
    // 供应商信息不存在变更，无需导入
    public static final String VENDOR_NOT_CHANGE = "********";
    
    // 参数格式错误
    public static final String ILLEGAL_PARAMETER = "********";
    
    /*-----------------公共返回码 End --------------------*/
    
    /*---------------UDC 返回成功结果码 Begin-------------*/
    public static final String UDC_SUCCESS = "0";
    /*---------------UDC 返回成功结果码 Begin-------------*/
    
    public static final String TSG_SUCCESS = "0";
    
    public static final String GW_SUCCESS = "0";
    
    public static final String GW_ERROR = "********";

    /**
     * 统一存储没有返回码默认成功 
     */
    public static final String UNISTORAGE_SUCCESS = "0000";
    /**
     * 统一存储返回码系统错误 
     */
    public static final String UNISTORAGE_SYSERROR = "9999";
    // 成功(统一日志响应码)
    public static final String FINAL_SUCCESS = "RC00000000";
    
    // 请求消息格式错误(统一日志响应码)
    public static final String FINAL_PARAMETER_ERROR = "RC********";
    
    //接口鉴权失败(统一日志响应码)
    public static final String FINAL_INTERFACE_ERROR = "RC********";
    
    //业务错误(统一日志响应码)
    public static final String FINAL_SERVICE_ERROR = "RC********";
    
    // 系统内部处理异常(统一日志响应码)
    public static final String FINAL_SYSTEM_ERROR = "RC********";
}
