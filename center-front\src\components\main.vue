
<template>
    <el-container class="home-container">
        <el-header class="home-header" style="height: 8vh">
            <span class="home_title">和彩印中央管理平台</span>
          <div class="tips_data scroll ticker-title" style="width: 70%;color: #F7F7F7" title="点击刷新" @click="menuListQuery">
            <p class="animate">
              <span class="el-dropdown-link " v-show="contentTipsInfo.personNum !=null">
              个人彩印待审核内容：{{contentTipsInfo.personNum||0}}条；
              </span>
              <span class="el-dropdown-link" v-show="contentTipsInfo.eFCompanyPrintNum!=null || contentTipsInfo.eFCardPrintNum!=null||contentTipsInfo.eFHotlinePrintNum!=null||contentTipsInfo.eFAdPrintNum!=null||contentTipsInfo.eFEnhanceMassPrintNum!=null||contentTipsInfo.eFHotlineGdTemplatePrintNum!=null">
                  企业彩印初审待审核内容：名片彩印：{{contentTipsInfo.eFCompanyPrintNum||0}}条，名片挂彩彩印：{{contentTipsInfo.eFCardPrintNum||0}}条，热线彩印：{{contentTipsInfo.eFHotlinePrintNum||0}}条，广告彩印：{{contentTipsInfo.eFAdPrintNum||0}}条，企业通知：{{contentTipsInfo.eFEnhanceMassPrintNum||0}}条，热线彩印固定模板：{{contentTipsInfo.eFHotlineGdTemplatePrintNum||0}}条；
                </span>
              <span class="el-dropdown-link " v-show="contentTipsInfo.eRCompanyPrintNum!=null||contentTipsInfo.eRCardPrintNum!=null||contentTipsInfo.eRHotlinePrintNum!=null||contentTipsInfo.eRAdPrintNum!=null||contentTipsInfo.eREnhanceMassPrintNum!=null||contentTipsInfo.eRHotlineGdTemplatePrintNum!=null">
                  企业彩印复审待审核内容：名片彩印：{{contentTipsInfo.eRCompanyPrintNum||0}}条，名片挂彩彩印：{{contentTipsInfo.eRCardPrintNum||0}}条，热线彩印：{{contentTipsInfo.eRHotlinePrintNum||0}}条，广告彩印：{{contentTipsInfo.eRAdPrintNum||0}}条，企业通知：{{contentTipsInfo.eREnhanceMassPrintNum||0}}条，热线彩印固定模板：{{contentTipsInfo.eRHotlineGdTemplatePrintNum||0}}条；
                </span>
            </p>

          </div>
            <el-dropdown @command="handleCommand">
          <span class="el-dropdown-link home_userinfo">
            {{userInfo.sysUserName}}<i class="el-icon-arrow-down el-icon--right"></i>
          </span>
                <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="password">修改密码</el-dropdown-item>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </el-header>
        <el-container>
            <el-aside class="home-aside" width="236px" style="height: 92vh;overflow: auto;overflow-x: hidden;">
                <div>
                    <menu-component></menu-component>
                </div>
            </el-aside>
            <!--操作区域-->
            <el-main style="padding: 2vh;height: 88vh">
                <div class="main">
                    <router-view></router-view>
                </div>
            </el-main>


            <!-- 弹窗 -->
            <div>
                <el-dialog
                        title="修改密码"
                        :visible.sync="this.modPasswordVisible"
                        append-to-body
                        :show-close="false">
                    <el-form class="demo-form-inline" label-width="25%"  style="width: 80%">
                        <el-form-item label="旧密码">
                            <el-input v-model="modForm.oldPassword" :minlength="6" :maxlength="16" type="password" class="app-bottom" size="small"></el-input>
                        </el-form-item>
                        <el-form-item label="新密码">
                            <el-input v-model="modForm.newPassword" :minlength="6" :maxlength="16" type="password" class="app-bottom" size="small"></el-input>
                        </el-form-item>
                        <el-form-item label="确认新密码">
                            <el-input v-model="modForm.newPassword2" :minlength="6" :maxlength="16" type="password" size="small"></el-input>
                        </el-form-item>
                    </el-form>
                    <div slot="footer" style="text-align: right;margin-left:186px;">
                        <el-button @click="modPasswordVisible = false" size="small">取 消</el-button>
                        <el-button type="primary" @click="mod()" size="small">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
        </el-container>
    </el-container>
</template>
<script>
    import menuComponent from '@/components/common/menu';
    import md5 from 'blueimp-md5';
    import { encrypt } from './../util/rsa.js'
    import {resetToken} from '../service/common/tokenProcess'
    import {post} from './../servers/httpServer.js';
    //import crypto from './../util/crypto.js';
    import Vue from 'vue'
    import { mapState } from 'vuex'

    export default {
      computed:{
        ...mapState([
            'menuList'
        ])
      },
        components:{
            menuComponent
        },
        data() {
            return {
                userInfo:JSON.parse(sessionStorage.getItem('userInfo')),
                modPasswordVisible:false,
                modForm: {
                    oldPassword: "",
                    newPassword: "",
                    newPassword2: ""
                },
                contentTipsInfo:{
                  personNum: null,
                  eFCompanyPrintNum:null,
                  eFHotlinePrintNum:null,
                  eFAdPrintNum:null,
                  eFEnhanceMassPrintNum:null,
                  eFCardPrintNum:null,
                  eFHotlineGdTemplatePrintNum:null,
                  eRCompanyPrintNum:null,
                  eRHotlinePrintNum:null,
                  eRAdPrintNum:null,
                  eREnhanceMassPrintNum:null,
                  eRCardPrintNum:null,
                  eRHotlineGdTemplatePrintNum:null,
                },
                dataConfig: {
                  40:{"url":"/content/auditText/getAuditText","isAsync":true,"params":{"isBatch":1,"svMold":1,"pageSize":1,"pageNum":1},"numField":"personNum","isPostForm":true},
                  41:{"url":"/content/auditText/getAuditText","isAsync":true,"params":{"isBatch":2,"svMold":1,"pageSize":1,"pageNum":1},"numField":"personNum","isPostForm":true},
                  93:{"url":"/content/auditText/getAuditText","isAsync":true,"params":{"svMold":2,"pageSize":1,"pageNum":1},"numField":"personNum","isPostForm":true},
                  72:{"url":"/entContent/corp/content/query","isAsync":false,"params":{"status":0,"pageSize":1,"pageNum":1},"numField":"eFCompanyPrintNum"},
                  104:{"url":"/entContent/corp/hangup/content/query","isAsync":false,"params":{"pageIndex":1,"p":1,"pz":1},"numField":"eFCardPrintNum"},
                  105:{"url":"/entContent/corp/hot/content/query","isAsync":false,"params":{"p":1,"pz":1},"numField":"eFHotlinePrintNum"},
                  110:{"url":"/entContent/corp/hangup/supper/query","isAsync":false,"params":{"p":1,"pz":1},"numField":"eFEnhanceMassPrintNum"},
                  106:{"url":"/entContent/corp/ad/hangup/query","isAsync":false,"params":{"p":1,"pz":1},"numField":"eFAdPrintNum"},
                  124:{"url":"/entContent/corp/hot/content/template/query","isAsync":false,"params":{"p":1,"pz":1},"numField":"eFHotlineGdTemplatePrintNum"},
                  139:{"url":"/entContent/corp/content/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eRCompanyPrintNum"},
                  140:{"url":"/entContent/corp/hangup/content/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eRCardPrintNum"},
                  141:{"url":"/entContent/corp/hot/content/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eRHotlinePrintNum"},
                  142:{"url":"/entContent/corp/hangup/supper/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eREnhanceMassPrintNum"},
                  143:{"url":"/entContent/corp/ad/hangup/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eRAdPrintNum"},
                  144:{"url":"/entContent/corp/hot/content/template/query","isAsync":false,"params":{"status":4,"p":1,"pz":1},"numField":"eRHotlineGdTemplatePrintNum"},
                }
            };
        },
        beforeMount(){
            this.queryProvince();
            this.menuListQuery();
            this.start();
        },
        methods:{
            start(){
              this.timer = setInterval(this.menuListQuery, 600000); // 注意: 第一个参数为方法名的时候不要加括号;
            },

            //展示小红点
          showRedDot(){
            let contentTipsInfo = this.contentTipsInfo
            let menuList= this.menuList;
            menuList.forEach(item => {
              if(item.children){
                item.children.forEach(item2 => {
                  if(item2.children){
                    item2.children.forEach(item3 => {
                      if(item3.children){
                        item3.children.forEach(item4 => {
                          if(item4.id === 40){
                            Vue.set(item4,'show',contentTipsInfo.personNum > 0);
                          }
                          if(item4.id === 72){
                            Vue.set(item4,'show',contentTipsInfo.eFCompanyPrintNum > 0);
                          }
                          if(item4.id === 104){
                            Vue.set(item4,'show',contentTipsInfo.eFCardPrintNum > 0);
                          }
                          if(item4.id === 105){
                            Vue.set(item4,'show',contentTipsInfo.eFHotlinePrintNum > 0);
                          }
                          if(item4.id === 106){
                            Vue.set(item4,'show',contentTipsInfo.eFAdPrintNum > 0);
                          }
                          if(item4.id === 110){
                            Vue.set(item4,'show',contentTipsInfo.eFEnhanceMassPrintNum > 0);
                          }
                          if(item4.id === 139){
                            Vue.set(item4,'show',contentTipsInfo.eRCompanyPrintNum > 0);
                          }
                          if(item4.id === 140){
                            Vue.set(item4,'show',contentTipsInfo.eRCardPrintNum > 0);
                          }
                          if(item4.id === 141){
                            Vue.set(item4,'show',contentTipsInfo.eRHotlinePrintNum > 0);
                          }
                          if(item4.id === 142){
                            Vue.set(item4,'show',contentTipsInfo.eREnhanceMassPrintNum > 0);
                          }
                          if(item4.id === 143){
                            Vue.set(item4,'show',contentTipsInfo.eRAdPrintNum > 0);
                          }
                        })
                      }
                    })
                  }
                })
              }
            })
            console.log('刷新',menuList)
            this.$store.dispatch('setMenuList', menuList);
            // sessionStorage.setItem('menuList', JSON.stringify(menuList));
          },

            menuListQuery() {
              this.$store.dispatch('coreModule/menuListQuery', {}).then(res => {
                if (res.result === 'success') {
                  this.contentTipsInfo = {
                        personNum: null,
                        eFCompanyPrintNum:null,
                        eFHotlinePrintNum:null,
                        eFAdPrintNum:null,
                        eFEnhanceMassPrintNum:null,
                        eFCardPrintNum:null,
                        eFHotlineGdTemplatePrintNum:null,
                        eRCompanyPrintNum:null,
                        eRHotlinePrintNum:null,
                        eRAdPrintNum:null,
                        eREnhanceMassPrintNum:null,
                        eRCardPrintNum:null,
                        eRHotlineGdTemplatePrintNum:null,
                  }
                  this.findTipsData(res.data.list);
                }
              })
            },
            findTipsData(listData) {
              if(listData){
                listData.forEach((item,index) =>{
                  if(this.dataConfig[item.id]){
                    console.log("找到权限" + item.id);
                    this.queryTipsData(this.dataConfig[item.id]);
                  }
                  if((index+1) === listData.length){
                    setTimeout(()=>{this.showRedDot();},1200)
                  }
                  if(item.children!=null && item.children.length > 0){
                    this.findTipsData(item.children)
                  }
                })
              }
            },
            queryTipsData(config){
              let postFunction = post;
              let _this = this;
              var op = {};
              if(config.isPostForm){
                op.emulateJSON = true;
              }
              if(config.postHeader){
                op.headers = config.postHeader;
              }
              this.$http.post(config.url,config.params, op).then(res=>{
                res = res.data;
                var num = 0;
                if(res.totalCount>0){
                  num = res.totalCount;
                }
                if(res.count>0){
                  num = res.count;
                }
                if(res.pageTotal>0){
                  num = res.pageTotal;
                }
                // if(num){
                  console.log("找到数据" + config.numField + "::" + num);
                  _this.contentTipsInfo[config.numField] = (_this.contentTipsInfo[config.numField]||0) + num;
                // }
              });

              // else {
              //   postFunction(config.url,config.params).then(res=>{
              //
              //   })
              // }





            },
            //初始化查询省份列表
            queryProvince(){
                post('/param/regionMgt/getProvince',{}).then(res=>{
                    if(res.status===200){
                        sessionStorage.setItem('provinceList',JSON.stringify(res.data));
                    }
                })
            },
            handleCommand(type){
                if (type === 'logout') {
                    resetToken()
                    this.$router.push('/')
                }else if(type === 'password'){
                    this.modForm.oldPassword = '';
                    this.modForm.newPassword = '';
                    this.modForm.newPassword2 = '';
                    this.modPasswordVisible =true;
                }
            },
            isPwd(val){
                let reg = /^[a-zA-Z][a-zA-Z0-9_!@#$^]{6,16}$/;
                return reg.test(val);
            },
            //修改密码
            mod() {
                if (this.modForm.oldPassword == null || this.modForm.oldPassword == "") {
                    this.$message.error("请输入旧密码");
                    this.modPasswordVisible=true;
                    return false;
                }
                if (this.modForm.newPassword == null || this.modForm.newPassword == "") {
                    this.$message.error("请输入新密码");
                    this.modPasswordVisible=true;
                    return false;
                }

                if(!this.isPwd(this.modForm.newPassword)){
                    this.$message.error("6-16位以字母开头的、由字母、数字或下划线组成、长度最多16位字符");
                    this.overdueVisible=true;
                    return false;
                }
                if (
                    this.modForm.newPassword2 == null ||
                    this.modForm.newPassword2 == ""
                ) {
                    this.$message.error("请再次输入新密码");
                    this.modPasswordVisible=true;
                    return false;
                }
                if (this.modForm.newPassword != this.modForm.newPassword2) {
                    this.$message.error("两次输入的密码不同");
                    this.modPasswordVisible=true;
                    return false;
                }
                this.modForm.newPassword2 = encrypt(this.modForm.newPassword2);

                this.modForm.oldPassword = encrypt(this.modForm.oldPassword);

                this.modForm.newPassword =  encrypt(this.modForm.newPassword);
                this.$http
                    .post(`${this.proxyUrl}/sys/sysUser/sysUserMod`, JSON.stringify(this.modForm))
                    .then(function(res) {
                        if (res.data.resStatus == 0) {
                            this.$message.success("密码修改成功");
                            sessionStorage.removeItem("username");
                            setTimeout(
                                function() {
                                    this.$router.push("/login");
                                }.bind(this),
                                1000
                            );
                            this.modForm.oldPassword='';
                            this.modForm.newPassword='';
                            this.modForm.newPassword2='';
                            modPasswordVisible = false;
                        } else {
                            this.$message.error(res.data.resText);
                            this.modForm.oldPassword='';
                            this.modForm.newPassword='';
                            this.modForm.newPassword2='';
                            modPasswordVisible = true;
                        }
                    });
            }
        }

    }



    // (function($){
    //   $.fn.toScroll = function(options){
    //     //默认配置
    //     var defaults = {
    //       speed:40,//滚动速度,值越大速度越慢
    //       rowHeight:24 //每行的高度
    //     };
    //     var opts = $.extend({}, defaults, options),intId = [];
    //     function marquee(obj, step){
    //       obj.find("ul").animate({
    //         marginTop: '-=1'
    //       },0,function(){
    //         var s = Math.abs(parseInt($(this).css("margin-top")));
    //         if(s >= step){
    //           $(this).find("li").slice(0, 1).appendTo($(this));
    //           $(this).css("margin-top", 0);
    //         }
    //       });
    //     }
    //     this.each(function(i){
    //       var sh = opts["rowHeight"],speed = opts["speed"],_this = $(this);
    //       intId[i] = setInterval(function(){
    //         if(_this.find("ul").height()<=_this.height()){
    //           clearInterval(intId[i]);
    //         }else{
    //           marquee(_this, sh);
    //         }
    //       }, speed);
    //     });
    //   }
    //
    //
    //   $('.scroll').toScroll({
    //     speed: 40, //数值越大，速度越慢
    //     rowHeight: 20 //li的高度
    //   });
    // })(jQuery);
</script>
<style>
  .ticker-title {
    margin-top: 5px;
    padding-bottom: 5px;
    text-transform: uppercase;
    white-space: nowrap;
    height: 30px;
    font-size: 18px;
    line-height: 30px;
    overflow: hidden;
    text-shadow: 0 0 2px #065b70;

  }
  .ticker-title > p.animate {
    display: inline-block;
    margin: 0px;
    color: white;
    white-space: nowrap;
    animation: title 45s infinite linear;
  }
  @keyframes title {
    0% {
      transform: translateX(50%);
      -webkit-transform: translateX(50%);
    }
    100% {
      transform: translateX(-100%);
      -webkit-transform: translateX(-100%);
    }
  }
</style>