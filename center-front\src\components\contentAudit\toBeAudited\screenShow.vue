<template scope="scope">
  <div>
    <div class="user-titler">广告彩印</div>
    <!--广告彩印-->
    <div class="wrap">
      <div class="itemWrap">
        <div class="item itemLeft">彩印ID：</div>
        <div class="item itemRight">{{adDetail.uuid}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">彩漫主题：</div>
        <div class="item itemRight">{{adDetail.activityName}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">屏显内容：</div>
        <div class="item itemRight">{{screenDisplay.content}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">活动规则：</div>
        <div class="item itemRight">{{adDetail.activityRule}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">获奖说明：</div>
        <div class="item itemRight">{{adDetail.awardDescription}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">获奖通知：</div>
        <div class="item itemRight">{{adDetail.awardNotice}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">备注：</div>
        <div class="item itemRight">{{adDetail.comment}}</div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">活动背景图：</div>
        <div class="item itemImg itemRight">
          <div class="minImgBox" v-if="adDetail.bgImage">
            <img :src="adDetail.bgImage" alt class="minImg" @click="_showBigImg(adDetail.bgImage)">            
          </div>
        </div>
      </div>
      <div class="itemWrap">
        <div class="item itemLeft">代言banner图：</div>
        <div class="item itemImg itemRight">
          <el-row>
            <el-col v-for="(item, index) in bannerImages" :key="index">
              <div class="minImgBox" v-if="item.imageLocalPath && (item.imageLocalPath != null)">
                <img
                  :src="item.imageLocalPath"
                  alt
                  class="minImg"
                  @click="_showBigImg(item.imageLocalPath)"
                >
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div class="bottom">
      <el-button size="small" type="primary" @click="back">返回</el-button>
    </div>

    <div v-show="showBigImg" class="dialog-bg" style="showBigImg" @click="_hideBigImg">
      <img :src="bigImg">
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      adDetail: {},
      screenDisplay: {},
      bannerImages: [],
      showBigImg: false,
      bigImg: ""
    };
  },
  created() {
    if(sessionStorage.getItem('adType') == 1) {
      this.$http.get(`${this.proxyUrl}/entContent/corp/ad/hangup/get?uuid=${sessionStorage.getItem('aduuid')}&from=web`).then(function(res){
          this.adDetail = res.data.data || {};
          this.init()
      })
    } else if (sessionStorage.getItem('adType') == 2) {
      this.adDetail = JSON.parse(sessionStorage.getItem("adDetail"));
      this.init()
    }
  },
  watch: {},
  methods: {
    init() {
      this.screenDisplay = this.adDetail.screenDisplay || {};
      this.bannerImages = this.adDetail.bannerImages || [];
      // this.bannerImages = [
      //   {imageLocalPath: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1558695375476&di=279093ce440be1c9ddb626be210d90f4&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201501%2F30%2F20150130131921_kw2sJ.jpeg"},
      //   {imageLocalPath: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1558695375476&di=279093ce440be1c9ddb626be210d90f4&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201501%2F30%2F20150130131921_kw2sJ.jpeg"},
      //   {imageLocalPath: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1558695375476&di=279093ce440be1c9ddb626be210d90f4&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201501%2F30%2F20150130131921_kw2sJ.jpeg"},
      //   {imageLocalPath: "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1558695375476&di=279093ce440be1c9ddb626be210d90f4&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201501%2F30%2F20150130131921_kw2sJ.jpeg"},
      // ]

      // this.adDetail.bgImage =
          // "https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1558695375476&di=279093ce440be1c9ddb626be210d90f4&imgtype=0&src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201501%2F30%2F20150130131921_kw2sJ.jpeg";
    },
    back() {
      window.history.back();
    },
    _showBigImg(src) {
      this.showBigImg = true;
      this.bigImg = src;
    },
    _hideBigImg() {
      this.showBigImg = false;
      this.bigImg = "";
    }
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.wrap {
  padding: 24px;
}

.itemWrap {
  margin: 6px 0;
  font-size: 0;
}

.itemWrap >>> .el-col {
  width: 200px;
}

.itemLeft {
  width: 120px;
  float: left;
  /* text-align: right; */
}

.itemRight {
  margin-left: 120px;
}

.item {
  font-size: 16px;
  min-height: 21px;
}

.itemImg {
  vertical-align: top;
}

.minImgBox{
  width: 160px;
  height: 100px;
  margin-bottom: 20px;
}

.minImg {
	width: auto;
	height: auto;
	max-width: 100%;
	max-height: 100%;
  cursor: pointer;
}

.bottom {
  border-top: 1px solid #d9d9d9;
  line-height: 56px;
  padding-left: 120px;
}

.dialog-bg {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.4);
}

.dialog-bg img {
  height: 90%;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  cursor: pointer;
}
</style>
