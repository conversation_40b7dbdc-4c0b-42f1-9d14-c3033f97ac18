<template>
	<div>
		<div class="user-detail">
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">用户号码:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{userInfo.pkCurUserid}}</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">昵称:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{userInfo.curNick}}
						<i class="el-icon-edit" @click="open('userName', userInfo.curNick)"></i>
					</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">省份:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{userInfo.curProvinceName}}</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">地区:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{userInfo.curAreaName}}</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">当前彩印推送业务:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{transLabel('pushTypeMap')}}
						<i class="el-icon-edit" @click="open('printPush', userInfo.pushTypeMap)"></i>
					</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">彩印接收类型:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{transLabel('recvMode')}}
						<i class="el-icon-edit" @click="open('recvMode', userInfo.recvMode)"></i>
					</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">当前彩印拒接业务:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{transLabel('rejectTypeMap')}}
						<i class="el-icon-edit" @click="open('rejectTypeMap', userInfo.rejectTypeMap)"></i>
					</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">黑白名单状态:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{transLabel('blackWhite')}}
						<i class="el-icon-edit" @click="open('blackWhite', userInfo.blackWhite)"></i>
					</div>
				</el-col>
			</el-row>
			<el-row :gutter="40">
				<el-col :span="5">
					<div class="grid-content bg-purple user-info-label">是否在红名单:</div>
				</el-col>
				<el-col :span="19">
					<div class="grid-content bg-purple">{{userInfo.atRed}}</div>
				</el-col>
			</el-row>
		</div>
		<div>
			<!--修改昵称-->
			<el-dialog title="修改昵称" v-if="modals.visible === 'userName'" :visible.sync="modals.visible === 'userName'" :show-close="false" onclose="modals.visible = ''">
				<el-form>
					<el-form-item label="昵称:">
						<el-input v-model="modals.tempValue"  :maxlength="15" clearable></el-input>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="modals.visible = ''">取 消</el-button>
					<el-button type="primary" @click="updateUserName()">确 定</el-button>
				</div>
			</el-dialog>
			<!--编辑彩印推送功能-->
			<el-dialog :title="modals.visible === 'printPush' ? '编辑彩印推送功能' : '编辑拒收彩印业务'" v-if="modals.visible === 'printPush' || modals.visible === 'rejectTypeMap'" :visible.sync="modals.visible === 'printPush' || modals.visible === 'rejectTypeMap'" :show-close="false" :close-on-click-modal="false">
				<el-form>
					<el-form-item :label="modals.visible === 'printPush' ? '彩印推送功能:' : '拒收彩印业务:'">
						<el-checkbox-group v-model="modals.tempArr">
							<el-checkbox v-for="(value, key, index) in modals.tempValue" v-show="modals.visible === 'printPush' ? index != 1:true" :checked="modals.visible === 'printPush' ? value === '1' : value === '0'" :label="key" :key="index">{{labels.printType[key]}}</el-checkbox>
						</el-checkbox-group>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<p style="text-align: left; font-size: 8px;color: #666666;">注:请选择需要{{modals.visible === 'printPush' ? '推送' : '拒收'}}的业务，未选表示关闭业务{{modals.visible === 'printPush' ? '推送' : '拒收'}}功能。</p>
					<el-button @click="modals.visible = ''">取 消</el-button>
					<el-button type="primary" v-if="modals.visible === 'printPush'" @click="updatePushType()">确 定</el-button>
					<el-button type="primary" v-if="modals.visible !== 'printPush'" @click="updateRejectType()">确 定</el-button>
				</div>
			</el-dialog>
			<!--编辑彩印接收类型-->
			<el-dialog title="编辑彩印接收类型" v-if="modals.visible === 'recvMode'" :visible.sync="modals.visible === 'recvMode'" :show-close="false" :close-on-click-modal="false">
				<el-form>
					<el-form-item label="彩印接收类型:">
						<el-select v-model="modals.tempValue" placeholder="请选择">
							<el-option v-for="(value, key, index) in labels.recvMode" :key="index" :label="value" :value="key">
							</el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="modals.visible = ''">取 消</el-button>
					<el-button type="primary" @click="updateRecvMode()">确 定</el-button>
				</div>
			</el-dialog>
            <!--编辑彩印接收类型-->
			<el-dialog title="编辑黑白名单状态" v-if="modals.visible === 'blackWhite'" :visible.sync="modals.visible === 'blackWhite'" :show-close="false" :close-on-click-modal="false">
				<el-form>
					<el-form-item label="黑白名单状态:">
						<el-select v-model="modals.tempValue" placeholder="请选择">
							<el-option v-for="(value, key, index) in labels.blackWhite" :key="index" :label="value" :value="key">
							</el-option>
						</el-select>
					</el-form-item>
				</el-form>
				<div slot="footer" class="dialog-footer">
					<el-button @click="modals.visible = ''">取 消</el-button>
					<el-button type="primary" @click="updateBlackWhite()">确 定</el-button>
				</div>
			</el-dialog>
		</div>
		<el-dialog
				title="提示"
				:visible.sync="propVisible"
				:close-on-click-modal="false"
				width="30%">
			<span>{{propMsg}}</span>
			<span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
		</el-dialog>
	</div>
</template>
<script>
	export default {
		name: 'UserList',
		data() {
			return {
                propMsg:'',
       			 userId:'',
				userInfo: {},
				labels: {
					printType: {
						personStatus: '个人彩印',
						mediaStatus: '新媒彩印',
						remindStatus: '提醒彩印',
						cdpStatus: '企业彩印'
					},
					recvMode: ['默认', 'USSD短信发送', '免提短信(闪信)发送', '普通短信发送'],
					blackWhite: ['白名单','黑名单', '关闭']
				},
				modals: {
					visible: '',
					nickVisible : false
				},
				propVisible:false,

			}
		},
		methods: {
      //查询用户详情
			query: function(param) {
				this.$http
				  	.post(`${this.proxyUrl}/user/cyuser/getCyUserDetail`, param, {
						emulateJSON: true
					})
					.then(function(res) {
						this.userInfo = res.data;
					})
			},
            transLabel: function(propName) {
				let value = this.userInfo[propName],
					selection = this.labels[propName];
				if(selection === undefined) {
					selection = this.labels.printType;
					let prints = [];
					for(let o in value) {
					    if(propName === 'pushTypeMap'){
                            if(value[o] == 1) {
                                prints.push(selection[o]);
                            }
						}else{
							if(propName === 'rejectTypeMap' && o === 'mediaStatus'){
								
							} else {
								if(value[o] == 0) {
	                                prints.push(selection[o]);
	                            }
							}
						}
					}
					return prints.join('、');
				} else {
                    //console.log(value);
					return selection[value];
				}
			},
			open(type, value) {
					this.modals.visible = type;
					this.$set(this.modals, 'tempArr', []);
					if(type === 'rejectTypeMap'){
						var newValue = {};
						newValue.personStatus = value.personStatus;
						newValue.remindStatus = value.remindStatus;
						newValue.cdpStatus = value.cdpStatus;
						this.$set(this.modals, 'tempValue', JSON.parse(JSON.stringify(newValue)));
					} else {
						this.$set(this.modals, 'tempValue', JSON.parse(JSON.stringify(value)));
					}
			},
			updateUserName() {
				let param = {
                    pkCurUserid: this.userInfo.pkCurUserid,
                    curNickName: this.modals.tempValue
				};
				this.modals.visible = '';
				this.$http.post(`${this.proxyUrl}/user/cyuser/modCustomerNick`, param, {
						emulateJSON: true
					})
					.then(function(res) {
                        //console.log(res.data.status);
                        this.showMessage(res.data.status);
					    if(res.data.status == 0){
                            this.query({
                                pkCurUserid: this.userInfo.pkCurUserid
                            });
						}

					})
			},
			updatePushType() {
				let param = {
                    pkCurUserid: this.userInfo.pkCurUserid
				}
				this.modals.visible = '';
				for(let o in this.modals.tempValue) {
					if(this.modals.tempArr.indexOf(o) !== -1) {
                        param[o] = 1;
					} else {
                        param[o] = 0;
					}
				}
				this.$http.post(`${this.proxyUrl}/user/cyuser/modPushTypes`, param, {
						emulateJSON: true
					})
					.then(function(res) {
                        this.showMessage(res.data.status);
                        if(res.data.status == 0){
                            this.query({
                                pkCurUserid: this.userInfo.pkCurUserid
                            });
                        }
					})
			},
			updateRecvMode() {
				let param = {
                    pkCurUserid: this.userInfo.pkCurUserid,
					recvMode: this.modals.tempValue
				}
				this.modals.visible = '';
				this.$http.post(`${this.proxyUrl}/user/cyuser/modRecvTypes`, param, {
						emulateJSON: true
					})
					.then(function(res) {
                        this.showMessage(res.data.status);
                        if(res.data.status == 0){
                            this.query({
                                pkCurUserid: this.userInfo.pkCurUserid
                            });
                        }
					})
			},
			updateRejectType() {
				let param = {
                    pkCurUserid: this.userInfo.pkCurUserid
				}
				this.modals.visible = '';
				for(let o in this.modals.tempValue) {
					if(this.modals.tempArr.indexOf(o) !== -1) {
						param[o] = 0;
					} else {
						param[o] = 1;
					}
				}
				param['mediaStatus'] = this.userInfo.rejectTypeMap.mediaStatus;
				this.$http.post(`${this.proxyUrl}/user/cyuser/modRejectTypes`, param, {
						emulateJSON: true
					})
					.then(function(res) {
                        this.showMessage(res.data.status);
                        if(res.data.status == 0){
                            this.query({
                                pkCurUserid: this.userInfo.pkCurUserid
                            });
                        }
					})
			},
			updateBlackWhite(){
			        console.info(this.modals.tempValue);
					let param = {
                        pkCurUserid: this.userInfo.pkCurUserid,
						blackWhite: this.modals.tempValue
					}
					this.modals.visible = '';
					this.$http.post(`${this.proxyUrl}/user/cyuser/modBlackWrite`, param, {
							emulateJSON: true
						})
						.then(function(res) {
                            this.showMessage(res.data.status);
                            if(res.data.status == 0){
                                this.query({
                                    pkCurUserid: this.userInfo.pkCurUserid
                                });
                            }
				})
			},
            showMessage(res){
                if(res == 0){
                    this.$message({message:'修改成功',type:'success'});
                }else{
                    this.$message({message:'修改失败',type:'error'});
                }
            },
		},
		mounted() {
			if(this.$route.params.pkCurUserid!=undefined){
				this.query({
                    pkCurUserid: this.$route.params.pkCurUserid
				});
			}
			else{
				this.query({
                    pkCurUserid:sessionStorage.getItem('pkCurUserid')
				});
			}
		},
		components: {}
	}
</script>
<style scoped>
	.user-detail .el-row {
		margin: 15px;
	}

	.user-detail .el-row .el-icon-edit {
		color: #308ee0;
		cursor: pointer;
	}

	.user-info-label {
		text-align: left;
		margin-left: 40px;
		color: #666;
		/*font-weight:bold;*/
	}
</style>