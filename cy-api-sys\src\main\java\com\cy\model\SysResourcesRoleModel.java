
package com.cy.model;

/**
 * 
 * @date 2018年4月20日 - 上午11:36:21
 * @Description 角色资源model
 */
public class SysResourcesRoleModel {
	private static final long serialVersionUID = 1L;
	/**
	 * 角色资源ID
	 */
	private Integer sysResourcesRoleId;
	/**
	 * 资源ID
	 */
	private Integer sysResourcesId;
	/**
	 * 角色ID
	 */
	private Integer sysRoleId;

	public Integer getSysResourcesRoleId() {
		return sysResourcesRoleId;
	}

	public void setSysResourcesRoleId(Integer sysResourcesRoleId) {
		this.sysResourcesRoleId = sysResourcesRoleId;
	}

	public Integer getSysResourcesId() {
		return sysResourcesId;
	}

	public void setSysResourcesId(Integer sysResourcesId) {
		this.sysResourcesId = sysResourcesId;
	}

	public Integer getSysRoleId() {
		return sysRoleId;
	}

	public void setSysRoleId(Integer sysRoleId) {
		this.sysRoleId = sysRoleId;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "SysResourcesRoleModel [sysResourcesRoleId=" + sysResourcesRoleId + ", sysResourcesId=" + sysResourcesId
				+ ", sysRoleId=" + sysRoleId + "]";
	}

}
