package com.cy.user.api;

import com.cy.user.common.CustomerHisCommon;
import com.cy.user.common.CyUserCommon;
import com.cy.user.model.CustomerHisPkgModel;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;



public interface CustomerHisPkgApi {
	
	
	@RequestMapping(value = "/customerhispkgservice/getCustomerPkgHis")
	List<CustomerHisPkgModel> getCustomerPkgHis(CustomerHisCommon common);
	
	
	@RequestMapping(value = "/customerhispkgservice/countCustomerPkgHis")
	int countCustomerPkgHis(CustomerHisCommon common);
	
	
//	@RequestMapping(value = "/customerhispkgservice/unsubscribeCsPkg")
//	int unsubscribeCsPkg(int csId);
	
	
	@RequestMapping(value = "/customerhispkgservice/getCustomerPkg")
	List<CustomerHisPkgModel> getCustomerPkg(CyUserCommon common);
	
	
	@RequestMapping(value = "/customerhispkgservice/isSubTypeBuisness")
	String isSubTypeBuisness(@RequestParam("uid") String uid,@RequestParam("type") String type);
	
	
	@RequestMapping(value = "/customerhispkgservice/getPkgList")
	List<CustomerHisPkgModel> getPkgList(@RequestParam("mid") String mid);


}
