<template>
    <div class="addenterprise">
        <!--<div class="addtitle">新增接入企业</div>-->
        <div class="content">
            <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline app-form-item" size="small" label-width="35%"  style="width: 80%">
                <el-form-item label="企业名称：" prop="companyName">
                    <el-input v-model="addForm.companyName"></el-input>
                </el-form-item>
                <el-form-item label="企业IP：" prop="companyIp">
                    <el-input v-model="addForm.companyIp"></el-input>
                </el-form-item>
                <el-form-item label="API接口名称：" prop="apiId">
                    <el-checkbox-group v-model="addForm.apiId">
                        <el-checkbox v-for="item in ApiCompany" :label="item.apiId" :key="item.apiId">{{item.apiName}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="submit" size="small">取 消</el-button>
            <el-button type="primary" @click="addcompany" size="small">确 定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'addenterprise',
        data(){
            return{
                name:'',
                addForm:{
                    companyName:'',//企业名称
                    companyIp:'',//企业IP
                    apiIds:'',//接口名id
                    apiId:[],//接口id
                },
                ApiCompany:[],//api接口
                rules:{
                    companyName: [
                        { required: true, message: '请输入企业名称', trigger: 'blur' },
                    ],
                    companyIp: [
                        { required: true, message: '请输入企业IP', trigger: 'blur' },
                    ],
                    apiId: [
                        { type: 'array', required: true, message: '请至少选择一个api接口', trigger: 'change' }
                    ]
                }
            }
        },
        components: {},
        created(){
            this.qeuryName();
        },
        props:['addVisible'],
        watch: {
            addVisible() {
                if(!this.addVisible){
                    this.addForm={
                        companyName:'',//企业名称
                        companyIp:'',//企业IP
                        apiIds:'',//接口名id
                        apiId:[],//接口id
                    }
                    this.resetForm('addForm');
                }else{
                    this.qeuryName();
                }
            }
        },
        methods:{
            //查询api接口
            qeuryName(){
                let vm = this;
                postHeader('queryApiSource', JSON.stringify(this.addForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.ApiCompany = data.data.numApiSourceList;
                    }
                })
            },
            //新增接入企业
            addcompany(){
                let vm = this;
                this.$refs['addForm'].validate((valid) => {
                    if(valid){
                        this.addForm.apiIds = this.addForm.apiId.join(',');
                        postHeader('addApiCompany', JSON.stringify(this.addForm)).then(res=>{
                            let data = res.data;
                            if(data.code==0){
                                vm.$message.success(data.data.insertMsg);
                            }else{
                                vm.$message.error("新增失败");
                            }
                            vm.submit();
                        })
                    }else{
                        return false;
                    }
                })
            },
            submit(){
                this.$emit('addList');
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style scoped>
    .addtitle{
        font-size: 18px;
        margin-left: 20px;
        margin-top: -20px;
    }
    .content{
        width: 640px;
        margin: 20px auto;
    }
    .content1{
        text-align: center;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px !important;
        margin-right: 20px;
    }
</style>
