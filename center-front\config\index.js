
'use strict'
// Template version: 1.1.3
// see http://vuejs-templates.github.io/webpack for documentation.

const path = require('path')

module.exports = {
  build: {
    env: require('./prod.env'),
    index: path.resolve(__dirname, '../dist/index.html'),
    assetsRoot: path.resolve(__dirname, '../dist'),
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    productionSourceMap: true,
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],
    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report
  },
  dev: {
    env: require('./dev.env'),
    port: process.env.PORT || 8083,
    autoOpenBrowser: true,
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
      proxyTable: {
        '/api': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/sys': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/user': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/entContent': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/content': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/oper': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/batchset': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/param': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: false,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/cySubject': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: true,
          pathRewrite: {
              '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        },
        '/stat': {
          // target: 'http://**************:19005/', //域名
          target: 'http://localhost:19005/', //域名
          secure: false,
          changeOrigin: true,
          pathRewrite: {
            '^/api': ''//这里理解成用‘/api'代替target里面的地址，后面组件中我们掉接口时直接用api代替
          }
        }
      },
    // In our experience, they generally work as expected,
    // just be aware of this issue when enabling this option.
    cssSourceMap: false
  }
}
