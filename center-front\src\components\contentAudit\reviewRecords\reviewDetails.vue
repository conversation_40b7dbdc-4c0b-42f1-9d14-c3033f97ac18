<template scope="scope">
    <div>
        <h1 class="user-title">审核明细</h1>
        <div class="user-line"></div>
          <!--审核明细表-->
            <div class="app-search">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="类型">
                  <el-select v-model="searchReq.svMold" placeholder="请选择" size="small"> 
                    <el-option
                        v-for="item in statusData"
                        :key="item.csTypeId"
                        :label="item.csTypeName"
                        :value="item.csTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="内容ID">
                  <el-input v-model="searchReq.svNumber" size="small" :maxlength="32" clearable></el-input>
                </el-form-item>
                <el-form-item label="内容">
                  <el-input v-model="searchReq.svCard" size="small" :maxlength="50" clearable></el-input>
                </el-form-item>

                <!-- <el-form-item label="审核人">
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName=='admin'" clearable></el-input>
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName=='shadmin'" clearable></el-input>
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName!='admin' && userInfo.sysUserName!='shadmin'" :disabled="true"></el-input>
                </el-form-item> -->

                <el-form-item label="审核意见">
                  <el-select v-model="searchReq.svStatus" placeholder="请选择" clearable size="small">
                    <el-option
                        v-for="item in auditData"
                        :key="item.auditStatusNo"
                        :label="item.auditStatusName"
                        :value="item.auditStatusNo">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="审核时间">
                  <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px" size="small"
                    />
                </el-form-item>
  
                <br>
                <el-form-item label="审核人">
                  <el-input v-model="searchReq.svAssessor" size="small" clearable></el-input>
                </el-form-item>
                <el-form-item label="提交人">
                  <el-input v-model="searchReq.svSubmitUser" size="small" clearable></el-input>
                </el-form-item>
                
                <el-form-item>
                  <el-button type="primary" @click="searchReq.pageNum = 1;search()" size="small">查询</el-button>
                  <el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
                </el-form-item>
              </el-form>
            </div>

              <div>
                  <el-table
                      v-loading="tableLoading"
                      :data="tableData"
                      border
                      class="app-tab"
                      @selection-change="handleSelectionChange"
                      :header-cell-class-name="tableheaderClassName">
                    <el-table-column
                        prop="svMoldName"
                        label="类型"
                        width="100">
                    </el-table-column>
                    <el-table-column
                        prop="svNumber"
                        label="内容ID"
                        width="240">
                    </el-table-column>
                    <el-table-column
                        label="内容"
                        width="400"
                        :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                          <el-button v-if="(scope.row.svMold==3)" type="text" size="small" @click="detailVisible=true;rowData=scope.row;">{{scope.row.svCard}}</el-button>
                          <span v-if="!(scope.row.svMold==3)">{{scope.row.svCard}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="资质" width="100">
                      <template slot-scope="scope">
                        <el-button type="text" size="small" @click="showOtherImage(scope.row.qualificationsUrlList)"
                                   :style="hasOtherImage(scope.row.qualificationsUrlList)?'':'color: #808080'">详情
                        </el-button>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="submitUser"
                        label="提交人"
                        width="200"  :formatter="formmatPhone">
                    </el-table-column>
                    <el-table-column
                        prop="submitTime"
                        label="提交时间"
                        width="200">
                    </el-table-column>
                    <el-table-column
                        prop="assessor"
                        label="审核人"
                        width="200">
                    </el-table-column>
                    <el-table-column
                        prop="svSssesstime"
                        label="审核时间"
                        width="200">
                    </el-table-column>
                    <el-table-column
                        label="审核意见状态"
                        width="150">
                         <template slot-scope="scope">
                            <span v-show="(scope.row.svStatus==1)">待审批</span>
                            <span v-show="(scope.row.svStatus==2)">审批不通过</span>
                            <span v-show="(scope.row.svStatus==3)">审批通过</span>
                            <span v-show="(scope.row.svStatus==4)">审批不通过</span>
                            <span v-show="(scope.row.svStatus==5)">已撤销</span>
                         </template>
                    </el-table-column>
                    <el-table-column
                        label="联通审核结果"
                        width="150">
                      <template slot-scope="scope">
                        <span v-show="(scope.row.unicomStatus==1)">待审批</span>
                        <span v-show="(scope.row.unicomStatus==2)">审批不通过</span>
                        <span v-show="(scope.row.unicomStatus==3)">审批通过</span>
                        <span v-show="(scope.row.unicomStatus==4)">审批不通过</span>
                        <span v-show="(scope.row.unicomStatus==5)">已撤销</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="unicomMessage"
                        label="联通审核意见"
                        width="200">
                    </el-table-column>
                    <el-table-column
                        label="电信审核结果"
                        width="150">
                      <template slot-scope="scope">
                        <span v-show="(scope.row.telecomStatus==1)">待审批</span>
                        <span v-show="(scope.row.telecomStatus==2)">审批不通过</span>
                        <span v-show="(scope.row.telecomStatus==3)">审批通过</span>
                        <span v-show="(scope.row.telecomStatus==4)">审批不通过</span>
                        <span v-show="(scope.row.telecomStatus==5)">已撤销</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                        prop="telecomMessage"
                        label="电信审核意见"
                        width="200">
                    </el-table-column>
                    <el-table-column
                        prop="svCause"
                        label="驳回原因"
                        width="200"
                        :show-overflow-tooltip="true">
                    </el-table-column>
                      <el-table-column
                              prop="revoke"
                              label="撤销人"
                              width="200">
                      </el-table-column>
                    <el-table-column
                        prop="revokeCause"
                        label="撤销原因"
                        width="200"
                        :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column
                        prop="revokeTime"
                        label="撤销时间"
                        width="200">
                    </el-table-column>
                  </el-table>
              
                  <!-- 分页 -->
                  <div class="block app-pageganit">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="searchReq.pageNum"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageTotal"  style="text-align: right;">
                    </el-pagination>
                  </div>
              </div>
      <el-dialog title="资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
        <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
          <li>
            <a :href="item" target="_blank">资质{{ index + 1 }}</a>
          </li>
        </ul>
      </el-dialog>
            <el-dialog
                        width="40%"
                        title="内容详情"
                        :visible.sync="detailVisible"
                        :close-on-click-modal="false"
                        append-to-body>
                <el-row>
                  <el-col :span="12">彩印ID</el-col>
                  <el-col :span="12">内容</el-col>
                </el-row>
                <div style="height:300px;overflow:auto;">
                  <el-row v-if="rowData.content1">
                    <el-col :span="12"><div v-html="rowData.svNumber+'1'"></div></el-col>
                    <el-col :span="12"><div v-html="highLight(rowData.sensitiveWords,rowData.content1)"></div></el-col>
                  </el-row>

                 <el-row v-if="rowData.content2">
                    <el-col :span="12"><div v-html="rowData.svNumber+'2'"></div></el-col>
                    <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content2)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content3">
                    <el-col :span="12"><div v-html="rowData.svNumber+'3'"></div></el-col>
                    <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content3)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content4">
                    <el-col :span="12"><div v-html="rowData.svNumber+'4'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content4)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content5">
                    <el-col :span="12"><div v-html="rowData.svNumber+'5'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content5)"></div></el-col>
                  </el-row>
                   <el-row v-if="rowData.content6">
                    <el-col :span="12"><div v-html="rowData.svNumber+'6'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content6)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content7">
                    <el-col :span="12"><div v-html="rowData.svNumber+'7'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content7)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content8">
                    <el-col :span="12"><div v-html="rowData.svNumber+'8'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content8)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content9">
                    <el-col :span="12"><div v-html="rowData.svNumber+'9'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content9)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content10">
                    <el-col :span="12"><div v-html="rowData.svNumber+'10'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content10)"></div></el-col>
                  </el-row>
                   <el-row v-if="rowData.content11">
                    <el-col :span="12"><div v-html="rowData.svNumber+'11'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content11)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content12">
                    <el-col :span="12"><div v-html="rowData.svNumber+'12'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content12)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content13">
                    <el-col :span="12"><div v-html="rowData.svNumber+'13'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content13)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content14">
                    <el-col :span="12"><div v-html="rowData.svNumber+'14'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content14)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content15">
                    <el-col :span="12"><div v-html="rowData.svNumber+'15'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content15)"></div></el-col>
                  </el-row>
                   <el-row v-if="rowData.content16">
                    <el-col :span="12"><div v-html="rowData.svNumber+'16'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content16)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content17">
                    <el-col :span="12"><div v-html="rowData.svNumber+'17'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content17)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content18">
                    <el-col :span="12"><div v-html="rowData.svNumber+'18'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content18)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content19">
                    <el-col :span="12"><div v-html="rowData.svNumber+'19'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content19)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content20">
                    <el-col :span="12"><div v-html="rowData.svNumber+'20'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content20)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content21">
                    <el-col :span="12"><div v-html="rowData.svNumber+'21'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content21)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content22">
                    <el-col :span="12"><div v-html="rowData.svNumber+'22'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content22)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content23">
                    <el-col :span="12"><div v-html="rowData.svNumber+'23'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content23)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content24">
                    <el-col :span="12"><div v-html="rowData.svNumber+'24'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content24)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content25">
                    <el-col :span="12"><div v-html="rowData.svNumber+'25'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content25)"></div></el-col>
                  </el-row>
                   <el-row v-if="rowData.content26">
                    <el-col :span="12"><div v-html="rowData.svNumber+'26'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content26)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content27">
                    <el-col :span="12"><div v-html="rowData.svNumber+'27'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content27)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content28">
                    <el-col :span="12"><div v-html="rowData.svNumber+'28'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content28)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content29">
                    <el-col :span="12"><div v-html="rowData.svNumber+'29'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content29)"></div></el-col>
                  </el-row>
                  <el-row v-if="rowData.content30">
                     <el-col :span="12"><div v-html="rowData.svNumber+'30'"></div></el-col>
                     <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content30)"></div></el-col>
                  </el-row>
                </div>
                <div slot="footer" style="text-align: right;">
                  <el-button type="primary" @click="detailVisible = false">确 定</el-button>
                </div>
              </el-dialog>

              <el-dialog
                  @open="exportClick"
                  title="导出"
                  :visible.sync="propVisible"
                  :close-on-click-modal="false"
                  width="45%">
                <el-form label-width="80px" justify="center" :model="searchReq" :rules="rules" ref="addReqForm">
                  <el-form-item label="文件名" prop="exportFileName">
                    <el-input v-model="searchReq.exportFileName" type="input" size="small" placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字" style="width: 90%;"></el-input>
                  </el-form-item>
                  <el-form-item label="备注" prop="remark">
                    <el-input v-model="searchReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256" style="width: 90%;"></el-input>
                  </el-form-item>
                </el-form>
                <div style=" margin-left: 80px; color: red;">
                  导出后请到系统管理-导出文件下载对应文件
                </div>

                <div slot="footer" class="dialog-footer" style="text-align: center;">
                  <el-button type="primary" @click="confirmExport">确定</el-button>
                  <el-button @click="cancelExport">取消</el-button>
                </div>
              </el-dialog>
    </div>
</template>
<script>
import {postDownload} from './../../../servers/httpServer.js';
import {formDate, dowandFile, replacePhone} from './../../../util/core.js';
export default {
  //        name: 'textCS',
  data() {
    return {
      propVisible:false,
      tableLoading: false,
      userInfo:JSON.parse(sessionStorage.getItem('userInfo')),
      pageNum:1,
      pageTotal:0,
      disabled: false,
      statusData:[],//类型下拉栏变量
      auditData:[
                  // {auditStatusNo:'1',auditStatusName:'待审批'},
                  {auditStatusNo:'2',auditStatusName:'审批不通过'},
                  {auditStatusNo:'3',auditStatusName:'审批通过'},
                  // {auditStatusNo:'4',auditStatusName:'失效'},
                  {auditStatusNo:'5',auditStatusName:'已撤销'}],

      rejectReason: "", //驳回原因
      activeName: "passRecords",
      // csTextStatus:[{'1':'上架',"2":'待上架','4':'下架'}],
      checked: [],
      editVisible: false,
      rowData: [],
      detailVisible: false,
      otherImageVisible: false,
      otherImage: [], //其他资质
      // tableData: [
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "1",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "2",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "4",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "1",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   }
      // ],
      tableData:[],
      multipleSelection: [],
      dateTime:[],
      //请求数据
      //查询
      searchReq: {
        svAssessor: "",
        svSubmitUser: "",
        svMold:1,
        svNumber: "",
        svCard : "",
        svStatus:"",
        svAssessor:"",
        startTime: "",
        endTime: "",
        auditStartTime: "",
        auditEndTime: "",
        pageSize: 10,
        pageNum: 1,
        exportFileName:"",
        remark:""
      },
      rules:{
        exportFileName: [
          { required: true, message: '请输入文件名', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
          { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
        ],
        remark: [
          { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
        ]
      },
      request: {},
      delRequest: {
        index: ""
      }
    };
  },
    created(){
        this.search();
    },
  //        created(){
  //          console.log(this.$route.params.index);
  //          this.activeName=this.$route.params.index;
  //        },
  mounted() {
    this.mold();
    //this.audit();
    // if(this.userInfo.sysUserName != "admin" && this.userInfo.sysUserName != "shadmin"){
    //     this.searchReq.svAssessor = this.userInfo.sysUserName;
    //   }
  },
  methods: {
    showOtherImage(otherImage) {
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    hasOtherImage(otherImage) {
      return !(otherImage == null || otherImage.length == 0);
    },
    formmatPhone(row, column) {
      if (row.submitUser != null) {
        return replacePhone(row.submitUser);
      }
    },
    exportClick(){
      this.$refs.addReqForm.resetFields();
    },
    confirmExport(){
        if(this.dateTime && this.dateTime.length>0){
          this.searchReq.auditStartTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          this.searchReq.auditEndTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          this.searchReq.auditStartTime='';
          this.searchReq.auditEndTime='';
        }
      let vm = this;
      this.$refs['addReqForm'].validate((valid) => {
        if (valid) {
          this.propVisible = !this.propVisible;
          this.$http.post(`${this.proxyUrl}/content/auditRecord/exportExeclAuditDetail`,this.searchReq).then(function (res) {
            let data = res.data;
            if(data.code==0){
              vm.$message.success("系统将生成文件名为"+this.searchReq.exportFileName+"的文件");
              // this.pageQuery();  这里可以选个查询
            }else{
              vm.$message.error(data.msg);
            }
          })
        } else {
          return false;
        }
      })
    },
    cancelExport() {
      this.propVisible = !this.propVisible;
      this.$refs.addReqForm.resetFields();
    },
    highLight(sensitiveWords,svCard){
      if(!sensitiveWords || !svCard){
        return svCard;
      }else{
        var specalKey=sensitiveWords.replace(new RegExp(',','g'),'|');
        var scCard=svCard.replace(new RegExp(specalKey,'g'),`<span style="color:red">$&</span>`);
        return scCard;
      }
    },
    addDisable: function(scope) {
      //            console.log(button);
      //            console.log($(`.revoke${scope.$index}`).attr("disabled"));
      //            $(`.revoke${scope.$index}`).attr("disabled");
      const button = document.querySelector(`.revoke${scope.$index}`);
      //              button.disabled=true;
      //            console.log(button.disabled);
      console.log(button);
    },
    check: function(index, row) {
      //              console.log(index,row);
      //              console.log(!(row.csTextStatus==2));
      console.log(this.checked);
    },
    //----------------------已通过审核记录--------------------------
    //类型下拉栏选项请求
    mold() {
      this.$http
        .get(`${this.proxyUrl}/content/csOff/getCsType`, { emulateJSON: true })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.statusData = res.data.datas;
          } else if (res.data.resStatus == "1") {
            console.log("请求失败");
          }
        });
    },
    //审核意见下拉栏选项请求
    // audit() {
    //   this.$http
    //     .get(`${this.proxyUrl}/content/content/getVsStatus`, { emulateJSON: true })
    //     .then(function(res) {
    //       if (res.data.resStatus == "0") {
    //         this.auditData = res.data.datas;
    //       } else if (res.data.resStatus == "1") {
    //         console.log("请求失败");
    //       }
    //     });
    // },
    //多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.checked = true;
      console.log(val);
    },
    download: function() {
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.auditStartTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.auditEndTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.auditStartTime='';
        this.searchReq.auditEndTime='';
      }
      postDownload(`${this.proxyUrl}/content/auditRecord/exportExeclAuditDetail`, this.searchReq).then(function(res) {
            dowandFile(res.data,'审核明细.xlsx');
        });
    },
    //查询请求
    search: function() {
      // console.log(this.userInfo.sysUserName);
      
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.auditStartTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.auditEndTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.auditStartTime='';
        this.searchReq.auditEndTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditRecord/getAuditRecordDetail`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal=res.data.pageTotal;
          this.tableLoading=false;
        });
    },
    //删除请求
    deltr: function() {
      //console.log(!tnis.tableData.csTextStatus == "2");
      //               this.$http
      //                   .post(`${this.proxyUrl}/content/csText/delCsTextInfo`,delRequest,{emulateJSON:true})
      //                   .then(function(res){
      //                     console.log(res);
      //                   })
    },
    handleSizeChange(val) {
      this.searchReq.pageNum = 1;
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.auditStartTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.auditEndTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.auditStartTime='';
        this.searchReq.auditEndTime='';
      }
      this.tableLoading=true;
      this.$http
          .post(`${this.proxyUrl}/content/auditRecord/getAuditRecordDetail`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
          this.tableLoading=false;
      })
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.auditStartTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.auditEndTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.auditStartTime='';
        this.searchReq.auditEndTime='';
      }
      this.tableLoading=true;
       this.$http
          .post(`${this.proxyUrl}/content/auditRecord/getAuditRecordDetail`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
          this.tableLoading=false;
        })
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
  background-color: #F5F5F5;
}
</style>
