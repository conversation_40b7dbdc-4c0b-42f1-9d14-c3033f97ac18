<template>
<div>
    <h1 class="user-title">导入文本素材</h1>
    <hr class="user-line"/>
    <el-form style="width:50%;align:center">
      <el-form-item >
        <el-col :span="11">
          <el-input v-model="fileName" disabled></el-input>
        </el-col>
        <el-col :span="3">
          <a href="javaScript:;"  @click="getTemp();">下载模板文件</a>
        </el-col>
      </el-form-item>
      <el-form-item>
         <el-upload
                class="upload-demo"
                ref="upload"
                action=""
                :auto-upload='false'
                :on-remove='fileRemove'
                :on-change="handleChange"
                accept=".xls, .xlsx">
              <el-button  type="primary" size="small">上传excel表</el-button>
          </el-upload>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  @click="submitForm()" size="small">提 交</el-button>
      </el-form-item>
    </el-form>
</div>
</template>
<script>
import {post} from './../../../../servers/httpServer.js';

export default {
  name: "importText",
  data() {
    return {
      file:'',
      fielName:'',
      fileList:[],
      request: {
        phone: "",
        status: "",
        csContent: "",
        startTime: "",
        endTime: ""
      }
    };
  },
  methods: {
    // 上传移除
    fileRemove(file){

    },
    handleChange(file,fileList){
      this.file=file.raw;
      this.fielName=file.name;
    },
    //提交批量用户信息
    submitForm(){
      if(!this.file){
        this.$message({
          message:'请上传excel文件',
          type:'warning'
        })
        return;
      }
      let formData=new FormData();
      formData.append('file',this.file);
      post(`/content/csText/uploadText`,formData).then(res=>{
        if(res.data.result==='success'){
          this.$message({
            message:'提交成功',
            type:'success'
          })
        }
      })
    },
    //下载模板文件
    getTemp(){
      window.open(`${this.proxyUrl}/content/csText/getTemplate`);
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search2 {
  width: 40%;
  margin: 0 auto;
  margin-top: 3%;
}
/* 弹窗checkbox样式 */
.el-form-item__content{
  margin:0 !important;
}
</style>
