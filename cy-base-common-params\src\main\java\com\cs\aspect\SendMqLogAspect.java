package com.cs.aspect;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.cs.param.constants.Constants;
import com.cs.param.constants.LogConstant;
import com.cs.param.utils.ManualLogUtils;
import com.cs.param.utils.Md5Utils;
import com.cs.param.utils.MqLogUtil;

/**
 * 
 * 发送MQ日志切面
 * 
 * <AUTHOR>
 * @version [版本号, 2022年5月11日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Aspect
@Component
public class SendMqLogAspect
{
	
	private static final String MQ_TYPE = "produce";
	
    /**
     * 检查点配置 resdcm接口
     * 
     * <AUTHOR> 2018年5月16日
     * @see [类、类#方法、类#成员]
     */
    @Pointcut("execution(public * org.springframework.amqp.rabbit.core.RabbitTemplate.convertAndSend(String,Object))")
    public void webLog()
    {
    }
    
    /**
     * 拦截前操作
     * 
     * <AUTHOR> 2018年5月11日
     * @param joinPoint 切入点
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint)
    {
    	
    }
    
    
    /**
     * 拦截后的操作
     * 
     * <AUTHOR> 2018年5月16日
     * @param ret 返回值
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object ret)
    {
        // 记录MQ发送的内容
        StringBuffer logMsgBuffer = new StringBuffer();
        logMsgBuffer.append(MQ_TYPE + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        //消息主题
        String queueName = joinPoint.getArgs()[0].toString();
        logMsgBuffer.append(queueName + Constants.Split.EUROPE_THREE);
        //唯一流水号
        String serialID = UUID.randomUUID().toString();
        logMsgBuffer.append("serialID_"+serialID + Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = UUID.randomUUID().toString();
        logMsgBuffer.append("linkID_"+linkID + Constants.Split.EUROPE_THREE);
        Object arg = joinPoint.getArgs()[1];
        String body = joinPoint.getArgs()[1].toString();
        if(arg instanceof String)
    	{
        	body = arg.toString();
    	}
    	else if(arg instanceof Object)
    	{
    		body = JSON.toJSONString(arg);
    	}
        String messageId = "";
        if(StringUtils.isNotEmpty(body)) {
        	messageId = Md5Utils.MD5Encode(body);
        }
        // 消息ID
        logMsgBuffer.append(messageId + Constants.Split.EUROPE_THREE);
        // 消息报文
        logMsgBuffer.append(body);
        MqLogUtil.getInstance().printSendMqLog(logMsgBuffer.toString(), Boolean.FALSE);
    }
    
    /**
     * 报错后的操作
     * 
     * @auth cWX319470 2018年5月11日
     * @see [类、类#方法、类#成员]
     */
    @AfterThrowing(throwing = "e", pointcut = "webLog()")
    public void doAfterError(JoinPoint joinPoint, Throwable e)
    {
    	// 记录MQ发送的内容
        StringBuffer logMsgBuffer = new StringBuffer();
        logMsgBuffer.append(MQ_TYPE + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        //消息主题
        String queueName = (String) joinPoint.getArgs()[0];
        logMsgBuffer.append(queueName + Constants.Split.EUROPE_THREE);
        //唯一流水号
        String serialID = UUID.randomUUID().toString();
        logMsgBuffer.append("serialID_"+serialID + Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = UUID.randomUUID().toString();
        logMsgBuffer.append("linkID_"+linkID + Constants.Split.EUROPE_THREE);
        Object arg = joinPoint.getArgs()[1];
        String body = joinPoint.getArgs()[1].toString();
        if(arg instanceof String)
    	{
        	body = arg.toString();
    	}
    	else if(arg instanceof Object)
    	{
    		body = JSON.toJSONString(arg);
    	}
        String messageId = "";
        if(StringUtils.isNotEmpty(body)) {
        	messageId = Md5Utils.MD5Encode(body);
        }
        // 消息ID
        logMsgBuffer.append(messageId + Constants.Split.EUROPE_THREE);
        // 消息报文
        //String resultMessage = e.getMessage();
        logMsgBuffer.append(body);
        ManualLogUtils.errorLog("system error!", e);
        MqLogUtil.getInstance().printSendMqLog(logMsgBuffer.toString(), Boolean.FALSE);
    }
}