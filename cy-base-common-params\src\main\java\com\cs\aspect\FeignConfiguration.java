package com.cs.aspect;

import java.util.Map;

import org.springframework.context.annotation.Configuration;

import com.cs.param.utils.ThreadLocalUtil;

import feign.RequestInterceptor;
import feign.RequestTemplate;


@Configuration
public class FeignConfiguration implements RequestInterceptor
{

    @Override
    public void apply(RequestTemplate requestTemplate)
    {
    	//链路ID
    	Map<String, String> headerMap = ThreadLocalUtil.get();
    	for(String key : headerMap.keySet()) {
    		if("linkID".equals(key)) {
    			requestTemplate.header("linkID_", headerMap.get(key));
    		}
    	}
    }
    
}
