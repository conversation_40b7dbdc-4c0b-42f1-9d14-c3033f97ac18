
package com.cy.model;

/**
 * 
 * @date 2018年4月20日 - 上午11:20:50
 * @Description 系统用户model
 */
public class SysUserModel {
	/**
	 * 编号ID
	 */
	private Integer sysUserId;
	/**
	 * 系统账号
	 */
	private String sysUserName;
	/**
	 * 系统password
	 */
	private String sysUserPassword;
	/**
	 * 用户名称
	 */
	private String sysStaffName;
	/**
	 * 性别
	 */
	private Integer sysUserSex;
	/**
	 * 手机号
	 */

	private String sysMobileNumber;
	/**
	 * 邮箱
	 */
	private String sysUserEmail;
	/**
	 * 公司
	 */
	private String sysUserCompany;
	/**
	 * 省份编码
	 */
	private String provinceCode;

	/**
	 * 状态
	 */
	private String sysState;

	/**
	 * 状态
	 */
	private String sysStateName;
	/**
	 * 创建时间
	 */

	private String sysCreateTime;
	/**
	 * 角色ID
	 */
	private Integer sysRoleId;
	/**
	 * 是否删除
	 */
	private String isDelete;
	/**
	 * 省份名称
	 */
	private String provinceName;

	/**
	 * 角色名称
	 */
	private String sysRoleName;

	public Integer getSysUserId() {
		return sysUserId;
	}

	public void setSysUserId(Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public String getSysUserName() {
		return sysUserName;
	}

	public void setSysUserName(String sysUserName) {
		this.sysUserName = sysUserName;
	}

	public String getSysUserPassword() {
		return sysUserPassword;
	}

	public void setSysUserPassword(String sysUserPassword) {
		this.sysUserPassword = sysUserPassword;
	}

	public String getSysStaffName() {
		return sysStaffName;
	}

	public void setSysStaffName(String sysStaffName) {
		this.sysStaffName = sysStaffName;
	}

	public Integer getSysUserSex() {
		return sysUserSex;
	}

	public void setSysUserSex(Integer sysUserSex) {
		this.sysUserSex = sysUserSex;
	}

	public String getSysMobileNumber() {
		return sysMobileNumber;
	}

	public void setSysMobileNumber(String sysMobileNumber) {
		this.sysMobileNumber = sysMobileNumber;
	}

	public String getSysUserEmail() {
		return sysUserEmail;
	}

	public void setSysUserEmail(String sysUserEmail) {
		this.sysUserEmail = sysUserEmail;
	}

	public String getSysUserCompany() {
		return sysUserCompany;
	}

	public void setSysUserCompany(String sysUserCompany) {
		this.sysUserCompany = sysUserCompany;
	}

	public String getSysState() {
		return sysState;
	}

	public void setSysState(String sysState) {
		this.sysState = sysState;
	}

	public String getSysCreateTime() {
		return sysCreateTime;
	}

	public void setSysCreateTime(String sysCreateTime) {
		this.sysCreateTime = sysCreateTime;
	}

	public Integer getSysRoleId() {
		return sysRoleId;
	}

	public void setSysRoleId(Integer sysRoleId) {
		this.sysRoleId = sysRoleId;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getSysRoleName() {
		return sysRoleName;
	}

	public void setSysRoleName(String sysRoleName) {
		this.sysRoleName = sysRoleName;
	}

	public String getSysStateName() {
		return sysStateName;
	}

	public void setSysStateName(String sysStateName) {
		this.sysStateName = sysStateName;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "SysUserModel [sysUserId=" + sysUserId + ", sysUserName=" + sysUserName + ", sysUserPassword="
				+ sysUserPassword + ", sysStaffName=" + sysStaffName + ", sysUserSex=" + sysUserSex
				+ ", sysMobileNumber=" + sysMobileNumber + ", sysUserEmail=" + sysUserEmail + ", sysUserCompany="
				+ sysUserCompany + ", provinceCode=" + provinceCode + ", sysState=" + sysState + ", sysCreateTime="
				+ sysCreateTime + ", sysRoleId=" + sysRoleId + ", isDelete=" + isDelete + ", provinceName="
				+ provinceName + ", sysRoleName=" + sysRoleName + "]";
	}

}
