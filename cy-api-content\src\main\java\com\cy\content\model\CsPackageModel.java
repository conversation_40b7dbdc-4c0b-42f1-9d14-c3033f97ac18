package com.cy.content.model;

import java.io.Serializable;

public class CsPackageModel  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 彩印盒Id
	 */
	private int csPkgId;
	/**
	 * 彩印盒编号
	 */
	private String csPkgNumber;
	/**
	 * 彩印盒子业务编码
	 */
	private String csPkgServiceCode;
	
	/**
	 * 彩印盒名称
	 */
	private String csPkgName;
	/**
	 * 状态
	 */
	private String csPkgStatus;
	
	private String csStatusName;
	/**
	 * 彩印盒分类id
	 */
	private String csPkgGroupId;
	/**
	 * 彩印盒分类名称
	 */
	private String csPkgGroupName;
	/**
	 * 彩印盒分类名称
	 */
	private String csPkgLabelId;
	/**
	 * 彩印盒标签名称
	 */
	private String csPkgLabelName;
	/**
	 * 提交时间
	 */
	private String csSubmitTime;
	/**
	 * 提交人
	 */
	private String csSubmitUser;
	/**
	 * 更新时间
	 */
	private String csPkgUpdateTime;
	/**
	 * 生效时间
	 */
	private String csPkgStartTime;
	/**
	 * 失效时间
	 */
	private String csPkgEndTime;
	/**
	 * 备注
	 */
	private String csPkgDesc;
	/**
	 * 设置人数
	 */
	private int useNumber;
	/**
	 * 彩印来源
	 */
	private int csPkgType;
	/**
	 * 审核人
	 */
	private String assessor;
	/**
	 * 通过审核时间
	 */
	private String assessTime;
	/**
	 * 省编码
	 */
	private String province;
	
	/**
	 * 彩印盒里的彩印内容
	 */
	private String csPkgContent1,csPkgContent2,csPkgContent3,csPkgContent4,csPkgContent5,csPkgContent6,csPkgContent7,csPkgContent8,csPkgContent9,csPkgContent10,
		csPkgContent11,csPkgContent12,csPkgContent13,csPkgContent14,csPkgContent15,csPkgContent16,csPkgContent17,csPkgContent18,csPkgContent19,csPkgContent20,
		csPkgContent21,csPkgContent22,csPkgContent23,csPkgContent24,csPkgContent25,csPkgContent26,csPkgContent27,csPkgContent28,csPkgContent29,csPkgContent30;
	
	private String contentId1, contentId2, contentId3, contentId4, contentId5, contentId6, contentId7, contentId8, contentId9, contentId10,
                contentId11, contentId12, contentId13, contentId14, contentId15, contentId16, contentId17, contentId18, contentId19,contentId20,
                contentId21, contentId22, contentId23, contentId24, contentId25, contentId26, contentId27, contentId28,contentId29, contentId30;
	/**
	 * 查询起始位置
	 */
	private int offset = 0;
	/**
	 * 查询个数
	 * limit #{offset},#{range}
	 */
	private int range = 0;
	/**
	 * 排序：0按最新上架的排序，1按最热门排序
	 */
	private int orderIndex = 0;
	
	private int actionType;
	
	private String startUpdateTime;
	
	private String endUpdateTime;
	
	private String startSubmitTime;
	
	private String endSubmitTime;
	
	private int tempId;
	
	public int getTempId() {
	    return tempId;
	}
	public void setTempId(int tempId) {
	    this.tempId = tempId;
	}
	public String getCsPkgServiceCode() {
	    return csPkgServiceCode;
	}
	public void setCsPkgServiceCode(String csPkgServiceCode) {
	    this.csPkgServiceCode = csPkgServiceCode;
	}
	public String getStartUpdateTime() {
	    return startUpdateTime;
	}
	public void setStartUpdateTime(String startUpdateTime) {
	    this.startUpdateTime = startUpdateTime;
	}
	public String getEndUpdateTime() {
	    return endUpdateTime;
	}
	public void setEndUpdateTime(String endUpdateTime) {
	    this.endUpdateTime = endUpdateTime;
	}
	public int getActionType() {
	    return actionType;
	}
	public void setActionType(int actionType) {
	    this.actionType = actionType;
	}
	public String getProvince() {
	    return province;
	}
	public void setProvince(String province) {
	    this.province = province;
	}
	public String getCsSubmitUser() {
	    return csSubmitUser;
	}
	public void setCsSubmitUser(String csSubmitUser) {
	    this.csSubmitUser = csSubmitUser;
	}
	public int getOffset() {
	    return offset;
	}
	public void setOffset(int offset) {
	    this.offset = offset;
	}
	public int getRange() {
	    return range;
	}
	public void setRange(int range) {
	    this.range = range;
	}
	public int getOrderIndex() {
	    return orderIndex;
	}
	public void setOrderIndex(int orderIndex) {
	    this.orderIndex = orderIndex;
	}
	public String getCsPkgNumber() {
	    return csPkgNumber;
	}
	public void setCsPkgNumber(String csPkgNumber) {
	    this.csPkgNumber = csPkgNumber;
	}
	public int getCsPkgId() {
		return csPkgId;
	}
	public void setCsPkgId(int csPkgId) {
		this.csPkgId = csPkgId;
	}
	public String getCsPkgName() {
		return csPkgName;
	}
	public void setCsPkgName(String csPkgName) {
		this.csPkgName = csPkgName;
	}
	public String getCsPkgGroupName() {
		return csPkgGroupName;
	}
	public void setCsPkgGroupName(String csPkgGroupName) {
		this.csPkgGroupName = csPkgGroupName;
	}
	public String getCsPkgLabelName() {
		return csPkgLabelName;
	}
	public String getCsPkgStatus() {
		return csPkgStatus;
	}
	public void setCsPkgStatus(String csPkgStatus) {
		this.csPkgStatus = csPkgStatus;
	}
	public void setCsPkgLabelName(String csPkgLabelName) {
		this.csPkgLabelName = csPkgLabelName;
	}
	public String getCsSubmitTime() {
		return csSubmitTime;
	}
	public void setCsSubmitTime(String csSubmitTime) {
		this.csSubmitTime = csSubmitTime;
	}
	public String getAssessor() {
		return assessor;
	}
	public void setAssessor(String assessor) {
		this.assessor = assessor;
	}
	public String getAssessTime() {
		return assessTime;
	}
	public void setAssessTime(String assessTime) {
		this.assessTime = assessTime;
	}
	public String getCsPkgGroupId() {
		return csPkgGroupId;
	}
	public void setCsPkgGroupId(String csPkgGroupId) {
		this.csPkgGroupId = csPkgGroupId;
	}
	public String getCsPkgLabelId() {
		return csPkgLabelId;
	}
	public void setCsPkgLabelId(String csPkgLabelId) {
		this.csPkgLabelId = csPkgLabelId;
	}
	public String getCsPkgContent1() {
		return csPkgContent1;
	}
	public void setCsPkgContent1(String csPkgContent1) {
		this.csPkgContent1 = csPkgContent1;
	}
	public String getCsPkgContent2() {
		return csPkgContent2;
	}
	public void setCsPkgContent2(String csPkgContent2) {
		this.csPkgContent2 = csPkgContent2;
	}
	public String getCsPkgContent3() {
		return csPkgContent3;
	}
	public void setCsPkgContent3(String csPkgContent3) {
		this.csPkgContent3 = csPkgContent3;
	}
	public String getCsPkgContent4() {
		return csPkgContent4;
	}
	public void setCsPkgContent4(String csPkgContent4) {
		this.csPkgContent4 = csPkgContent4;
	}
	public String getCsPkgContent5() {
		return csPkgContent5;
	}
	public void setCsPkgContent5(String csPkgContent5) {
		this.csPkgContent5 = csPkgContent5;
	}
	public String getCsStatusName() {
		return csStatusName;
	}
	public void setCsStatusName(String csStatusName) {
		this.csStatusName = csStatusName;
	}
	public String getCsPkgUpdateTime() {
	    return csPkgUpdateTime;
	}
	public void setCsPkgUpdateTime(String csPkgUpdateTime) {
	    this.csPkgUpdateTime = csPkgUpdateTime;
	}
	public int getUseNumber() {
	    return useNumber;
	}
	public void setUseNumber(int useNumber) {
	    this.useNumber = useNumber;
	}
	public int getCsPkgType() {
	    return csPkgType;
	}
	public void setCsPkgType(int csPkgType) {
	    this.csPkgType = csPkgType;
	}
	public String getCsPkgContent6() {
	    return csPkgContent6;
	}
	public void setCsPkgContent6(String csPkgContent6) {
	    this.csPkgContent6 = csPkgContent6;
	}
	public String getCsPkgContent7() {
	    return csPkgContent7;
	}
	public void setCsPkgContent7(String csPkgContent7) {
	    this.csPkgContent7 = csPkgContent7;
	}
	public String getCsPkgContent8() {
	    return csPkgContent8;
	}
	public void setCsPkgContent8(String csPkgContent8) {
	    this.csPkgContent8 = csPkgContent8;
	}
	public String getCsPkgContent9() {
	    return csPkgContent9;
	}
	public void setCsPkgContent9(String csPkgContent9) {
	    this.csPkgContent9 = csPkgContent9;
	}
	public String getCsPkgContent10() {
	    return csPkgContent10;
	}
	public void setCsPkgContent10(String csPkgContent10) {
	    this.csPkgContent10 = csPkgContent10;
	}
	public String getCsPkgContent11() {
	    return csPkgContent11;
	}
	public void setCsPkgContent11(String csPkgContent11) {
	    this.csPkgContent11 = csPkgContent11;
	}
	public String getCsPkgContent12() {
	    return csPkgContent12;
	}
	public void setCsPkgContent12(String csPkgContent12) {
	    this.csPkgContent12 = csPkgContent12;
	}
	public String getCsPkgContent13() {
	    return csPkgContent13;
	}
	public void setCsPkgContent13(String csPkgContent13) {
	    this.csPkgContent13 = csPkgContent13;
	}
	public String getCsPkgContent14() {
	    return csPkgContent14;
	}
	public void setCsPkgContent14(String csPkgContent14) {
	    this.csPkgContent14 = csPkgContent14;
	}
	public String getCsPkgContent15() {
	    return csPkgContent15;
	}
	public void setCsPkgContent15(String csPkgContent15) {
	    this.csPkgContent15 = csPkgContent15;
	}
	public String getCsPkgContent16() {
	    return csPkgContent16;
	}
	public void setCsPkgContent16(String csPkgContent16) {
	    this.csPkgContent16 = csPkgContent16;
	}
	public String getCsPkgContent17() {
	    return csPkgContent17;
	}
	public void setCsPkgContent17(String csPkgContent17) {
	    this.csPkgContent17 = csPkgContent17;
	}
	public String getCsPkgContent18() {
	    return csPkgContent18;
	}
	public void setCsPkgContent18(String csPkgContent18) {
	    this.csPkgContent18 = csPkgContent18;
	}
	public String getCsPkgContent19() {
	    return csPkgContent19;
	}
	public void setCsPkgContent19(String csPkgContent19) {
	    this.csPkgContent19 = csPkgContent19;
	}
	public String getCsPkgContent20() {
	    return csPkgContent20;
	}
	public void setCsPkgContent20(String csPkgContent20) {
	    this.csPkgContent20 = csPkgContent20;
	}
	public String getCsPkgContent21() {
	    return csPkgContent21;
	}
	public void setCsPkgContent21(String csPkgContent21) {
	    this.csPkgContent21 = csPkgContent21;
	}
	public String getCsPkgContent22() {
	    return csPkgContent22;
	}
	public void setCsPkgContent22(String csPkgContent22) {
	    this.csPkgContent22 = csPkgContent22;
	}
	public String getCsPkgContent23() {
	    return csPkgContent23;
	}
	public void setCsPkgContent23(String csPkgContent23) {
	    this.csPkgContent23 = csPkgContent23;
	}
	public String getCsPkgContent24() {
	    return csPkgContent24;
	}
	public void setCsPkgContent24(String csPkgContent24) {
	    this.csPkgContent24 = csPkgContent24;
	}
	public String getCsPkgContent25() {
	    return csPkgContent25;
	}
	public void setCsPkgContent25(String csPkgContent25) {
	    this.csPkgContent25 = csPkgContent25;
	}
	public String getCsPkgContent26() {
	    return csPkgContent26;
	}
	public void setCsPkgContent26(String csPkgContent26) {
	    this.csPkgContent26 = csPkgContent26;
	}
	public String getCsPkgContent27() {
	    return csPkgContent27;
	}
	public void setCsPkgContent27(String csPkgContent27) {
	    this.csPkgContent27 = csPkgContent27;
	}
	public String getCsPkgContent28() {
	    return csPkgContent28;
	}
	public void setCsPkgContent28(String csPkgContent28) {
	    this.csPkgContent28 = csPkgContent28;
	}
	public String getCsPkgContent29() {
	    return csPkgContent29;
	}
	public void setCsPkgContent29(String csPkgContent29) {
	    this.csPkgContent29 = csPkgContent29;
	}
	public String getCsPkgContent30() {
	    return csPkgContent30;
	}
	public void setCsPkgContent30(String csPkgContent30) {
	    this.csPkgContent30 = csPkgContent30;
	}
	public String getContentId1() {
	    return contentId1;
	}
	public void setContentId1(String contentId1) {
	    this.contentId1 = contentId1;
	}
	public String getContentId2() {
	    return contentId2;
	}
	public void setContentId2(String contentId2) {
	    this.contentId2 = contentId2;
	}
	public String getContentId3() {
	    return contentId3;
	}
	public void setContentId3(String contentId3) {
	    this.contentId3 = contentId3;
	}
	public String getContentId4() {
	    return contentId4;
	}
	public void setContentId4(String contentId4) {
	    this.contentId4 = contentId4;
	}
	public String getContentId5() {
	    return contentId5;
	}
	public void setContentId5(String contentId5) {
	    this.contentId5 = contentId5;
	}
	public String getContentId6() {
	    return contentId6;
	}
	public void setContentId6(String contentId6) {
	    this.contentId6 = contentId6;
	}
	public String getContentId7() {
	    return contentId7;
	}
	public void setContentId7(String contentId7) {
	    this.contentId7 = contentId7;
	}
	public String getContentId8() {
	    return contentId8;
	}
	public void setContentId8(String contentId8) {
	    this.contentId8 = contentId8;
	}
	public String getContentId9() {
	    return contentId9;
	}
	public void setContentId9(String contentId9) {
	    this.contentId9 = contentId9;
	}
	public String getContentId10() {
	    return contentId10;
	}
	public void setContentId10(String contentId10) {
	    this.contentId10 = contentId10;
	}
	public String getContentId11() {
	    return contentId11;
	}
	public void setContentId11(String contentId11) {
	    this.contentId11 = contentId11;
	}
	public String getContentId12() {
	    return contentId12;
	}
	public void setContentId12(String contentId12) {
	    this.contentId12 = contentId12;
	}
	public String getContentId13() {
	    return contentId13;
	}
	public void setContentId13(String contentId13) {
	    this.contentId13 = contentId13;
	}
	public String getContentId14() {
	    return contentId14;
	}
	public void setContentId14(String contentId14) {
	    this.contentId14 = contentId14;
	}
	public String getContentId15() {
	    return contentId15;
	}
	public void setContentId15(String contentId15) {
	    this.contentId15 = contentId15;
	}
	public String getContentId16() {
	    return contentId16;
	}
	public void setContentId16(String contentId16) {
	    this.contentId16 = contentId16;
	}
	public String getContentId17() {
	    return contentId17;
	}
	public void setContentId17(String contentId17) {
	    this.contentId17 = contentId17;
	}
	public String getContentId18() {
	    return contentId18;
	}
	public void setContentId18(String contentId18) {
	    this.contentId18 = contentId18;
	}
	public String getContentId19() {
	    return contentId19;
	}
	public void setContentId19(String contentId19) {
	    this.contentId19 = contentId19;
	}
	public String getContentId20() {
	    return contentId20;
	}
	public void setContentId20(String contentId20) {
	    this.contentId20 = contentId20;
	}
	public String getContentId21() {
	    return contentId21;
	}
	public void setContentId21(String contentId21) {
	    this.contentId21 = contentId21;
	}
	public String getContentId22() {
	    return contentId22;
	}
	public void setContentId22(String contentId22) {
	    this.contentId22 = contentId22;
	}
	public String getContentId23() {
	    return contentId23;
	}
	public void setContentId23(String contentId23) {
	    this.contentId23 = contentId23;
	}
	public String getContentId24() {
	    return contentId24;
	}
	public void setContentId24(String contentId24) {
	    this.contentId24 = contentId24;
	}
	public String getContentId25() {
	    return contentId25;
	}
	public void setContentId25(String contentId25) {
	    this.contentId25 = contentId25;
	}
	public String getContentId26() {
	    return contentId26;
	}
	public void setContentId26(String contentId26) {
	    this.contentId26 = contentId26;
	}
	public String getContentId27() {
	    return contentId27;
	}
	public void setContentId27(String contentId27) {
	    this.contentId27 = contentId27;
	}
	public String getContentId28() {
	    return contentId28;
	}
	public void setContentId28(String contentId28) {
	    this.contentId28 = contentId28;
	}
	public String getContentId29() {
	    return contentId29;
	}
	public void setContentId29(String contentId29) {
	    this.contentId29 = contentId29;
	}
	public String getContentId30() {
	    return contentId30;
	}
	public void setContentId30(String contentId30) {
	    this.contentId30 = contentId30;
	}
	public String getCsPkgStartTime() {
	    return csPkgStartTime;
	}
	public void setCsPkgStartTime(String csPkgStartTime) {
	    this.csPkgStartTime = csPkgStartTime;
	}
	public String getCsPkgEndTime() {
	    return csPkgEndTime;
	}
	public void setCsPkgEndTime(String csPkgEndTime) {
	    this.csPkgEndTime = csPkgEndTime;
	}
	public String getCsPkgDesc() {
	    return csPkgDesc;
	}
	public void setCsPkgDesc(String csPkgDesc) {
	    this.csPkgDesc = csPkgDesc;
	}
	public String getStartSubmitTime() {
	    return startSubmitTime;
	}
	public void setStartSubmitTime(String startSubmitTime) {
	    this.startSubmitTime = startSubmitTime;
	}
	public String getEndSubmitTime() {
	    return endSubmitTime;
	}
	public void setEndSubmitTime(String endSubmitTime) {
	    this.endSubmitTime = endSubmitTime;
	}
}
