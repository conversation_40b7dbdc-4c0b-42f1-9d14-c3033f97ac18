package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParPackageCommon;
import com.cs.param.model.ParPackageModel;

@Repository
public interface ParPackageMapper {

	List<ParPackageModel> getParPackageMark() throws SQLException;

	List<ParPackageModel> queryPageInfo(ParPackageCommon common) throws SQLException;

	Integer queryPageCount(ParPackageCommon common) throws SQLException;

}
