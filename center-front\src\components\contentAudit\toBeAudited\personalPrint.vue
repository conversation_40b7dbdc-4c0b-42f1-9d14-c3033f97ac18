<template scope="scope">
    <div>
        <h1 class="user-title">文本彩印</h1>
        <div class="user-line"></div>
          <!--个人彩印-->
            <div class="app-search">
              <el-form :inline="true" class="demo-form-inline">

                <el-form-item label="类型">
                  <el-select v-model="searchReq.svMold" placeholder="请选择" size="small"  style="width:150px;">
                    <el-option
                        v-for="item in sildeData"
                        :key="item.csTypeId"
                        :label="item.csTypeName"
                        :value="item.csTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="设置类型">
                  <el-select v-model="searchReq.isBatch" size="small" style="width:150px;">
                    <el-option
                        v-for="item in batchType"
                        :key="item.batchTypeId"
                        :label="item.batchTypeName"
                        :value="item.batchTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="彩印ID">
                  <el-input v-model="searchReq.svNumber" size="small" clearable></el-input>
                </el-form-item>
                <el-form-item label="彩印内容">
                  <el-input v-model="searchReq.svCard" size="small" clearable></el-input>
                </el-form-item>
                 <!--<el-form-item label="提交人">-->
                  <!--<el-input v-model="searchReq.svSubmitUser" size="small" clearable></el-input>-->
                <!--</el-form-item>-->
                <el-form-item label="提交时间">
                  <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px"
                    size="small"
                    />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchReq.pageNum = 1;search(searchReq)" size="small">查询</el-button>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
<!--                  <el-button id="btn1" size="small" v-bind:disabled="btnhide" v-bind:type="btype" @click="passVisible=true;passType=2;">批量通过</el-button>-->
<!--                  <el-button id="btn2" size="small" v-bind:disabled="btnhide" v-bind:type="btype" @click="rejectVisible=true;rejectReq.svCause='';rejectType=2;">批量驳回</el-button>-->
                </el-form-item>
              </el-form>
            </div>
            <div>
              <el-table
                  v-loading="tableLoading"
                  :data="tableData"
                  border
                  class="app-tab"
                  @selection-change="handleSelectionChange"
                  :header-cell-class-name="tableheaderClassName">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="svMoldName"
                    label="类型"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="svNumber"
                    label="彩印ID"
                    width="240">
                </el-table-column>
                <el-table-column
                    label="彩印内容"
                    width="400"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <div v-html="highLight(scope.row)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="资质" width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showOtherImage(scope.row.qualificationsUrlList)"
                               :style="hasOtherImage(scope.row.qualificationsUrlList)?'':'color: #808080'">详情
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="submitUser"
                    label="提交人"
                    width="200"  :formatter="formmatPhone">
                </el-table-column>
                <el-table-column
                    prop="submitCount"
                    label="提交次数"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="submitTime"
                    label="提交时间"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="svProName"
                    label="省份"
                    width="150">
                </el-table-column>
                 <el-table-column
                    prop="recUser"
                    label="接收人"
                    width="150">
                </el-table-column>
                 <el-table-column
                    prop="batchNo"
                    label="批次号"
                    width="150">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="160">
                  <template slot-scope="scope">
<!--                    <el-button type="text" size="small" @click="passVisible=true;rowData=scope.row;passType=1;">通过</el-button>-->
<!--                    <el-button @click="rejectVisible=true;rowData=scope.row;rejectReq.svCause='';rejectType=1;" type="text" size="small">驳回</el-button>-->
                    <el-button v-show="searchReq.isBatch == 2" :disabled="!scope.row.svSftpUrl" @click="download(scope.row)" type="text" size="small">下载</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-dialog title="资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
                <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
                  <li>
                    <a :href="item" target="_blank">资质{{ index + 1 }}</a>
                  </li>
                </ul>
              </el-dialog>
              <el-dialog
                        width="30%"
                        title="通过"
                        :visible.sync="passVisible"
                        :close-on-click-modal="false"
                        append-to-body>
                        <div>是否通过该内容？</div>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="passVisible = false" size="small">取 消</el-button>
                        <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
                      </div>
                </el-dialog>
                <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="rejectVisible"
                        :close-on-click-modal="false"
                        append-to-body>
                      <el-form >
                        <el-form-item>
                          <el-radio v-model="radio" label="1">手动输入</el-radio>
                          <el-radio v-model="radio" label="2">系统预设</el-radio>
                        </el-form-item>

                        <el-form-item label="驳回原因">

                          <el-input type="textarea" v-if="radio=='1'" v-model="rejectReq.svCause" size="small" style="width:250px;"></el-input>
                          <p v-if="rejectReq.svCause.length > 1024" style="color: red">不能超过1024个字</p>
                          <el-select v-if="radio=='2'" v-model="rejectReq.svCause" placeholder="请选择">
                            <el-option
                              v-for="item in options"
                              :key="item.value"
                              :label="item.value"
                              :value="item.value">
                            </el-option>
                          </el-select>
                        </el-form-item>

                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="rejectVisible = false"size="small">取 消</el-button>
                        <el-button type="primary" @click="rejectVisible = false;rejectCheck(rowData)"size="small" :disabled="rejectReq.svCause.length > 1024">确 定</el-button>
                      </div>
                </el-dialog>
              <!-- 分页 -->
              <div class="block app-pageganit">
              <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="searchReq.pageNum"
                      :page-sizes="[10, 20, 30, 50,100]"
                      :page-size="10"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="pageTotal"  style="text-align: right;">
              </el-pagination>
              </div>
            </div>
    </div>
</template>
<script>
import {formDate} from './../../../util/core.js';
import {replacePhone} from './../../../util/core.js';

export default {
  data() {
    return {
      tableLoading: false,
      radio:'1',
      btype:'info',
      btnhide:true,
      pageTotal: 0,
      rowData: "",
      sildeData: [{
          csTypeId: '1',
          csTypeName: '用户DIY'
        }, {
          csTypeId: '2',
          csTypeName: '个人彩印'
        }
        , {
        csTypeId: '5',
        csTypeName: '挂机短信'
      }, {
        csTypeId: '6',
        csTypeName: '企业视彩号主叫彩印'
      }, {
        csTypeId: '7',
        csTypeName: '企业视彩号被叫彩印'
      }
        , {
          csTypeId: '8',
          csTypeName: '主叫文本彩印'
        }
        , {
          csTypeId: '9',
          csTypeName: '被叫文本彩印'
        }
        , {
          csTypeId: '10',
          csTypeName: '用户主叫DIY'
        }
        , {
          csTypeId: '11',
          csTypeName: '用户被叫DIY'
        }, {
          csTypeId: '12',
          csTypeName: '直投闪短信'
        }
        ],

      batchType:[{
          batchTypeId: '1',
          batchTypeName: '普通设置'
        }, {
          batchTypeId: '2',
          batchTypeName: '批量设置'
        }],
      checked: [],
      passVisible:false,
      rejectVisible: false,
      passType:0,
      rejectType:0,
      otherImageVisible: false,
      otherImage: [], //其他资质
      options: [{
          value: '出现敏感词',
        }, {
          value: '缺少必填项',
        }, {
          value: '已存在相同彩印',
        }, {
          value: '内容不友好',
        }, {
          value: '其他问题',
        }],
      //  tableData: [
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "1",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "2",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "2",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "3",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "4",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "4",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    }
      //  ],
      tableData: [],
      multipleSelection: [],
      dateTime:[],
      searchReq: {
        // isBatch:"1",
        svMold: "1",
        svNumber: "",
        svCard: "",
        svSubmitUser: "",
        startTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1
      },
      //通过请求参数
      passReq: {
        ids: []
      },
      //驳回请求参数
      rejectReq: {
        svCause:'',
        ids: []
      },
      clickPassFlag: false,
      clickRejectFlag: false
    };
  },
  mounted() {
    // this.slideData();
    this.search(this.searchReq)
  },
  methods: {
    showOtherImage(otherImage) {
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    hasOtherImage(otherImage) {
      return !(otherImage == null || otherImage.length == 0);
    },
     formmatPhone(row, column){
          return replacePhone(row.submitUser);
      },
    //下拉栏请求
    slideData: function() {
      this.$http
        .get(`${this.proxyUrl}/content/csOff/getCsType`, { emulateJSON: true })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.sildeData = res.data.datas;
          } else if (res.data.resStatus == "1") {
            console.log("下拉栏请求失败");
          }
        });
    },
    //多选框
    handleSelectionChange(val) {
      this.rejectReq.ids.length=0;
      this.passReq.ids.length=0;
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.ids.push(val[i].svId);
        this.passReq.ids.push(val[i].svId);
      }
      if(this.rejectReq.ids.length>0){
        this.btnhide=false;
        this.btype='primary'
      }else{
        this.btnhide=true;
         this.btype='info';
      }
    },
    highLight(row){
      if(!(row.sensitiveWords)){
        return row.svCard;

      }else{
        var specalKey=row.sensitiveWords.replace(new RegExp(',','g'),'|').replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
        var scCard=row.svCard.replace(new RegExp(specalKey,'g'),`<span style="color:red">$&</span>`);
        return scCard;
      }
    },
    //------------------个人彩印--------------------
    //查询请求
    search: function(searchReq) {
      if(this.dateTime && this.dateTime.length>0){
        searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        searchReq.startTime='';
        searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/auditText/getAuditText`, searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
          this.tableLoading=false;
        });
    },
    //判断是批量但是单操作，发相应请求
    passCheck(val){
      if(this.clickPassFlag) {
        return
      }
      this.clickPassFlag = true;
      //1为单，2为多
      if(this.passType==1){
        this.pass(val);
      }
      else if(this.passType==2){
        this.passlist();
      }
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val){
      if(this.clickRejectFlag) {
        return
      }
      this.clickRejectFlag = true;
      //1为单，2为多
      if(this.rejectType==1){
        this.reject(val);
      }
      else if(this.rejectType==2){
        this.rejectlist();
      }
    },
    //通过请求---单
    pass: function(val) {
      this.passReq.ids = [];
      this.passReq.ids[0]=val.svId;
      this.tableLoading = true;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(`${this.proxyUrl}/content/auditText/passAuditText`, JSON.stringify(this.passReq))
        .then(function(res) {
          loading.close();
          this.clickPassFlag = false;
          if (res.data.resStatus == "0") {
              this.tableLoading =false;
              this.$message.success("通过成功");
              this.search(this.searchReq);
              this.passReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("通过失败 "+res.data.resText);
              this.passReq.ids.length=0;
              this.tableLoading =false;
            }
        });
    },
    //通过请求---多
    passlist: function() {

      if(!this.passReq.ids.length>0){
        this.$message.error('请选择批量通过的内容');
        this.clickPassFlag = false;
        return false;
      }
      this.tableLoading = true;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(`${this.proxyUrl}/content/auditText/passAuditText`, JSON.stringify(this.passReq))
        .then(function(res) {
          loading.close();
          this.clickPassFlag = false;
          if (res.data.resStatus == "0") {
              this.tableLoading =false;
              this.$message.success("通过成功");
              this.search(this.searchReq);
              this.passReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
               this.$message.error("通过失败 "+res.data.resText);
              this.passReq.ids.length=0;
              this.tableLoading =false;
            }
        });
    },
    //驳回请求---单
    reject: function(val) {
      if(!this.rejectReq.svCause){
        this.$message.error("请填写驳回原因");
        this.rejectVisible=true;
        this.clickRejectFlag = false;
        return false;
      }

      this.rejectReq.ids = [];
      this.rejectReq.ids[0] = val.svId;

      this.tableLoading =true;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(`${this.proxyUrl}/content/auditText/rejectAuditText`, JSON.stringify(this.rejectReq))
        .then(function(res) {
          loading.close();
          this.clickRejectFlag = false;
          if (res.data.resStatus == "0") {
              this.tableLoading =false;
              this.$message.success("驳回成功");
              this.search(this.searchReq);
              this.rejectReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
               this.$message.error("驳回失败 "+res.data.resText);
              this.rejectReq.ids.length=0;
              this.tableLoading =false;
            }
        });
    },
    //驳回请求---多
    rejectlist: function() {
      if(!this.rejectReq.svCause){
        this.$message.error("请填写驳回原因");
        this.rejectVisible=true;
        this.clickRejectFlag = false;
        return false;
      }
      if(!this.rejectReq.ids.length>0){
        this.$message.error("请选择批量驳回的内容");
        this.clickRejectFlag = false;
        return false;
      }

      this.tableLoading =true;
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(`${this.proxyUrl}/content/auditText/rejectAuditText`, JSON.stringify(this.rejectReq))
        .then(function(res) {
          loading.close();
          this.clickRejectFlag = false;
          if (res.data.resStatus == "0") {
              this.tableLoading =false;
              this.$message.success("驳回成功");
              this.search(this.searchReq);
              this.rejectReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("驳回失败 "+res.data.resText);
              this.rejectReq.ids.length=0;
              this.tableLoading =false;
            }
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageNum = 1;
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/auditText/getAuditText`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/auditText/getAuditText`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    },
    download(row) {
      window.location.href = `${this.proxyUrl}/user/batchContent/downloadFile?path=${row.svSftpUrl}`
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
    background-color: #F5F5F5;
}
</style>
