<template>
  <li
    @mouseenter="iconIsShow"  @mouseleave="iconHide"
    @click.stop="selectOptionClick"
    class="el-select-dropdown__item"
    v-show="visible"
    :class="{
      'selected': itemSelected,
      'is-disabled': disabled || groupDisabled || limitReached,
      'hover': hover
    }">
    <slot>
      <div style="width: 80%;float:left">
        <span>{{ currentLabel }}</span>
      </div>
    </slot>
    <div style="float:right">
      <i v-show="buttonShow" class="el-icon-edit" @click.stop="editOptionClick"></i>
      <i v-show="buttonShow" class="el-icon-close" @click.stop="closeOptionClick"></i>
    </div>
  </li>
</template>

<script type="text/babel">
  import Emitter from 'element-ui/src/mixins/emitter';
  import { getValueByPath } from 'element-ui/src/utils/util';

  export default {
    mixins: [Emitter],

    name: 'DefOption',

    componentName: 'DefOption',

    inject: ['select'],

    props: {
      value: {
        required: true
      },
      label: [String, Number],
      created: <PERSON><PERSON><PERSON>,
      disabled: {
        type: <PERSON><PERSON><PERSON>,
        default: false
      }
    },

    data() {
      return {
        index: -1,
        groupDisabled: false,
        visible: true,
        hitState: false,
        hover: false,
        buttonShow: false,
        event:''
      };
    },

    computed: {
      isObject() {
        return Object.prototype.toString.call(this.value).toLowerCase() === '[object object]';
      },

      currentLabel() {
        return this.label || (this.isObject ? '' : this.value);
      },

      currentValue() {
        return this.value || this.label || '';
      },

      itemSelected() {
        if (!this.select.multiple) {
          return this.isEqual(this.value, this.select.value);
        } else {
          return this.contains(this.select.value, this.value);
        }
      },

      limitReached() {
        if (this.select.multiple) {
          return !this.itemSelected &&
            (this.select.value || []).length >= this.select.multipleLimit &&
            this.select.multipleLimit > 0;
        } else {
          return false;
        }
      }
    },

    watch: {
      currentLabel() {
        if (!this.created && !this.select.remote) this.dispatch('DefSelect', 'setSelected');
      },
      value() {
        if (!this.created && !this.select.remote) this.dispatch('DefSelect', 'setSelected');
      }
    },

    methods: {
      isEqual(a, b) {
        if (!this.isObject) {
          return a === b;
        } else {
          const valueKey = this.select.valueKey;
          return getValueByPath(a, valueKey) === getValueByPath(b, valueKey);
        }
      },

      contains(arr = [], target) {
        if (!this.isObject) {
          return arr.indexOf(target) > -1;
        } else {
          const valueKey = this.select.valueKey;
          return arr.some(item => {
            return getValueByPath(item, valueKey) === getValueByPath(target, valueKey);
          });
        }
      },

      handleGroupDisabled(val) {
        this.groupDisabled = val;
      },

      iconIsShow() {
        if (!this.disabled && !this.groupDisabled) {
          this.select.hoverIndex = this.select.options.indexOf(this);
          this.buttonShow=this.select.iconShow;
        }
      },
      iconHide() {
          this.buttonShow=false;
      },

      selectOptionClick() {
        if (this.disabled !== true && this.groupDisabled !== true) {
          this.dispatch('DefSelect', 'handleOptionClick', this);
        }
      },

      editOptionClick(event) {
        this.event = event;
        if (this.disabled !== true && this.groupDisabled !== true) {
          this.dispatch('DefSelect', 'editClick', this);
        }
      },

      closeOptionClick(event) {
        this.event = event;
        if (this.disabled !== true && this.groupDisabled !== true) {
          this.dispatch('DefSelect', 'closeClick', this);
        }
      },

      queryChange(query) {
        // query 里如果有正则中的特殊字符，需要先将这些字符转义
        let parsedQuery = String(query).replace(/(\^|\(|\)|\[|\]|\$|\*|\+|\.|\?|\\|\{|\}|\|)/g, '\\$1');
        this.visible = new RegExp(parsedQuery, 'i').test(this.currentLabel) || this.created;
        if (!this.visible) {
          this.select.filteredOptionsCount--;
        }
      }
    },

    created() {
      this.select.options.push(this);
      this.select.cachedOptions.push(this);
      this.select.optionsCount++;
      this.select.filteredOptionsCount++;

      this.$on('queryChange', this.queryChange);
      this.$on('handleGroupDisabled', this.handleGroupDisabled);
    },

    beforeDestroy() {
      this.select.onOptionDestroy(this.select.options.indexOf(this));
    }
  };
</script>
