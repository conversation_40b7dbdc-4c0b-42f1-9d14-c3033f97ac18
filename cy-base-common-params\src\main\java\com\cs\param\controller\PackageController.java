package com.cs.param.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.common.ParPackageCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.dao.ParPackageMapper;
import com.cs.param.dao.ParPackageMarkMapper;
import com.cs.param.model.ParPackageMarkModel;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;

/**
 * 
 * 彩印套餐功能管理Controller
 *
 */
@RequestMapping("/packageFunction")
@RestController
public class PackageController {

	private static final Logger log = LoggerFactory.getLogger(PackageController.class);

	@Autowired
	private ParPackageMapper parPackageMapper;

	@Autowired
	private ParPackageMarkMapper parPackageMarkMapper;

	/**
	 * 
	 * 获取彩印套餐功能配置列表
	 *
	 */
	@RequestMapping(value = "getPackagePage")
	@CySysLog(methodName = "获取彩印套餐功能配置列表", modularName = "公参模块", optContent = "获取彩印套餐功能配置列表")
	public ResultListCommon getPackagePage(@ModelAttribute("common") ParPackageCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getPackagePage", "获取彩印套餐功能配置列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parPackageMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parPackageMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "", "获取彩印套餐功能配置列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取套餐标识列表
	 *
	 */
	@RequestMapping(value = "getPackageMark")
	public List<ParPackageMarkModel> getPackageMark() throws Exception {
		try {
			return parPackageMarkMapper.getAllParPackageMark();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getPackageMark", "获取套餐标识列表出错！", e);
			return null;
		}
	}

}
