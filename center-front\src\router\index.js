import Vue from "vue";
import store from "./../store/store.js";
import loginToHome from "./../components/login/login.js";
import Router from "vue-router";
import * as routeType from "./routeType";
import { fileDownLoad, forgetPwd } from "./routeType";
import { post } from "./../servers/httpServer.js";
Vue.use(Router);
const routerList = [
  { path: "/", redirect: "/login" },
  {
    path: "/pos",
    name: "pos",
    component: routeType.Pos,
  },
  {
    path: "/login",
    name: "login",
    component: routeType.login,
  },
  {
    path: "/forgetPwd",
    name: "forgetPwd",
    component: routeType.forgetPwd,
  },
  {
    path: "/main",
    name: "main",
    component: routeType.main,
    children: [
      {
        path: "/home",
        name: "home",
        component: routeType.home,
      },
      {
        path: "/404",
        name: "404",
        component: routeType.errorFour,
      },
      {
        path: "/500",
        name: "500",
        component: routeType.errorFive,
      },
      {
        path: "/noPermission",
        name: "noPermission",
        component: routeType.noPermission,
      },
      {
        path: "/subjectLabel",
        name: "subjectLabel",
        component: routeType.subjectLabel,
      },
      {
        path: "/subjectPrint",
        name: "subjectPrint",
        component: routeType.subjectPrint,
      },
      {
        path: "/newSubjectLabel",
        name: "newSubjectLabel",
        component: routeType.newSubjectLabel,
      },
      {
        path: "/newSubjectPrint",
        name: "newSubjectPrint",
        component: routeType.newSubjectPrint,
      },
      {
        path: "/uploadSubjectPrint",
        name: "uploadSubjectPrint",
        component: routeType.uploadSubjectPrint,
      },
      {
        path: "/user",
        name: "user",
        component: routeType.user,
        children: [
          {
            path: "/list",
            name: "用户列表",
            component: routeType.userList,
          },
          {
            path: "/detail",
            name: "用户详情",
            component: routeType.userDetail,
          },
          {
            path: "/userPrint",
            name: "个人彩印",
            component: routeType.userPrint,
          },
          {
            path: "/newmediaPrint",
            name: "新媒彩印",
            component: routeType.newmediaPrint,
          },
          {
            path: "/enterprisePrint",
            name: "企业彩印",
            component: routeType.enterprisePrint,
          },
          {
            path: "/swindleNumber",
            name: "防诈骗号码来电提示服务",
            component: routeType.swindleNumber,
          },
          {
            path: "/userBlackWhite",
            name: "黑白名单管理",
            component: routeType.userBlackWhite,
          },
          {
            path: "/contentHistory",
            name: "内容设置历史",
            component: routeType.contentHistory,
          },
          {
            path: "/customerHisPkg",
            name: "套餐包订购历史",
            component: routeType.customerHisPkg,
          },
        ],
      },
      {
        path: "/rejectionRecord",
        name: "rejection_record",
        component: routeType.rejection_record,
      },
      {
        path: "/userList",
        name: "userList",
        component: routeType.userList,
      },
      {
        path: "/userPush",
        name: "用户推送规则",
        component: routeType.userPush,
      },
      {
        path: "/batchMod",
        name: "批量修改用户信息",
        component: routeType.batchMod,
      },
      {
        path: "/batchContent",
        name: "内容批设",
        component: routeType.batchContent,
      },
      {
        path: "/batchSetting",
        name: "默认批量设置",
        component: routeType.batchSetting,
      },
      {
        path: "/batchCarrayTurn",
        name: "携转号码导入",
        component: routeType.carray_turn,
      },
      {
        path: "/carrayTurnList",
        name: "携转号码统计",
        component: routeType.carray_turn_list,
      },
      {
        path: "/switchAccount",
        name: "个人用户开户",
        component: routeType.switchAccount,
      },
      {
        path: "/delAccount",
        name: "个人用户销户",
        component: routeType.delAccount,
      },
      {
        //运营
        path: "/bm/procMoni",
        name: "bm_procMoni",
        component: routeType.bm_procMoni,
      },
      {
        path: "/bm/warnMoni",
        name: "bm_warnMoni",
        component: routeType.bm_warnMoni,
      },
      {
        //开销户预警
        path: "/bm/warnMoniNewUser",
        name: "warnMoniNewUser",
        component: routeType.warnMoniNewUser,
      },
      {
        //推送量预警
        path: "/bm/warnMoniPush",
        name: "warnMoniPush",
        component: routeType.warnMoniPush,
      },
      {
        //推送实验预警
        path: "/bm/warnMoniTest",
        name: "warnMoniTest",
        component: routeType.warnMoniTest,
      },
      {
        //告警人员管理
        path: "/bm/warnMoniWarnUser",
        name: "warnMoniWarnUser",
        component: routeType.warnMoniWarnUser,
      },
      {
        //订购关系
        path: "/bm/orderRelationship",
        name: "orderRelationship",
        component: routeType.orderRelationship,
      },
      {
        //推送规则
        path: "/bm/pushRule",
        name: "pushRule",
        component: routeType.pushRule,
      },
      {
        path: "/stat/userStat",
        name: "stat_userStat",
        component: routeType.stat_userStat,
      },
      {
        path: "/stat/pushStat",
        name: "stat_pushStat",
        component: routeType.stat_pushStat,
      },
      {
        path: "/stat/activityStat",
        name: "stat_activityStat",
        component: routeType.stat_activityStat,
      },
      {
        path: "/stat/contentStat",
        name: "stat_contentStat",
        component: routeType.stat_contentStat,
      },
      {
        path: "/stat/hotAdFixTemplateStat",
        name: "stat_hotAdFixTemplateStat",
        component: routeType.stat_hotAdFixedTemplateStat,
      },
      {
        path: "/remind/dataStat",
        name: "remind_dataStat",
        component: routeType.remind_dataStat,
      },
      {
        path: "/remind/numberDimension",
        name: "remind_numberDimension",
        component: routeType.remind_numberDimension,
      },
      {
        path: "/remind/userDimension",
        name: "remind_userDimension",
        component: routeType.remind_userDimension,
      },
      {
        path: "/remind/remindList",
        name: "remind_remindList",
        component: routeType.remind_remindList,
      },

      {
        //用户DIY内容
        path: "/userContentDIY",
        name: "userContentDIY",
        component: routeType.userContentDIY,
      },

      {
        //内容分类
        path: "/contentCategory",
        name: "contentCategory",
        component: routeType.contentCategory,
      },
      {
        //内容标签
        path: "/contentLabel",
        name: "contentLabel",
        component: routeType.contentLabel,
      },
      {
        //文本彩印
        path: "/textCS",
        name: "textCS",
        component: routeType.textCS,
      },
      {
        //文本彩印-新建标签
        path: "/creatMaterial",
        name: "creatMaterial",
        component: routeType.creatMaterial,
      },
      {
        //文本彩印-导入文本彩印
        path: "/importText",
        name: "importText",
        component: routeType.importText,
      },
      {
        //彩印盒
        path: "/CSbox",
        name: "CSbox",
        component: routeType.CSbox,
      },
      {
        //彩印盒-新建彩印盒
        path: "/creatBox",
        name: "creatBox",
        component: routeType.creatBox,
      },
      {
        //彩印盒-导入彩印盒
        path: "/importBox",
        name: "importBox",
        component: routeType.importBox,
      },
      {
        //批量下架内容
        path: "/batchoffSale",
        name: "batchoffSale",
        component: routeType.batchoffSale,
      },
      {
        //下架内容
        path: "/offSale",
        name: "offSale",
        component: routeType.offSale,
      },
      {
        //热线内容
        path: "/hotContent",
        name: "hotContent",
        component: routeType.hotContent,
      },
      {
        //内容审核-待审核
        path: "/contentAudit",
        name: "contentAudit",
        component: routeType.contentAudit,
      },
      {
        //内容审核-待审核-文本彩印
        path: "/contentAudit/personalPrint",
        name: "personalPrint",
        component: routeType.personalPrint,
      },
      {
        //内容审核-待审核-彩印盒
        path: "/contentAudit/printBox",
        name: "printBox",
        component: routeType.printBox,
      },
      // 内容审核-待审核-行业名片号
      {
        path: "/contentAudit/professionCard",
        name: "professionCard",
      },
      // 内容审核-待审核-行业名片号-热线彩印
      {
        path: "/contentAudit/professionCard/hotLinePrint",
        name: "professionHotLinePrint",
        component: routeType.professionHotLinePrint,
      },
       // 内容审核-待审核-行业名片号-名片号彩印
      // {
      //   path: "/contentAudit/professionCard/cardPrint",
      //   name: "professionCardPrint",
      //   component: routeType.professionCardPrint,
      // },
      {
        //内容审核-待审核-企业彩印
        path: "/contentAudit/companyPrint",
        name: "companyPrint",
        component: routeType.companyPrint,
      },
      {
        //内容审核-待审核-新媒彩印
        path: "/contentAudit/mediaPrint",
        name: "mediaPrint",
        component: routeType.mediaPrint,
      },
      {
        //内容审核-待审核-省市提醒模板
        path: "/contentAudit/provTemplate",
        name: "省市提醒模板",
        component: routeType.provTemplate,
      },
      {
        //内容审核-待审核-特定号码提醒模板
        path: "/contentAudit/numTemplate",
        name: "numTemplate",
        component: routeType.numTemplate,
      },
      {
        //内容审核-待审核-增强群发彩印
        path: "/contentAudit/enhanceMassPrint",
        name: "enhanceMassPrint",
        component: routeType.enhanceMassPrint,
      },

      {
        // 内容审核-待审核-增强群发彩印详情
        path: "/contentAudit/massDetail",
        name: "massDetail",
        component: routeType.massDetail,
      },
      // 内容审核-初审审核记录-行业名片号-热线彩印审核明细
      {
        path: "/contentAudit/professionHotLinePrintViewDetails",
        name: "hotLinePrint",
        component: routeType.professionHotLinePrintViewDetails,
      },
      {
        //内容审核-审核记录-已通过审核记录
        path: "/contentAudit/passRecords",
        name: "passRecords",
        component: routeType.passRecords,
      },
      {
        //内容审核-审核记录-审核明细
        path: "/contentAudit/reviewDetails",
        name: "reviewDetails",
        component: routeType.reviewDetails,
      },
      {
        //内容审核-审核记录-企业已通过审核记录
        path: "/contentAudit/enterprisepassRecords",
        name: "passRecords",
        component: routeType.enterprisepassRecords,
      },
      {
        //内容审核-审核记录-企业审核明细
        path: "/contentAudit/enterprisereviewDetails",
        name: "名片文本审核记录",
        component: routeType.enterprisereviewDetails,
      },
      {
        //内容审核-审核记录-省市已通过审核记录
        path: "/contentAudit/provTemplatepassRecords",
        name: "省市已通过审核记录",
        component: routeType.provTemplatepassRecords,
      },
      {
        //内容审核-审核记录-省市审核明细
        path: "/contentAudit/provTemplatereviewDetails",
        name: "省市审核明细",
        component: routeType.provTemplatereviewDetails,
      },
      {
        //内容审核-审核记录-名片彩印已审核明细
        path: "/contentAudit/cardprintviewDetails",
        name: "cardprintviewDetails",
        component: routeType.cardprintviewDetails,
      },
      {
        //内容审核-审核记录-热线彩印已审核明细
        path: "/contentAudit/hotlineprintviewDetails",
        name: "hotlineprintviewDetails",
        component: routeType.hotlineprintviewDetails,
      },
      {
        //内容审核-审核记录-热线彩印固定模板已审核明细
        path: "/contentAudit/hotlineGdTemplatePrintViewDetails",
        name: "hotlineGdTemplatePrintViewDetails",
        component: routeType.hotlineGdTemplatePrintViewDetails,
      },
      {
        //内容审核-审核记录-广告彩印已审核明细
        path: "/contentAudit/adprintviewDetails",
        name: "adprintviewDetails",
        component: routeType.adprintviewDetails,
      },
      {
        //内容审核-审核记录-咪咕名片
        path: "/contentAudit/miguCardviewDetails",
        name: "miguCardviewDetails",
        component: routeType.miguCardviewDetails,
      },
      {
        //内容审核-审核记录-咪咕名片详情
        path: "/contentAudit/miguCardviewVerifyDetails",
        name: "miguCardviewVerifyDetails",
        component: routeType.miguCardviewVerifyDetails,
      },
      // 名片彩印
      {
        path: "/contentAudit/cardPrint",
        name: "cardPrint",
        component: routeType.cardPrint,
      },
      // 热线彩印
      {
        path: "/contentAudit/hotlinePrint",
        name: "hotlinePrint",
        component: routeType.hotlinePrint,
      },
      //待审核- 热线彩印固定模板
      {
        path: "/contentAudit/hotlineGdTemplatePrint",
        name: "hotlineGdTemplatePrint",
        component: routeType.hotlineGdTemplatePrint,
      },
      // 咪咕名片
      {
        path: "/contentAudit/miguCard",
        name: "miguCard",
        component: routeType.miguCard,
      },
      // 咪咕名片
      {
        path: "/contentAudit/miguCardDetails",
        name: "miguCardDetails",
        component: routeType.miguCardDetails,
      },
      // 广告彩印
      {
        path: "/contentAudit/adPrint",
        name: "adPrint",
        component: routeType.adPrint,
      },
      // 广告彩印-屏显
      {
        path: "/contentAudit/screenShow",
        name: "screenShow",
        component: routeType.screenShow,
      },
      // 广告彩印-挂机
      {
        path: "/contentAudit/hangUp",
        name: "hangUp",
        component: routeType.hangUp,
      },
      // 彩印专题审核
      {
        path: "/contentAudit/colourPrint",
        name: "colourPrint",
        component: routeType.colourPrint,
      },
      // 彩印专题审核记录
      {
        path: "/contentAudit/colourPrintviewDetails",
        name: "colourPrintviewDetails",
        component: routeType.colourPrintviewDetails,
      },

      {
        // 内容审核-审核记录-增彩群发审核明细列表
        path: "/contentAudit/enhanceMassReviewList",
        name: "enhanceMassReviewList",
        component: routeType.enhanceMassReviewList,
      },

      // 010新增
      {
        // 内容审核-复审-名片文本彩印
        path: "/contentReAudit/companyPrint",
        name: "companyPrint",
        component: routeType.reAuditCompanyPrint,
      },
      {
        // 内容审核-复审-名片挂彩彩印
        path: "/contentReAudit/cardPrint",
        name: "cardPrint",
        component: routeType.reAuditCardPrint,
      },
      {
        // 内容审核-复审-热线彩印
        path: "/contentReAudit/hotlinePrint",
        name: "hotlinePrint",
        component: routeType.reAuditHotlinePrint,
      },
      {
        // 内容审核-复审-企业通知
        path: "/contentReAudit/enhanceMassPrint",
        name: "enhanceMassPrint",
        component: routeType.reAuditEnhanceMassPrint,
      },
      {
        // 内容审核-复审-广告彩印
        path: "/contentReAudit/adPrint",
        name: "adPrint",
        component: routeType.reAuditAdPrint,
      },
      {
        // 内容审核-复审-热线彩印固定模板
        path: "/contentReAudit/hotlineGdTemplatePrint",
        name: "hotlineGdTemplatePrint",
        component: routeType.reAuditHotlineGdTemplatePrint,
      },
      {
        // 内容审核-复审记录-名片文本审核记录
        path: "/contentReAudit/enterprisereviewDetails",
        name: "名片文本审核记录",
        component: routeType.reAuditEnterprisereviewDetails,
      },
      {
        // 内容审核-复审记录-名片挂彩审核记录
        path: "/contentReAudit/cardprintviewDetails",
        name: "cardprintviewDetails",
        component: routeType.reAuditCardprintviewDetails,
      },
      {
        // 内容审核-复审记录-热线彩印审核记录
        path: "/contentReAudit/hotlineprintviewDetails",
        name: "hotlineprintviewDetails",
        component: routeType.reAuditHotlineprintviewDetails,
      },
      {
        // 内容审核-复审记录-企业通知审核记录
        path: "/contentReAudit/enhanceMassReviewList",
        name: "enhanceMassReviewList",
        component: routeType.reAuditEnhanceMassReviewList,
      },
      {
        // 内容审核-复审记录-广告彩印审核记录
        path: "/contentReAudit/adprintviewDetails",
        name: "adprintviewDetails",
        component: routeType.reAuditAdprintviewDetails,
      },
      {
        // 内容审核-复审记录-热线彩印固定模板审核记录
        path: "/contentReAudit/hotlineGdTemplatePrintViewDetails",
        name: "hotlineGdTemplatePrintViewDetails",
        component: routeType.reAuditHotlineGdTemplatePrintViewDetails,
      },

      //个人彩印
      {
        path: "/textPrinting",
        name: "textPrinting",
        component: routeType.textPrinting,
      },
      //个人彩印-编辑
      {
        path: "/upPrinting",
        name: "upPrinting",
        component: routeType.upPrinting,
      },
      //个人彩印盒-编辑
      {
        path: "/upPrintingH",
        name: "upPrintingH",
        component: routeType.upPrintingH,
      },

      //企业彩印
      {
        path: "/companyPrinting",
        name: "companyPrinting",
        component: routeType.companyPrinting,
      },

      //企业彩印-编辑
      {
        path: "/upcompanyPrinting",
        name: "upcompanyPrinting",
        component: routeType.upcompanyPrinting,
      },

      //企业彩印盒-编辑
      {
        path: "/upcompanyPrintingH",
        name: "upcompanyPrintingH",
        component: routeType.upcompanyPrintingH,
      },

      //新媒彩印
      {
        path: "/mediaPrinting",
        name: "mediaPrinting",
        component: routeType.mediaPrinting,
      },

      //新媒彩印-编辑
      {
        path: "/upmediaPrinting",
        name: "upmediaPrinting",
        component: routeType.upmediaPrinting,
      },

      //省市地区提醒模版
      {
        path: "/remindPrinting",
        name: "省市地区提醒模版",
        component: routeType.remindPrinting,
      },

      //省市地区提醒模版-编辑
      {
        path: "/upremindPrinting",
        name: "upremindPrinting",
        component: routeType.upremindPrinting,
      },

      //特定号码提醒模版
      {
        path: "/givenremindPrinting",
        name: "givenremindPrinting",
        component: routeType.givenremindPrinting,
      },

      //特定号码提醒模版-编辑
      {
        path: "/upgivenremindPrinting",
        name: "upgivenremindPrinting",
        component: routeType.upgivenremindPrinting,
      },

      //我的个人彩印
      {
        path: "/myPrinting",
        name: "myPrinting",
        component: routeType.myPrinting,
      },

      //我的企业彩印
      {
        path: "/mycompanyPrinting",
        name: "mycompanyPrinting",
        component: routeType.mycompanyPrinting,
      },

      //我的新媒彩印
      {
        path: "/mymediaPrinting",
        name: "mymediaPrinting",
        component: routeType.mymediaPrinting,
      },

      //我的省市地区提醒模版
      {
        path: "/myremindPrinting",
        name: "省市地区提醒模版",
        component: routeType.myremindPrinting,
      },

      //我的特定号码提醒模版
      {
        path: "/mygivenremindPrinting",
        name: "mygivenremindPrinting",
        component: routeType.mygivenremindPrinting,
      },
      {
        path: "/cdpRuleList",
        name: "推送规则同步",
        component: routeType.cdpRuleList,
      },

      {
        path: "/sysRedList",
        name: "系统红名单",
        component: routeType.sysRedList,
      },
      {
        path: "/sysBlackList",
        name: "系统黑名单",
        component: routeType.sysBlackList,
      },
      {
        path: "/smsCommand",
        name: "短信指令模板",
        component: routeType.smsCommand,
      },
      {
        path: "/smsForwardCommand",
        name: "短信转发指令模板",
        component: routeType.smsForwardCommand,
      },

      {
        path: "/smsSysDef",
        name: "系统运营短信",
        component: routeType.smsSysDef,
      },
      {
        path: "/smsAlarm",
        name: "告警短信",
        component: routeType.smsAlarm,
      },

      {
        path: "/thresholdConf",
        name: "阈值配置",
        component: routeType.thresholdConf,
      },
      {
        path: "/otherConf",
        name: "其他配置",
        component: routeType.otherConf,
      },

      {
        path: "/proAndRegionList",
        name: "省号和区号",
        component: routeType.proAndRegionList,
      },

      {
        path: "/sectionManagerList",
        name: "号段管理",
        component: routeType.sectionManagerList,
      },

      {
        path: "/setMealList",
        name: "彩印套餐功能配置",
        component: routeType.setMealList,
      },

      {
        path: "/deliveryWay",
        name: "异网通道设置",
        component: routeType.deliveryWay,
      },

      {
        path: "/deliveryWay",
        name: "异网通道管理",
        component: routeType.deliveryWay,
      },
      {
        path: "/setWhiteEffDate",
        name: "白名单有效期",
        component: routeType.setWhiteEffDate,
      },
      {
        path: "/roleList",
        name: "角色管理",
        component: routeType.roleList,
      },

      {
        path: "/sysUserList",
        name: "系统用户管理",
        component: routeType.sysUserList,
      },

      {
        path: "/logList",
        name: "日主管理",
        component: routeType.logList,
      },

      {
        path: "/asynTaskList",
        name: "异步任务管理",
        component: routeType.asynTaskList,
      },
      {
        path: "/fileDownLoad",
        name: "导出文件下载",
        component: routeType.fileDownLoad,
      },
      //提醒彩印
      {
        path: "/remind",
        name: "提醒彩印",
        component: routeType.remind,
        redirect: "/remind/classifyset/classify",
        children: [
          {
            path: "/remind/classifyset",
            name: "分类设置",
            redirect: "/remind/classifyset/classify",
            component: routeType.classifyset,
            children: [
              {
                path: "/remind/classifyset/classify",
                name: "分类",
                component: routeType.classify,
              },
              // {
              //     path:'/remind/classifyset/standard',
              //     name:'标准类型',
              //     component:routeType.standard
              // }
            ],
          },
          {
            path: "/remind/masterplate",
            name: "提醒模版",
            redirect: "/remind/masterplate/provinces",
            component: routeType.masterplate,
            children: [
              {
                path: "/remind/masterplate/provinces",
                name: "省市提醒模版",
                component: routeType.provinces,
              },
              {
                path: "/remind/masterplate/specific",
                name: "特定号码提醒模版",
                component: routeType.specific,
              },
            ],
          },
          {
            path: "/remind/numberstore",
            name: "号码库管理",
            component: routeType.effect,
          },
          {
            path: "/remind/apimanage",
            name: "API能力开放",
            redirect: "/remind/apimanage/apilist",
            component: routeType.apimanage,
            children: [
              {
                path: "/remind/apimanage/apilist",
                name: "API管理列表",
                component: routeType.apilist,
              },
              {
                path: "/remind/apimanage/enterprise",
                name: "API企业接入管理",
                component: routeType.enterprise,
              },
              {
                path: "/remind/apimanage/inquiry",
                name: "企业查询记录",
                component: routeType.inquiry,
              },
            ],
          },
          {
            path: "/remind/offline",
            name: "离线包能力开放",
            redirect: "/remind/offline/manage",
            component: routeType.offline,
            children: [
              {
                path: "/remind/offline/manage",
                name: "离线包管理",
                component: routeType.manage,
              },
              {
                path: "/remind/offline/rule",
                name: "离线包规则管理",
                component: routeType.rule,
              },
              {
                path: "/remind/offline/download",
                name: "企业下载记录",
                component: routeType.download,
              },
            ],
          },
          {
            path: "/remind/provincial",
            name: "分省规则管理",
            component: routeType.provincial,
          },
        ],
      },
      //活动管理
      {
        path: "/activityAll",
        name: "活动管理",
        component: routeType.activityManager,
      },
      //监控指标
      {
        path: "/monitorList",
        name: "监控指标",
        component: routeType.monitorList,
      },
      //告警配置
      {
        path: "/setAlarm",
        name: "告警配置",
        component: routeType.setAlarm,
      },

    ],
  },
];
const routers = new Router({
  mode: "history",
  base: __dirname,
  routes: routerList,
});

function getQueryString(name) {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");

  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return null;
}

function getQueryString2(url, name) {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  let r = url.match(reg);
  if (r != null) return unescape(r[2]);
  return null;
}

routers.beforeEach(async (to, form, next) => {
  let queryString = getQueryString("ticket");
  if (queryString) {
    console.log("4A携带token单点登录过来" + queryString);
    if (queryString) {
      var req = { ticket: queryString };
      let result = await post("/sys/4aAuth/JKService/getToken", req);
      console.log(result);
      var data = result.data;
      console.log(data);

      if (data) {
        let tokenString = data.token;
        if (tokenString) {
          sessionStorage.setItem("TOKEN", tokenString);
        }
        console.log("token信息" + +tokenString);
        let userInfo = data.userInfo;
        console.log("userInfo信息" + +userInfo);
        if (userInfo) {
          sessionStorage.setItem("userInfo", userInfo);
        }
      } else {
        return;
      }

      /*let menuList = getQueryString("menuList");
            console.log("menuList信息" +  + menuList)
            if(menuList){
                sessionStorage.setItem('menuList', menuList);
            }
            let refuseList = getQueryString("refuseList");
            console.log("refuseList信息" +  + refuseList)
            if(refuseList){
                sessionStorage.setItem('refuseList', refuseList);
            }*/
    }
    //loginToHome.methods.menuListQuery();
    if (to.fullPath !== "/home") {
      next("/home");
    } else {
      next();
    }
  }
  let token = sessionStorage.getItem("TOKEN");
  if (token) {
    store.dispatch("coreModule/refuseListQuery", {}).then((res) => {
      console.log("test" + JSON.stringify(res));
      sessionStorage.setItem("refuseList", JSON.stringify(res));
    });
    console.log("menu", store.state.menuList);
    if (store.state.menuList.length == 0) {
      store.dispatch("coreModule/menuListQuery", {}).then((res) => {
        if (res.result === "success") {
          const list = [];
          for (let i = 0; i < res.data.list.length; i++) {
            if ("menu" == res.data.list[i].type) {
              list.push(res.data.list[i]);
            }
          }
          console.log("menuList", list);
          store.dispatch("setMenuList", list).finally(() => {
            next();
          });
          // sessionStorage.setItem('menuList', JSON.stringify(res.data.list));
        }
      });
    } else {
      next();
    }
    //权限校验
    /*if(window.location.href.indexOf("/home") != -1 || window.location.href.indexOf("/login") != -1){
            next();
        }else {
            //debugger
            let menuList = JSON.parse(sessionStorage.getItem('menuList'))
            let urlHidden = false;
            for(let i=0;i<menuList.length;i++){
                if(menuList[i].children != undefined && menuList[i].children.length > 0){
                    for(let j=0;j<menuList[i].children.length;j++){
                        if(menuList[i].children[j].href===to.fullPath){
                            urlHidden=true;
                            next();
                            break;
                        }else{
                            if(menuList[i].children[j].children != undefined && menuList[i].children[j].children.length > 0){
                                for(let k=0;k<menuList[i].children[j].children.length;k++){
                                    if(menuList[i].children[j].children[k].href===to.fullPath){
                                        urlHidden=true;
                                        next();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(!urlHidden){
                routers.go(-1);
            }
        }*/
  } else {
    console.log(to.fullPath);
    if (to.fullPath !== "/login" && to.fullPath !== "/forgetPwd") {
      next("/login");
    } else {
      next();
    }
  }
});

export default routers;
