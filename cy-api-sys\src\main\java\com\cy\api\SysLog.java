package com.cy.api;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.cy.common.ResultListCommon;
import com.cy.common.SysLogCommon;
import com.cy.model.SysLogModel;
import com.cy.model.SysResourcesModel;

public interface SysLog {

	@RequestMapping(value = "/sysLogCore/getAllSysResources")
	List<SysResourcesModel> getAllSysResources();

	@RequestMapping(value = "/sysLogCore/getSysLogPage")
	ResultListCommon getSysLogPage(SysLogCommon common);
	
	@RequestMapping(value = "/sysLogCore/exportSysLogInfo")
	List<SysLogModel> exportSysLogInfo(SysLogCommon common);
	
	
	@RequestMapping(value = "/sysLogCore/insertSysLogInfo")
	int insertSysLogInfo(SysLogModel sysLogModel);
}
