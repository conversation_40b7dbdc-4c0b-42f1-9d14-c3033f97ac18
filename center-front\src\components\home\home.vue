<template>
    <div class="fun_page">
        <h1 class="user-title">彩印运营数据</h1>
        <div class="chartArea">
            <el-form :inline="true" :model="searchForm1" size="small">
                <el-row>
                    <el-col :span="24">
                        <el-form-item>
                            <el-date-picker v-model="searchForm1.date2" type="daterange" range-separator="至"
                                            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 230px;" :picker-options="pickerOptions"/>
                        </el-form-item>
                        <el-form-item label="省份：">
                            <el-select v-model="searchForm1.provinceId" clearable>
                                <el-option v-for="item in provinceList"
                                           :key="item.provinceCode"
                                           :label="item.provinceName"
                                           :value="item.provinceCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search">查询</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-row :gutter="2">
                <el-col :span="15">
                    <!-- <span style="margin: 0 0 0 20px;font-size: 20px">区域分布</span> -->
                    <!-- <ve-map :data="provinceInfo" :settings="mapchartSettings "></ve-map> -->
                    <div id="myChart" style="width:100%;height:400px;"></div>
                </el-col>
                <el-col :span="9">
                    <el-table :data="provinceInfo.rows" height='443'>
                        <el-table-column prop="provinceName" label="省份"></el-table-column>
                        <el-table-column prop="total" label="人数(单位:人)"></el-table-column>
                    </el-table>
                </el-col>
            </el-row>
            <el-row :gutter="20" class="main-grid-title">
                <el-col :span="6" class="main-titlebox">
                    <div class="grid-content bg-purple" v-if="isShow">
                        <span name="ble" style="margin-left:80px">新增用户数</span>
                        <ve-pie :data="piechartData" :colors="['#31DFDF','#66cc66']" :tooltip="tooltip" :settings="piechartSettings" height="240px"></ve-pie>
                    </div>
                </el-col>
                <el-col :span="6" class="main-titlebox">
                    <div class="grid-content bg-purple" v-if="isShow">
                        <span name="ble" style="margin-left:80px">提醒号码分类</span>
                        <ve-pie :data="calllMobileInfo" :tooltip="tooltip" :settings="calllMobileInfoSettings" height="240px" style="top:-60px"></ve-pie>
                    </div>
                </el-col>
                <el-col :span="6" class="main-titlebox">
                    <div class="grid-content bg-purple" v-if="isShow">
                        <span name="ble" style="margin-left:80px">呼叫推送量</span>
                        <ve-histogram :data="callPushInfo" :legend="legend" :settings="callPushInfoSettings" :colors="['#993399']"  height="240px"></ve-histogram>
                    </div>
                </el-col>
                <el-col :span="6" class="main-titlebox">
                    <div class="grid-content bg-purple" v-if="isShow">
                        <span name="ble" style="margin-left:80px">提醒次数</span>
                        <ve-line :data="callInfo"  :settings="callInfoSettings" :legend-visible="legend_visible" :colors="['#ffcc33']"  height="240px" v-loading="chartLoading"/>
                    </div>
                </el-col>

            </el-row>
        </div>
        <div class="chartArea">
            <h1 class="user-title">活跃数据统计</h1>
            <div class="ceshi">
                <ul>
                    <li>
                        <center>
                            <p style="height:37px">活跃用户数</p>
                            <p style="font-size: 25px">{{activeUser}}</p>
                        </center>
                    </li>
                    <li>
                        <center>
                            <p style="height:37px">活跃次数</p>
                            <p style="font-size: 25px">{{activeCount}}</p>
                        </center>
                    </li>
                    <li>
                        <center>
                            <p style="height:37px">修改彩印数</p>
                            <p style="font-size: 25px">{{cyModifyCount}}</p>
                        </center>
                    </li>
                    <li>
                        <center>
                            <p style="height:37px">彩印推送次数</p>
                            <p style="font-size: 25px">{{cyDeliveryCount}}</p>
                        </center>
                    </li>
                </ul>
            </div>
        </div>
        <div class="chartArea">
            <el-row :gutter="20">

                <el-col :span="11">
                    <div class="" ><!--v-if="isShow"-->
                        <h3 class="user-title" style="background:none;">有效用户数折线图</h3>
                        <ve-line :data="effectUserInfo"  :settings="effectUserInfoSettings" :colors="chartColors" :legend-visible="legend_visible"  v-loading="chartLoading" class="graid-box" style="margin-top:15px;"/>
                    </div>
                </el-col>
                <el-col :span="13">
                    <div class="">
                        <h1 class="main-title userpadding">修改彩印次数</h1>
                        <el-table
                                :data="cyModifyInfo"
                                height='450'
                                style="width: 100%;margin-left:10px;margin-top:10px;">
                            <el-table-column
                                    prop="provinceName"
                                    label="省份"
                                    width="180">
                            </el-table-column>
                            <el-table-column
                                    prop="modifyCount"
                                    label="修改次数"
                                    width="180">
                            </el-table-column>
                            <el-table-column
                                    prop="diyCount"
                                    label="DIY彩印次数">
                            </el-table-column>
                        </el-table>
                    </div>
                </el-col>
            </el-row>
        </div>
        <div class="user-line">
        </div>
        <!--图表信息-->
    </div>
</template>

<script>
    import Vue from 'vue'
    import VeLine from 'v-charts/lib/line'
    import VePie from 'v-charts/lib/pie'
    import VeHistogram from 'v-charts/lib/histogram'
    import VeMap from 'v-charts/lib/map'
    import moment from 'moment'
    import axios from '../../../node_modules/axios/dist/axios';
    import {fmoney} from './../../util/core.js';
    import echarts from 'echarts/lib/echarts';
    import 'echarts/lib/chart/map';
    import 'echarts/map/js/china.js';
    import 'echarts/lib/component/tooltip';
    import 'echarts/lib/component/title';
    Vue.component(VeLine.name, VeLine)
    Vue.component(VePie.name, VePie)
    Vue.component(VeHistogram.name, VeHistogram)
    Vue.component(VeMap.name, VeMap)
    export default {
        name: 'procMoni',
        data() {
            return {
                searchForm1:{
                    date1:'last_seven_days',
                    date2:[],
                    startDate:'',
                    endDate:'',
                    selectedOptions:[],
                    provinceId:'',
                    cityId:'',
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                searchForm2:{
                    date:'',
                    selectedOptions:[],
                    startDate:'',
                    endDate:'',
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                //省份列表
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                regionList:new Array(),//城市列表
                chartData:{},
                chartLoading:false,
                chartSettings:{},
                tableData: [],
                tableLoading:false,
                pickerOptions:{
                    disabledDate:function (today) {
                        return today.getTime()>Date.now();
                    }
                },
                cyModifyInfo:[],
                effectUserInfo:{rows:{}},
                provinceInfo:{rows:[]},
                callInfo:{rows:{}},
                calllMobileInfo:{rows:{}},
                callPushInfo:{rows:{}},
                callPushInfoSettings:{},
                activeUser:0,
                activeCount:0,
                cyModifyCount:0,
                cyDeliveryCount:0,
                newUser:0,
                piechartData:{},
                isShow:false
            }
        },
        computed:{

        },
        mounted(){
            this.search();
            this.drow(this.tableData);
        },
        created: function () {
            this.searchForm1.date2=[this.getDay(-1),this.getDay(0)];
            this.chartColors=['#c23531','#2f4554', '#61a0a8',
                '#d48265', '#91c7ae','#749f83',
                '#ca8622', '#bda29a','#6e7074',
                '#546570', '#c4ccd3']
            this.legend_visible=false;
            this.chartSettings = {
                // stack: { '售价': ['成本', '利润'] },
                area: true
            };
            this.piechartData = {
                columns: ['日期', '成本', '利润'],
                rows: [
                    { '日期': '付费用户数', '成本': 123, '利润': 333333 },
                    { '日期': '免费用户数', '成本': 1223, '利润': 666666 }
                ]
            }

            this.legend = {
                show:false
            }
            this.piechartSettings = {
                dimension: 'name',
                metrics: 'data',
                radius: 70,
                legendLimit:1,
                offsetY:100,
                labelLine:{show:false},
                label:{
                    formatter:'{b}',
                    position:'inside'
                }
            }
            this.calllMobileInfoSettings = {
                dimension: 'name',
                metrics: 'calllMobileCount',
                radius: 70,
                legendLimit:2,
                offsetY:160,
                labelLine:{show:false},
                label:{
                    formatter:'{b}',
                    position:'inside'
                }
            }

            this.callPushInfoSettings = {
                labelMap: {
                    callPushCount: '推送量',
                },
                'xAxis.0.axisLabel.rotate': 45
            }
            this.callInfoSettings = {
                metrics: ['callCount'],
                dimension: ['statsDate'],
                labelMap: {
                    callCount: '次数',
                }
            }
            this.effectUserInfoSettings = {
                metrics: ['effectUser'],
                dimension: ['statsDate'],
                labelMap: {
                    effectUser: '用户数',
                }
            }

            //X轴
            this.xAxis=[
                {
                    axisLabel:{
                        interval:0,
                        rotate:45
                    }
                }
            ]

            this.tooltip={
                trigger: 'item',
                formatter: "{b} <br/> {c} ({d}%)"
            }
            this.mapchartSettings = {
                position: 'china',
                // selectData: true,
                selectedMode: 'single',
                dimension: 'provinceName',
                metrics: ['effectUser', 'zhanbi'],
                labelMap: {
                    effectUser: '有效用户数',
                    zhanbi: '占比',
                },
                dataType: {
                    'zhanbi': 'percent'
                }
            }
        },
        methods: {
            check(vm){
                if (vm.searchForm1.provinceId == '' || vm.searchForm1.provinceId == undefined) {
                    vm.$message.error("请选择省份");
                    return false;
                }
                return true;
            },
            getDay(day){
                var today = new Date();

                var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;

                today.setTime(targetday_milliseconds); //注意，这行是关键代码

                var tYear = today.getFullYear();
                var tMonth = today.getMonth();
                var tDate = today.getDate();
                tMonth = this.doHandleMonth(tMonth + 1);
                tDate = this.doHandleMonth(tDate);
                return tYear+"-"+tMonth+"-"+tDate;
            },
            doHandleMonth(month){
                var m = month;
                if(month.toString().length == 1){
                    m = "0" + month;
                }
                return m;
            },
            getDateStr(today){
                var tYear = today.getFullYear();
                var tMonth = today.getMonth();
                var tDate = today.getDate();
                tMonth = this.doHandleMonth(tMonth + 1);
                tDate = this.doHandleMonth(tDate);
                return tYear+"-"+tMonth+"-"+tDate;
            },
            search() {
                const vm = this;
                // if(!vm.check(vm)){
                //     return false;
                // }
                vm.tableLoading=true;
                vm.searchForm1.locationId = vm.searchForm1.selectedOptions.join("");
                vm.searchForm1.startDate = vm.searchForm1.date2[0];
                vm.searchForm1.endDate = vm.searchForm1.date2[1];
                // let loading=this.$loading(true);
                var params = {

                    startDate:this.getDateStr(new Date(vm.searchForm1.date2[0])),
                    endDate:this.getDateStr(new Date(vm.searchForm1.date2[1])),
                    // startDate:vm.searchForm1.date2[0],
                    // endDate:vm.searchForm1.date2[1],
                    provinceId:vm.searchForm1.provinceId
                    // provinceId:'01'
                }
                // if(vm.searchForm1.date1=='last_seven_days'){
                //     params.startDate=this.getDay(-7);
                //     params.endDate=this.getDay(0);
                // }else if(vm.searchForm1.date1=='last_fifteen_days'){
                //     params.startDate=this.getDay(-15);
                //     params.endDate=this.getDay(0);
                // }else if(vm.searchForm1.date1=='last_month'){
                //     params.startDate=this.getDay(-30);
                //     params.endDate=this.getDay(0);
                // }

                //从服务器获取数据
                axios
                    .post(`${this.proxyUrl}/oper/sop`,params,{
                        headers: {
                            'CY-operation': 'getHomepageInfos'
                        }
                    })
                    .then(function(response) {
                        // loading.close();
                        vm.cyDeliveryCount=fmoney(response.data.data.cyDeliveryCount,0);
                        vm.cyModifyCount=fmoney(response.data.data.cyModifyCount,0);
                        vm.activeCount=fmoney(response.data.data.activeCount,0);
                        vm.activeUser=fmoney(response.data.data.activeUser,0);
                        vm.newUser=response.data.data.newUser;

                        vm.piechartData = {
                            columns: ['name','data'],
                            rows: [
                                { 'name': '付费用户数', 'data': response.data.data.payUser},
                                { 'name': '免费用户数', 'data': response.data.data.freeUser}
                            ]
                        }

                        vm.callPushInfo={
                            columns: ['name', 'callPushCount'],
                            rows:response.data.data.callPushInfo
                        }
                        var provinceInfolist=[];

                        if(response.data.data.provinceInfo!=undefined){

                            for(var i=0;i<response.data.data.provinceInfo.length;i++){
                                provinceInfolist.push(response.data.data.provinceInfo[i]);
                            }
                        }

                        var count=0;
                        for(var i=0;i<provinceInfolist.length;i++){
                            count+=provinceInfolist[i].effectUser;
                        }
                        for(var i=0;i<provinceInfolist.length;i++){
                            if(count!=0){
                                provinceInfolist[i].zhanbi=parseFloat((provinceInfolist[i].effectUser/count).toFixed(4));
                            }

                        }
                        vm.provinceInfo = {
                            columns: ['effectUser', 'zhanbi'],
                            rows:provinceInfolist
                        }
                        let mapList=new Array();
                        for(let i=0;i<provinceInfolist.length;i++){
                            mapList.push({
                                name:provinceInfolist[i].provinceName,
                                value:(provinceInfolist[i].zhanbi*100).toFixed(2),
                                total:provinceInfolist[i].total
                            })
                        }
                        vm.drow(mapList);
                        vm.callInfo={
                            rows:response.data.data.callInfo
                        }
                        vm.calllMobileInfo ={
                            rows:response.data.data.calllMobileInfo
                        }

                        vm.effectUserInfo = {
                            rows:response.data.data.effectUserInfo
                        }
                        vm.cyModifyInfo = response.data.data.cyModifyInfo;
                        vm.isShow=true;
                    })
                    .catch(function (error) {
                        // console.log(error);
                    }).finally(function () {
                    vm.tableLoading=false;
                });
            },
            //根据省份查询城市列表
            selectProvince(){
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.form.provinceId},{emulateJSON:true})
                    .then((res)=>{
                        this.regionList=res.data;
                    })
            },
            //画图
            drow(provinceInfolist){
                let myChart=echarts.init(document.getElementById('myChart'));
                let option = {
                    title: {
                        text:'区域分布'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter:function(a){
                            if(a && a.data){
                                return a.data.name+'</br>占比:'+a.data.value+'%</br>'+'人数:'+a.data.total;
                            }
                        }
                    },
                    dataRange: {
                        orient: 'horizontal',
                        min: 0,
                        max: 100,
                        text:['高','低'],           // 文本，默认为数值文本
                        splitNumber:0,
                        inRange: {
                            color: ['lightskyblue','yellow', 'orangered']
                        }
                    },
                    series: [
                        {
                            name: '中国',
                            type: 'map',
                            mapType: 'china', // 自定义扩展图表类型
                            itemStyle:{
                                normal:{label:{show:true}},
                                emphasis:{label:{show:true}}
                            },
                            data:provinceInfolist,
                        }
                    ]
                };
                myChart.setOption(option);
            }
        }
    }
</script>
<style scoped>
    .user-line {
        /* width: 94%; */
        margin: 0 auto;
        margin-top: 3%;
        margin-bottom: 3%;
        border-bottom: 1px solid #439ae6;
    }
    .el-radio-button:first-child .el-radio-button__inner,.el-radio-button:last-child .el-radio-button__inner{
        border-radius:0;
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #434343;
        border-color:#DDDFE6;
        box-shadow:-1px 0 0 0 #DDDFE6;
        color:#fff;
    }
    .chartArea{
        border:1px solid #DDDFE6;
        margin-top:-1px;
        /* background-color: #F2F2F2; */
    }
    .chartArea form{
        margin:20px 0 0 20px;
    }
    .chartArea .el-radio-button__inner{
        background:transparent;
        border: 1px solid transparent;
    }
    .chartArea .el-radio-button__orig-radio:checked+.el-radio-button__inner{
        background-color:#3B9ED8;
    }

    .el-row {
        margin-bottom: 20px;
    }
    :last-child {
        margin-bottom: 0;
    }
    .el-col {
        border-radius: 4px;
    }
    .bg-purple-dark {
        background: #99a9bf;
    }
    .bg-purple {
        /* background: #d3dce6; */
    }
    .bg-purple-light {
        background: #e5e9f2;
    }
    .grid-content {
        border-radius: 4px;
        min-height: 36px;
    }

    #pro{
        height:543px !important;
    }

    .row_index > div:first-child {
        margin-right: 15px;
    }
</style>
