package com.cy.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import com.cy.jwt.JwtUtil.JWTHelper;

import feign.RequestInterceptor;
import feign.RequestTemplate;

@Component
public class FeginClientConfig {
    
    @Bean
    public RequestInterceptor headerInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate requestTemplate) {
                String token = JWTHelper.getToken();
                if(StringUtils.isNotBlank(token)){
                    requestTemplate.header("token", token);
                }
                    
            }
        };
    }
 
}