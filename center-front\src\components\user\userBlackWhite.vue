<template>
    <div>
        <div>
            <el-row style="width: 94%;margin: 15px auto">
                <span style="font-weight:bold">当前状态:</span>
                <span>{{userBwTypeValue}}</span>
            </el-row>
            <el-row style="width: 94%;margin: 15px auto">
                <span style="font-weight:bold">接收方号码:</span>
                <el-input v-model="param.recPhone" max-length="11" style="width:200px" size="small"></el-input>
                <el-button @click="pageQuery(param);" class="el-button el-button--primary" type="small">查询</el-button>
                <el-button v-if="this.tableData.userBwType != 2" @click="open()" class="el-button el-button--primary" type="small">新增</el-button>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <el-table :data="tableData" border style="margin:0 auto;width: 100%;">
                    <el-table-column prop="receivePhone" label="接收号码">
                    </el-table-column>
                    <el-table-column label="类型">
                        <template slot-scope="scope">
                            <span>{{scope.row.bwType == 3 ? '白名单': '黑名单'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="状态">
                        <template slot-scope="scope">
                            <span>{{scope.row.bwStatus == 1 ? '删除':'正常'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="updateTime" label="更新时间" width="180">
                    </el-table-column>
                    <el-table-column prop="remark" label="备注">
                    </el-table-column>
                    <el-table-column label="操作">
                        <template slot-scope="scope" v-if="scope.row.bwStatus != 1">
                            <el-button v-if="(tableData.userBwType == 0 && scope.row.bwType == 3) || (tableData.userBwType == 1 && scope.row.bwType == 2)"  @click="open(scope.row)" type="text" size="small">修改</el-button>
                            <el-button  v-if="(tableData.userBwType == 0 && scope.row.bwType == 3) || (tableData.userBwType == 1 && scope.row.bwType == 2)"  type="text" size="small" @click="deletePhone(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="block app-pageganit">
                <el-pagination  class="user-page" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="param.pageNum" :page-sizes="[10,20,30,50]" :page-size="param.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="param.pageTotal">
                </el-pagination>
            </div>
        </div>
        <div>
            <el-dialog width="400px" :title="modals.title"  :visible.sync="modals.visible === 'blackWhiteList'" :show-close="false" :close-on-click-modal="false">
                <el-form :model="modals.info" :rules="rules" ref="ruleForm" label-width="100px">
                    <el-input v-model="modals.info.oldPkCgmMsisdn" type="hidden"></el-input>
                    <el-form-item label="用户号码:">
                        <div class="grid-content bg-purple">{{param.phone}}</div>
                    </el-form-item>
                    <el-form-item label="接收号码:">
                        <el-input v-model="modals.info.receivePhone" style="width:200px" size="small" :maxlength="11"></el-input>
                    </el-form-item>
                    <el-form-item label="类型" >
                        <span>{{userBwTypeValue}}</span>
                    </el-form-item>
                    <el-form-item label="备注:" >
                        <el-input v-model="modals.info.remark" style="width:200px" row="5" :maxlength="200"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button size="small" @click="modals.visible = ''">取消</el-button>
                    <el-button type="primary"  size="small" @click="modifyList" :disabled="modals.isInvalid">确定</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
export default {
    name: 'UserList',
    data() {
        let checkReceivePhone = (rule, value, callback) => {
            let reg = /^0?(13[0-9]|15[012356789]|17[013678]|18[0-9]|14[57])[0-9]{8}$/;
            if (!reg.test(value)) {
                return callback(new Error('请输入正确的接收号码'));
            }
        };
        return {
            visible2: false,
            totalCount: 0,
            param: {
                phone:'',
                recPhone: '',
                pageTotal:0,
                pageNum: 1,
                pageSize: 10,
                total : 0,
            },
            labels: {
                setCsType: [],
                setRecType: [],
                csIdLabel: [],
                channel: {}
            },
            tableData: [],
            rules: {
                receivePhone: [{ required: true, message: '请输入接收号码', trigger: 'blur' },
                               {  trigger: 'blur',validator:checkReceivePhone }
                              ]
            },
            modals: {
                info: {}
            }
        }
    },
    watch: {
        'modals.info': {
            handler: function() {
                if (!this.$refs['ruleForm']) {
                    this.$set(this.modals, 'isInvalid', true);
                } else {
                    this.$refs['ruleForm'].validate((valid) => {
                        this.$set(this.modals, 'isInvalid', !valid);
                    });
                }
            },
            deep: true
        }
    },
    methods: {
        // query: function(phone) {
        //     this.page.phone = phone;
        //     this.$set(this.page, 'pageNum', 1);
        //     this.pageQuery(this.page);
        // },
        //进入页面查询
        pageQuery: function(param) {
            this.$http
                .post(`${this.proxyUrl}/user/blackWhite/getBlackWhite`, param, {
                    emulateJSON: true
                })
                .then(function(res) {
                    this.tableData = res.data.datas;
                    this.tableData.userBwType = res.data.userBwType;
                    this.param.pageTotal=res.data.pageTotal;
                }) 
        },

        handleSizeChange: function(size) {
            this.$set(this.param, 'pageSize', size);
            this.$set(this.param, 'pageNum', 1);
            this.pageQuery(this.param);
        },
        handleCurrentChange: function(currentPage) {
            this.$set(this.param, 'pageNum', currentPage);
            this.pageQuery(this.param);
        },
        open: function(info) {
            this.modals.info = {};
            this.$set(this.modals, 'visible', 'blackWhiteList');
            this.$set(this.modals, 'isUpdate', !!info);
            // this.$set(this.modals, 'info', info || { bwType: 0 });

            this.$set(this.modals, 'isInvalid', false);
            if (!info) {
                this.$set(this.modals, 'title', '新增名单');
                this.modals.info.bwType = this.tableData.userBwType;
            } else {
                this.modals.info = JSON.parse(JSON.stringify(info)) || { bwType: 0 };
                this.modals.info.oldPkCgmMsisdn = info.receivePhone;
                this.$set(this.modals, 'title', "修改" + (info.bwType === 3 ? '白名单' : '黑名单'));
                this.$set(this.modals, 'isInvalid', false);
                this.$set(this.modals, 'bwType', info.bwType);
            }
        },
        modifyList: function() {
            if(this.modals.info.receivePhone == null || this.modals.info.receivePhone == ''){
                this.$message.error("请输入接收号码");
                return false;
            }

            if(!this.isPhoneNo(this.modals.info.receivePhone)){
                this.$message.error("请输入正确手机号码");
                return false;
            }
            let userBwStatus = '2';
            if(this.tableData.userBwType == 0){
                userBwStatus = '3';
            }
            let param = {
                "cbwId": this.modals.info.cbwId === undefined ? null : this.modals.info.cbwId, //id
                "phone": this.param.phone,
                "blackWhite": userBwStatus, //类型 0黑 1白
                "recPhone": this.modals.info.receivePhone,
                "remark": this.modals.info.remark || null,
                "oldPkCgmMsisdn" : this.modals.info.oldPkCgmMsisdn
            }
            if (this.modals.isUpdate) {

                post(`/user/blackWhite/modBlackWhite`, param).then(res=>{
                        if(res.data.status == 0){
                            this.$message.success("修改成功");
                            this.pageQuery(this.param);
                        }else{
                            this.$message.error("修改失败");
                        }
                    });
            } else {
                this.param.blackWhite = userBwStatus;
                this.$http
                    post(`/user/blackWhite/addBlackWhite`, param).then(res=>{
                        if(res.data.status == 0){
                            this.$message.success("新增成功");
                            this.pageQuery(this.param);
                        }else{
                            this.$message.error("新增失败"+res.data.resText);
                        }
                    });
            }
            this.modals.visible = '';
        },
        deletePhone: function(row) {
            let param = {
                cbwId: row.cbwId,
                "phone": this.param.phone,
                "recPhone": row.receivePhone,
                "blackWhite": row.bwType //类型 2黑 3白
            };
            this.$confirm('确认是否删除数据?')
                .then(_ => {
                    post(`/user/blackWhite/delBlackWhite`, param).then(res=>{
                        if(res.data.status == 0){
                            this.$message.success("删除成功");
                            this.pageQuery(this.param);
                        }else{
                            this.$message.error("删除失败");
                        }
                    })
                })
        },
        isPhoneNo:function(val){
            let reg = /^0?(13[0-9]|15[012356789]|17[013678]|18[0-9]|14[57])[0-9]{8}$/;
            return reg.test(val);
        }
    },
    mounted() {
        this.$set(this.param, 'phone', sessionStorage.getItem('pkCurUserid'));
        this.pageQuery(this.param);
    },
    computed: {
        userBwTypeValue(){
            if(this.tableData.userBwType == 0){
                return '白名单';
            }else if(this.tableData.userBwType == 1){
                return '黑名单';
            }else if(this.tableData.userBwType == 2){
                return '关闭';
            }

        }


    },

    components: {}
}
</script>
<style>
</style>