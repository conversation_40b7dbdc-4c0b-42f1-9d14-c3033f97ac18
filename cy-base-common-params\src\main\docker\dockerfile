# from base image centos
FROM cy-harbor.mg.cn/caiyin/openjdk:21-jdk
MAINTAINER houmaoqing
#install java
#RUN yum -y install java
#inatall app 
#ADD <src> <dest> from appUrl to container destUrl
#ADD tar /root/project
#cp app
COPY cy-base-common-params-0.0.1.jar /usr/local/app.jar

RUN cp -aRp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

#start app
ENTRYPOINT ["java", "-Xms1g", "-Xmx18g","-jar", "/usr/local/app.jar"]
