package com.cy.content.Common;

import java.io.Serializable;
import java.util.List;

public class Result implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	//0成功，1失败
	private int resStatus;
	//返回失败或成功消息
	private String resText;
	//数据集
	private List<?> datas;
	//总条数
	private int pageTotal;
	//查询页码
	private int pageNum;
	
	private static Result res = new Result();
	
	public static Result getInstance(int resStatus, String resText){
		res.setResStatus(resStatus);
		res.setResText(resText);
		return res;
	}
	public static Result getInstance(List<?> datas, int resStatus, String resText){
		res.setDatas(datas);
		getInstance(resStatus,resText);
		return res;
	}
	
	public static Result getInstance(List<?> datas, int pageTotal, int pageNum){
		res.setDatas(datas);
		res.setPageTotal(pageTotal);
		res.setPageNum(pageNum);
		return res;
	}
	public List<?> getDatas() {
		return datas;
	}

	public void setDatas(List<?> datas) {
		this.datas = datas;
	}

	public static Result getRes() {
		return res;
	}

	public static void setRes(Result res) {
		Result.res = res;
	}

	public int getResStatus() {
		return resStatus;
	}
	public void setResStatus(int resStatus) {
		this.resStatus = resStatus;
	}
	public String getResText() {
		return resText;
	}
	public void setResText(String resText) {
		this.resText = resText;
	}
	public int getPageTotal() {
		return pageTotal;
	}
	public void setPageTotal(int pageTotal) {
		this.pageTotal = pageTotal;
	}
	public int getPageNum() {
		return pageNum;
	}
	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}
	
}
