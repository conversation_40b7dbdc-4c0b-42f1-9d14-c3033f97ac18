package com.cs.param.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.common.ParAlarmSmsCommon;
import com.cs.param.common.ParOpeSmsCommon;
import com.cs.param.common.ParSmsForCommon;
import com.cs.param.common.ParSmsInsCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.dao.ParAlarmSmsMapper;
import com.cs.param.dao.ParExcTypeMapper;
import com.cs.param.dao.ParOpeSmsMapper;
import com.cs.param.dao.ParSmsForMapper;
import com.cs.param.dao.ParSmsInsMapper;
import com.cs.param.dao.ParTriSceMapper;
import com.cs.param.model.ParAlarmSmsModel;
import com.cs.param.model.ParExcTypeModel;
import com.cs.param.model.ParOpeSmsModel;
import com.cs.param.model.ParSmsForModel;
import com.cs.param.model.ParSmsInsModel;
import com.cs.param.model.ParTriSceModel;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;

/**
 * 
 * 短信管理Controller
 *
 */
@RequestMapping("/smsMgt")
@RestController
public class SmsMatController {

	private static final Logger log = LoggerFactory.getLogger(SmsMatController.class);

	@Autowired
	private ParExcTypeMapper parExcTypeMapper;

	@Autowired
	private ParTriSceMapper parTriSceMapper;

	@Autowired
	private ParSmsInsMapper parSmsInsMapper;

	@Autowired
	private ParSmsForMapper parSmsForMapper;

	@Autowired
	private ParOpeSmsMapper parOpeSmsMapper;

	@Autowired
	private ParAlarmSmsMapper parAlarmSmsMapper;

	/**
	 * 
	 * 获取异常类型列表
	 *
	 */
	@RequestMapping(value = "getAllParExcType")
	public List<ParExcTypeModel> getAllParExcType() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getAllParExcType", "获取异常类型列表");
		try {
			return parExcTypeMapper.getAllParExcType();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getAllParExcType", "获取异常类型列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 获取触发场景列表
	 *
	 */
	@RequestMapping(value = "getAllParTriSce")
	public List<ParTriSceModel> getAllParTriSce() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getAllParTriSce", "获取触发场景列表");
		try {
			return parTriSceMapper.getAllParTriSce();
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getAllParTriSce", "获取触发场景列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 获取短信指令模板列表
	 *
	 */
	@RequestMapping(value = "getSmsInsPage")
	@CySysLog(methodName = "获取短信指令模板列表", modularName = "公参模块", optContent = "获取短信指令模板列表")
	public ResultListCommon getSmsInsPage(@ModelAttribute("common") ParSmsInsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getSmsInsPage", "获取短信指令模板列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parSmsInsMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parSmsInsMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getSmsInsPage", "获取短信指令模板列表出错！", e);
		}
		return result;
	}

	/**
	 * 开启/关闭短信指令模板
	 *
	 */
	@RequestMapping(value = "openOrCloseSmsIns")
	public ResultCommon openOrCloseSmsIns(@ModelAttribute("common") ParSmsInsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "openOrCloseSmsIns", "开启/关闭短信指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		if (null == common.getId()) {
			return result;
		}
		try {
			ParSmsInsModel model = parSmsInsMapper.queryStatusById(common);
			// 判断状态是否关闭，如果关闭，则开启。如果开启，则关闭。0：关闭，1开启
			if (model.getStatus().equals("0")) {
				common.setStatus("1");
			} else if (model.getStatus().equals("1")) {
				common.setStatus("0");
			}
			if (parSmsInsMapper.openOrCloseStatusById(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "openOrCloseSmsIns", "开启/关闭短信指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 编辑短信指令模板
	 *
	 */
	@RequestMapping(value = "updateParSmsIns")
	public ResultCommon updateParSmsIns(@ModelAttribute("common") ParSmsInsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateParSmsIns", "编辑短信指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parSmsInsMapper.updateParSmsInsByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "updateParSmsIns", "编辑短信指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 删除短信指令模板
	 *
	 */
	@RequestMapping(value = "deleteParSmsIns")
	public ResultCommon deleteParSmsIns(@ModelAttribute("common") ParSmsInsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParSmsIns", "删除短信指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parSmsInsMapper.deleteParSmsInsByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "deleteParSmsIns", "删除短信指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取短信转发指令模板列表
	 *
	 */
	@RequestMapping(value = "getSmsForPage")
	@CySysLog(methodName = "获取短信转发指令模板列表", modularName = "公参模块", optContent = "获取短信转发指令模板列表")
	public ResultListCommon getSmsForPage(@ModelAttribute("common") ParSmsForCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getSmsForPage", "获取短信转发指令模板列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parSmsForMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parSmsForMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getSmsForPage", "获取短信转发指令模板列表出错！", e);
		}
		return result;
	}

	/**
	 * 开启/关闭短信转发指令模板
	 *
	 */
	@RequestMapping(value = "openOrCloseSmsFor")
	public ResultCommon openOrCloseSmsFor(@ModelAttribute("common") ParSmsForCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "openOrCloseSmsFor", "开启/关闭短信转发指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		if (null == common.getId()) {
			return result;
		}
		try {
			ParSmsForModel model = parSmsForMapper.queryStatusById(common);
			// 判断状态是否关闭，如果关闭，则开启。如果开启，则关闭。0：关闭，1开启
			if (model.getStatus().equals("0")) {
				common.setStatus("1");
			} else if (model.getStatus().equals("1")) {
				common.setStatus("0");
			}
			if (parSmsForMapper.openOrCloseStatusById(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "openOrCloseSmsFor", "开启/关闭短信转发指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 编辑短信转发指令模板
	 *
	 */
	@RequestMapping(value = "updateParSmsFor")
	public ResultCommon updateParSmsFor(@ModelAttribute("common") ParSmsForCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateParSmsFor", "编辑短信转发指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parSmsForMapper.updateParSmsForByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "updateParSmsFor", "编辑短信转发指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 删除短信转发指令模板
	 *
	 */
	@RequestMapping(value = "deleteParSmsFor")
	public ResultCommon deleteParSmsFor(@ModelAttribute("common") ParSmsForCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParSmsFor", "删除短信转发指令模板", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parSmsForMapper.deleteParSmsForByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "deleteParSmsFor", "删除短信转发指令模板出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取系统运营短信列表
	 *
	 */
	@RequestMapping(value = "getOpeSmsPage")
	@CySysLog(methodName = "获取系统运营短信列表", modularName = "公参模块", optContent = "获取系统运营短信列表")
	public ResultListCommon getOpeSmsPage(@ModelAttribute("common") ParOpeSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getOpeSmsPage", "获取系统运营短信列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parOpeSmsMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parOpeSmsMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getOpeSmsPage", "获取系统运营短信列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 开启/关闭系统运营短信
	 *
	 */
	@RequestMapping(value = "openOrCloseOpeSms")
	public ResultCommon openOrCloseOpeSms(@ModelAttribute("common") ParOpeSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "openOrCloseOpeSms", "开启/关闭系统运营短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		if (null == common.getId()) {
			return result;
		}
		try {
			ParOpeSmsModel model = parOpeSmsMapper.queryStatusById(common);
			// 判断状态是否关闭，如果关闭，则开启。如果开启，则关闭。0：关闭，1开启
			if (model.getStatus().equals("0")) {
				common.setStatus("1");
			} else if (model.getStatus().equals("1")) {
				common.setStatus("0");
			}
			if (parOpeSmsMapper.openOrCloseStatusById(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "openOrCloseOpeSms", "开启/关闭系统运营短信出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 新增系统运营短信
	 *
	 */
	@RequestMapping(value = "addParOpeSms")
	public ResultCommon addParOpeSms(@ModelAttribute("common") ParOpeSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "addParOpeSms", "新增系统运营短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parOpeSmsMapper.insertParOpeSms(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOpeSms", "新增系统运营短信出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 编辑系统运营短信
	 *
	 */
	@RequestMapping(value = "updateParOpeSms")
	public ResultCommon updateParOpeSms(@ModelAttribute("common") ParOpeSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateParOpeSms", "编辑系统运营短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parOpeSmsMapper.updateParOpeSmsByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "updateParOpeSms", "编辑系统运营短信出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除系统运营短信
	 *
	 */
	@RequestMapping(value = "deleteParOpeSms")
	public ResultCommon deleteParOpeSms(@ModelAttribute("common") ParOpeSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParOpeSms", "删除系统运营短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parOpeSmsMapper.deleteParOpeSmsByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "deleteParOpeSms", "删除系统运营短信出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取告警短信列表
	 *
	 */
	@RequestMapping(value = "getAlarmSmsPage")
	@CySysLog(methodName = "获取告警短信列表", modularName = "公参模块", optContent = "获取告警短信列表")
	public ResultListCommon getAlarmSmsPage(@ModelAttribute("common") ParAlarmSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getAlarmSmsPage", "获取告警短信列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parAlarmSmsMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parAlarmSmsMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getAlarmSmsPage", "获取告警短信列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 开启/关闭系统运营短信
	 *
	 */
	@RequestMapping(value = "openOrCloseAlarmSms")
	public ResultCommon openOrCloseAlarmSms(@ModelAttribute("common") ParAlarmSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "openOrCloseAlarmSms", "开启/关闭系统运营短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		if (null == common.getId()) {
			return result;
		}
		try {
			ParAlarmSmsModel model = parAlarmSmsMapper.getParAlarmSmsByPK(common);
			// 判断状态是否关闭，如果关闭，则开启。如果开启，则关闭。0：关闭，1开启
			if (model.getStatus().equals("0")) {
				common.setStatus("1");
			} else if (model.getStatus().equals("1")) {
				common.setStatus("0");
			}
			if (parAlarmSmsMapper.openOrCloseStatusById(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "openOrCloseAlarmSms", "开启/关闭系统运营短信出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 新增告警短信
	 *
	 */
	@RequestMapping(value = "addParAlarmSms")
	public ResultCommon addParAlarmSms(@ModelAttribute("common") ParAlarmSmsCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "addParAlarmSms", "新增告警短信", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parAlarmSmsMapper.insertParAlarmSms(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParAlarmSms", "新增告警短信出错！", e);
		}
		return result;
	}

}
