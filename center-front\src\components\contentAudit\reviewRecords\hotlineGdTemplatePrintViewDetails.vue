<template scope="scope">
  <div>
    <div class="user-titler">热线彩印固定模板审核记录</div>
    <!--热线彩印固定模板-->
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="彩印类型">
              <el-select
                v-model="searchReq.serviceId"
                class="inputWidth"
                placeholder="请选择"
                size="small"
                clearable
              >
                <el-option
                  v-for="item in caiyinType"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="彩印ID">
              <el-input v-model="searchReq.uuid" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="彩印内容">
              <el-input v-model="searchReq.subject" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核意见">
              <el-select
                v-model="searchReq.status"
                class="inputWidth"
                placeholder="请选择"
                size="small"
                clearable
              >
                <el-option label="初审通过" value="4"></el-option>
                <el-option label="初审驳回" value="5"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="企业名称">
              <el-input v-model="searchReq.submitter" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                        v-model="searchReq.timearr"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
             <el-col :span="6">
             <el-form-item label="内容编号">
                <el-input v-model="searchReq.contentID" size="small" class="inputWidth" clearable></el-input>
              </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="审核人">
              <el-input v-model="searchReq.reviewer" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审核时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="searchBtn" size="small">查询</el-button>
              <el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column prop="uuid" label="彩印ID" width="140"></el-table-column>
        <el-table-column  label="彩印内容" width="200">
          <template slot-scope="scope">
            <div v-html="scope.row.subject"></div>
          </template>
        </el-table-column>
        <el-table-column label="敏感词"
                         width="200">
          <template slot-scope="scope">
            <div  v-html="scope.row.sensitiveWords"></div>
          </template>
        </el-table-column>
        <el-table-column label="热线号码" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showPhoneDetail(scope.row.phones)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="caiyinType" label="彩印类型" width="100"></el-table-column>
        <el-table-column prop="status" label="审核意见" width="100"></el-table-column>
        <el-table-column prop="reason" label="驳回原因" width="100"></el-table-column>
        <el-table-column prop="submitter" label="企业名称" width="100"></el-table-column>
        <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="其他资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="submitDate" label="提交时间" width="200"></el-table-column>
          <el-table-column
                prop="contentID"
                label="内容编号"
                width="200">
        </el-table-column>
        <el-table-column prop="reviewer" label="审核人" width="100"></el-table-column>
        <el-table-column prop="reviewDate" label="审核时间" width="200"></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.p"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
    <div>
      <el-dialog title="热线号码" width="30%" :visible.sync="phonesVisible">
        <el-table :data="phonesData">
          <el-table-column type="index" label="序号" width="120"></el-table-column>
          <el-table-column prop="phone" label="热线号码"></el-table-column>
        </el-table>
      </el-dialog>
      <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
        <img style="width: 100%;" :src="corpImage" alt>
      </el-dialog>
      <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
        <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
           <li >
             <a :href="item" target="_blank">其他资质{{index+1}}</a>
           </li>
        </ul>
      </el-dialog>
    </div>

    <el-dialog
        @open="exportClick"
        title="导出"
        :visible.sync="propVisible"
        :close-on-click-modal="false"
        width="45%">
      <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
        <el-form-item label="文件名" prop="fileName">
          <el-input v-model="addReq.fileName" type="input" size="small"
                    placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字"
                    style="width: 90%;"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256"
                    style="width: 90%;"></el-input>
        </el-form-item>
      </el-form>
      <div style=" margin-left: 80px; color: red;">
        导出后请到系统管理-导出文件下载对应文件
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="confirmExport">确定</el-button>
        <el-button @click="cancelExport">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from 'moment';
import {postDownload} from './../../../servers/httpServer.js';
import {dowandFile} from './../../../util/core.js';
import {dealSensitiveWord} from './../../../util/core.js';
import axios from '../../../../node_modules/axios/dist/axios';

export default {
  data() {
    return {
      propVisible: false,
      addReq:{
        fileName: '',
        remark: ''
      },
      rules:{
        fileName: [
          { required: true, message: '请输入文件名', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
          { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
        ],
        remark: [
          { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
        ]
      },
      tableLoading: false,
      phonesVisible: false,
      corpImageVisible: false,
      corpImage: "", //企业资质
      otherImageVisible: false,
      otherImage: [], //其他资质
      phonesData: [],
      pageTotal: 0, //总条数
      caiyinType: [
        {
          id: '05100',
          name: "省内版-主被叫彩印"
        },
        {
          id: '01124',
          name: "二级企业-主被叫彩印"
        }
      ],
      //查询条件
      searchReq: {
        contentID:"",//内容编号
        serviceId: "",
        uuid: "", //内容id
        subject: "",
        status: "", //  #默认0 0未审核，1审核通过，2审核不通过，3,撤销';
        submitter: "", //提交人
        timearr: [], //提交时间
        submitStart: "",
        submitEnd: "",
        reviewer: "", //审核人
        timearr1: [], //审核时间
        reviewerStart: "",
        reviewerEnd: "",
        contentTemplateType: "HOT_GD_TEMPLATE",
        p: 1, //页码
        pz: 10 //一页的数量
      },
      //数据表
      tableData: []
    };
  },
  created() {
    // this.formatData();
    this.search();
  },
  methods: {
    exportClick(){
      this.$refs.addReqForm.resetFields();
    },
    confirmExport() {
      this.$refs.addReqForm.validate(valid => {
        if (valid) {
          this.propVisible = !this.propVisible;

          if (this.searchReq.timearr) {
            this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.submitStart = "";
            this.searchReq.submitEnd = "";
          }
          //审核时间
          if (this.searchReq.timearr1) {
            this.searchReq.reviewerStart = this.searchReq.timearr1[0] ? moment(new Date(this.searchReq.timearr1[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.reviewerEnd = this.searchReq.timearr1[1] ? moment(new Date(this.searchReq.timearr1[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.reviewerStart = "";
            this.searchReq.reviewerEnd = "";
          }
          const {timearr, timearr1, ...searchReq} = this.searchReq;

          const vm = this;
          var req = {
            fileName: this.addReq.fileName,
            remark: this.addReq.remark,
            taskType: 8,
            params: JSON.stringify(this.searchReq)
          }
          axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then(function (res) {

            let data = res.data;
            if (data.code == 0) {
              vm.$message.success("系统将生成文件名为" + vm.addReq.fileName + "的文件");
            } else {
              vm.$message.error(data.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    cancelExport() {
      this.propVisible = !this.propVisible;
      this.$refs.addReqForm.resetFields();
    },
    showCorpImage(corpImage) {
      console.log(corpImage);
      this.corpImage = corpImage;
      this.corpImageVisible = true;
    },
    showOtherImage(otherImage) {
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    searchBtn() {
      this.searchReq.p = 1;
      this.search()
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      //提交时间
      if (this.searchReq.timearr) {
        this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
        this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
      } else {
        this.searchReq.submitStart = "";
        this.searchReq.submitEnd = "";
      }
      //审核时间
      if (this.searchReq.timearr1) {
        this.searchReq.reviewerStart = this.searchReq.timearr1[0] ? moment(new Date(this.searchReq.timearr1[0])).format('YYYYMMDDHHmmss') : '';
        this.searchReq.reviewerEnd = this.searchReq.timearr1[1] ? moment(new Date(this.searchReq.timearr1[1])).format('YYYYMMDDHHmmss') : '';
      } else {
        this.searchReq.reviewerStart = "";
        this.searchReq.reviewerEnd = "";
      }
      const { timearr, timearr1, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/template/type/review`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data;
            this.tableData.forEach(val => {
              let sensitiveWords = "";
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"03",true);
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"02",true);
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"01",true);

              val.sensitiveWords = sensitiveWords;
            })
            this.formatData();
            this.pageTotal = res.count;
          } else {
            this.tableData = [];
            this.formatData();
            this.pageTotal = 0;
          }
        });
    },
    formatData() {
      this.tableData.forEach(function(val, index) {
        if (val.serviceId == '05100') {
          val.caiyinType = "省内版-主被叫彩印";
        } else if (val.serviceId == '01124'){
          val.caiyinType = "二级企业-主被叫彩印";
        }
        //0未审核，1审核不通过，2审核通过,3撤销
        switch (val.status) {
          case 0:
            val.status = "未审核";
            break;
          case 1:
            val.status = "驳回";
            break;
          case 2:
            val.status = "已通过";
            break;
          case 3:
            val.status = "已撤销";
            break;
          case 4:
            val.status = "初审通过";
            break;
          case 5:
            val.status = "初审驳回";
            break;
          default:
            break;
        }
        val.submitDate = (val.submitDate != null) && moment(val.submitDate).format("YYYY-MM-DD HH:mm:ss");
        val.reviewDate = (val.reviewDate != null) && moment(val.reviewDate).format("YYYY-MM-DD HH:mm:ss");
      });
    },
    handleSizeChange(val) {
      this.searchReq.p = 1;
      //每页条数
      this.searchReq.pz = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.p = val;
      this.search();
    },
    showPhoneDetail(phones) {
      this.phonesData = (phones || []).map(item => ({ phone: item }));
      this.phonesVisible = true;
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    }
  }
};
</script>
<style scoped>
.inputWidth {
  width: 160px !important;
}
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  /* margin-left: 3%; */
  /* margin-top: 3%; */
  border: 1px solid #ecebe9;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
