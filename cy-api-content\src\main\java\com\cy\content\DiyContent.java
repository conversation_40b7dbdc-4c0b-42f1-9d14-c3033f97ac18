package com.cy.content;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.cy.content.model.DiyModel;

public interface DiyContent {
    /**
     * 查询diy内容
     * @param diyModel
     * @return List<DiyModel>
     */
    @RequestMapping(value="/diyContent/getDiyContent")
    List<DiyModel> getDiyContent(DiyModel diyModel);
    
    /**
     * 批量删除
     * @param ids
     * @return List<String>
     */
    @RequestMapping(value="/diyContent/batchDel")
    int batchDel(List<String> ids);
}
