package com.cs.param.api.controller;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSON;
import com.cs.param.dao.ParNumberMapper;
import com.cs.param.model.*;
import com.cs.param.model.request.QueryAttributionByPhoneSectionReq;
import com.cs.param.model.response.QueryAttributionByPhoneSectionResp;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.api.dao.NumberMapper;
import com.cs.param.api.dao.PackageMapper;
import com.cs.param.api.dao.ProvinceMapper;
import com.cs.param.api.dao.SignMapper;
import com.cs.param.common.ParOtherConfCommon;
import com.cs.param.common.ParRedListCommon;
import com.cs.param.controller.PackageController;
import com.cs.param.dao.ParOtherConfMapper;
import com.cs.param.dao.ParRedListMapper;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.api.CommonParams;
import com.cy.model.CityModel;
import com.cy.model.DefaultSignModel;
import com.cy.model.NumberModel;
import com.cy.model.PackageModel;
import com.cy.model.PageModel;
import com.cy.model.ProvinceModel;

/**
 * 
 * 彩印套餐功能管理Controller
 *
 */
@RestController
public class CommonParamApiController implements CommonParams {

	private static final Logger log = LoggerFactory.getLogger(PackageController.class);

	@Autowired
	private PackageMapper packageMapper;

	@Autowired
	private NumberMapper numberMapper;

	@Autowired
	private ProvinceMapper provinceMapper;

	@Autowired
	private SignMapper signMapper;

	@Autowired
	private ParRedListMapper parRedListMapper;

	@Autowired
	private ParOtherConfMapper parOtherConfMapper;


	@Autowired
	private ParNumberMapper parNumberMapper;
	@Override
	public PackageModel getPackageByProductNo(@RequestBody String productNo) {
		LogUtil.info(log, LogUtil.BIZ, productNo, "根据产品ID获取套餐信息。");
		return packageMapper.getPackageByProductNo(productNo);
	}

	@Override
	public List<PackageModel> getAllPackage() {
		LogUtil.info(log, LogUtil.BIZ, "getAllPackage", "获取所有套餐信息。");
		return packageMapper.getAllPackage();
	}

	@Override
	public NumberModel getSectionByPhone(@RequestBody String phoneNo) {
		LogUtil.info(log, LogUtil.BIZ, phoneNo, "根据手机号码获取号段信息");
		String sectionNo = "";
		if (phoneNo.startsWith("144") && phoneNo.length() > 9) {// 特殊处理144物联网号码，取前9位
			sectionNo = phoneNo.substring(0, 9);
		} else if (phoneNo.length() > 7) {
			sectionNo = phoneNo.substring(0, 7);
		} else {
			sectionNo = phoneNo;
		}
		NumberModel model = numberMapper.getSectionByNo(sectionNo);
		if (model != null) {
			model.setPhoneNo(phoneNo);
		}
		return model;
	}

	@Override
	public DefaultSignModel getDefaultSign(@RequestBody String provinceCode) {
		LogUtil.info(log, LogUtil.BIZ, "getDefaultSign", "获取默认彩印和彩印盒信息");
		DefaultSignModel defaultModel = new DefaultSignModel();
		defaultModel.setProvinceCode(provinceCode);
		if (provinceCode != null && !"".equals(provinceCode)) {
			DefaultSignModel signModel = signMapper.getDefaultSignByProvince(provinceCode);
			DefaultSignModel signBoxModel = signMapper.getDefaultSignBoxByProvince(provinceCode);
			if (!Util.isEmpty(signModel.getSignId())) {
				defaultModel.setSignId(signModel.getSignId());
				defaultModel.setSignContent(signModel.getSignContent());
			} else {
				DefaultSignModel model = signMapper.getDefaultSign();
				if (model != null) {
					defaultModel.setSignId(model.getSignId());
					defaultModel.setSignContent(model.getSignContent());
				}
			}
			if (!Util.isEmpty(signBoxModel.getSignBoxId())) {
				defaultModel.setSignBoxId(signBoxModel.getSignBoxId());
				defaultModel.setSignBoxName(signBoxModel.getSignBoxName());
			} else {
				DefaultSignModel model = signMapper.getDefaultSign();
				if (model != null) {
					defaultModel.setSignBoxId(model.getSignBoxId());
					defaultModel.setSignBoxName(model.getSignBoxName());
				}
			}

		} else {
			DefaultSignModel model = signMapper.getDefaultSign();
			if (model != null) {
				defaultModel.setSignId(model.getSignId());
				defaultModel.setSignContent(model.getSignContent());
				defaultModel.setSignBoxId(model.getSignBoxId());
				defaultModel.setSignBoxName(model.getSignBoxName());
			}

		}
		return defaultModel;
	}

	@Override
	public DefaultSignModel getDefaultSignBySectionNo(@RequestBody String sectionNo) {
		LogUtil.info(log, LogUtil.BIZ, "getDefaultSign", "根据号段获取默认彩印和彩印盒信息" + sectionNo);
		DefaultSignModel defaultModel = new DefaultSignModel();
		try {
			NumberModel numberModel = numberMapper.getSectionByNo(sectionNo);
			if (Util.isEmpty(numberModel)) {
				DefaultSignModel model = signMapper.getDefaultSign();
				LogUtil.info(log, LogUtil.BIZ, "getDefaultSign", "根据号段没有获取到号段信息:" + sectionNo + ",获取全国默认彩印盒", model);
				if (model != null) {
					defaultModel.setSignId(model.getSignId());
					defaultModel.setSignContent(model.getSignContent());
					defaultModel.setSignBoxId(model.getSignBoxId());
					defaultModel.setSignBoxName(model.getSignBoxName());
				}
			} else {
				String provinceCode = numberModel.getProvinceCode();
				String regionCode = numberModel.getCityCode();
				ParOtherConfCommon common = new ParOtherConfCommon();
				common.setProvinceCode(provinceCode);
				common.setRegionCode(regionCode);
				common.setPageSize(1);
				common.setPageNum(0);
				defaultModel.setProvinceCode(provinceCode);
				List<ParOtherConfModel> list = parOtherConfMapper.queryPageInfo(common);
				if (list != null && list.size() > 0) {
					ParOtherConfModel model = list.get(0);
					defaultModel.setSignBoxId(model.getDefPerBoxId());
					defaultModel.setSignBoxName(model.getDefPerBoxName());
				} else {
					DefaultSignModel signBoxModel = signMapper.getDefaultSignBoxByProvince(provinceCode);
					if (!Util.isEmpty(signBoxModel) && !Util.isEmpty(signBoxModel.getSignBoxId())) {
						defaultModel.setSignBoxId(signBoxModel.getSignBoxId());
						defaultModel.setSignBoxName(signBoxModel.getSignBoxName());
					} else {
						DefaultSignModel model = signMapper.getDefaultSign();
						if (model != null) {
							defaultModel.setSignBoxId(model.getSignBoxId());
							defaultModel.setSignBoxName(model.getSignBoxName());
						}
					}
				}
				LogUtil.info(log, LogUtil.BIZ, "getDefaultSignBySectionNo", "根据号段获取默认彩印和彩印盒信息", defaultModel);
			}
			return defaultModel;
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getDefaultSignBySectionNo", "根据号段获取默认彩印和彩印盒信息失败" + e.getMessage());
		}
		return defaultModel;
	}

	@Override
	public List<ProvinceModel> getAllProvinces() {
		LogUtil.info(log, LogUtil.BIZ, "getAllProvinces", "获取所有省份信息。");
		return provinceMapper.getAllProvinces();
	}

	@Override
	public List<CityModel> getCityByProvince(@RequestBody CityModel model) {
		LogUtil.info(log, LogUtil.BIZ, "getCityByProvince", "根据省份获取城市信息");
		if (model == null) {
			model = new CityModel();
		}
		return provinceMapper.getCityByProvince(model);
	}

	@Override
	public List<CityModel> getProvinceByCity(@RequestBody String cityCode) {
		LogUtil.info(log, LogUtil.BIZ, cityCode, "根据城市获取省份信息");
		return provinceMapper.getProvinceByCity(cityCode);
	}

	@Override
	public String getZoneByPhoneNo(@RequestBody String phoneNo) {
		LogUtil.info(log, LogUtil.BIZ, phoneNo, "根据手机号编码获取属于分区编码");
		String sectionNo = "";
		if (phoneNo != null && !"".equals(phoneNo)) {
			if (phoneNo.startsWith("144") && phoneNo.length() > 9) {// 特殊处理144物联网号码，取前9位
				sectionNo = phoneNo.substring(0, 9);
			} else if (phoneNo.length() > 7) {
				sectionNo = phoneNo.substring(0, 7);
			} else {
				sectionNo = phoneNo;
			}
			return numberMapper.getZoneByPhoneNo(sectionNo);
		}
		return "";
	}

	@Override
	public boolean isRedNumber(@RequestBody String phoneNo) {
		String number = numberMapper.getRedNumber(phoneNo);
		return StringUtils.isNotEmpty(number);
	}

	@Override
	public int getRedNumberCount() {
		ParRedListCommon common = new ParRedListCommon();
		try {
			return parRedListMapper.queryPageCount(common);
		} catch (SQLException e) {
			LogUtil.info(log, LogUtil.BIZ, "getRedNumberCount", "获取对账系统红名单数量出错", e);
		}
		return -1;
	}

	@Override
	public List<String> getRedNumberByPage(@RequestBody PageModel page) {
		ParRedListCommon common = new ParRedListCommon();
		List<String> result = new ArrayList<>();
		try {
			common.setPageNum(page.getIndex());
			common.setPageSize(page.getPageSize());
			List<ParRedListModel> list = parRedListMapper.queryPageInfo(common);
			if (list != null && list.size() > 0) {
				for (ParRedListModel parRedListModel : list) {
					result.add(parRedListModel.getPhone());
				}
			}
		} catch (SQLException e) {
			LogUtil.info(log, LogUtil.BIZ, "getRedNumberByPage", "获取对账系统红名单出错", e);
		}
		return result;
	}


	@RequestMapping(value = "/commonParam/queryAttributionByPhoneSection", method = RequestMethod.POST)
	public QueryAttributionByPhoneSectionResp queryAttributionByPhoneSection(@RequestBody QueryAttributionByPhoneSectionReq queryAttributionByPhoneSectionReq){
		QueryAttributionByPhoneSectionResp queryAttributionByPhoneSectionResp = new QueryAttributionByPhoneSectionResp();
		queryAttributionByPhoneSectionResp.setCode("0");
		queryAttributionByPhoneSectionResp.setMessage("success");
		try {
			if(queryAttributionByPhoneSectionReq.getPhoneSectionList()!=null&&queryAttributionByPhoneSectionReq.getPhoneSectionList().size()>0){
				List<ParNumberModel> sectionModels = parNumberMapper.queryPageInfoList(queryAttributionByPhoneSectionReq.getPhoneSectionList());
				List<PhoneAttribution> phoneSectionList  = new ArrayList<>();
				for(ParNumberModel parNumberModel :sectionModels){
					PhoneAttribution phoneAttribution = new PhoneAttribution();
					phoneAttribution.setPhoneSection(parNumberModel.getPhoneSection());
					phoneAttribution.setProvinceCode(parNumberModel.getProvinceCode());
					phoneAttribution.setProvinceName(parNumberModel.getProvinceName());
					phoneAttribution.setCityCode(parNumberModel.getRegionCode());
					phoneAttribution.setCityName(parNumberModel.getRegionName());
					phoneSectionList.add(phoneAttribution);
				}
				queryAttributionByPhoneSectionResp.setPhoneAttributionList(phoneSectionList);
			}
		}catch (Exception e){
			queryAttributionByPhoneSectionResp.setCode("1");
			queryAttributionByPhoneSectionResp.setMessage("error");
			log.error("queryAttributionByPhoneSection error ",e);
		}

		return queryAttributionByPhoneSectionResp;
	};
}
