<template>
    <div  >
        <div class="header-fanhui"><h1 v-if="!showSubMenu">用户列表</h1></div>
        <div v-show="showSubMenu" class="user-sub-menu" style="width: 1000px">
            <span v-for="(subMenu, index) in subMenus" :key="index" index="index" v-on:click="goView(subMenu)" v-bind:class="{ active: isActive(subMenu.label)}">{{subMenu.label}}</span>
        </div>
        <hr class="user-line">
        <router-view></router-view>
    </div>
</template>
<script>
export default {
    name: 'user',
    data() {
        return {
            urlPath:this.$route.fullPath,
            subMenus: [{
                label: '用户详情',
                path: 'detail'
            }, {
                label: '个人彩印',
                path: 'userPrint'
            },{
                label: '新媒彩印',
                path: 'newmediaPrint'
            },{
                label:'企业彩印',
                path: 'enterprisePrint'
            },{
                label:'防诈骗号码来电提示服务',
                path: 'swindleNumber'
            },{
                label:'黑白名单管理',
                path: 'userBlackWhite'
            },{
                label:'内容设置历史',
                path: 'contentHistory'
            },{
                label:'套餐包订购历史',
                path: 'customerHisPkg'
            }]
        }
    },
    methods: {
        goView: function(subMenu) {
            this.$router.push(subMenu.path);
            // this.param.phone=sessionStroage.getItem('userId');
        },
        isActive: function(label) {
            return this.$route.name === label;
        }
    },
    computed: {
        showSubMenu: function() {
            return this.$route.name !== '用户列表';
        }
    }
}
</script>
<style scoped>
.header-fanhui{
    padding:10px 0px 1% 0px;
}
.user-title {
    margin-left: 3%;
    margin-bottom: 4%;
    background-color: white;
}

.user-line {
    margin-top: 3%;
    border-color: #DDDFE6;
    border-width: 0px 0px 1px 0px;
    width: 100%;
    margin: 0 auto;
}

.user-sub-menu {
    line-height: 30px;
    text-align: left;
    padding-left: 2%;
}

.user-sub-menu span {
    color: #A1A1A1;
    font-size: 16px;
    font-family: SimSum, Arial;
    padding: 4px 15px;
    cursor: pointer;
}

.user-sub-menu span.active {
    color: black;
    font-weight: bold;
    border-bottom: 2px solid #308ee0;
}
</style>