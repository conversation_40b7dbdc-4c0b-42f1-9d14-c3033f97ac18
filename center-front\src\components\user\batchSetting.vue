<template scope="scope">
  <div>
    <div class="user-titler">默认批量设置</div>
    <!--广告彩印-->
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="任务类型">
              <el-select
                v-model="searchReq.type"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option label="沉默批设" value="0"></el-option>
                <!-- <el-option label="新用户批设" value="1"></el-option> -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省份">
              <el-select
                v-model="searchReq.prov_id"
                clearable
                placeholder="请选择"
                size="small"
                @change="querySearchRegionList(1)"
                class="app-input"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.provinceName"
                  :label="item.provinceName"
                  :value="item.provinceCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地市">
              <el-select
                v-model="searchReq.location_id"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option
                  v-for="item in city"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.tasktime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                  class="app-input"
                  size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <!-- </el-row>
          <el-row>-->
          <el-col :span="8">
            <el-form-item label="任务状态">
              <el-select
                v-model="searchReq.taskstatus"
                class="app-input"
                placeholder="请选择"
                size="small"
                clearable
              >
                <el-option label="待处理" value="0"></el-option>
                <el-option label="已处理" value="1"></el-option>
                <el-option label="处理中" value="2"></el-option>
                <el-option label="已停止" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button type="primary" @click="searchHandle" size="small">查询</el-button>
              <el-button type="primary" @click="dialogFormVisible = true;" size="small">新建任务</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <el-table v-loading="tableLoading" :data="tableData" border class="app-tab">
        <el-table-column prop="id" label="任务ID" width="80"></el-table-column>
        <el-table-column prop="type" label="任务类型" width="200"></el-table-column>
        <el-table-column prop="prov_id" label="省份" width="100"></el-table-column>
        <el-table-column prop="location_id" label="地市" width="100"></el-table-column>
        <el-table-column prop="tasktime" label="任务时间" width="200"></el-table-column>
        <el-table-column prop="tasktotal" label="用户数" width="120"></el-table-column>
        <el-table-column prop="taskstatus" label="任务状态" width="100"></el-table-column>
        <el-table-column prop="content" label="彩印内容" width="120"></el-table-column>
        <el-table-column prop="tasksuccess" label="已处理用户数" width="140"></el-table-column>
        <el-table-column prop="taskfail" label="未处理用户数" width="140"></el-table-column>
        <el-table-column prop="taskcommituser" label="提交人" width="150"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" :disabled="scope.row.taskstatus === '已处理' || scope.row.taskstatus === '已停止'" @click="passVisible=true;id=scope.row.id">停止</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
    <div>
      <el-dialog title="新建任务" :visible.sync="dialogFormVisible" width="500px" :close-on-press-escape="false" :before-close="handleClose">
        <el-form :model="form" :rules="rules" ref="ruleForm" class="createTaskWrap">
          <el-form-item label="类型" :label-width="formLabelWidth" prop="type">
            <el-select
              v-model="form.type"
              placeholder="请选择"
              size="small"
              class="app-input"
            >
              <el-option label="沉默批设" value="0"></el-option>
              <!-- <el-option label="新用户批设" value="1"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label="省份" :label-width="formLabelWidth" prop="prov_id">
            <el-select
              v-model="form.prov_id"
              clearable
              placeholder="请选择"
              size="small"
              @change="querySearchRegionList(2)"
              class="app-input"
            >
              <el-option
                v-for="item in provinceList"
                :key="item.provinceName"
                :label="item.provinceName"
                :value="item.provinceCode"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="地市" :label-width="formLabelWidth" prop="location_id">
            <el-select
              v-model="form.location_id"
              clearable
              placeholder="请选择"
              size="small"
              class="app-input"
            >
              <el-option
                v-for="item in city1"
                :key="item.regionCode"
                :label="item.regionName"
                :value="item.regionCode"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="提交时间" :label-width="formLabelWidth" prop="tasktime">
            <div class="block">
              <el-date-picker
                v-model="form.tasktime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                size="small"
              ></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item label="用户数" :label-width="formLabelWidth" prop="usernum">
            <el-input v-model.number="form.usernum" class="app-input" size="small" clearable></el-input>
          </el-form-item>
          <el-form-item v-if="isShow" label="彩印内容" :label-width="formLabelWidth" prop="content">
            <el-input
              type="textarea"
              :rows="2"
              class="app-input"
              placeholder="请输入内容"
              v-model="form.content"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="isShow" label="是否投递" :label-width="formLabelWidth" prop="delivery">
            <el-radio v-model="form.delivery" label="1">是</el-radio>
            <el-radio v-model="form.delivery" label="2">否</el-radio>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelcreateTask('ruleForm')">取 消</el-button>
          <el-button type="primary" @click="createTask('ruleForm')">提 交</el-button>
        </div>
      </el-dialog>
      <el-dialog
        width="30%"
        custom-class="deleteDialog"
        :visible.sync="passVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <div>是否停止该任务？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="passVisible = false;deleteTask();">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from "moment";

export default {
  data() {
    var checkUsernum = (rule, value, callback) => {
      if (!value && value != 0) {
        return callback(new Error('请填写用户数'));
      }
      setTimeout(() => {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'));
        } else {
          if (value < 0) {
            callback(new Error('请输入不小于0的数值'));
          } else {
            callback();
          }
        }
      }, 0);
    };
    return {
      tableLoading: false,
      //查询条件
      searchReq: {
        type: "",
        prov_id: "",
        location_id: "",
        tasktime: "",
        taskstatus: "",
        currentPage: 1, //页码
        pageSize: 10 //一页的数量
      },
      //省份
      provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
      //地市
      city: [],
      city1: [],
      //数据表
      tableData: [],
      pageTotal: 0, //总条数
      dialogFormVisible: false,
      passVisible: false,
      form: {
        type: '0',
        prov_id: "",
        location_id: "",
        tasktime: "",
        usernum: "",
        content: "",
        delivery: "",
        taskcommituser: sessionStorage.getItem("userInfo") && JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,

      },
      formLabelWidth: "120px",
      id: "",
      rules: {
        type: [{ required: true, message: "请选择类型", trigger: 'blur' }],
        prov_id: [{ required: true, message: "请选择省份", trigger: 'blur' }],
        tasktime: [{ required: true, message: "请选择提交时间", trigger: 'blur' }],
        usernum: [{ validator: checkUsernum, required: true, trigger: 'blur' }],
        content: [{ required: true, message: "请填写彩印内容", trigger: 'blur'}],
        delivery: [{ required: true, message: "请选择是否投递", trigger: 'blur'}],
      },
      isShow: false,
    };
  },
  watch: {
    'form.type'(val) {
      if(val == 1) {
        this.isShow = true;
      } else {
        this.isShow = false;
        this.form.content = '';
        this.form.delivery = '';
      }
    }
  },
  created() {
    // this.formatData();
    this.search();
  },
  methods: {
    //查询请求
    search: function() {
      this.tableLoading = true;
      this.$http
        .post(
          `${this.proxyUrl}/batchset/queryTask`,
          JSON.stringify(this.searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data.list || [];
            this.pageTotal = res.data.total;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    //查询地市
    querySearchRegionList(type) {
      var queryRegion = {
        provinceCode: type == 1 ? this.searchReq.prov_id : this.form.prov_id
      };
      this.$http
        .post(`${this.proxyUrl}/param/regionMgt/getRegion`, queryRegion, {
          emulateJSON: true
        })
        .then(function(res) {
          if (type == 1) {
            this.city = res.data;
            this.searchReq.location_id = "";
          } else if (type == 2) {
            this.city1 = res.data;
            this.form.location_id = "";
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    handleSizeChange(val) {
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.currentPage = val;
      this.search();
    },
    searchHandle() {
      this.searchReq.currentPage = 1;
      this.search();
    },
    deleteTask() {
      this.tableLoading = true;
      this.$http
        .get(`${this.proxyUrl}/batchset/deleteTask?id=${this.id}`)
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.$message({
              message: "停止任务成功",
              type: "success"
            });
            this.search();
          } else {
            this.$message({
              message: res.msg,
              type: "error"
            });
          }
        });
    },
    createTask(formName) {
      this.$refs[formName].validate(valid => {
        console.log(valid);
        if (valid) {
          this.tableLoading = true;
          this.dialogFormVisible = false;
          this.$http
            .post(
              `${this.proxyUrl}/batchset/createTask`,
              JSON.stringify(this.form),
              {
                headers: {
                  "X-Requested-With": "XMLHttpRequest",
                  contentType: "application/json",
                  charset: "utf-8"
                },
                emulateJSON: true,
                timeout: 5000
              }
            )
            .then(res => {
              this.tableLoading = false;
              var res = res.data;
              console.log(res);
              if (res.code == 0) {
                this.$message({
                  message: "新建任务成功",
                  type: "success"
                });
                const { currentPage } = this.searchReq;
                this.searchReq = {
                  type: "",
                  prov_id: "",
                  location_id: "",
                  tasktime: "",
                  taskstatus: "",
                  currentPage: currentPage,
                  pageSize: 10,
                };
                this.search();
              } else {
                this.$message({
                  message: res.msg,
                  type: "error"
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    cancelcreateTask(formName) {
      this.dialogFormVisible = false;
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$refs.ruleForm.resetFields();
      done()
    }
  }
};
</script>
<style scoped>
.inputWidth {
  width: 160px !important;
}

.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}

.createTaskWrap>>>.el-form-item {
    margin-bottom: 22px!important;
}

.el-dialog__wrapper>>>.deleteDialog .el-dialog__header {
  display: none;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
