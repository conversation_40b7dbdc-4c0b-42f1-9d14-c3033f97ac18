<template>
            <div>
                <h1 class="user-title">阈值配置</h1>
                <div class="user-line"></div>
                <div class="app-search">
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                        <el-form-item label="省份">
                            <el-select v-model="searchForm.provinceCode" clearable placeholder="请选择" size="small" class="app-input">
                                <el-option
                                        v-for="item in provinceList"
                                        :key="item.provinceCode"
                                        :label="item.provinceName"
                                        :value="item.provinceCode">
                             </el-option>
                        </el-select>
                      </el-form-item>

                        <!--<el-form-item>
                            <el-button type="primary" @click="addVisible = true" size="small">新增</el-button>
                        </el-form-item>-->
                        <el-form-item>
                            <el-button type="primary" @click="changeProvince" size="small">查询</el-button>
                        </el-form-item>
                    </el-form>

                </div>

                <el-table ref="multipleTable" :data="tableData" :span-method="objectSpanMethod" border tooltip-effect="dark" class="app-tab"
                          :header-cell-class-name="tableheaderClassName">
                    <el-table-column prop="provinceName" label="省份" />
                    <el-table-column prop="signType" label="彩印类型" />
                    <el-table-column prop="delayTime" label="时延阈值（秒）" />
                    <el-table-column prop="pushTime" label="同一彩印接收方的推送时间间隔（分钟）" />
                    <el-table-column prop="pushMaxNum" label="对同一彩印接收方每天最大推送次数" />
                    <el-table-column prop="allPushMaxNum" label="每天最大推送次数 " />
                    <el-table-column prop="createTime" label="创建时间" />
                    <el-table-column prop="oper" label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="showEditConf(scope.row)" >编辑</el-button>
<!--                            <el-button type="text" size="small" @click="delConf(scope.row)" >删除</el-button>-->
                        </template>
                    </el-table-column>
                </el-table>

                <div class="block app-pageganit">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>

                <div>
                    <el-dialog title="配置详情" :visible.sync="showVisible" :close-on-click-modal="false">
                        <el-form :model="editForm"  ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 100%">
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" class="title-text-style">省份 : {{editForm.provinceName}}</el-col>
                                <el-col :span="8" class="title-text-style">地区 : {{editForm.regionName}}</el-col>
                                <el-col :span="8"></el-col>
                            </el-row>
                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">个人彩印</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;">同一彩印接收方的推送时间间隔</el-col>
                                <el-col :span="9" style="display: inline-block;">对同一彩印接收方每天最大推送次数</el-col>
                                每天最大推送次数
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    {{editForm.perPushTime}}min
                                </el-col>
                                <el-col :span="9" >
                                    {{editForm.perPushMaxNum}}次
                                </el-col>
                                <el-col :span="7">
                                    {{editForm.perAllPushMaxNum}}次
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">企业彩印</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;">同一彩印接收方的推送时间间隔</el-col>
                                <el-col :span="9" style="display: inline-block;">对同一彩印接收方每天最大推送次数</el-col>
                                每天最大推送次数
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    {{editForm.bussPushTime}}min
                                </el-col>
                                <el-col :span="9">
                                    {{editForm.bussPushMaxNum}}次
                                </el-col>
                                <el-col :span="7">
                                    {{editForm.bussAllPushMaxNum}}次
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">新媒彩印</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;">同一彩印接收方的推送时间间隔</el-col>
                                <el-col :span="9" style="display: inline-block;">对同一彩印接收方每天最大推送次数</el-col>
                                每天最大推送次数
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" >
                                    {{editForm.mediaPushTime}}min
                                </el-col>
                                <el-col :span="9">
                                    {{editForm.mediaPushMaxNum}}次
                                </el-col>
                                <el-col :span="7">
                                    {{editForm.mediaAllPushMaxNum}}次
                                </el-col>
                            </el-row>
                        </el-form>
                    </el-dialog>
                </div>

                <div>
                    <el-dialog title="编辑阀值配置" :visible.sync="editVisible" :close-on-click-modal="false">
                        <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 100%">
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" class="title-text-style">省份 : {{editForm.provinceName}}</el-col>
                                <el-col :span="16"></el-col>
                            </el-row>
                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">同一彩印接收方的推送时间间隔</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;">个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;">企业彩印</el-col>
                                新媒彩印
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    <el-col :span="12">
                                        <el-input v-model="editForm.perPushTime" size="mini"></el-input>
                                    </el-col>
                                    分钟
                                </el-col>
                                <el-col :span="9" >
                                    <div class="demo-input-suffix">
                                        <el-col :span="11">
                                            <el-input v-model.number="editForm.bussPushTime" size="mini"></el-input>
                                        </el-col>
                                        分钟
                                    </div>

                                </el-col>
                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="editForm.mediaPushTime" style="width: 100%;margin-left: 0px;" size="mini"></el-input>
                                    </el-col>
                                    分钟
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">对同一彩印接收方每天最大推送次数</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">个人彩印</el-col>
                                <el-col :span="9">企业彩印</el-col>
                                新媒彩印
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    <el-col :span="12">
                                        <el-input v-model="editForm.perPushMaxNum" size="mini"></el-input>

                                    </el-col>
                                    次
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">
                                        <el-input v-model.number="editForm.bussPushMaxNum" size="mini"></el-input>

                                    </el-col>
                                    次
                                </el-col>
                                <el-col :span="7">
                                    <el-col :span="13">

                                        <el-input v-model.number="editForm.mediaPushMaxNum"  size="mini"></el-input>
                                    </el-col>
                                    次
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">每天最大推送次数</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;" >个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;" >企业彩印</el-col>
                                <div>新媒彩印</div>
                            </el-row>

                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" >
                                    <el-col :span="12">
                                        <el-input v-model="editForm.perAllPushMaxNum" append="" size="mini"></el-input>
                                    </el-col>次
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">

                                        <el-input v-model.number="editForm.bussAllPushMaxNum" size="mini"></el-input>
                                    </el-col>
                                    次
                                 </el-col>

                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="editForm.mediaAllPushMaxNum" size="mini"></el-input>
                                    </el-col>
                                    次
                                </el-col>
                            </el-row>
                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">时延阈值</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;" >个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;" >企业彩印</el-col>
                                <div>新媒彩印</div>
                            </el-row>

                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" >
                                    <el-col :span="12">
                                        <el-input v-model.number="editForm.perDelayTime" style="width: 100%;margin-left: 0px;" size="mini"></el-input>
                                    </el-col>秒
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">
                                        <el-input v-model.number="editForm.bussDelayTime"  size="mini"></el-input>
                                    </el-col>
                                    秒
                                </el-col>

                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="editForm.mediaDelayTime" size="mini"></el-input>
                                    </el-col>
                                    秒
                                </el-col>
                            </el-row>

                        </el-form>
                        <div slot="footer" class="dialog-footer" style="text-align: right;">
                            <el-button @click="editVisible=false;" size="small">取消</el-button>
                            <el-button type="primary" @click="editConf('editForm')" size="small">提交</el-button>
                        </div>
                    </el-dialog>
                </div>

                <div>
                    <el-dialog title="新增阀值配置" :visible.sync="addVisible" :close-on-click-modal="false">
                        <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" label-width="25%"  style="width: 100%">
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="11" class="title-text-style">
                                    <el-form-item label="省份"  prop="provinceCode">
                                        <el-select v-model="addForm.provinceCode" placeholder="请选择" size="mini">
                                            <el-option
                                                    v-for="item in provinceList"
                                                    :key="item.provinceCode"
                                                    :label="item.provinceName"
                                                    :value="item.provinceCode">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="13"></el-col>
                            </el-row>
                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">同一彩印接收方的推送时间间隔</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;">个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;">企业彩印</el-col>
                                新媒彩印
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    <el-col :span="12">
                                        <el-input v-model="addForm.perPushTime" size="mini"></el-input>
                                    </el-col>
                                    分钟
                                </el-col>
                                <el-col :span="9" >
                                    <div class="demo-input-suffix">
                                        <el-col :span="11">
                                            <el-input v-model.number="addForm.bussPushTime" size="mini"></el-input>
                                        </el-col>
                                        分钟
                                    </div>

                                </el-col>
                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="addForm.mediaPushTime" style="width: 100%;margin-left: 0px;" size="mini"></el-input>
                                    </el-col>
                                    分钟
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">对同一彩印接收方每天最大推送次数</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">个人彩印</el-col>
                                <el-col :span="9">企业彩印</el-col>
                                新媒彩印
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8">
                                    <el-col :span="12">
                                        <el-input v-model="addForm.perPushMaxNum" size="mini"></el-input>

                                    </el-col>
                                    次
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">
                                        <el-input v-model.number="addForm.bussPushMaxNum" size="mini"></el-input>

                                    </el-col>
                                    次
                                </el-col>
                                <el-col :span="7">
                                    <el-col :span="13">

                                        <el-input v-model.number="addForm.mediaPushMaxNum"  size="mini"></el-input>
                                    </el-col>
                                    次
                                </el-col>
                            </el-row>

                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">每天最大推送次数</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;" >个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;" >企业彩印</el-col>
                                <div>新媒彩印</div>
                            </el-row>

                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" >
                                    <el-col :span="12">
                                        <el-input v-model="addForm.perAllPushMaxNum" append="" size="mini"></el-input>
                                    </el-col>次
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">

                                        <el-input v-model.number="addForm.bussAllPushMaxNum" size="mini"></el-input>
                                    </el-col>
                                    次
                                </el-col>

                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="addForm.mediaAllPushMaxNum" size="mini"></el-input>
                                    </el-col>
                                    次
                                </el-col>
                            </el-row>
                            <hr class="el-row-line"/>
                            <el-row :gutter="24">
                                <el-col :span="24" class="title-text-style">时延阈值</el-col>
                            </el-row>
                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" style="display: inline-block;" >个人彩印</el-col>
                                <el-col :span="9" style="display: inline-block;" >企业彩印</el-col>
                                <div>新媒彩印</div>
                            </el-row>

                            <el-row :gutter="24" class="el-row-style">
                                <el-col :span="8" >
                                    <el-col :span="12">
                                        <el-input v-model.number="addForm.perDelayTime" style="width: 100%;margin-left: 0px;" size="mini"></el-input>
                                    </el-col>秒
                                </el-col>
                                <el-col :span="9">
                                    <el-col :span="11">
                                        <el-input v-model.number="addForm.bussDelayTime"  size="mini"></el-input>
                                    </el-col>
                                    秒
                                </el-col>

                                <el-col :span="7">
                                    <el-col :span="13">
                                        <el-input v-model.number="addForm.mediaDelayTime" size="mini"></el-input>
                                    </el-col>
                                    秒
                                </el-col>
                            </el-row>

                        </el-form>
                        <div slot="footer" class="dialog-footer" style="text-align: right;">
                            <el-button @click="addVisible=false;" size="small">取消</el-button>
                            <el-button type="primary" @click="addConf('addForm')" size="small">提交</el-button>
                        </div>
                    </el-dialog>
                </div>

                <el-dialog
                        title="提示"
                        :visible.sync="propVisible"
                        width="30%"
                        :before-close="handleCloseConfirm">
                    <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
                </el-dialog>

            </div>
</template>
<script>
    export default {
        data() {
            return {
                activeName:'thresholdConf',
                addVisible:false,
                editVisible:false,
                showVisible:false,
                propVisible:false,
                propMsg:'',
                searchForm: {
                    provinceCode:'',
                    regionCode:'',
                    pageSize:10,// 每页显示条数
                    pageNum :1
                },
                //编辑form对象定义
                addForm: {
                    id:'', //阈值配置id
                    provinceCode:'',
                    provinceName:'',
                    regionCode:'',
                    regionName:'',
                    createTime:'',//创建时间
                    perPushTime:'',//个人彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    perPushMaxNum:'',//个人彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    perAllPushMaxNum:'',//个人彩印，每天最大推送次数（整个彩印）,单位：次数
                    perDelayTime:'',
                    bussPushTime:'',//企业彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    bussPushMaxNum:'',// 企业彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    bussAllPushMaxNum:'',// 企业彩印，每天最大推送次数（整个彩印）,单位：次数
                    bussDelayTime:'',
                    mediaPushTime :'',// 新媒彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    mediaPushMaxNum :'',// 媒彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    mediaAllPushMaxNum :'',// 新媒彩印，每天最大推送次数（整个彩印）,单位：次数
                    mediaDelayTime:''

                },
                //编辑form对象定义
                editForm: {
                    id:'', //阈值配置id
                    provinceCode:'',
                    provinceName:'',
                    regionCode:'',
                    regionName:'',
                    createTime:'',//创建时间
                    perPushTime:'',//个人彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    perPushMaxNum:'',//个人彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    perAllPushMaxNum:'',//个人彩印，每天最大推送次数（整个彩印）,单位：次数
                    perDelayTime:'',
                    bussPushTime:'',//企业彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    bussPushMaxNum:'',// 企业彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    bussAllPushMaxNum:'',// 企业彩印，每天最大推送次数（整个彩印）,单位：次数
                    bussDelayTime:'',
                    mediaPushTime :'',// 新媒彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
                    mediaPushMaxNum :'',// 媒彩印，对同一彩印接收方每天最大推送次数,单位：次数
                    mediaAllPushMaxNum :'',// 新媒彩印，每天最大推送次数（整个彩印）,单位：次数
                    mediaDelayTime:''
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'', //id
                    provinceCode:''
                },

                statusList:[
                    {
                        key:'',
                        value:'请选择'
                    },
                    {
                        key:'0',
                        value:'禁用'
                    },{
                        key:'1',
                        value:'启用'
                    }
                ],
                provinceList:[],
                regionList:[],
                queryRegion:{
                    provinceCode: ''
                },
                rules: {
                    provinceCode: [
                        { required: true, message: '请选择省份' },
                    ],
                    // perPushTime: [
                    //     { required: true, message: '请填写时间' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // perPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // perAllPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // bussPushTime: [
                    //     { required: true, message: '请填写时间' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // bussPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // bussAllPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // mediaPushTime: [
                    //     { required: true, message: '请填写时间' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // mediaPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // mediaAllPushMaxNum: [
                    //     { required: true, message: '请填写次数' },
                    //     { type: 'number', message: '必须是数字' }
                    // ],
                    // delayTime: [
                    //     { required: true, message: '请填写时延' },
                    //     { type: 'number', message: '必须是数字' }
                    // ]
                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
//            this.slideData(this.proxyUrl);
            this.search(this.searchForm);
            this.getProvinceList();
            this. getRegionList();
        },
        methods: {
            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
                if (columnIndex === 0||columnIndex === 6||columnIndex === 7) {
                    if (rowIndex % 3 === 0) {
                        return {
                            rowspan: 3,
                            colspan: 1
                        };
                    } else {
                        return {
                            rowspan: 0,
                            colspan: 0
                        };
                    }
                }
            },
            //获取省份list
            getProvinceList:function(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getProvince`).then(function(res){
                    console.log(res);
                    this.provinceList=res.data;
                })
            },

            changeProvince:function () {
                this.search(this.searchForm);
            },

            //获取省份list
            getRegionList:function(){
                this.queryRegion.provinceCode = this.searchForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                        .then(function(res){
                            this.regionList=res.data;
                        })
            },
            getAllRegionList:function(){
                this.queryRegion.provinceCode = this.addForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.regionList=res.data;
                    })
            },
            //查询列表请求
            search:function(searchForm){
                console.log(searchForm);
                this.$http.post(`${this.proxyUrl}/param/regConf/getThreConfPage`,searchForm,{emulateJSON:true}).then(function(res){
                    this.currentPage=res.data.pageNum;
                    this.total=res.data.pageTotal;
                    this.tableData=res.data.datas;
                })
            },
            // 弹出修改框
            showEditConf(editForm){
                this.editVisible = true;
                this.queryOrDelForm.id=editForm.id;
                this.queryOrDelForm.provinceCode=editForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regConf/getParThreConfDetail`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    this.editForm = res.data;
                })
            },

            //修改请求
            editConf(editForm){
                console.log(this.editForm);
                this.$refs[editForm].validate((valid) => {
                    console.log(valid);
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/regConf/updateParThreConf`,this.editForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.$message({
                                    message: '修改成功！',
                                    type: 'success'
                                });
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.$message.error('修改失败!'+ res.data.resText);
                            }
                        })
                    } else {
                        return false;
            }
            });
            },

            //新增请求
            addConf(FormName){
                console.log(this.addForm);
                this.$refs[FormName].validate((valid) => {
                    console.log(valid);
                if (valid) {
                    this.$http.post(`${this.proxyUrl}/param/regConf/addParThreConf`,this.addForm,{emulateJSON:true}).then(function(res){
                        if(res.data.resStatus == 0){
                            this.$message({
                                message: '新增成功！',
                                type: 'success'
                            });
                            this.addVisible = false;
                            this.search(this.searchForm);
                        }else{
                            this.$message.error('新增失败!'+ res.data.resText);
                        }
                    })
                } else {
                    return false;
                }
            });
            },

            //删除请求
            delConf(role){
                this.queryOrDelForm.id = role.id;
                this.queryOrDelForm.provinceCode = role.provinceCode;
                this.$confirm('确认要删除吗？')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/param/regConf/deleteParThreConf`, this.queryOrDelForm, {emulateJSON: true})
                            .then(function (res) {
                                if (res.data.resStatus == 0) {
                                    this.$message({
                                        message: '删除成功！',
                                        type: 'success'
                                    });
                                    this.editVisible = false;
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('删除失败!'+ res.data.resText);
                                }
                            })
                    })
            },

//            objectSpanMethod({ row, column, rowIndex, columnIndex }) {
//                console.log('row:' + row+',column:' + column+',rowIndex:' + rowIndex+',columnIndex:' + columnIndex);
//                if (columnIndex === 0) {
//                    if (rowIndex % 2 === 0) {
//                        return {
//                            rowspan: 2,
//                            colspan: 1
//                        };
//                    } else {
//                        return {
//                            rowspan: 0,
//                            colspan: 0
//                        };
//                    }
//                }
//            },

            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            handleClick(activeName){
                console.log(activeName);
                this.$router.push(activeName);
            },

            //清空提示信息
            resetForm(formName){
                this.$nextTick(() => {
                    this.$refs[formName].resetFields();
            });
            },

            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .el-row-style {
        margin-top: 2%;
        margin-bottom: 2%;
    }

    .title-text-style{
        font-weight:bold;
    }
    .el-row-line{
        margin-top: 1%;
        margin-bottom: 1%;
        background-color: #D6DBDF;
        height:1px;border:none;
        border-top:1px
    }
    .def-tab {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .el-tabs__item{
        font-size: 24px;
        -webkit-margin-before: 0.67em;
        -webkit-margin-after: 0.67em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        font-weight: bold;
    }

    .user-title{
        margin-top: 8px;
        margin-left: 16px;
        background-color: white;
    }
    .user-line{
        margin-top: 8px;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
