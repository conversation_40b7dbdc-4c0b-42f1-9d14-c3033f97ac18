package com.cy.user.api;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cy.user.common.CyUserCommon;
import com.cy.user.common.ResultCommon;
import com.cy.user.model.UserModel;
import com.cy.user.vo.CyUserDetailVo;

public interface CyUserApi {
	@RequestMapping(value = "/cyuserservice/getCyUsersInfo")
	public ResultCommon getCyUsersInfo(CyUserCommon common);

	@RequestMapping(value = "/cyuserservice/getCyUserDetail")
	public CyUserDetailVo getCyUserDetail(CyUserCommon common);

	@RequestMapping(value = "/cyuserservice/modCustomerNick")
	public int modeCustomerNick(CyUserCommon common);
	
	
	@RequestMapping(value = "/cyuserservice/findUserById")
    public UserModel fingUserById(@RequestParam("iphone") String iphone);
	
	
	@RequestMapping(value = "/cyuserservice/updateUser")
    public int updateUser(UserModel user);
	
}
