
package com.cy.common;

public class SysLogCommon {
	private Integer sysLogId; // 日志id
	private String sysUseName;// 操作者
	private String sysUseModule;// 操作模块id
	private String sysUseTime;// 操作时间
	private String sysUseObject;// 操作对象
	private String sysUseContent;// 操作内容
	private String sysUseResult;// 操作结果
	private String sysUseIp;// ip
	private String sysUseType;// 操作类型
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private String startTime;// 开始时间
	private String endTime;// 结束时间

	public Integer getSysLogId() {
		return sysLogId;
	}

	public void setSysLogId(Integer sysLogId) {
		this.sysLogId = sysLogId;
	}

	public String getSysUseName() {
		return sysUseName;
	}

	public void setSysUseName(String sysUseName) {
		this.sysUseName = sysUseName;
	}

	public String getSysUseTime() {
		return sysUseTime;
	}

	public void setSysUseTime(String sysUseTime) {
		this.sysUseTime = sysUseTime;
	}

	public String getSysUseObject() {
		return sysUseObject;
	}

	public void setSysUseObject(String sysUseObject) {
		this.sysUseObject = sysUseObject;
	}

	public String getSysUseContent() {
		return sysUseContent;
	}

	public void setSysUseContent(String sysUseContent) {
		this.sysUseContent = sysUseContent;
	}

	public String getSysUseResult() {
		return sysUseResult;
	}

	public void setSysUseResult(String sysUseResult) {
		this.sysUseResult = sysUseResult;
	}

	public String getSysUseIp() {
		return sysUseIp;
	}

	public void setSysUseIp(String sysUseIp) {
		this.sysUseIp = sysUseIp;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getSysUseType() {
		return sysUseType;
	}

	public void setSysUseType(String sysUseType) {
		this.sysUseType = sysUseType;
	}

	public String getSysUseModule() {
		return sysUseModule;
	}

	public void setSysUseModule(String sysUseModule) {
		this.sysUseModule = sysUseModule;
	}

}
