<template>
    <div class="addeffect">
        <div class="content">
            <el-form :model="addForm" ref="addForm" class="demo-form-inline app-form-item" size="small" label-width="35%"  style="width: 80%">
                <el-form-item label="号码：" prop="sysUserName">
                    <el-input v-model="addForm.phoneNumber" style="width: 80%;"></el-input>
                </el-form-item>
                <el-form-item label="省份：" prop="sysStaffName">
                    <p>{{addForm.provinceName}}</p>
                </el-form-item>
                <el-form-item label="地区：" prop="sysUserCompany">
                    <p>{{addForm.countyName}}</p>
                </el-form-item>
                <el-form-item label="分类：" prop="provinceCode">
                    <el-select v-model="addForm.categoryId" placeholder="请选择">
                        <el-option
                                v-for="item in classify"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="号码来源：" prop="sysMobileNumber">
                    <el-select v-model="addForm.sourceId" placeholder="请选择" @change="queryMarkType(addForm.sourceId)">
                        <el-option
                                v-for="item in sourceList"
                                :key="item.sourceId"
                                :label="item.sourceName"
                                :value="item.sourceId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!--显示内容切换-->
                <div v-show="addForm.categoryId==2">
                    <el-form-item label="描述：">
                        <el-input
                                type="textarea"
                                :rows="2"
                                placeholder=""
                                v-model="addForm.classBtype">
                        </el-input>
                    </el-form-item>
                </div>
                <div v-show="addForm.categoryId==3">
                    <el-form-item label="标记类型：" prop="sysMobileNumber">
                        <template>
                            <el-checkbox-group v-model="addForm.markTypeIds">
                                <el-checkbox label="复选框 A"></el-checkbox>
                                <el-checkbox label="复选框 B"></el-checkbox>
                                <el-checkbox label="复选框 C"></el-checkbox>
                                <el-checkbox label="禁用" disabled></el-checkbox>
                                <el-checkbox label="选中且禁用" disabled></el-checkbox>
                            </el-checkbox-group>
                        </template>
                    </el-form-item>
                    <el-form-item label="标准类型：" prop="sysMobileNumber">
                        <el-select v-model="addForm.standardTypeId" placeholder="请选择">
                            <el-option
                                    v-for="item in standardType"
                                    :key="item.standardTypeId"
                                    :label="item.standardTypeName"
                                    :value="item.standardTypeId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="标记次数：" prop="sysUserName">
                        <el-input v-model="addForm.markTimes" style="width: 80%;"></el-input>
                    </el-form-item>
                </div>
            </el-form>
        </div>
        <div class="content1" style="text-align: right;">
            <el-button @click="cancle" size="small">取消</el-button>
            <el-button type="primary" @click="submit" size="small">确定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'addeffect',
        data(){
            return{
                name:'',
                addForm:{
                    type:1,//1.生效号码 2.白名单
                    phoneNumber:'',//号码
                    provinceId:'',//省份ID
                    provinceName:'',//省份名称
                    countyId:'',//地区ID
                    countyName:'',//地区名称
                    categoryId:1,//分类（1.诈骗 2黄页 3标记)
                    standardTypeId:'',//标准类型id
                    markTypeName:[],//标记类型名称
                    markTypeIds:[],//标记类型id
                    sourceId:'',//来源
                    markTimes:'',//标记次数
                    classBtype:'',//号码描述
                    operatorName:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//操作者姓名
                },
                //分类
                classify:[
                    {
                        id:1,
                        name:'诈骗'
                    },
                    {
                        id:2,
                        name:'黄页'
                    },
                    {
                        id:3,
                        name:'标记'
                    }
                ],
                searchForm:{
                    pageSize:1000000,
                    pageNo:1
                },
                standardType:[],
                sourceList:[],//号码来源
                markTypeList:[],//标记类型
            }
        },
        components: {},
        watch:{
            'addForm.phoneNumber':function () {
                if(this.addForm.phoneNumber.length==11){
                    this.seaNumber();//查询号码省份
                    this.queryNumIsExist();//查询号码是否已存在
                }
            },
            'addForm.categoryId':function () {
                if(this.addForm.categoryId==3){
                    this.searchstand();//标准类型
                }
                this.queryNumIsExist();//查询号码是否已存在
            }
        },
        created(){
            this.querySource();//号码来源
        },
        methods:{
            //查询号码省份
            seaNumber(){
                let vm = this;
                postHeader('queryProvinceInfo', JSON.stringify({'phoneNumber':vm.addForm.phoneNumber})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.addForm.provinceId = data.data.provinceId;
                        vm.addForm.provinceName = data.data.provinceName;
                        vm.addForm.countyId = data.data.countyId;
                        vm.addForm.countyName = data.data.countyName;
                    }else{

                    }
                })
            },
            //查询标准类型
            searchstand(){
                let vm = this;
                postHeader('queryStandardType',JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.standardType = data.data.standardTypeList;
                    }
                })
            },
            //查询号码来源
            querySource(){
                let vm = this;
                postHeader('querySource',JSON.stringify({})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.sourceList = data.data.sourceList;
                    }
                })
            },
            //查询标记类型
            queryMarkType(sourceId){
                let vm = this;
                postHeader('queryMarkType',JSON.stringify({'sourceId':sourceId})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.markTypeList = data.data.markTypeList;
                    }
                })
            },
            //查询号码是否存在
            queryNumIsExist(){
                let vm = this;
//                vm.querySource();
                postHeader('queryNumIsExist',JSON.stringify(vm.addForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        if(data.data.queryNumInfoList){
                            let queryNumInfoList = data.data.queryNumInfoList;
                            queryNumInfoList.forEach(item=>{
                                vm.sourceList.forEach((val,index)=>{
                                    if(val.sourceId==item.sourceId){
                                        vm.sourceList.splice(index,1);
                                        return;
                                    }
                                })
                            })
                        }
                    }
                })
            },
            submit(){
                let vm = this;
                postHeader('addNumber', JSON.stringify(vm.addForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("新增成功");
                    }else{
                        vm.$message.error("新增失败");
                    }
                })
                this.$emit('addList');
            },
            cancle(){
                this.$emit('addList');
            }
        },

    }
</script>

<style scoped>
    .content{
        width: 640px;
    }
    .content1{
        text-align: center;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px;
        margin-right: 20px;
    }
</style>
