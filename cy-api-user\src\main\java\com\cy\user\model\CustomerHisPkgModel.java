package com.cy.user.model;

public class CustomerHisPkgModel {

	private int pkgId;
	/**
	 * 企业代码
	 */
	private String bossId;
	/**
	 * 订购手机号码
	 */
	private String userId;
	/**
	 * 业务代码
	 */
	private String serviceId;
	/**
	 * 产品代码
	 */
	private String productId;
	/**
	 * 套餐包名称
	 */
	private String packageName;
	/**
	 * 业务资费
	 */
	private String serviceCost;
	/**
	 * 支付方式业务开通来源 0：非boss来源 1：来源于boss
	 */
	private int payType;

	private String payTypeName;
	/**
	 * 套餐包状态
	 */
	private int pkgStatus;
	/**
	 * 套餐包生效时间
	 */
	private String effectiveTime;

	/**
	 * 套餐包生效时间
	 */
	private String expiredTime;
	/**
	 * 套餐包退订时间
	 */
	private String unSubScriTtime;
	/**
	 * 开户渠道
	 */
	private String pkgChannelOn;
	/**
	 * 开户时间
	 */
	private String createTime;

	private String pkgChannelOnName;
	/**
	 * 退订渠道
	 */
	private String pkgChannelOff;

	private String pkgChannelOffName;

	// 套餐类型 0 普通套餐 1 体验套餐
	private String trailFlag;

	private String des;

	private String cyType;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public int getPkgId() {
		return pkgId;
	}

	public void setPkgId(int pkgId) {
		this.pkgId = pkgId;
	}

	public String getBossId() {
		return bossId;
	}

	public void setBossId(String bossId) {
		this.bossId = bossId;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getProductId() {
		return productId;
	}

	public void setProductId(String productId) {
		this.productId = productId;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getServiceCost() {
		return serviceCost;
	}

	public void setServiceCost(String serviceCost) {
		this.serviceCost = serviceCost;
	}

	public int getPayType() {
		return payType;
	}

	public void setPayType(int payType) {
		this.payType = payType;
	}

	public int getPkgStatus() {
		return pkgStatus;
	}

	public void setPkgStatus(int pkgStatus) {
		this.pkgStatus = pkgStatus;
	}

	public String getEffectiveTime() {
		return effectiveTime;
	}

	public void setEffectiveTime(String effectiveTime) {
		this.effectiveTime = effectiveTime;
	}

	public String getUnSubScriTtime() {
		return unSubScriTtime;
	}

	public void setUnSubScriTtime(String unSubScriTtime) {
		this.unSubScriTtime = unSubScriTtime;
	}

	public String getPkgChannelOn() {
		return pkgChannelOn;
	}

	public void setPkgChannelOn(String pkgChannelOn) {
		this.pkgChannelOn = pkgChannelOn;
	}

	public String getPkgChannelOff() {
		return pkgChannelOff;
	}

	public void setPkgChannelOff(String pkgChannelOff) {
		this.pkgChannelOff = pkgChannelOff;
	}

	public String getPkgChannelOnName() {
		return pkgChannelOnName;
	}

	public void setPkgChannelOnName(String pkgChannelOnName) {
		this.pkgChannelOnName = pkgChannelOnName;
	}

	public String getPkgChannelOffName() {
		return pkgChannelOffName;
	}

	public void setPkgChannelOffName(String pkgChannelOffName) {
		this.pkgChannelOffName = pkgChannelOffName;
	}

	public String getPayTypeName() {
		return payTypeName;
	}

	public void setPayTypeName(String payTypeName) {
		this.payTypeName = payTypeName;
	}

	public String getTrailFlag() {
		return trailFlag;
	}

	public void setTrailFlag(String trailFlag) {
		this.trailFlag = trailFlag;
	}

	public String getDes() {
		return des;
	}

	public void setDes(String des) {
		this.des = des;
	}

	public String getExpiredTime() {
		return expiredTime;
	}

	public void setExpiredTime(String expiredTime) {
		this.expiredTime = expiredTime;
	}

	public String getCyType() {
		return cyType;
	}

	public void setCyType(String cyType) {
		this.cyType = cyType;
	}
}
