package com.cy.user.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 业务控制表
 */
@Data
public class CsEnterpriseServControlModel {

    /**
     * 编号
     */
    private Integer id;

    /**
     * 企业ID
     */

    private Integer enterpriseId;

    /**
     * 热线业务开关，xx,第一位表示闪信，第二位表示短信，0关闭，1开启
     */
    private Integer hotSwitch;

    /**
     * 内容审核回调地址
     */

    private String contentCallback;

    /**
     * 投递回调地址
     */
    private String deliveryCallback;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

}
