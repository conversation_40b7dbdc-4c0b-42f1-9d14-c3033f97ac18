/**
 * 文 件 名: ResultListCommon.java
 * 版    权:  China Mobile Communications Corporation. Copyright YYYY-YYYY,  All rights reserved
 * 描    述:  <描述>
 * 修 改 人:  yuanwenbin
 * 修改时间:  2018年3月30日
 * 跟踪单号:  <跟踪单号>
 * 修改单号:  <修改单号>
 * 修改内容:  <修改内容>
 */
package com.cs.param.common;

/**
 * 查询类结果对象
 *
 * <AUTHOR>
 * @version [SAS4 V401R001, 2018年3月30日]
 *
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class ResultListCommon {

	private Integer pageTotal; // 总条数
	private Integer pageNum; // 查询的页码
	private Object datas; // 结果对象

	public Integer getPageTotal() {
		return pageTotal;
	}

	public void setPageTotal(Integer pageTotal) {
		this.pageTotal = pageTotal;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Object getDatas() {
		return datas;
	}

	public void setDatas(Object datas) {
		this.datas = datas;
	}

}
