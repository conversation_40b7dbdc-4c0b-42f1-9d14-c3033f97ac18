<template>
    <div>
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline" @submit.native.prevent>
                <el-form-item label="手机号">
                    <el-input placeholder="手机号" v-model="param.pkCurUserid" clearable size="small" :maxlength="11"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" v-on:click="query(param.pkCurUserid)" size="small" class="app-bnt" v-bind:disabled="btnhide">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table :data="tableData" border  class="app-tab">
            <el-table-column prop="pkCurUserid" label="用户号码" width="140">
            </el-table-column>
            <el-table-column prop="curNick" label="昵称"  :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="curProvinceName" label="省份">
            </el-table-column>
            <el-table-column prop="curAreaName" label="地区">
            </el-table-column>
            <el-table-column label="个人彩印状态" width="140">
                <template slot-scope="scope">
                    <div>{{getPersonStatus(scope.row)}}</div>
                </template>
            </el-table-column>
            <el-table-column label="新媒彩印状态" width="140">
                <template slot-scope="scope">
                    <div>{{getMediaStatus(scope.row)}}</div>
                </template>
            </el-table-column>
            <el-table-column label="提醒彩印状态" width="140">
                <template slot-scope="scope">
                    <div>{{getRemindStatus(scope.row)}}</div>
                </template>
            </el-table-column>
            <el-table-column label="企业彩印状态" width="140">
                <template slot-scope="scope">
                    <div>{{getCdpStatus(scope.row)}}</div>
                </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="100">
                <template slot-scope="scope">
                    <el-button @click="userDetail(scope.row)" type="text" size="small">查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block app-pageganit">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="page.pageNum" :page-sizes="[10,20,30, 50]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="page.pageTotal">
            </el-pagination>
        </div>

        <jkAuth menuCode="CPUserList" @buttonDisable="setButton"></jkAuth>
    </div>
</template>
<script>
  //import cbauth from '@/components/common/cbauth';
  import jkAuth from '@/components/common/jkAuth';
  export default {
    components:{
        jkAuth
    },
    name: 'UserList',
    data() {
        return {
            btnhide:true,
            param: {
                pkCurUserid: ''
            },
            functionCode: '010101',
            operationCode: '1001',
            page: {
                pageNum: 1,
                pageSize: 10,
                pageTotal:0
            },
            tableData: []
        }
    },
    methods: {
        setButton(data){
            this.btnhide = data;
        },
        getPersonStatus(row){
            if(row.personStatus==0){
                return '未订购';
            }
            if(row.personStatus==1){
                return '已开通';
            }
            if(row.personStatus==2){
                return '已退订';
            }
        },
        getMediaStatus(row){
            if(row.mediaStatus==0){
                return '未订购';
            }
            if(row.mediaStatus==1){
                return '已开通';
            }
            if(row.mediaStatus==2){
                return '已退订';
            }
        },
        getRemindStatus(row){
            if(row.remindStatus==0){
                return '未订购';
            }
            if(row.remindStatus==1){
                return '已开通';
            }
            if(row.remindStatus==2){
                return '已退订';
            }
        },
        getCdpStatus(row){
            if(row.cdpStatus==0){
                return '未订购';
            }
            if(row.cdpStatus==1){
                return '已开通';
            }
            if(row.cdpStatus==2){
                return '已退订';
            }
        },
        pageQuery: function(param) {
            this.$http
                .post(`${this.proxyUrl}/user/cyuser/getCyUsersInfo`, param, { emulateJSON: true })
                .then(function(res) {
                    this.$set(this.page,'pageTotal',res.data.pageTotal);
                    this.tableData = res.data.datas;
                })
        },
        query: function(pkCurUserid) {
            if(pkCurUserid == null || pkCurUserid == ''){
                this.$message.error("请输入用户号码");
                return false;
            }
            this.$set(this.page, 'pageNum', 1);
            this.$set(this.page, 'pkCurUserid', pkCurUserid);
            this.pageQuery(this.page);
        },
        handleSizeChange: function(size) {
            this.$set(this.page, 'pageSize', size);
            this.$set(this.page, 'pageNum', 1);
           // this.$options.methods.query(this.page);
        },
        handleCurrentChange: function(currentPage) {
            this.$set(this.page, 'pageNum', currentPage);
            this.pageQuery(this.page);
            // this.$options.methods.query(this.page);
        },
        userDetail: function(row) {
            this.$router.push({ name: '用户详情', params: row});
            sessionStorage.setItem('pkCurUserid', row.pkCurUserid);
        },
    },
    mounted() {
        //this.pageQuery();
    }
}
</script>
<style scoped>

</style>