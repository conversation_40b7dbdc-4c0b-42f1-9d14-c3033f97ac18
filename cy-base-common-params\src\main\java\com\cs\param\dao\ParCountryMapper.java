package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParCountryCommon;
import com.cs.param.model.ParCountryModel;

@Repository
public interface ParCountryMapper {

	int insertParCountry(ParCountryCommon common) throws SQLException;

	int updateParCountryByPK(ParCountryCommon common) throws SQLException;

	int deleteParCountryByCode(ParCountryCommon common) throws SQLException;

	List<ParCountryModel> getParCountryListByCond(ParCountryCommon common) throws SQLException;

	Integer queryCountByCode(ParCountryCommon common) throws SQLException;

}
