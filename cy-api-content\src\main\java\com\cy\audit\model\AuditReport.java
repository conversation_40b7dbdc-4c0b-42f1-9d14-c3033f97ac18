package com.cy.audit.model;

import java.io.Serializable;
import java.util.List;

public class AuditReport implements Serializable {
    private String type; // 所上传待审核资源的类型
    private List<AuditData> sources; // 待审核资源的集合
    private boolean manualAudit; // 是否需要人工审核
    private String notifyName; // 回调名称
    private String account; // 系统账号
    private String preset; // 模版名称

    public AuditReport() {

    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<AuditData> getSources() {
        return sources;
    }

    public void setSources(List<AuditData> sources) {
        this.sources = sources;
    }

    public boolean isManualAudit() {
        return manualAudit;
    }

    public void setManualAudit(boolean manualAudit) {
        this.manualAudit = manualAudit;
    }

    public String getNotifyName() {
        return notifyName;
    }

    public void setNotifyName(String notifyName) {
        this.notifyName = notifyName;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPreset() {
        return preset;
    }

    public void setPreset(String preset) {
        this.preset = preset;
    }
}
