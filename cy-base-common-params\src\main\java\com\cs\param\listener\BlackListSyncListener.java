package com.cs.param.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cs.param.common.ParBlackListCommon;
import com.cs.param.constants.QueueNames;
import com.cs.param.services.ParBlackListService;
import com.cs.param.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 同步黑名单监听器
 *
 * <AUTHOR>
 * @date 2021/9/1 16:38
 */
@Component
@Slf4j
public class BlackListSyncListener {

    @Autowired
    private ParBlackListService service;

    @RabbitListener(queues = QueueNames.BLACKLIST_SYNC,containerFactory = "rabbitListenerContainerFactory")
    public void process(String message) {
        LogUtil.info(log, LogUtil.SVC, "同步黑名单开始", message);
        try {
            JSONObject json = JSON.parseObject(message);
            String phone = json.getString("userNumber");
            String operatorName = json.getString("operatorName");
            String type = json.getString("type");
            String status = json.getString("status");
            if (StringUtils.equals("0",status)){
                LogUtil.info(log,LogUtil.SVC,phone,"拒接操作失败，不同步黑名单");
                return;
            }
            ParBlackListCommon common = new ParBlackListCommon().setPhone(phone).setSysUserName(operatorName);
            if (service.isExisted(phone)) {
                LogUtil.info(log, LogUtil.SVC, phone, "该号码在黑名单中已存在");
                if (StringUtils.equals("1111", type)) {
                    LogUtil.info(log, LogUtil.SVC, phone, "该号码取消所有拒接" + ",type:" + type);
                    service.delBlackList(common);
                }
            } else if(!StringUtils.equals("1111",type)){
                service.insert(common);
            }
        } catch (Exception e) {
            LogUtil.error(log, LogUtil.SVC, "同步黑名单异常", message, e);
        }
    }
}
