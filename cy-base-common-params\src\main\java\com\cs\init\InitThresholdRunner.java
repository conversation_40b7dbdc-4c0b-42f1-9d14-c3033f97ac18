package com.cs.init;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.RandomAccessFile;
import java.nio.charset.Charset;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.cs.param.common.ParNumberCommon;
import com.cs.param.dao.ParNumberMapper;
import com.cs.param.execl.SectionData;
import com.github.crab2died.ExcelUtils;

/**
 * 初始化阈值数据到Redis数据库
 *
 */
@Component
@Order(value = 1)
public class InitThresholdRunner implements CommandLineRunner {

//	@Autowired
//	private ParNumberMapper parNumberMapper;

	@Override
	public void run(String... args) throws Exception {
//		File file = new File("C:\\Users\\<USER>\\Desktop\\section_template.xlsx");
//		InputStream in = new FileInputStream(file);
//		List<SectionData> list = ExcelUtils.getInstance().readExcel2Objects(in, SectionData.class, 0, Integer.MAX_VALUE, 0);
//		System.out.println("-------------" + list.size());
//		RandomAccessFile rf = new RandomAccessFile("C:\\Users\\<USER>\\Desktop\\增量.csv", "rw");
//		long startTime = System.currentTimeMillis();
//		int i = 1;
//		for (SectionData sectionData : list) {
//			System.out.println("===================== " + i++);
//			ParNumberCommon common = new ParNumberCommon();
//			if (sectionData.getPhoneSection() != null && !"".equals(sectionData.getPhoneSection())) {
//				common.setPhoneSection(sectionData.getPhoneSection());
//				common.setStatus("0");
//				int count = parNumberMapper.queryPageCount(common);
//				if (count == 0) {
//					String getPhoneSection = sectionData.getPhoneSection();
//					String getOptCode = "";
//					String getNetCode = sectionData.getNetCode();
//					String getProvinceCode = sectionData.getProvinceCode();
//					String getRegionCode = sectionData.getRegionCode();
//					String getHlrAddr = "";
//					String getTerminalCode = sectionData.getTerminalCode();
//					String str = getPhoneSection + "," + getOptCode + "," + getNetCode + "," + getProvinceCode + "," + getRegionCode + ","
//							+ getHlrAddr + "," + getTerminalCode + "\r\n";
//					rf.write(str.getBytes(Charset.forName("gbk")));
//				}
//			}
//		}
//		long endTime = System.currentTimeMillis();
//		System.out.println("-------------" + (endTime - startTime) / 1000);
//		rf.close();
	}

}
