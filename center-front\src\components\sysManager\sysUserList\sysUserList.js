export default {
    data() {
        return {
            addVisible: false,
            visibleText:'',
            editVisible: false,
            propVisible:false,
            propMsg:'',
            //查询form对象定义
            searchForm: {
                sysState:'', //状态
                sysRoleId:'',//角色id,
                sysUserName:'', //模糊查询
                sysUserCompany: '', //模糊查询
                provinceCode: '',
                startTime:'',//创建时间
                endTime:'',//创建时间
                pageSize:10,// 每页显示条数
                pageNum:1 // 查询的页码
            },
            //新增form对象定义
            sysUserFrom: {
                sysUserId:'',
                sysUserName:'',
                sysUserPassword:'',
                confirmPassword:'',
                sysStaffName:'',
                sysRoleId:'',//角色id
                sysUserCompany:'',
                provinceCode:'',
                sysMobileNumber:'',
                sysUserEmail:'',
                sysState:''
            },
            //查询或删除form
            queryOrDelForm:{
                sysUserId:''
            },
            rules: {
                sysUserName: [
                    { required: true, message: '请输入用户名称', trigger: 'blur' },
                    { min: 3, max: 50, message: '长度在 3 到 50 个字符', trigger: 'blur' }
                ],
                sysStaffName: [
                    { required: true, message: '请输入姓名', trigger: 'blur' },
                ],
                sysRoleId: [
                    { required: true, message: '请选择角色', trigger: 'change' },
                ],
                sysUserCompany: [
                    { required: true, message: '请输入公司名称', trigger: 'blur' },
                ],
                provinceCode: [
                    { required: true, message: '请选择省份', trigger: 'change' },
                ],
                sysMobileNumber: [
                    { required: true, message: '请输入电话号码', trigger: 'blur' }
                ],
                sysUserEmail: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' }
                ]
//                    ,
//                    sysState: [
//                        { required: true, message: '请选择状态', trigger: 'change' }
//                    ]
            },
            statusList:[],
            roleList:[],
            provinceList:[],
            tableData:[],
            currentPage: 1,
            total:0

        }
    },
    mounted(){
//            this.slideData(this.proxyUrl);
    },
    methods: {
        //获取省份list
        getProvinceList:function(){
            this.$store.dispatch('sysUserListModule/getProvinceList',{}).then(res=>{
                this.provinceList=res.data;
            })
        },
        //获取角色list
        getRoleList:function(){
            this.$store.dispatch('sysUserListModule/getRoleList',{}).then(res=>{
                this.roleList=res.data;
            })
        },
        //查询列表请求
        search:function(searchForm){
            this.$store.dispatch('sysUserListModule/sysUserList',searchForm).then(res=>{
                this.currentPage=res.data.pageNum;
                this.total=res.data.pageTotal;
                this.tableData=res.data.list;
            })
        },
        //新增或修改系统用户
        saveOrUpdateType(text,value){
            this.addVisible=true;
            this.visibleText=text;
            if(this.sysUserFrom){
                this.sysUserFrom=Object.assign({},value);
            }
        },
        // 新增
        addOrUpdateUser(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    if(this.sysUserFrom.sysUserId){
                        this.$store.dispatch('sysUserListModule/updateSysUser',this.sysUserFrom).then(res=>{
                            if(res.data.resStatus==0){
                                this.$message('修改成功');
                                this.propVisible=true;
                                this.addVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.$message('修改失败!'+res.data.resText);
                                this.propVisible=true;
                            }
                        });
                    }else{
                        this.$store.dispatch('sysUserListModule/addSysUser',this.sysUserFrom).then(res=>{
                            if(res.data.resStatus==0){
                                this.$message('新增成功');
                                this.propVisible=true;
                                this.addVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.$message('新增失败!'+res.data.resText);
                                this.propVisible=true;
                            }
                        });
                    }
                }
            })
        },
        //删除请求
        delUser(role){
            console.log(role);
            this.queryOrDelForm.sysUserId = role.sysUserId;
            console.log(this.queryOrDelForm);
            this.$http.post(`${this.proxyUrl}/sys/sysUser/deleteSysUser`,this.queryOrDelForm,{emulateJSON:true})
                    .then(function(res){
                        console.log(res)
                        if(res.data.resStatus == 0){
                            this.propMsg = '删除成功！';
                            this.propVisible=true;
                            this.search(this.searchForm);
                        }else{
                            this.propMsg = '删除失败!'+ res.data.resText;;
                            this.propVisible=true;
                        }
                    })
        },
        handleSizeChange(val) {
            this.searchForm.pageSize=val;
            this.search(this.searchForm);
        },
        handleCurrentChange(val) {
            this.searchForm.pageNum=val;
            this.search(this.searchForm);
        },

        //清空提示信息
        resetForm(formName){
            this.$refs[formName].resetFields();
//                this.$nextTick(() => {
//            });
        },
        // 关闭弹出框
        handleClose(done) {
            this.$confirm('确认关闭？')
                    .then(_ => {
                done();
        })
        .catch(_ => {});
        },
        // 关闭提示框
        handleCloseConfirm(done) {
            done();
        }

    },
    created() {
        //获取省份list
        this.getProvinceList();
        //获取角色list
        this.getRoleList();
        //查询列表请求
        this.search(this.searchForm);
    },
    components: {}
}