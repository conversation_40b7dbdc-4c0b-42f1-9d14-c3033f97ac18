<template scope="scope">
    <div>
        <h1 class="user-title">批量下架内容 <div style="font-size:15px; color: #cc0000">(该操作会下架所属该批次的所有的内容，请谨慎操作。如需下架指定的内容，请在菜单《下架内容》操作)</div></h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="类型">
              <el-select v-model="searchReq.csType" size="small">
                <el-option
                    v-for="item in statusData"
                    :key="item.csTypeId"
                    :label="item.csTypeName"
                    :value="item.csTypeId">
                </el-option>
              </el-select>
            </el-form-item>
                <el-form-item label="内容ID" >
                    <el-input v-model="searchReq.csContentNo" size="small" :maxlength="32" clearable></el-input>
                </el-form-item>

                <el-form-item label="内容">
                    <el-input v-model="searchReq.csContent" size="small" :maxlength="50" clearable></el-input>
                </el-form-item>


                <el-form-item>
                    <el-button type="primary" @click="showMessage();search(searchReq)" size="small">查询</el-button>
                </el-form-item>
            </el-form>

          <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
              <el-button @click="offVisible = true;" size="small" v-bind:disabled="btnhide"  v-bind:type="btype" >批量下架</el-button>
            </el-form-item>
          </el-form>

         
        </div>
        <div style="position: relative;">
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    @selection-change="handleSelectionChange"
                    :header-cell-class-name="tableheaderClassName">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                        prop="moldId"
                        label="类型"
                        width="100"
                        :formatter="formatterType">
                </el-table-column>
                <el-table-column
                        prop="csId"
                        label="内容ID"
                        width="240">
                </el-table-column>
                <el-table-column
                        prop="csContent"
                        label="内容"
                        width="400"
                        :show-overflow-tooltip="true">
                      <template slot-scope="scope">
                      <el-button v-if="(scope.row.moldId==3)" type="text" size="small" @click="detailVisible=true;rowData=scope.row;">{{scope.row.csContent}}</el-button>
                      <span v-if="!(scope.row.moldId==3)">{{scope.row.csContent}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template slot-scope="scope">
                    <div>{{getCsTypeName(scope.row)}}</div>
                 </template>
                </el-table-column>
                <el-table-column
                        prop="submitTime"
                        label="提交时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="assessorTime"
                        label="审核时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="usedCount"
                        label="使用人数"
                        width="100">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-button :disabled="scope.row.csType == '2' || scope.row.csType == '4'" @click="delVisible=true;rowData=scope.row;" type="text" size="small">下架</el-button>
                  </template>
                </el-table-column>
            </el-table>
            <!--弹窗 -->
            <el-dialog
                width="30%"
                title="下架"
                :visible.sync="delVisible"
                :close-on-click-modal="false"
                append-to-body>
              <span style="font-size:15px;">内容下架后，用户当前设置将失效，请确认？</span>
              <div slot="footer" class="dialog-footer" style="text-align: right;">
                <el-button @click="delVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="delVisible = false;offSale(rowData);" size="small">确 定</el-button>
              </div>
            </el-dialog>

            <el-dialog
                width="30%"
                title="下架"
                :visible.sync="offVisible"
                :close-on-click-modal="false"
                append-to-body>
              <span style="font-size:15px;">内容下架后，用户当前设置将失效，请确认？</span>
              <div slot="footer" class="dialog-footer" style="text-align: right;">
                <el-button @click="offVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="offVisible = false;offSaleAny();" size="small">确 定</el-button>
              </div>
            </el-dialog>

            <el-dialog
                width="40%"
                title="内容详情"
                :visible.sync="detailVisible"
                :close-on-click-modal="false"
                append-to-body>
                <el-row>
                  <el-col :span="12">彩印ID</el-col>
                  <el-col :span="12">内容</el-col>
                </el-row>
                <div style="height:300px;overflow:auto;">
                  <el-row v-if="rowData.content1">
                    <el-col :span="12"><div v-html="rowData.csId+'1'"></div></el-col>
                    <el-col :span="12"><div v-html="rowData.content1"></div></el-col>
                  </el-row>

                   <el-row v-if="rowData.content2">
                      <el-col :span="12"><div v-html="rowData.csId+'2'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.content2"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content3">
                      <el-col :span="12"><div v-html="rowData.csId+'3'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.content3"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content4">
                      <el-col :span="12"><div v-html="rowData.csId+'4'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content4"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content5">
                      <el-col :span="12"><div v-html="rowData.csId+'5'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content5"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content6">
                      <el-col :span="12"><div v-html="rowData.csId+'6'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content6"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content7">
                      <el-col :span="12"><div v-html="rowData.csId+'7'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content7"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content8">
                      <el-col :span="12"><div v-html="rowData.csId+'8'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content8"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content9">
                      <el-col :span="12"><div v-html="rowData.csId+'9'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content9"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content10">
                      <el-col :span="12"><div v-html="rowData.csId+'10'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content10"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content11">
                      <el-col :span="12"><div v-html="rowData.csId+'11'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content11"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content12">
                      <el-col :span="12"><div v-html="rowData.csId+'12'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content12"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content13">
                      <el-col :span="12"><div v-html="rowData.csId+'13'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content13"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content14">
                      <el-col :span="12"><div v-html="rowData.csId+'14'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content14"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content15">
                      <el-col :span="12"><div v-html="rowData.csId+'15'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content15"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content16">
                      <el-col :span="12"><div v-html="rowData.csId+'16'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content16"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content17">
                      <el-col :span="12"><div v-html="rowData.csId+'17'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content17"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content18">
                      <el-col :span="12"><div v-html="rowData.csId+'18'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content18"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content19">
                      <el-col :span="12"><div v-html="rowData.csId+'19'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content19"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content20">
                      <el-col :span="12"><div v-html="rowData.csId+'20'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content20"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content21">
                      <el-col :span="12"><div v-html="rowData.csId+'21'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content21"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content22">
                      <el-col :span="12"><div v-html="rowData.csId+'22'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content22"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content23">
                      <el-col :span="12"><div v-html="rowData.csId+'23'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content23"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content24">
                      <el-col :span="12"><div v-html="rowData.csId+'24'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content24"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content25">
                      <el-col :span="12"><div v-html="rowData.csId+'25'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content25"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content26">
                      <el-col :span="12"><div v-html="rowData.csId+'26'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content26"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content27">
                      <el-col :span="12"><div v-html="rowData.csId+'27'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content27"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content28">
                      <el-col :span="12"><div v-html="rowData.csId+'28'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content28"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content29">
                      <el-col :span="12"><div v-html="rowData.csId+'29'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content29"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content30">
                       <el-col :span="12"><div v-html="rowData.csId+'30'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content30"></div></el-col>
                    </el-row>
                  </div>
            </el-dialog>
            <transition name="fade">
              <div class="ts-model" v-show="showModel">注意：内容模糊搜索时，仅展示近3万条记录的内容。</div>
            </transition>
            <!-- 分页 -->
            <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.pageNum"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageTotal"  style="text-align: right;">
            </el-pagination>
        </div>
        </div>

    </div>
</template>
<script>
export default {
  name: "textCS",
  data() {
    return {
      showModel: false,
      btype:'info',
      btnhide:true,
      rowData: "",
      pageTotal: 0,
      offData: {},
      statusData: [], //类型下拉框选项
      checked: [],
      offVisible: false,
      delVisible: false,
      detailVisible: false,
      tableLoading:false,
      tableData: [
        // {
        //   moldId: 1,
        //   moldName: "彩印盒",
        //   pkId: "7",
        //   csId: "123123123",
        //   csContent: "信你那快",
        //   csType: "1"
        // },
        // {
        //   moldId: 2,
        //   moldName: "彩印盒",
        //   pkId: "7",
        //   csId: "123123123",
        //   csContent: "信你那快",
        //   csType: "1"
        // },
        // {
        //   moldId: 3,
        //   moldName: "彩印盒",
        //   pkId: "7",
        //   csId: "123123123",
        //   csContent: "信你那快",
        //   csType: "1"
        // }
      ],
      // tableData: [],
      multipleSelection: [],
      searchReq: {
        csType: 1,
        csContentNo: "",
        csContent:"",
        pageSize: 10,
        pageNum: 1
      },
      delRequest: {
        csId: []
      },
      offSaleReq: {
        diyIds: [], //1
        csIds: [], //2
        pkgIds: [] //3
      },
      offSaleAnyReq: {
        diyIds: [], //1
        csIds: [], //2
        pkgIds: [] //3
      },
      request: {
        
      }
    };
  },
  mounted() {
    this.statusSilde();
  },
  methods: {
    showMessage() {
      this.showModel = true;
      setTimeout(() => {
        this.showModel = false;
      }, 3000)
    },
    formatterType(row, column) {
          switch(row.moldId){
              case 1:
                  return '用户DIY';
                  break;
              case 2:
                  return '个人彩印';
                  break;
              case 3:
                  return '彩印盒';
                  break;
              case 5:
                return '挂机短信';
                break;
              case 6:
                return '企业视彩号主叫彩印';
                break;
              case 7:
                return '企业视彩号被叫彩印';
                break;
              case 8:
                return '主叫文本彩印';
                break;
              case 9:
                return '被叫文本彩印';
                break;
              case 10:
                return '用户主叫DIY';
                break;
              case 11:
                return '用户被叫DIY';
                break;
              case 12:
                return '直投闪短信';
                break;
          }
      },

    getCsTypeName(row) {
      if(row.csType == 1) {
        return '上架'
      } else if(row.csType == 2) {
        return '待上架'
      } else if(row.csType == 4) {
        return '已下架'
      }

    },
    //表格多选框
    handleSelectionChange(val) {
      // this.multipleSelection = val;
      //   this.delRequest.csId.length = 0;
      // console.log(this.multipleSelection);
      this.offSaleAnyReq.diyIds.length = 0;
      this.offSaleAnyReq.csIds.length = 0;
      this.offSaleAnyReq.pkgIds.length = 0;
      for (var i = 0; i < val.length; i++) {
        if (val[i].moldId == 1 || val[i].moldId == 10 || val[i].moldId == 11) {
          this.offSaleAnyReq.diyIds.push(val[i].pkId);
        }
        if (val[i].moldId == 2 || val[i].moldId == 5 || val[i].moldId == 6 || val[i].moldId == 7 || val[i].moldId == 8 || val[i].moldId == 9 || val[i].moldId == 12) {
          this.offSaleAnyReq.csIds.push(val[i].pkId);
        }
        if (val[i].moldId == 3) {
          this.offSaleAnyReq.pkgIds.push(val[i].pkId);
        }
      }
      var offsize = this.offSaleAnyReq.diyIds.length+this.offSaleAnyReq.csIds.length+this.offSaleAnyReq.pkgIds.length;
      if(offsize>0){
        this.btnhide=false;
        this.btype='primary'
      }else{
        this.btnhide=true;
        this.btype='info';
      }
    },
    //请求----------------------------
    //查询请求
    search: function(searchReq) {
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csOff/getUsedContent`, searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
          this.tableLoading=false;
        });
    },
    //类型选项请求
    statusSilde: function() {
      this.$http
        .get(`${this.proxyUrl}/content/csOff/getCsType`, { emulateJSON: true })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.statusData = res.data.datas;
          } else if (res.data.resStatus == "1") {
            console.log("请求失败");
          }
        });
    },
    //下架参数
    offDatas: function(val) {},
    //下架请求
    offSale: function(row) {
      if (row.moldId == 1 || row.moldId == 10 || row.moldId == 11) {
        this.offSaleReq.diyIds.push(row.pkId);
      }
      if (row.moldId == 2 || row.moldId == 5 || row.moldId == 6 || row.moldId == 7|| row.moldId == 8|| row.moldId == 9|| row.moldId == 12) {
        this.offSaleReq.csIds.push(row.pkId);
      }
      if (row.moldId == 3) {
        this.offSaleReq.pkgIds.push(row.pkId);
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csOff/offContent`,JSON.stringify(this.offSaleReq))
        .then(
          function(res) {
            if (res.data.resStatus == "0") {
              this.$message.success("下架成功");
              this.offSaleReq.diyIds.length = 0;
              this.offSaleReq.csIds.length = 0;
              this.offSaleReq.pkgIds.length = 0;
              this.tableLoading = false;
              this.search(this.searchReq);
            } else if (res.data.resStatus == "1") {
              this.$message.error(res.data.resText);
              this.offSaleReq.diyIds.length = 0;
              this.offSaleReq.csIds.length = 0;
              this.offSaleReq.pkgIds.length = 0;
              this.tableLoading = false;
            }
          },
          function() {
            this.$message.error(res.data.resText);
            this.offSaleReq.diyIds.length = 0;
            this.offSaleReq.csIds.length = 0;
            this.offSaleReq.pkgIds.length = 0;
            this.tableLoading = false;
          }
        );
        
    },
    //批量下架
    offSaleAny: function() {
      if(this.offSaleAnyReq.diyIds.length==0 && this.offSaleAnyReq.csIds.length==0 && this.offSaleAnyReq.pkgIds.length==0){
        this.$message("未选中下架内容");
        return;
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csOff/offContent`,JSON.stringify(this.offSaleAnyReq))
        .then(
          function(res) {
            if (res.data.resStatus == "0") {
              this.$message.success("下架成功");
              this.offSaleAnyReq.diyIds.length = 0;
              this.offSaleAnyReq.csIds.length = 0;
              this.offSaleAnyReq.pkgIds.length = 0;
              this.tableLoading = false;
              this.search(this.searchReq);
            } else if (res.data.resStatus == "1") {
              this.$message.error("下架失败 "+res.data.resText);
              this.offSaleAnyReq.diyIds.length = 0;
              this.offSaleAnyReq.csIds.length = 0;
              this.offSaleAnyReq.pkgIds.length = 0;
              this.tableLoading = false;
            }
          },
          function() {
            this.$message.error("下架失败 "+res.data.resText);
            this.offSaleAnyReq.diyIds.length = 0;
            this.offSaleAnyReq.csIds.length = 0;
            this.offSaleAnyReq.pkgIds.length = 0;
            this.tableLoading = false;
          }
        );
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csOff/getUsedContent`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
    handleCurrentChange(val) {
      this.searchReq.pageNum = val;
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csOff/getUsedContent`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-title-remark {
    text-indent: 12px;
    background-color: white;
    width: 100%;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
    background-color: #F5F5F5;
}
.ts-model {
  position: absolute; 
  left: 50%; 
  transform: translateX(-50%); 
  top: 40px; 
  background: #fdf6ec;
  width: 250px;
  padding: 20px;
  border-radius: 5px;
  color: #e6a23c;
  z-index: 99999999999;
}
</style>
