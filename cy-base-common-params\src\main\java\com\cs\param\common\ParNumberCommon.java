
package com.cs.param.common;

public class ParNumberCommon {
	private Integer id; // 号段id
	private String phoneSection;// 号段
	private String optCode;// 运营商编码
	private String countryCode;// 国家代码
	private String startSuffix;// 开始后缀
	private String endSuffix;// 结束后缀
	private String brandCode;// 品牌code
	private String netCode;// 所在网络code
	private String provinceCode;// 省号
	private String regionCode;// 区号
	private String hlrAddr;// 所属hlr地址
	private String terminalCode;// 终端类型code
	private String status;// 是否删除状态
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPhoneSection() {
		return phoneSection;
	}

	public void setPhoneSection(String phoneSection) {
		this.phoneSection = phoneSection;
	}

	public String getOptCode() {
		return optCode;
	}

	public void setOptCode(String optCode) {
		this.optCode = optCode;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getStartSuffix() {
		return startSuffix;
	}

	public void setStartSuffix(String startSuffix) {
		this.startSuffix = startSuffix;
	}

	public String getEndSuffix() {
		return endSuffix;
	}

	public void setEndSuffix(String endSuffix) {
		this.endSuffix = endSuffix;
	}

	public String getBrandCode() {
		return brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	public String getNetCode() {
		return netCode;
	}

	public void setNetCode(String netCode) {
		this.netCode = netCode;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getHlrAddr() {
		return hlrAddr;
	}

	public void setHlrAddr(String hlrAddr) {
		this.hlrAddr = hlrAddr;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

}
