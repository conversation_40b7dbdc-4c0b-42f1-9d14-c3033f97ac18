package com.cy.content.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import java.util.List;
@JacksonXmlRootElement(localName = "Response")
public class GetContentByLabelOrGroupRsp {
    @JacksonXmlProperty(localName = "ResultCode")
    private String resultCode;
    @JacksonXmlProperty(localName = "ResultDesc")
    private String resultDesc;
    @JacksonXmlElementWrapper(localName = "ContentList")
    @JacksonXmlProperty(localName = "Content")
    private List<Content> contentList;


    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    public List<Content> getContentList() {
        return contentList;
    }

    public void setContentList(List<Content> contentList) {
        this.contentList = contentList;
    }

    @Override
    public String toString() {
        return "GetContentByLabelOrGroupRsp{" +
                "resultCode='" + resultCode + '\'' +
                ", resultDesc='" + resultDesc + '\'' +
                ", contentList=" + contentList +
                '}';
    }
}
