<template>
  <div>
    <h1 class="user-title">个人用户销户</h1>
    <div class="user-line"></div>
    <div class="user-search2">
      <el-form @submit.native.prevent>
        <el-form-item label="用户号码:"><br>
          <el-input v-model="delReq.phone"  size="small"></el-input>
          <el-button type="primary" @click="del()" size="small" class="blue-btn">提 交</el-button>
        </el-form-item>
        <el-form-item>
          <el-dialog
              width="30%"
              title="销户"
              :visible.sync="delVisible"
              append-to-body
              :close-on-click-modal="false">
            <span style="font-size:16px;">确定销除此用户吗？</span>
            <div slot="footer" class="dialog-footer" style="text-align: right;">
              <el-button @click="delVisible = false" size="small">取 消</el-button>
              <el-button type="primary" @click="delVisible = false;del();" size="small">确 定</el-button>
            </div>
          </el-dialog>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script type="text/ecmascript-6">
export default {
  data() {
    return {
      noticeRadio:'1',
      delVisible:false,
      delReq:{
        phone:''
      }
    };
  },
  mounted(){

  },
  methods: {
    //销户请求
    del(){
      let vm=this;
        if(this.delReq.phone == '' || this.delReq.phone == null){
            this.$message.error('请输入用户号码');
            return false;
        }
        this.$confirm('确定销除此用户吗？?')
            .then(_ => {
                this.$http
                    .post(`${this.proxyUrl}/user/switchAccount/delAccount`,this.delReq,{ emulateJSON: true })
                    .then((res)=>{
                        if (res.data.status == 0) {
                            this.delReq.phone = '';
                            this.$message.success(res.data.resText);
                            //vm.$router.push({path: '/list'})
                        }else{
                            this.$message.error(res.data.resText);
                        }
                    })
            })

    }
  }
};
</script>
<style scoped>
  label{
    font-weight: bold;
    font-size:16px !important;
  }
.fl {
  float: left;
}
.user-title {
     padding: 10px 0px 0px 0px;
}
.user-line {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border-bottom: 1px solid #439ae6;
}
.user-search2 {
  width: 40%;
  margin-top: 3%;
  margin-left: 3%;
}
</style>
