<template>
  <div v-loading="loading">
    <h1 class="user-title">内容类别</h1>
    <div class="user-line"></div>
    <div
      class="app-search"
      style="margin-top: 10px; padding-left: 24px !important"
    >
      <el-form :inline="true" class="demo-form-inline" @submit.native.prevent>
        <el-form-item label="类别名称">
          <el-input
            v-model="request.csGroupName"
            size="small"
            :maxlength="8"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="searchDesc(request)"
            size="small"
            class="app-bnt"
            >查询</el-button
          >
        </el-form-item>
      </el-form>

      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" @click="addVisible = true" size="small"
            >新建类别</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <!-- 类别展示 -->
    <div style="margin-left: 3%; margin-top: 3%">
      <b class="block" style="margin-bottom: 30px" v-show="descData.length"
        >共{{ descData.length }}个类别</b
      >
      <el-tabs tab-position="top" style="height: 200px" v-model="tabName">
        <el-tab-pane :label="`个人彩印(${personalArr.length})`" name="personal">
          <div v-if="personalArr.length">
            <el-form
              v-for="(value, index) in personalArr"
              :inline="true"
              :key="`personal_${index}`"
              class="demo-form-inline"
              style="display: inline-block; margin-right: 3%; margin-bottom: 2%"
            >
              <el-tooltip placement="bottom-end">
                <div slot="content">
                  <a
                    href="javaScript:;"
                    @click="openEdit(value)"
                    style="color: #ffffff"
                    >编辑</a
                  >
                  <a
                    href="javaScript:;"
                    @click="openDel(value)"
                    style="margin-left: 10px; color: #ffffff"
                    >删除</a
                  >
                </div>
                <el-button size="small">{{ value.groupName }}</el-button>
              </el-tooltip>
            </el-form>
          </div>
          <el-empty v-else description="暂无数据"></el-empty>
        </el-tab-pane>
        <el-tab-pane  :label="`名片号(${ cardArr.length })`" name="card">
          <div v-if="cardArr.length">
            <el-form
              v-for="(value, index) in cardArr"
              :inline="true"
              :key="`card_${index}`"
              class="demo-form-inline"
              style="display: inline-block; margin-right: 3%; margin-bottom: 2%"
            >
              <el-tooltip placement="bottom-end">
                <div slot="content">
                  <a
                    href="javaScript:;"
                    @click="openEdit(value)"
                    style="color: #ffffff"
                    >编辑</a
                  >
                  <a
                    href="javaScript:;"
                    @click="openDel(value)"
                    style="margin-left: 10px; color: #ffffff"
                    >删除</a
                  >
                </div>
                <el-button size="small">{{ value.groupName }}</el-button>
              </el-tooltip>
            </el-form>
          </div>
          <el-empty v-else description="暂无数据"></el-empty
        ></el-tab-pane>
      </el-tabs>
    </div>
    <!-- 新建类别 -->
    <el-dialog
      title="新建类别"
      :visible.sync="addVisible"
      :close-on-click-modal="false"
      width="30%"
    >
      <el-form
        @submit.native.prevent
        status-icon
        :rules="rules2"
        :model="request2"
        ref="request2"
      >
        <el-form-item label="类别名称" prop="csGroupName">
          <el-input
            v-model="request2.csGroupName"
            size="small"
            :maxlength="8"
            style="width: 170px"
          ></el-input>
        </el-form-item>
        <el-form-item label="业务类型" prop="serviceType">
          <el-select
            v-model="request2.serviceType"
            placeholder="请选择"
            size="small"
          >
            <el-option label="个人彩印" :value="1"></el-option>
            <el-option label="名片号" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right; margin-top: 20px">
          <el-button
            @click="
              addVisible = false;
              resetForm('request2');
            "
            size="small"
            >取 消</el-button
          >
          <el-button type="primary" @click="add('request2')" size="small"
            >确 定</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 编辑弹窗 -->
    <el-dialog
      width="30%"
      title="编辑"
      :visible.sync="innerEditVisible"
      :close-on-click-modal="false"
    >
      <el-form
        @submit.native.prevent
        status-icon
        :rules="rules2"
        :model="request3"
        ref="request3"
      >
        <el-form-item label="类别名称" prop="csGroupName">
          <el-input
            v-model="request3.csGroupName"
            size="small"
            :maxlength="8"
            style="width: 170px"
          ></el-input>
        </el-form-item>
        <el-form-item label="业务类型" prop="serviceType">
          <el-select
            v-model="request3.serviceType"
            placeholder="请选择"
            disabled
            size="small"
          >
            <el-option label="个人彩印" :value="1"></el-option>
            <el-option label="名片号" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="text-align: right; margin-top: 20px">
          <el-button
            @click="
              innerEditVisible = false;
              resetForm('request3');
            "
            size="small"
            >取 消</el-button
          >
          <el-button type="primary" @click="edit('request3')" size="small"
            >确 定</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 删除弹窗 -->
    <el-dialog
      width="30%"
      title="删除"
      :visible.sync="innerDelVisible"
      :close-on-click-modal="false"
      append-to-body
    >
      <span style="font-size: 20px">确定删除此类别？</span>
      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button @click="innerDelVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="
            innerDelVisible = false;
            del();
          "
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "contentCategory",
  data () {
    let validChinese = (rule, value, callback) => {
      if (value == null || value.trim() == '') {
        callback(new Error('请填写类别名称'))
      }
      if (!/^[\u4e00-\u9fa5]+$/i.test(value)) {
        callback(new Error('必须是中文字符'))
      } else {
        callback()
      }
    };
    return {
      loading: false,
      addVisible: false,
      visible1: false,
      innerEditVisible: false,
      innerDelVisible: false,
      descData: [],
      // 个人彩印类别数组
      personalArr: [],
      // 名片号类别数组
      cardArr: [],
      // 当前默认展示的选项卡名字 personal/card
      tabName: "personal",
      // 查询
      request: {
        csGroupName: "",
        serviceType: -1,// 默认为查询所有
      },
      // 新建
      request2: {
        csGroupName: "",
        serviceType: 1
      },
      //编辑
      request3: {
        csGroupName: "",
        csGroupId: "",
        serviceType: ""
      },
      //删除
      request4: {
        csGroupName: "",
        csGroupId: "",
        serviceType: ""
      },
      rules2: {
        csGroupName: [
          { trigger: 'blur', validator: validChinese }
        ]
      }
    };
  },
  mounted () {
    this.searchDesc(this.request);
  },
  methods: {
    // isChinese(val){
    //     return /^[\u4e00-\u9fa5]+$/i.test(val);
    // },
    openEdit: function (val) {
      this.innerEditVisible = true;
      this.request3.csGroupId = val.groupId;
      this.request3.csGroupName = val.groupName;
      this.request3.serviceType = val.serviceType || "";
      this.request4.csGroupId = val.groupId;
      this.request4.csGroupName = val.groupName;
      this.request4.serviceType = val.serviceType || "";
    },
    openDel: function (val) {
      this.innerDelVisible = true;
      this.request3.csGroupId = val.groupId;
      this.request3.csGroupName = val.groupName;
      this.request3.serviceType = val.serviceType || "";
      this.request4.csGroupId = val.groupId;
      this.request4.csGroupName = val.groupName;
      this.request4.serviceType = val.serviceType || "";
    },
    resetForm (formName) {
      this.$refs[formName].resetFields();
    },
    //查询请求
    searchDesc: function (request) {
      this.loading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, request, {
          emulateJSON: true
        })
        .then((res) => {
          this.descData = res.data;
          this.personalArr = this.descData.filter(item => item.serviceType == 1);
          this.cardArr = this.descData.filter(item => item.serviceType == 2);
          this.loading = false;
          this.tabName = this.request.serviceType == 2 ? "card" : "personal";
        });
    },
    //新建类别
    add: function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$http
            .post(`${this.proxyUrl}/content/csGroup/addCsGroup`, this.request2, {
              emulateJSON: true
            })
            .then(function (res) {
              if (res.data.resStatus == "0") {
                this.$message.success('新建成功');
                this.searchDesc(this.request);
                this.request2.csGroupName = "";
                this.addVisible = false;
                // 数据清空
                this.resetForm(formName);
              } else if (res.data.resStatus == "1") {
                this.$message.error(res.data.resText || '新建失败');
              }
            });
        }
      });

    },
    //编辑
    edit: function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.innerEditVisible = false;
          this.$http
            .post(`${this.proxyUrl}/content/csGroup/modCsGroup`, this.request3, {
              emulateJSON: true
            })
            .then(function (res) {
              if (res.data.resStatus == "0") {
                this.$message.success('编辑成功');
                this.searchDesc(this.request);
              } else if (res.data.resStatus == "1") {
                this.$message.error('编辑失败');
              }
            });
        }
      });
    },
    // isChina:function(varStr){
    //   var reg=/^[\u2E80-\u9FFF]+$/;//Unicode编码中的汉字范围
    //   if(reg.text(varStr)){
    //     return true;
    //   }
    //   return false;
    // },

    //删除
    del: function () {
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/delCsGroup`, this.request4, {
          emulateJSON: true
        })
        .then(function (res) {
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.searchDesc(this.request);
          } else if (res.data.resStatus == "1") {
            this.$message.error(res.data.resText);
          }
        });
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
  margin-top: 3%;
  background-color: blue;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-dropdown-menu {
  width: auto;
}
.el-table {
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border: 1px solid #ecebe9;
}
.desc-content {
  margin-top: 30px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
