package com.cy.audit.model;

import java.io.Serializable;
import java.util.List;

public class AuditTextModel  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private int svMold;//1用户diy彩印 2单条彩印3彩印盒（必填）
	private String svId;//传参识别唯一id（必填）
	private String svNumber;//彩印id
	private int svType;//0：新增1：删除2：修改3:下架
	private String svName;//彩印盒名称
	private String svCard;//彩印内容（必填）
	private String submitUser;//彩印提交用户（必填）
	private String submitTime;//提交时间
	private String submitType;//彩印来源  0：短信  1：WEB 2：SMC 3：WAP 4：手机客户端 5：第三方（必填）
	private int submitCount;//审核提交次数
	private int svStatus;//1：待审批  2：审批不通过 3：审核通过 4：失效（提交新纪录，或彩印被删除）
	private String svStatusName;//审核状态名称
	private String svSssesstime;//审核时间
	private String svAssessor;//审核人
	private String svCause;//驳回原因
	private String svMoldName;//svMold对应名称
	private String groupName;//类别名称
	private String labelName;//标签名称
	private String groupId;//类别id
	private String labelId;//标签id
	private String operatorUser;//操作人
	private String recType;//接收类型
	private String recUser;//接收人
	
	private String revokeCause;
	private String revokeTime;
	
	private String svSerialId;//序号id
	
	private String sensitiveWords;//敏感词
	
	private String svProId;//省份id
	
	private String svProName;//省份名称
	
	private String batchNo;//批次号

    private String reciver;//接收号码

	private int  svRuleStatus;//1：不生成规则；0生成规则
    private String svSftpUrl;

	private int svSubmitType;

	private String qualificationsUrls;

	private String operator;

	/**
	 * 联通审核状态
	 * 1：待审批
	 * 2：审批不通过
	 * 3：审核通过
	 */
	private Integer unicomStatus;

	/**
	 * 联通审核意见
	 */
	private String unicomMessage;

	/**
	 * 电信审核状态
	 * 1：待审批
	 * 2：审批不通过
	 * 3：审核通过
	 */
	private Integer telecomStatus;

	/**
	 * 电信审核意见
	 */
	private String telecomMessage;

	private String notifyUrl;

	private String deliveryType;

	private String qualificationsFiles;

	private Integer serviceType;

	public String getQualificationsFiles() {
		return qualificationsFiles;
	}

	public void setQualificationsFiles(String qualificationsFiles) {
		this.qualificationsFiles = qualificationsFiles;
	}

	public String getDeliveryType() {
		return deliveryType;
	}

	public void setDeliveryType(String deliveryType) {
		this.deliveryType = deliveryType;
	}

	public String getNotifyUrl() {
		return notifyUrl;
	}

	public void setNotifyUrl(String notifyUrl) {
		this.notifyUrl = notifyUrl;
	}

	public String getTelecomMessage() {
		return telecomMessage;
	}

	public void setTelecomMessage(String telecomMessage) {
		this.telecomMessage = telecomMessage;
	}

	public Integer getTelecomStatus() {
		return telecomStatus;
	}

	public void setTelecomStatus(Integer telecomStatus) {
		this.telecomStatus = telecomStatus;
	}

	public Integer getUnicomStatus() {
		return unicomStatus;
	}

	public void setUnicomStatus(Integer unicomStatus) {
		this.unicomStatus = unicomStatus;
	}

	public String getUnicomMessage() {
		return unicomMessage;
	}

	public void setUnicomMessage(String unicomMessage) {
		this.unicomMessage = unicomMessage;
	}


	private String weekFlag;

	private String startTime;

	private String endTime;

	private String contactGroupId;


	public String getContactGroupId() {
		return contactGroupId;
	}

	public void setContactGroupId(String contactGroupId) {
		this.contactGroupId = contactGroupId;
	}


	public String getWeekFlag() {
		return weekFlag;
	}

	public void setWeekFlag(String weekFlag) {
		this.weekFlag = weekFlag;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	private List<String> qualificationsUrlList;

	public List<String> getQualificationsUrlList() {
		return qualificationsUrlList;
	}

	public void setQualificationsUrlList(List<String> qualificationsUrlList) {
		this.qualificationsUrlList = qualificationsUrlList;
	}

	public String getQualificationsUrls() {
		return qualificationsUrls;
	}

	public void setQualificationsUrls(String qualificationsUrls) {
		this.qualificationsUrls = qualificationsUrls;
	}

	public int getSvSubmitType(){
		return svSubmitType;
	}

	public void setSvSubmitType(int svSubmitType){
		this.svSubmitType = svSubmitType;
	}

    public String getSvSftpUrl() {
        return svSftpUrl;
    }

    public void setSvSftpUrl(String svSftpUrl) {
        this.svSftpUrl = svSftpUrl;
	}

	public int getSvRuleStatus() {
		return svRuleStatus;
	}

	public void setSvRuleStatus(int svRuleStatus) {
		this.svRuleStatus = svRuleStatus;
	}

	public String getReciver() {
		return reciver;
	}

	public void setReciver(String reciver) {
		this.reciver = reciver;
	}

	public String getBatchNo() {
	    return batchNo;
	}
	public void setBatchNo(String batchNo) {
	    this.batchNo = batchNo;
	}
	public String getSvProId() {
	    return svProId;
	}
	public void setSvProId(String svProId) {
	    this.svProId = svProId;
	}
	public String getSvProName() {
	    return svProName;
	}
	public void setSvProName(String svProName) {
	    this.svProName = svProName;
	}
	public String getSvSerialId() {
	    return svSerialId;
	}
	public void setSvSerialId(String svSerialId) {
	    this.svSerialId = svSerialId;
	}
	public String getSensitiveWords() {
	    return sensitiveWords;
	}
	public void setSensitiveWords(String sensitiveWords) {
	    this.sensitiveWords = sensitiveWords;
	}
	public String getSvStatusName() {
	    return svStatusName;
	}
	public void setSvStatusName(String svStatusName) {
	    this.svStatusName = svStatusName;
	}
	public String getSvAssessor() {
	    return svAssessor;
	}
	public void setSvAssessor(String svAssessor) {
	    this.svAssessor = svAssessor;
	}
	public String getSubmitType() {
	    return submitType;
	}
	public void setSubmitType(String submitType) {
	    this.submitType = submitType;
	}
	public String getGroupId() {
	    return groupId;
	}
	public void setGroupId(String groupId) {
	    this.groupId = groupId;
	}
	public String getLabelId() {
	    return labelId;
	}
	public void setLabelId(String labelId) {
	    this.labelId = labelId;
	}
	public String getGroupName() {
	    return groupName;
	}
	public void setGroupName(String groupName) {
	    this.groupName = groupName;
	}
	public String getLabelName() {
	    return labelName;
	}
	public void setLabelName(String labelName) {
	    this.labelName = labelName;
	}
	public int getSvType() {
	    return svType;
	}
	public void setSvType(int svType) {
	    this.svType = svType;
	}
	public String getSvName() {
	    return svName;
	}
	public void setSvName(String svName) {
	    this.svName = svName;
	}
	public String getSvNumber() {
	    return svNumber;
	}
	public void setSvNumber(String svNumber) {
	    this.svNumber = svNumber;
	}
	public String getSvMoldName() {
		return svMoldName;
	}
	public void setSvMoldName(String svMoldName) {
		this.svMoldName = svMoldName;
	}
	public String getRevokeCause() {
		return revokeCause;
	}
	public void setRevokeCause(String revokeCause) {
		this.revokeCause = revokeCause;
	}
	public String getRevokeTime() {
		return revokeTime;
	}
	public void setRevokeTime(String revokeTime) {
		this.revokeTime = revokeTime;
	}
	public int getSvStatus() {
		return svStatus;
	}
	public void setSvStatus(int svStatus) {
		this.svStatus = svStatus;
	}
	public String getSvSssesstime() {
		return svSssesstime;
	}
	public void setSvSssesstime(String svSssesstime) {
		this.svSssesstime = svSssesstime;
	}
	public String getSvCause() {
		return svCause;
	}
	public void setSvCause(String svCause) {
		this.svCause = svCause;
	}
	public int getSvMold() {
		return svMold;
	}
	public void setSvMold(int svMold) {
		this.svMold = svMold;
	}
	public String getSvId() {
		return svId;
	}
	public void setSvId(String svId) {
		this.svId = svId;
	}
	public String getSvCard() {
		return svCard;
	}
	public void setSvCard(String svCard) {
		this.svCard = svCard;
	}
	public String getSubmitUser() {
		return submitUser;
	}
	public void setSubmitUser(String submitUser) {
		this.submitUser = submitUser;
	}
	public String getSubmitTime() {
		return submitTime;
	}
	public void setSubmitTime(String submitTime) {
		this.submitTime = submitTime;
	}
	public int getSubmitCount() {
		return submitCount;
	}
	public void setSubmitCount(int submitCount) {
		this.submitCount = submitCount;
	}
	public String getOperatorUser() {
	    return operatorUser;
	}
	public void setOperatorUser(String operatorUser) {
	    this.operatorUser = operatorUser;
	}
	public String getRecType() {
	    return recType;
	}
	public void setRecType(String recType) {
	    this.recType = recType;
	}
	public String getRecUser() {
	    return recUser;
	}
	public void setRecUser(String recUser) {
	    this.recUser = recUser;
	}

	@Override
	public String toString() {
		return "AuditTextModel{" +
				"svMold=" + svMold +
				", svId='" + svId + '\'' +
				", svNumber='" + svNumber + '\'' +
				", svType=" + svType +
				", svName='" + svName + '\'' +
				", svCard='" + svCard + '\'' +
				", submitUser='" + submitUser + '\'' +
				", submitTime='" + submitTime + '\'' +
				", submitType='" + submitType + '\'' +
				", submitCount=" + submitCount +
				", svStatus=" + svStatus +
				", svStatusName='" + svStatusName + '\'' +
				", svSssesstime='" + svSssesstime + '\'' +
				", svAssessor='" + svAssessor + '\'' +
				", svCause='" + svCause + '\'' +
				", svMoldName='" + svMoldName + '\'' +
				", groupName='" + groupName + '\'' +
				", labelName='" + labelName + '\'' +
				", groupId='" + groupId + '\'' +
				", labelId='" + labelId + '\'' +
				", operatorUser='" + operatorUser + '\'' +
				", recType='" + recType + '\'' +
				", recUser='" + recUser + '\'' +
				", revokeCause='" + revokeCause + '\'' +
				", revokeTime='" + revokeTime + '\'' +
				", svSerialId='" + svSerialId + '\'' +
				", sensitiveWords='" + sensitiveWords + '\'' +
				", svProId='" + svProId + '\'' +
				", svProName='" + svProName + '\'' +
				", batchNo='" + batchNo + '\'' +
				", reciver='" + reciver + '\'' +
				", svRuleStatus=" + svRuleStatus +
				", svSftpUrl='" + svSftpUrl + '\'' +
				", svSubmitType=" + svSubmitType +
				", qualificationsUrls='" + qualificationsUrls + '\'' +
				", operator='" + operator + '\'' +
				", unicomStatus=" + unicomStatus +
				", unicomMessage='" + unicomMessage + '\'' +
				", telecomStatus=" + telecomStatus +
				", telecomMessage='" + telecomMessage + '\'' +
				", notifyUrl='" + notifyUrl + '\'' +
				", deliveryType='" + deliveryType + '\'' +
				", weekFlag='" + weekFlag + '\'' +
				", startTime='" + startTime + '\'' +
				", endTime='" + endTime + '\'' +
				", contactGroupId='" + contactGroupId + '\'' +
				", qualificationsUrlList=" + qualificationsUrlList +
				'}';
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}
}
