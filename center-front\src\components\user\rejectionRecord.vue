<template>
    <div>
        <h1 class="user-title">拒接操作记录</h1>
        <div class="user-line"></div>

        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="操作名称">
                    <el-input  v-model="searchForm.operatorName" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="操作手机">
                    <el-input  v-model="searchForm.userNumber" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="getModulelList(searchForm)" size="small">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
                  class="app-tab">
            <el-table-column type="index" label="序号" width="80"/>
            <el-table-column prop="operatorNumber" label="操作ID" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="operatorName" label="操作名字" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="userNumber" label="操作手机号" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="grcy" label="个人彩印" width="80" :show-overflow-tooltip="true"/>
            <el-table-column prop="xmcy" label="新媒彩印" width="80" :show-overflow-tooltip="true"/>
            <el-table-column prop="txcy" label="提醒彩印" width="80" :show-overflow-tooltip="true"/>
            <el-table-column prop="qycy" label="企业彩印" width="80" :show-overflow-tooltip="true"/>
            <el-table-column prop="status" label="操作结果" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="operationDate" label="操作时间" width="180" :show-overflow-tooltip="true"/>
        </el-table>

        <div>
            <div class="block app-pageganit">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total" style="text-align: right;">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "rejectionRecord",
        data() {
            return {
                resultList:[
                    {
                        key:'1',
                        value:'成功'
                    },
                    {
                        key: '0',
                        value: '失败'
                    }
                ],
                searchForm: {
                    operatorName: '',
                    userNumber: '',
                    pageSize: 10,
                    pageNum: 1,// 查询的页码

                },
                tableData: [],
                currentPage: 1,
                total: 0
            }
        },
        mounted(){
//            this.slideData(this.proxyUrl);
            this.getModulelList(this.searchForm);
        },
        methods: {
            //查询请求
            getModulelList: function (searchForm) {
                this.$http.post(`${this.proxyUrl}/user/customerRule/rejectionRecord`, JSON.stringify(searchForm))
                    .then(function (res) {
                        for(let i= 0; i < res.data.datas.length;i++){
                            if(1 == res.data.datas[i]['status']){
                                res.data.datas[i]['status'] = '成功'
                            }else {
                                res.data.datas[i]['status'] = '失败'
                            }
                        }
                        this.tableData = res.data.datas;
                        this.currentPage=res.data.pageNum;
                        this.total=res.data.pageTotal;
                    })
            },
            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.getModulelList(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.getModulelList(this.searchForm);
            },
        }
    }
</script>

<style scoped>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
</style>
