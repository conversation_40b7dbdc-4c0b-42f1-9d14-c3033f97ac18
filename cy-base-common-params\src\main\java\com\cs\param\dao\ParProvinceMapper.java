package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParProvinceCommon;
import com.cs.param.model.ParProvinceModel;

@Repository
public interface ParProvinceMapper {

	int insertParProvince(ParProvinceCommon common) throws SQLException;

	int updateParProvinceByPK(ParProvinceCommon common) throws SQLException;

	int deleteParProvinceByCode(ParProvinceCommon common) throws SQLException;

	List<ParProvinceModel> getParProvinceListByCond(ParProvinceCommon common) throws SQLException;

	Integer queryCountByCode(ParProvinceCommon common) throws SQLException;

	List<ParProvinceModel> getParProvinceListByCodes(String[] codes) throws SQLException;

	void updatePartitionCode(String partitionCode) throws SQLException;

}
