package com.cy.jwt.JwtUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.cy.common.LogUtil;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.SignatureException;

@Component
public class JWTUtils {

	private static final Logger logger = LoggerFactory.getLogger(JWTUtils.class);
	
	
	public static final String JWT_KEY_USER_INFO = "sysUser";
	public static final String JWT_KEY_SUBJECT = "cy";

	// 过期时间
	private static int expireTime = 1000 * 60;

	public static String generateToken(String sysUser,String key,int expiration) {

		String compactJws = Jwts.builder().setSubject(JWT_KEY_SUBJECT).claim(JWT_KEY_USER_INFO, sysUser)
				.setExpiration(new Date(System.currentTimeMillis() + (expireTime*expiration)))
				.signWith(SignatureAlgorithm.HS512, key).compact();

		return compactJws;
	}

	public static Jws<Claims> parseToken(String token,String key) {
		try {
			Jws<Claims> parseClaimsJws = Jwts.parser().setSigningKey(key).parseClaimsJws(token);
			return parseClaimsJws;
		} catch (SignatureException e) {
			LogUtil.info(logger, LogUtil.SVC, token, "token is not correct");
			return null;
		}catch(ExpiredJwtException e){

		    LogUtil.info(logger, LogUtil.SVC, token, "token is expired" + e.getMessage());
		    return null;
		}
	}

	public static void main(String[] args){
		Map<String,String> map = new HashMap<>();
		map.put(null,"1");
		System.out.println(map);

		String key = "aabb";
		System.out.println(Integer.toBinaryString(key.hashCode()));

		int h = key.hashCode();
		System.out.println(Integer.toBinaryString((h >>> 16)));
		System.out.println(Integer.toBinaryString((h) ^ (h >>> 16)));




	}
	
}
