import {postDownloadHeader,postHeader} from './../../../servers/httpServer.js';
//订购关系数据查询
export async function orderRelationQuery(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//导出差异清单
export async function orderDiffExport(url,params) {
    let result=await postDownloadHeader(url,params);
    return result.data;
}
//推送规则查询
export async function pushRuleQuery(url,params){
    let result=await postHeader(url,params);
    return result.data;
}