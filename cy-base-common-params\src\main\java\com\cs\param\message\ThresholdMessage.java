package com.cs.param.message;

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: Threshold.proto

public final class ThresholdMessage {
	private ThresholdMessage() {
	}

	public static void registerAllExtensions(com.google.protobuf.ExtensionRegistry registry) {
	}

	public interface ThresholdEntityOrBuilder extends com.google.protobuf.MessageOrBuilder {

		// required string provinceCode = 1;
		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		boolean hasProvinceCode();

		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		java.lang.String getProvinceCode();

		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		com.google.protobuf.ByteString getProvinceCodeBytes();

		// optional string perPushTime = 2;
		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		boolean hasPerPushTime();

		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		java.lang.String getPerPushTime();

		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		com.google.protobuf.ByteString getPerPushTimeBytes();

		// optional string perPushMaxNum = 3;
		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		boolean hasPerPushMaxNum();

		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		java.lang.String getPerPushMaxNum();

		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		com.google.protobuf.ByteString getPerPushMaxNumBytes();

		// optional string perAllPushMaxNum = 4;
		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		boolean hasPerAllPushMaxNum();

		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		java.lang.String getPerAllPushMaxNum();

		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		com.google.protobuf.ByteString getPerAllPushMaxNumBytes();

		// optional string perDelayTime = 5;
		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		boolean hasPerDelayTime();

		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		java.lang.String getPerDelayTime();

		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		com.google.protobuf.ByteString getPerDelayTimeBytes();

		// optional string bussPushTime = 6;
		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		boolean hasBussPushTime();

		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		java.lang.String getBussPushTime();

		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		com.google.protobuf.ByteString getBussPushTimeBytes();

		// optional string bussPushMaxNum = 7;
		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		boolean hasBussPushMaxNum();

		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		java.lang.String getBussPushMaxNum();

		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		com.google.protobuf.ByteString getBussPushMaxNumBytes();

		// optional string bussAllPushMaxNum = 8;
		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		boolean hasBussAllPushMaxNum();

		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		java.lang.String getBussAllPushMaxNum();

		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		com.google.protobuf.ByteString getBussAllPushMaxNumBytes();

		// optional string bussDelayTime = 9;
		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		boolean hasBussDelayTime();

		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		java.lang.String getBussDelayTime();

		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		com.google.protobuf.ByteString getBussDelayTimeBytes();

		// optional string mediaPushTime = 10;
		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		boolean hasMediaPushTime();

		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		java.lang.String getMediaPushTime();

		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		com.google.protobuf.ByteString getMediaPushTimeBytes();

		// optional string mediaPushMaxNum = 11;
		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		boolean hasMediaPushMaxNum();

		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		java.lang.String getMediaPushMaxNum();

		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		com.google.protobuf.ByteString getMediaPushMaxNumBytes();

		// optional string mediaAllPushMaxNum = 12;
		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		boolean hasMediaAllPushMaxNum();

		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		java.lang.String getMediaAllPushMaxNum();

		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		com.google.protobuf.ByteString getMediaAllPushMaxNumBytes();

		// optional string mediaDelayTime = 13;
		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		boolean hasMediaDelayTime();

		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		java.lang.String getMediaDelayTime();

		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		com.google.protobuf.ByteString getMediaDelayTimeBytes();
	}

	/**
	 * Protobuf type {@code ThresholdEntity}
	 */
	public static final class ThresholdEntity extends com.google.protobuf.GeneratedMessage
			implements ThresholdEntityOrBuilder {
		// Use ThresholdEntity.newBuilder() to construct.
		private ThresholdEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
			super(builder);
			this.unknownFields = builder.getUnknownFields();
		}

		private ThresholdEntity(boolean noInit) {
			this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance();
		}

		private static final ThresholdEntity defaultInstance;

		public static ThresholdEntity getDefaultInstance() {
			return defaultInstance;
		}

		public ThresholdEntity getDefaultInstanceForType() {
			return defaultInstance;
		}

		private final com.google.protobuf.UnknownFieldSet unknownFields;

		@java.lang.Override
		public final com.google.protobuf.UnknownFieldSet getUnknownFields() {
			return this.unknownFields;
		}

		private ThresholdEntity(com.google.protobuf.CodedInputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			initFields();
			int mutable_bitField0_ = 0;
			com.google.protobuf.UnknownFieldSet.Builder unknownFields = com.google.protobuf.UnknownFieldSet
					.newBuilder();
			try {
				boolean done = false;
				while (!done) {
					int tag = input.readTag();
					switch (tag) {
					case 0:
						done = true;
						break;
					default: {
						if (!parseUnknownField(input, unknownFields, extensionRegistry, tag)) {
							done = true;
						}
						break;
					}
					case 10: {
						bitField0_ |= 0x00000001;
						provinceCode_ = input.readBytes();
						break;
					}
					case 18: {
						bitField0_ |= 0x00000002;
						perPushTime_ = input.readBytes();
						break;
					}
					case 26: {
						bitField0_ |= 0x00000004;
						perPushMaxNum_ = input.readBytes();
						break;
					}
					case 34: {
						bitField0_ |= 0x00000008;
						perAllPushMaxNum_ = input.readBytes();
						break;
					}
					case 42: {
						bitField0_ |= 0x00000010;
						perDelayTime_ = input.readBytes();
						break;
					}
					case 50: {
						bitField0_ |= 0x00000020;
						bussPushTime_ = input.readBytes();
						break;
					}
					case 58: {
						bitField0_ |= 0x00000040;
						bussPushMaxNum_ = input.readBytes();
						break;
					}
					case 66: {
						bitField0_ |= 0x00000080;
						bussAllPushMaxNum_ = input.readBytes();
						break;
					}
					case 74: {
						bitField0_ |= 0x00000100;
						bussDelayTime_ = input.readBytes();
						break;
					}
					case 82: {
						bitField0_ |= 0x00000200;
						mediaPushTime_ = input.readBytes();
						break;
					}
					case 90: {
						bitField0_ |= 0x00000400;
						mediaPushMaxNum_ = input.readBytes();
						break;
					}
					case 98: {
						bitField0_ |= 0x00000800;
						mediaAllPushMaxNum_ = input.readBytes();
						break;
					}
					case 106: {
						bitField0_ |= 0x00001000;
						mediaDelayTime_ = input.readBytes();
						break;
					}
					}
				}
			} catch (com.google.protobuf.InvalidProtocolBufferException e) {
				throw e.setUnfinishedMessage(this);
			} catch (java.io.IOException e) {
				throw new com.google.protobuf.InvalidProtocolBufferException(e.getMessage()).setUnfinishedMessage(this);
			} finally {
				this.unknownFields = unknownFields.build();
				makeExtensionsImmutable();
			}
		}

		public static final com.google.protobuf.Descriptors.Descriptor getDescriptor() {
			return ThresholdMessage.internal_static_ThresholdEntity_descriptor;
		}

		protected com.google.protobuf.GeneratedMessage.FieldAccessorTable internalGetFieldAccessorTable() {
			return ThresholdMessage.internal_static_ThresholdEntity_fieldAccessorTable.ensureFieldAccessorsInitialized(
					ThresholdMessage.ThresholdEntity.class, ThresholdMessage.ThresholdEntity.Builder.class);
		}

		public static com.google.protobuf.Parser<ThresholdEntity> PARSER = new com.google.protobuf.AbstractParser<ThresholdEntity>() {
			public ThresholdEntity parsePartialFrom(com.google.protobuf.CodedInputStream input,
					com.google.protobuf.ExtensionRegistryLite extensionRegistry)
					throws com.google.protobuf.InvalidProtocolBufferException {
				return new ThresholdEntity(input, extensionRegistry);
			}
		};

		@java.lang.Override
		public com.google.protobuf.Parser<ThresholdEntity> getParserForType() {
			return PARSER;
		}

		private int bitField0_;
		// required string provinceCode = 1;
		public static final int PROVINCECODE_FIELD_NUMBER = 1;
		private java.lang.Object provinceCode_;

		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		public boolean hasProvinceCode() {
			return ((bitField0_ & 0x00000001) == 0x00000001);
		}

		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		public java.lang.String getProvinceCode() {
			java.lang.Object ref = provinceCode_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					provinceCode_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>required string provinceCode = 1;</code>
		 */
		public com.google.protobuf.ByteString getProvinceCodeBytes() {
			java.lang.Object ref = provinceCode_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				provinceCode_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string perPushTime = 2;
		public static final int PERPUSHTIME_FIELD_NUMBER = 2;
		private java.lang.Object perPushTime_;

		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		public boolean hasPerPushTime() {
			return ((bitField0_ & 0x00000002) == 0x00000002);
		}

		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		public java.lang.String getPerPushTime() {
			java.lang.Object ref = perPushTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					perPushTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string perPushTime = 2;</code>
		 */
		public com.google.protobuf.ByteString getPerPushTimeBytes() {
			java.lang.Object ref = perPushTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				perPushTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string perPushMaxNum = 3;
		public static final int PERPUSHMAXNUM_FIELD_NUMBER = 3;
		private java.lang.Object perPushMaxNum_;

		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		public boolean hasPerPushMaxNum() {
			return ((bitField0_ & 0x00000004) == 0x00000004);
		}

		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		public java.lang.String getPerPushMaxNum() {
			java.lang.Object ref = perPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					perPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string perPushMaxNum = 3;</code>
		 */
		public com.google.protobuf.ByteString getPerPushMaxNumBytes() {
			java.lang.Object ref = perPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				perPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string perAllPushMaxNum = 4;
		public static final int PERALLPUSHMAXNUM_FIELD_NUMBER = 4;
		private java.lang.Object perAllPushMaxNum_;

		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		public boolean hasPerAllPushMaxNum() {
			return ((bitField0_ & 0x00000008) == 0x00000008);
		}

		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		public java.lang.String getPerAllPushMaxNum() {
			java.lang.Object ref = perAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					perAllPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string perAllPushMaxNum = 4;</code>
		 */
		public com.google.protobuf.ByteString getPerAllPushMaxNumBytes() {
			java.lang.Object ref = perAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				perAllPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string perDelayTime = 5;
		public static final int PERDELAYTIME_FIELD_NUMBER = 5;
		private java.lang.Object perDelayTime_;

		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		public boolean hasPerDelayTime() {
			return ((bitField0_ & 0x00000010) == 0x00000010);
		}

		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		public java.lang.String getPerDelayTime() {
			java.lang.Object ref = perDelayTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					perDelayTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string perDelayTime = 5;</code>
		 */
		public com.google.protobuf.ByteString getPerDelayTimeBytes() {
			java.lang.Object ref = perDelayTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				perDelayTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string bussPushTime = 6;
		public static final int BUSSPUSHTIME_FIELD_NUMBER = 6;
		private java.lang.Object bussPushTime_;

		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		public boolean hasBussPushTime() {
			return ((bitField0_ & 0x00000020) == 0x00000020);
		}

		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		public java.lang.String getBussPushTime() {
			java.lang.Object ref = bussPushTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					bussPushTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string bussPushTime = 6;</code>
		 */
		public com.google.protobuf.ByteString getBussPushTimeBytes() {
			java.lang.Object ref = bussPushTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				bussPushTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string bussPushMaxNum = 7;
		public static final int BUSSPUSHMAXNUM_FIELD_NUMBER = 7;
		private java.lang.Object bussPushMaxNum_;

		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		public boolean hasBussPushMaxNum() {
			return ((bitField0_ & 0x00000040) == 0x00000040);
		}

		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		public java.lang.String getBussPushMaxNum() {
			java.lang.Object ref = bussPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					bussPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string bussPushMaxNum = 7;</code>
		 */
		public com.google.protobuf.ByteString getBussPushMaxNumBytes() {
			java.lang.Object ref = bussPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				bussPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string bussAllPushMaxNum = 8;
		public static final int BUSSALLPUSHMAXNUM_FIELD_NUMBER = 8;
		private java.lang.Object bussAllPushMaxNum_;

		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		public boolean hasBussAllPushMaxNum() {
			return ((bitField0_ & 0x00000080) == 0x00000080);
		}

		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		public java.lang.String getBussAllPushMaxNum() {
			java.lang.Object ref = bussAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					bussAllPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string bussAllPushMaxNum = 8;</code>
		 */
		public com.google.protobuf.ByteString getBussAllPushMaxNumBytes() {
			java.lang.Object ref = bussAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				bussAllPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string bussDelayTime = 9;
		public static final int BUSSDELAYTIME_FIELD_NUMBER = 9;
		private java.lang.Object bussDelayTime_;

		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		public boolean hasBussDelayTime() {
			return ((bitField0_ & 0x00000100) == 0x00000100);
		}

		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		public java.lang.String getBussDelayTime() {
			java.lang.Object ref = bussDelayTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					bussDelayTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string bussDelayTime = 9;</code>
		 */
		public com.google.protobuf.ByteString getBussDelayTimeBytes() {
			java.lang.Object ref = bussDelayTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				bussDelayTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string mediaPushTime = 10;
		public static final int MEDIAPUSHTIME_FIELD_NUMBER = 10;
		private java.lang.Object mediaPushTime_;

		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		public boolean hasMediaPushTime() {
			return ((bitField0_ & 0x00000200) == 0x00000200);
		}

		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		public java.lang.String getMediaPushTime() {
			java.lang.Object ref = mediaPushTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					mediaPushTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string mediaPushTime = 10;</code>
		 */
		public com.google.protobuf.ByteString getMediaPushTimeBytes() {
			java.lang.Object ref = mediaPushTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				mediaPushTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string mediaPushMaxNum = 11;
		public static final int MEDIAPUSHMAXNUM_FIELD_NUMBER = 11;
		private java.lang.Object mediaPushMaxNum_;

		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		public boolean hasMediaPushMaxNum() {
			return ((bitField0_ & 0x00000400) == 0x00000400);
		}

		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		public java.lang.String getMediaPushMaxNum() {
			java.lang.Object ref = mediaPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					mediaPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string mediaPushMaxNum = 11;</code>
		 */
		public com.google.protobuf.ByteString getMediaPushMaxNumBytes() {
			java.lang.Object ref = mediaPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				mediaPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string mediaAllPushMaxNum = 12;
		public static final int MEDIAALLPUSHMAXNUM_FIELD_NUMBER = 12;
		private java.lang.Object mediaAllPushMaxNum_;

		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		public boolean hasMediaAllPushMaxNum() {
			return ((bitField0_ & 0x00000800) == 0x00000800);
		}

		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		public java.lang.String getMediaAllPushMaxNum() {
			java.lang.Object ref = mediaAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					mediaAllPushMaxNum_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string mediaAllPushMaxNum = 12;</code>
		 */
		public com.google.protobuf.ByteString getMediaAllPushMaxNumBytes() {
			java.lang.Object ref = mediaAllPushMaxNum_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				mediaAllPushMaxNum_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		// optional string mediaDelayTime = 13;
		public static final int MEDIADELAYTIME_FIELD_NUMBER = 13;
		private java.lang.Object mediaDelayTime_;

		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		public boolean hasMediaDelayTime() {
			return ((bitField0_ & 0x00001000) == 0x00001000);
		}

		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		public java.lang.String getMediaDelayTime() {
			java.lang.Object ref = mediaDelayTime_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					mediaDelayTime_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>optional string mediaDelayTime = 13;</code>
		 */
		public com.google.protobuf.ByteString getMediaDelayTimeBytes() {
			java.lang.Object ref = mediaDelayTime_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				mediaDelayTime_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		private void initFields() {
			provinceCode_ = "";
			perPushTime_ = "";
			perPushMaxNum_ = "";
			perAllPushMaxNum_ = "";
			perDelayTime_ = "";
			bussPushTime_ = "";
			bussPushMaxNum_ = "";
			bussAllPushMaxNum_ = "";
			bussDelayTime_ = "";
			mediaPushTime_ = "";
			mediaPushMaxNum_ = "";
			mediaAllPushMaxNum_ = "";
			mediaDelayTime_ = "";
		}

		private byte memoizedIsInitialized = -1;

		public final boolean isInitialized() {
			byte isInitialized = memoizedIsInitialized;
			if (isInitialized != -1)
				return isInitialized == 1;

			if (!hasProvinceCode()) {
				memoizedIsInitialized = 0;
				return false;
			}
			memoizedIsInitialized = 1;
			return true;
		}

		public void writeTo(com.google.protobuf.CodedOutputStream output) throws java.io.IOException {
			getSerializedSize();
			if (((bitField0_ & 0x00000001) == 0x00000001)) {
				output.writeBytes(1, getProvinceCodeBytes());
			}
			if (((bitField0_ & 0x00000002) == 0x00000002)) {
				output.writeBytes(2, getPerPushTimeBytes());
			}
			if (((bitField0_ & 0x00000004) == 0x00000004)) {
				output.writeBytes(3, getPerPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000008) == 0x00000008)) {
				output.writeBytes(4, getPerAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000010) == 0x00000010)) {
				output.writeBytes(5, getPerDelayTimeBytes());
			}
			if (((bitField0_ & 0x00000020) == 0x00000020)) {
				output.writeBytes(6, getBussPushTimeBytes());
			}
			if (((bitField0_ & 0x00000040) == 0x00000040)) {
				output.writeBytes(7, getBussPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000080) == 0x00000080)) {
				output.writeBytes(8, getBussAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000100) == 0x00000100)) {
				output.writeBytes(9, getBussDelayTimeBytes());
			}
			if (((bitField0_ & 0x00000200) == 0x00000200)) {
				output.writeBytes(10, getMediaPushTimeBytes());
			}
			if (((bitField0_ & 0x00000400) == 0x00000400)) {
				output.writeBytes(11, getMediaPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000800) == 0x00000800)) {
				output.writeBytes(12, getMediaAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00001000) == 0x00001000)) {
				output.writeBytes(13, getMediaDelayTimeBytes());
			}
			getUnknownFields().writeTo(output);
		}

		private int memoizedSerializedSize = -1;

		public int getSerializedSize() {
			int size = memoizedSerializedSize;
			if (size != -1)
				return size;

			size = 0;
			if (((bitField0_ & 0x00000001) == 0x00000001)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(1, getProvinceCodeBytes());
			}
			if (((bitField0_ & 0x00000002) == 0x00000002)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(2, getPerPushTimeBytes());
			}
			if (((bitField0_ & 0x00000004) == 0x00000004)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(3, getPerPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000008) == 0x00000008)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(4, getPerAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000010) == 0x00000010)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(5, getPerDelayTimeBytes());
			}
			if (((bitField0_ & 0x00000020) == 0x00000020)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(6, getBussPushTimeBytes());
			}
			if (((bitField0_ & 0x00000040) == 0x00000040)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(7, getBussPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000080) == 0x00000080)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(8, getBussAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000100) == 0x00000100)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(9, getBussDelayTimeBytes());
			}
			if (((bitField0_ & 0x00000200) == 0x00000200)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(10, getMediaPushTimeBytes());
			}
			if (((bitField0_ & 0x00000400) == 0x00000400)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(11, getMediaPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00000800) == 0x00000800)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(12, getMediaAllPushMaxNumBytes());
			}
			if (((bitField0_ & 0x00001000) == 0x00001000)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(13, getMediaDelayTimeBytes());
			}
			size += getUnknownFields().getSerializedSize();
			memoizedSerializedSize = size;
			return size;
		}

		private static final long serialVersionUID = 0L;

		@java.lang.Override
		protected java.lang.Object writeReplace() throws java.io.ObjectStreamException {
			return super.writeReplace();
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(com.google.protobuf.ByteString data)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(com.google.protobuf.ByteString data,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data, extensionRegistry);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(byte[] data)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(byte[] data,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data, extensionRegistry);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(java.io.InputStream input) throws java.io.IOException {
			return PARSER.parseFrom(input);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(java.io.InputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseFrom(input, extensionRegistry);
		}

		public static ThresholdMessage.ThresholdEntity parseDelimitedFrom(java.io.InputStream input)
				throws java.io.IOException {
			return PARSER.parseDelimitedFrom(input);
		}

		public static ThresholdMessage.ThresholdEntity parseDelimitedFrom(java.io.InputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseDelimitedFrom(input, extensionRegistry);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(com.google.protobuf.CodedInputStream input)
				throws java.io.IOException {
			return PARSER.parseFrom(input);
		}

		public static ThresholdMessage.ThresholdEntity parseFrom(com.google.protobuf.CodedInputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseFrom(input, extensionRegistry);
		}

		public static Builder newBuilder() {
			return Builder.create();
		}

		public Builder newBuilderForType() {
			return newBuilder();
		}

		public static Builder newBuilder(ThresholdMessage.ThresholdEntity prototype) {
			return newBuilder().mergeFrom(prototype);
		}

		public Builder toBuilder() {
			return newBuilder(this);
		}

		@java.lang.Override
		protected Builder newBuilderForType(com.google.protobuf.GeneratedMessage.BuilderParent parent) {
			Builder builder = new Builder(parent);
			return builder;
		}

		/**
		 * Protobuf type {@code ThresholdEntity}
		 */
		public static final class Builder extends com.google.protobuf.GeneratedMessage.Builder<Builder>
				implements ThresholdMessage.ThresholdEntityOrBuilder {
			public static final com.google.protobuf.Descriptors.Descriptor getDescriptor() {
				return ThresholdMessage.internal_static_ThresholdEntity_descriptor;
			}

			protected com.google.protobuf.GeneratedMessage.FieldAccessorTable internalGetFieldAccessorTable() {
				return ThresholdMessage.internal_static_ThresholdEntity_fieldAccessorTable
						.ensureFieldAccessorsInitialized(ThresholdMessage.ThresholdEntity.class,
								ThresholdMessage.ThresholdEntity.Builder.class);
			}

			// Construct using ThresholdMessage.ThresholdEntity.newBuilder()
			private Builder() {
				maybeForceBuilderInitialization();
			}

			private Builder(com.google.protobuf.GeneratedMessage.BuilderParent parent) {
				super(parent);
				maybeForceBuilderInitialization();
			}

			private void maybeForceBuilderInitialization() {
				if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
				}
			}

			private static Builder create() {
				return new Builder();
			}

			public Builder clear() {
				super.clear();
				provinceCode_ = "";
				bitField0_ = (bitField0_ & ~0x00000001);
				perPushTime_ = "";
				bitField0_ = (bitField0_ & ~0x00000002);
				perPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000004);
				perAllPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000008);
				perDelayTime_ = "";
				bitField0_ = (bitField0_ & ~0x00000010);
				bussPushTime_ = "";
				bitField0_ = (bitField0_ & ~0x00000020);
				bussPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000040);
				bussAllPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000080);
				bussDelayTime_ = "";
				bitField0_ = (bitField0_ & ~0x00000100);
				mediaPushTime_ = "";
				bitField0_ = (bitField0_ & ~0x00000200);
				mediaPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000400);
				mediaAllPushMaxNum_ = "";
				bitField0_ = (bitField0_ & ~0x00000800);
				mediaDelayTime_ = "";
				bitField0_ = (bitField0_ & ~0x00001000);
				return this;
			}

			public Builder clone() {
				return create().mergeFrom(buildPartial());
			}

			public com.google.protobuf.Descriptors.Descriptor getDescriptorForType() {
				return ThresholdMessage.internal_static_ThresholdEntity_descriptor;
			}

			public ThresholdMessage.ThresholdEntity getDefaultInstanceForType() {
				return ThresholdMessage.ThresholdEntity.getDefaultInstance();
			}

			public ThresholdMessage.ThresholdEntity build() {
				ThresholdMessage.ThresholdEntity result = buildPartial();
				if (!result.isInitialized()) {
					throw newUninitializedMessageException(result);
				}
				return result;
			}

			public ThresholdMessage.ThresholdEntity buildPartial() {
				ThresholdMessage.ThresholdEntity result = new ThresholdMessage.ThresholdEntity(this);
				int from_bitField0_ = bitField0_;
				int to_bitField0_ = 0;
				if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
					to_bitField0_ |= 0x00000001;
				}
				result.provinceCode_ = provinceCode_;
				if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
					to_bitField0_ |= 0x00000002;
				}
				result.perPushTime_ = perPushTime_;
				if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
					to_bitField0_ |= 0x00000004;
				}
				result.perPushMaxNum_ = perPushMaxNum_;
				if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
					to_bitField0_ |= 0x00000008;
				}
				result.perAllPushMaxNum_ = perAllPushMaxNum_;
				if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
					to_bitField0_ |= 0x00000010;
				}
				result.perDelayTime_ = perDelayTime_;
				if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
					to_bitField0_ |= 0x00000020;
				}
				result.bussPushTime_ = bussPushTime_;
				if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
					to_bitField0_ |= 0x00000040;
				}
				result.bussPushMaxNum_ = bussPushMaxNum_;
				if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
					to_bitField0_ |= 0x00000080;
				}
				result.bussAllPushMaxNum_ = bussAllPushMaxNum_;
				if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
					to_bitField0_ |= 0x00000100;
				}
				result.bussDelayTime_ = bussDelayTime_;
				if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
					to_bitField0_ |= 0x00000200;
				}
				result.mediaPushTime_ = mediaPushTime_;
				if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
					to_bitField0_ |= 0x00000400;
				}
				result.mediaPushMaxNum_ = mediaPushMaxNum_;
				if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
					to_bitField0_ |= 0x00000800;
				}
				result.mediaAllPushMaxNum_ = mediaAllPushMaxNum_;
				if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
					to_bitField0_ |= 0x00001000;
				}
				result.mediaDelayTime_ = mediaDelayTime_;
				result.bitField0_ = to_bitField0_;
				onBuilt();
				return result;
			}

			public Builder mergeFrom(com.google.protobuf.Message other) {
				if (other instanceof ThresholdMessage.ThresholdEntity) {
					return mergeFrom((ThresholdMessage.ThresholdEntity) other);
				} else {
					super.mergeFrom(other);
					return this;
				}
			}

			public Builder mergeFrom(ThresholdMessage.ThresholdEntity other) {
				if (other == ThresholdMessage.ThresholdEntity.getDefaultInstance())
					return this;
				if (other.hasProvinceCode()) {
					bitField0_ |= 0x00000001;
					provinceCode_ = other.provinceCode_;
					onChanged();
				}
				if (other.hasPerPushTime()) {
					bitField0_ |= 0x00000002;
					perPushTime_ = other.perPushTime_;
					onChanged();
				}
				if (other.hasPerPushMaxNum()) {
					bitField0_ |= 0x00000004;
					perPushMaxNum_ = other.perPushMaxNum_;
					onChanged();
				}
				if (other.hasPerAllPushMaxNum()) {
					bitField0_ |= 0x00000008;
					perAllPushMaxNum_ = other.perAllPushMaxNum_;
					onChanged();
				}
				if (other.hasPerDelayTime()) {
					bitField0_ |= 0x00000010;
					perDelayTime_ = other.perDelayTime_;
					onChanged();
				}
				if (other.hasBussPushTime()) {
					bitField0_ |= 0x00000020;
					bussPushTime_ = other.bussPushTime_;
					onChanged();
				}
				if (other.hasBussPushMaxNum()) {
					bitField0_ |= 0x00000040;
					bussPushMaxNum_ = other.bussPushMaxNum_;
					onChanged();
				}
				if (other.hasBussAllPushMaxNum()) {
					bitField0_ |= 0x00000080;
					bussAllPushMaxNum_ = other.bussAllPushMaxNum_;
					onChanged();
				}
				if (other.hasBussDelayTime()) {
					bitField0_ |= 0x00000100;
					bussDelayTime_ = other.bussDelayTime_;
					onChanged();
				}
				if (other.hasMediaPushTime()) {
					bitField0_ |= 0x00000200;
					mediaPushTime_ = other.mediaPushTime_;
					onChanged();
				}
				if (other.hasMediaPushMaxNum()) {
					bitField0_ |= 0x00000400;
					mediaPushMaxNum_ = other.mediaPushMaxNum_;
					onChanged();
				}
				if (other.hasMediaAllPushMaxNum()) {
					bitField0_ |= 0x00000800;
					mediaAllPushMaxNum_ = other.mediaAllPushMaxNum_;
					onChanged();
				}
				if (other.hasMediaDelayTime()) {
					bitField0_ |= 0x00001000;
					mediaDelayTime_ = other.mediaDelayTime_;
					onChanged();
				}
				this.mergeUnknownFields(other.getUnknownFields());
				return this;
			}

			public final boolean isInitialized() {
				if (!hasProvinceCode()) {

					return false;
				}
				return true;
			}

			public Builder mergeFrom(com.google.protobuf.CodedInputStream input,
					com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
				ThresholdMessage.ThresholdEntity parsedMessage = null;
				try {
					parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
				} catch (com.google.protobuf.InvalidProtocolBufferException e) {
					parsedMessage = (ThresholdMessage.ThresholdEntity) e.getUnfinishedMessage();
					throw e;
				} finally {
					if (parsedMessage != null) {
						mergeFrom(parsedMessage);
					}
				}
				return this;
			}

			private int bitField0_;

			// required string provinceCode = 1;
			private java.lang.Object provinceCode_ = "";

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public boolean hasProvinceCode() {
				return ((bitField0_ & 0x00000001) == 0x00000001);
			}

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public java.lang.String getProvinceCode() {
				java.lang.Object ref = provinceCode_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					provinceCode_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public com.google.protobuf.ByteString getProvinceCodeBytes() {
				java.lang.Object ref = provinceCode_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					provinceCode_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public Builder setProvinceCode(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000001;
				provinceCode_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public Builder clearProvinceCode() {
				bitField0_ = (bitField0_ & ~0x00000001);
				provinceCode_ = getDefaultInstance().getProvinceCode();
				onChanged();
				return this;
			}

			/**
			 * <code>required string provinceCode = 1;</code>
			 */
			public Builder setProvinceCodeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000001;
				provinceCode_ = value;
				onChanged();
				return this;
			}

			// optional string perPushTime = 2;
			private java.lang.Object perPushTime_ = "";

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public boolean hasPerPushTime() {
				return ((bitField0_ & 0x00000002) == 0x00000002);
			}

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public java.lang.String getPerPushTime() {
				java.lang.Object ref = perPushTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					perPushTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public com.google.protobuf.ByteString getPerPushTimeBytes() {
				java.lang.Object ref = perPushTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					perPushTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public Builder setPerPushTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000002;
				perPushTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public Builder clearPerPushTime() {
				bitField0_ = (bitField0_ & ~0x00000002);
				perPushTime_ = getDefaultInstance().getPerPushTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perPushTime = 2;</code>
			 */
			public Builder setPerPushTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000002;
				perPushTime_ = value;
				onChanged();
				return this;
			}

			// optional string perPushMaxNum = 3;
			private java.lang.Object perPushMaxNum_ = "";

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public boolean hasPerPushMaxNum() {
				return ((bitField0_ & 0x00000004) == 0x00000004);
			}

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public java.lang.String getPerPushMaxNum() {
				java.lang.Object ref = perPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					perPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public com.google.protobuf.ByteString getPerPushMaxNumBytes() {
				java.lang.Object ref = perPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					perPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public Builder setPerPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000004;
				perPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public Builder clearPerPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000004);
				perPushMaxNum_ = getDefaultInstance().getPerPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perPushMaxNum = 3;</code>
			 */
			public Builder setPerPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000004;
				perPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string perAllPushMaxNum = 4;
			private java.lang.Object perAllPushMaxNum_ = "";

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public boolean hasPerAllPushMaxNum() {
				return ((bitField0_ & 0x00000008) == 0x00000008);
			}

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public java.lang.String getPerAllPushMaxNum() {
				java.lang.Object ref = perAllPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					perAllPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public com.google.protobuf.ByteString getPerAllPushMaxNumBytes() {
				java.lang.Object ref = perAllPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					perAllPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public Builder setPerAllPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000008;
				perAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public Builder clearPerAllPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000008);
				perAllPushMaxNum_ = getDefaultInstance().getPerAllPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perAllPushMaxNum = 4;</code>
			 */
			public Builder setPerAllPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000008;
				perAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string perDelayTime = 5;
			private java.lang.Object perDelayTime_ = "";

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public boolean hasPerDelayTime() {
				return ((bitField0_ & 0x00000010) == 0x00000010);
			}

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public java.lang.String getPerDelayTime() {
				java.lang.Object ref = perDelayTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					perDelayTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public com.google.protobuf.ByteString getPerDelayTimeBytes() {
				java.lang.Object ref = perDelayTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					perDelayTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public Builder setPerDelayTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000010;
				perDelayTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public Builder clearPerDelayTime() {
				bitField0_ = (bitField0_ & ~0x00000010);
				perDelayTime_ = getDefaultInstance().getPerDelayTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string perDelayTime = 5;</code>
			 */
			public Builder setPerDelayTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000010;
				perDelayTime_ = value;
				onChanged();
				return this;
			}

			// optional string bussPushTime = 6;
			private java.lang.Object bussPushTime_ = "";

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public boolean hasBussPushTime() {
				return ((bitField0_ & 0x00000020) == 0x00000020);
			}

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public java.lang.String getBussPushTime() {
				java.lang.Object ref = bussPushTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					bussPushTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public com.google.protobuf.ByteString getBussPushTimeBytes() {
				java.lang.Object ref = bussPushTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					bussPushTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public Builder setBussPushTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000020;
				bussPushTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public Builder clearBussPushTime() {
				bitField0_ = (bitField0_ & ~0x00000020);
				bussPushTime_ = getDefaultInstance().getBussPushTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussPushTime = 6;</code>
			 */
			public Builder setBussPushTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000020;
				bussPushTime_ = value;
				onChanged();
				return this;
			}

			// optional string bussPushMaxNum = 7;
			private java.lang.Object bussPushMaxNum_ = "";

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public boolean hasBussPushMaxNum() {
				return ((bitField0_ & 0x00000040) == 0x00000040);
			}

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public java.lang.String getBussPushMaxNum() {
				java.lang.Object ref = bussPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					bussPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public com.google.protobuf.ByteString getBussPushMaxNumBytes() {
				java.lang.Object ref = bussPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					bussPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public Builder setBussPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000040;
				bussPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public Builder clearBussPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000040);
				bussPushMaxNum_ = getDefaultInstance().getBussPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussPushMaxNum = 7;</code>
			 */
			public Builder setBussPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000040;
				bussPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string bussAllPushMaxNum = 8;
			private java.lang.Object bussAllPushMaxNum_ = "";

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public boolean hasBussAllPushMaxNum() {
				return ((bitField0_ & 0x00000080) == 0x00000080);
			}

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public java.lang.String getBussAllPushMaxNum() {
				java.lang.Object ref = bussAllPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					bussAllPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public com.google.protobuf.ByteString getBussAllPushMaxNumBytes() {
				java.lang.Object ref = bussAllPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					bussAllPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public Builder setBussAllPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000080;
				bussAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public Builder clearBussAllPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000080);
				bussAllPushMaxNum_ = getDefaultInstance().getBussAllPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussAllPushMaxNum = 8;</code>
			 */
			public Builder setBussAllPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000080;
				bussAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string bussDelayTime = 9;
			private java.lang.Object bussDelayTime_ = "";

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public boolean hasBussDelayTime() {
				return ((bitField0_ & 0x00000100) == 0x00000100);
			}

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public java.lang.String getBussDelayTime() {
				java.lang.Object ref = bussDelayTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					bussDelayTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public com.google.protobuf.ByteString getBussDelayTimeBytes() {
				java.lang.Object ref = bussDelayTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					bussDelayTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public Builder setBussDelayTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000100;
				bussDelayTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public Builder clearBussDelayTime() {
				bitField0_ = (bitField0_ & ~0x00000100);
				bussDelayTime_ = getDefaultInstance().getBussDelayTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string bussDelayTime = 9;</code>
			 */
			public Builder setBussDelayTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000100;
				bussDelayTime_ = value;
				onChanged();
				return this;
			}

			// optional string mediaPushTime = 10;
			private java.lang.Object mediaPushTime_ = "";

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public boolean hasMediaPushTime() {
				return ((bitField0_ & 0x00000200) == 0x00000200);
			}

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public java.lang.String getMediaPushTime() {
				java.lang.Object ref = mediaPushTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					mediaPushTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public com.google.protobuf.ByteString getMediaPushTimeBytes() {
				java.lang.Object ref = mediaPushTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					mediaPushTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public Builder setMediaPushTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000200;
				mediaPushTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public Builder clearMediaPushTime() {
				bitField0_ = (bitField0_ & ~0x00000200);
				mediaPushTime_ = getDefaultInstance().getMediaPushTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaPushTime = 10;</code>
			 */
			public Builder setMediaPushTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000200;
				mediaPushTime_ = value;
				onChanged();
				return this;
			}

			// optional string mediaPushMaxNum = 11;
			private java.lang.Object mediaPushMaxNum_ = "";

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public boolean hasMediaPushMaxNum() {
				return ((bitField0_ & 0x00000400) == 0x00000400);
			}

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public java.lang.String getMediaPushMaxNum() {
				java.lang.Object ref = mediaPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					mediaPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public com.google.protobuf.ByteString getMediaPushMaxNumBytes() {
				java.lang.Object ref = mediaPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					mediaPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public Builder setMediaPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000400;
				mediaPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public Builder clearMediaPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000400);
				mediaPushMaxNum_ = getDefaultInstance().getMediaPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaPushMaxNum = 11;</code>
			 */
			public Builder setMediaPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000400;
				mediaPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string mediaAllPushMaxNum = 12;
			private java.lang.Object mediaAllPushMaxNum_ = "";

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public boolean hasMediaAllPushMaxNum() {
				return ((bitField0_ & 0x00000800) == 0x00000800);
			}

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public java.lang.String getMediaAllPushMaxNum() {
				java.lang.Object ref = mediaAllPushMaxNum_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					mediaAllPushMaxNum_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public com.google.protobuf.ByteString getMediaAllPushMaxNumBytes() {
				java.lang.Object ref = mediaAllPushMaxNum_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					mediaAllPushMaxNum_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public Builder setMediaAllPushMaxNum(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000800;
				mediaAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public Builder clearMediaAllPushMaxNum() {
				bitField0_ = (bitField0_ & ~0x00000800);
				mediaAllPushMaxNum_ = getDefaultInstance().getMediaAllPushMaxNum();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaAllPushMaxNum = 12;</code>
			 */
			public Builder setMediaAllPushMaxNumBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000800;
				mediaAllPushMaxNum_ = value;
				onChanged();
				return this;
			}

			// optional string mediaDelayTime = 13;
			private java.lang.Object mediaDelayTime_ = "";

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public boolean hasMediaDelayTime() {
				return ((bitField0_ & 0x00001000) == 0x00001000);
			}

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public java.lang.String getMediaDelayTime() {
				java.lang.Object ref = mediaDelayTime_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					mediaDelayTime_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public com.google.protobuf.ByteString getMediaDelayTimeBytes() {
				java.lang.Object ref = mediaDelayTime_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					mediaDelayTime_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public Builder setMediaDelayTime(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00001000;
				mediaDelayTime_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public Builder clearMediaDelayTime() {
				bitField0_ = (bitField0_ & ~0x00001000);
				mediaDelayTime_ = getDefaultInstance().getMediaDelayTime();
				onChanged();
				return this;
			}

			/**
			 * <code>optional string mediaDelayTime = 13;</code>
			 */
			public Builder setMediaDelayTimeBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00001000;
				mediaDelayTime_ = value;
				onChanged();
				return this;
			}

			// @@protoc_insertion_point(builder_scope:ThresholdEntity)
		}

		static {
			defaultInstance = new ThresholdEntity(true);
			defaultInstance.initFields();
		}

		// @@protoc_insertion_point(class_scope:ThresholdEntity)
	}

	private static com.google.protobuf.Descriptors.Descriptor internal_static_ThresholdEntity_descriptor;
	private static com.google.protobuf.GeneratedMessage.FieldAccessorTable internal_static_ThresholdEntity_fieldAccessorTable;

	public static com.google.protobuf.Descriptors.FileDescriptor getDescriptor() {
		return descriptor;
	}

	private static com.google.protobuf.Descriptors.FileDescriptor descriptor;
	static {
		java.lang.String[] descriptorData = { "\n\017Threshold.proto\"\307\002\n\017ThresholdEntity\022\024\n"
				+ "\014provinceCode\030\001 \002(\t\022\023\n\013perPushTime\030\002 \001(\t"
				+ "\022\025\n\rperPushMaxNum\030\003 \001(\t\022\030\n\020perAllPushMax"
				+ "Num\030\004 \001(\t\022\024\n\014perDelayTime\030\005 \001(\t\022\024\n\014bussP"
				+ "ushTime\030\006 \001(\t\022\026\n\016bussPushMaxNum\030\007 \001(\t\022\031\n"
				+ "\021bussAllPushMaxNum\030\010 \001(\t\022\025\n\rbussDelayTim"
				+ "e\030\t \001(\t\022\025\n\rmediaPushTime\030\n \001(\t\022\027\n\017mediaP"
				+ "ushMaxNum\030\013 \001(\t\022\032\n\022mediaAllPushMaxNum\030\014 "
				+ "\001(\t\022\026\n\016mediaDelayTime\030\r \001(\tB\022B\020Threshold" + "Message" };
		com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner = new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
			public com.google.protobuf.ExtensionRegistry assignDescriptors(
					com.google.protobuf.Descriptors.FileDescriptor root) {
				descriptor = root;
				internal_static_ThresholdEntity_descriptor = getDescriptor().getMessageTypes().get(0);
				internal_static_ThresholdEntity_fieldAccessorTable = new com.google.protobuf.GeneratedMessage.FieldAccessorTable(
						internal_static_ThresholdEntity_descriptor,
						new java.lang.String[] { "ProvinceCode", "PerPushTime", "PerPushMaxNum", "PerAllPushMaxNum",
								"PerDelayTime", "BussPushTime", "BussPushMaxNum", "BussAllPushMaxNum", "BussDelayTime",
								"MediaPushTime", "MediaPushMaxNum", "MediaAllPushMaxNum", "MediaDelayTime", });
				return null;
			}
		};
		com.google.protobuf.Descriptors.FileDescriptor.internalBuildGeneratedFileFrom(descriptorData,
				new com.google.protobuf.Descriptors.FileDescriptor[] {}, assigner);
	}

	// @@protoc_insertion_point(outer_class_scope)
}
