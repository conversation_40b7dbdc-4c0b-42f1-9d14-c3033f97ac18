import {postHeader} from './../../../servers/httpServer.js';
//查询
export async function warnUserQuery(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//新增
export async function addWarnUser(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//修改
export async function updateWarnUser(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//删除
export async function deleteWarnUser(url,params){
    let result=await postHeader(url,params);
    return result.data;
}