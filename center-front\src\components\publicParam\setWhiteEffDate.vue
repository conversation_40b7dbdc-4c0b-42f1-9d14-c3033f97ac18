<template>
    <div>
        <h1 class="user-title">彩印套餐功能配置</h1>
        <hr class="user-line"/>
        <div class="user-search">
                白名单有效期   :  {{whiteEffDate}}个月
                 <el-button type="primary" @click="dialogVisible = true">设置有效期</el-button>
        </div>

        <el-dialog
                title="提示"
                :visible.sync="dialogVisible"
                width="20%"
                :before-close="handleClose">
            <el-form :model="editForm" :rules="rules" ref="editForm" :inline="true" class="demo-form-inline" style="margin-left: 5%;">
                <el-form-item label="有效期" prop="whiteEffDate" :inline="true" >
                <el-row :gutter="24">
                    <el-col :span="20" >
                            <el-input  v-model.number="editForm.whiteEffDate" placeholder=""></el-input>
                    </el-col>
                    <el-col :span="4">月</el-col>
                </el-row>
                </el-form-item>

            </el-form>
              <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="setWhiteEffDate('editForm')">确 定</el-button>
              </span>
        </el-dialog>

        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
        </el-dialog>
    </div>




</template>
<script>
    export default {
        data() {
            return {
                propMsg:'',
                propVisible:false,
                dialogVisible:false,
                //白名单有效期
                whiteEffDate:'0',
                editForm:{
                    whiteEffDate:''
                },
                rules: {
                    whiteEffDate: [
                        { required: true, message: '请输入月份'  },
                        { type: 'number', message: '月份必须是数字' },
                    ]
                }
            }
        },

        mounted(){
//            this.slideData(this.proxyUrl);
            this.search();
        },
        methods: {
            search(){
                this.$http.get(`${this.proxyUrl}/param/whiteEffData/getParWhiteDate`).then(function(res){

                    this.whiteEffDate=res.data.whiteEffDate;
                })
            },


            setWhiteEffDate(editForm){
                console.log(this.editForm)
                this.$refs[editForm].validate((valid) => {
                    console.log(valid);
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/whiteEffData/updateEff`,this.editForm,{emulateJSON:true})
                                .then(function(res){
                                    if(res.data.resStatus == 0){
                                        this.propMsg = '修改成功！';
                                        this.propVisible=true;
                                        this.dialogVisible=false;
                                        this.search();
                                    }else{
                                        this.propMsg = '修改失败!'+ res.data.resText;;
                                        this.propVisible=true;
                                    }
                                })
                    } else {
                        return false;
                }
                });
            },
            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            }
        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
