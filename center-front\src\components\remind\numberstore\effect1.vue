<template>
    <div class="innerbox">
        <div class="effect">
            <div>
                <!--查询条件-->
                <div>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" label-width="60px">
                        <el-form-item label="号码">
                            <el-input  v-model="searchForm.phoneNumber" placeholder="" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="search(1)">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" style="margin-left: 25px;">
                        <!--<el-form-item>-->
                            <!--<el-button type="primary" size="small" @click="addList">新增号码</el-button>-->
                        <!--</el-form-item>-->
                        <el-form-item>
                            <el-button type="primary" size="small" @click="addall" plain>批量新增</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <div>
                    <!--表格-->
                    <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                        <el-table-column prop="phoneNumber" label="号码" />
                        <el-table-column prop="classBtype" label="号码描述" />
                        <el-table-column prop="provinceName" label="省份" />
                        <el-table-column prop="countyName" label="地市"/>
                        <el-table-column prop="categoryName" label="分类"/>
                        <el-table-column prop="standardTypeName" label="标准类型"/>
                        <el-table-column prop="markType" label="标记类型"/>
                        <el-table-column prop="sourceName" label="号码来源"/>
                        <el-table-column prop="markTimes" label="标记次数"/>
                        <el-table-column prop="createTime" label="入库时间"/>
                        <el-table-column label="操作">
                            <template slot-scope="scope">
                                <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                                    <p style="margin: 10px;text-align:center">确定删除此号码?</p>
                                    <div style="margin: 10px;text-align:center">
                                        <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                        <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                                    </div>
                                    <div slot="reference">
                                        <el-button  type="text" size="small" >删除</el-button>
                                    </div>
                                </el-popover>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <!--分页-->
                <div class="block app-pageganit" v-show="total>20">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="50"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
            <!--新增-->
            <div>
                <el-dialog title="新增号码" :visible.sync="addVisible"   :close-on-click-modal="false">
                    <addeffect @addList="addList"></addeffect>
                </el-dialog>
            </div>
            <!--批量新增-->
            <div>
                <el-dialog title="导入号码" :visible.sync="alladdVisible"   :close-on-click-modal="false">
                    <leadingin :alladdVisible="alladdVisible" @addall="addall"></leadingin>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
    import addeffect from './addeffect';
    import leadingin from './leadingin';
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'effect',
        data(){
            return{
                addVisible:false,//新增
                alladdVisible:false,//批量新增
                //查询form对象定义
                searchForm: {
                    numType:1, //号码库
                    phoneNumber:'',//号码
                    pageSize:50,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                tableData:[],//表数据
                currentPage: 1,
                total:0
            }
        },
        components: {
            addeffect,
            leadingin
        },
        created(){
//            this.search();
        },
        methods:{
            //每页条数
            handleSizeChange(val) {
                this.searchForm.pageSize = val;
                this.search();
            },
            //当前页面
            handleCurrentChange(val) {
                this.searchForm.pageNo = val;
                this.search();
            },
            //查询请求
            search: function(pg) {
                let vm = this;
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                if(!this.searchForm.phoneNumber){
                    vm.$message.error("请输入号码查询");
                    return;
                }
                postHeader('queryNumInfo', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.queryNumInfoList;
                        vm.total = data.data.total;
                    }
                })
            },
            //新增按钮
            addList(){
                let vm = this;
                vm.addVisible=!vm.addVisible;
            },
            //批量新增
            addall(){
                let vm = this;
                vm.alladdVisible = !vm.alladdVisible
            },
            //删除
            deletebtn(row) {
                let vm = this;
                let parms = {
                    type:1,
                    categoryId:row.categoryId,
                    phoneNumber:row.phoneNumber
                }
                postHeader('deleteNumber', JSON.stringify(parms)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("删除成功");
                        this.search(1);
                    }else{
                        vm.$message.error("删除失败");
                    }
                })
                row.show = false;
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .innerbox{
        margin-bottom: 20px;
    }
    .el-table{
        margin: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
