package com.cy.user.model;

public class CyUser {

	private String pkCurUserid; // 用户手机号码或固话号码
	private String curNick; // 用户昵称
	private String curUserFlag; // 用户标识位
	private String curPkgFuncFlag; // 用户套餐功能标识合集
	private String curFuncFlag; // 用户业务功能标识最终状态
	/*
	 * 彩印状态 0：已注销； 1：正常 2：暂停（可被恢复到正常状态） 3：预开户（新SIM卡用户在激活前处于该状态）
	 */
	private String curRegisterFlag;

	private String curBindTime; // 运营平台注册时间
	private String curCancelTime;// 运营平台注销时间
	private String cur_TonormalTime;// 体验包转正时间
	private String curSuspendTime;// 彩印暂停时间
	private String curResumeTime;// 彩印恢复时间
	private String curCard;// 用户通用彩印内容
	private String curSignPkgid; // 用户通用彩印签名盒
	private String curSignNumber;// 彩印编号
	private String curSerialNumber;// 彩印序列编号
	private String curDefaultSign;// 用户默认彩印
	private String curDefaultSignNum;// 用户默认彩印ID
	private String curDefaultSignPkgid;// 用户默认彩印盒ID

	/*
	 * 开户操作来源 1:WEB 2:网上营业厅 3:WAP 4:SMS 7:10086语音 8:营业厅 97：Portal 98：短信自行开户 99：SMC
	 * 102：套卡免费
	 */
	private String curOprsrc;
	/*
	 * 彩印销户操作来源 99：SMC 104：BOSS对账销户 123：套餐失效自动销户 其它：来自于BOSS接口
	 */

	private String curCancelOprsrc;
	private int curModTimes; // 用户当月修改彩印次数
	private String curLastModtTime; // 通用彩印（彩印盒）最后修改时间
	private int curBrand; // 用户品牌
	private String curProvinceno; // 省编码
	private String curProvinceName;// 省名称
	private String curAreano; // 地区编码
	private String curAreaName; // 地区名称
	private String curBossbindTime;
	private String curBosscanTime;
	private String cur_BossbindTid;
	private String curRecvsm1Time;
	private String curOrderid;
	private String curProductid;
	private String curEmail;
	private String curBirthday;
	private int curGender;

	public String getPkCurUserid() {
		return pkCurUserid;
	}

	public void setPkCurUserid(String pkCurUserid) {
		this.pkCurUserid = pkCurUserid;
	}

	public String getCurNick() {
		return curNick;
	}

	public void setCurNick(String curNick) {
		this.curNick = curNick;
	}

	public String getCurUserFlag() {
		return curUserFlag;
	}

	public void setCurUserFlag(String curUserFlag) {
		this.curUserFlag = curUserFlag;
	}

	public String getCurPkgFuncFlag() {
		return curPkgFuncFlag;
	}

	public void setCurPkgFuncFlag(String curPkgFuncFlag) {
		this.curPkgFuncFlag = curPkgFuncFlag;
	}

	public String getCurFuncFlag() {
		return curFuncFlag;
	}

	public void setCurFuncFlag(String curFuncFlag) {
		this.curFuncFlag = curFuncFlag;
	}

	public String getCurRegisterFlag() {
		return curRegisterFlag;
	}

	public void setCurRegisterFlag(String curRegisterFlag) {
		this.curRegisterFlag = curRegisterFlag;
	}

	public String getCurBindTime() {
		return curBindTime;
	}

	public void setCurBindTime(String curBindTime) {
		this.curBindTime = curBindTime;
	}

	public String getCurCancelTime() {
		return curCancelTime;
	}

	public void setCurCancelTime(String curCancelTime) {
		this.curCancelTime = curCancelTime;
	}

	public String getCur_TonormalTime() {
		return cur_TonormalTime;
	}

	public void setCur_TonormalTime(String cur_TonormalTime) {
		this.cur_TonormalTime = cur_TonormalTime;
	}

	public String getCurSuspendTime() {
		return curSuspendTime;
	}

	public void setCurSuspendTime(String curSuspendTime) {
		this.curSuspendTime = curSuspendTime;
	}

	public String getCurResumeTime() {
		return curResumeTime;
	}

	public void setCurResumeTime(String curResumeTime) {
		this.curResumeTime = curResumeTime;
	}

	public String getCurCard() {
		return curCard;
	}

	public void setCurCard(String curCard) {
		this.curCard = curCard;
	}

	public String getCurSignPkgid() {
		return curSignPkgid;
	}

	public void setCurSignPkgid(String curSignPkgid) {
		this.curSignPkgid = curSignPkgid;
	}

	public String getCurSignNumber() {
		return curSignNumber;
	}

	public void setCurSignNumber(String curSignNumber) {
		this.curSignNumber = curSignNumber;
	}

	public String getCurSerialNumber() {
		return curSerialNumber;
	}

	public void setCurSerialNumber(String curSerialNumber) {
		this.curSerialNumber = curSerialNumber;
	}

	public String getCurDefaultSign() {
		return curDefaultSign;
	}

	public void setCurDefaultSign(String curDefaultSign) {
		this.curDefaultSign = curDefaultSign;
	}

	public String getCurDefaultSignNum() {
		return curDefaultSignNum;
	}

	public void setCurDefaultSignNum(String curDefaultSignNum) {
		this.curDefaultSignNum = curDefaultSignNum;
	}

	public String getCurDefaultSignPkgid() {
		return curDefaultSignPkgid;
	}

	public void setCurDefaultSignPkgid(String curDefaultSignPkgid) {
		this.curDefaultSignPkgid = curDefaultSignPkgid;
	}

	public String getCurOprsrc() {
		return curOprsrc;
	}

	public void setCurOprsrc(String curOprsrc) {
		this.curOprsrc = curOprsrc;
	}

	public String getCurCancelOprsrc() {
		return curCancelOprsrc;
	}

	public void setCurCancelOprsrc(String curCancelOprsrc) {
		this.curCancelOprsrc = curCancelOprsrc;
	}

	public int getCurModTimes() {
		return curModTimes;
	}

	public void setCurModTimes(int curModTimes) {
		this.curModTimes = curModTimes;
	}

	public String getCurLastModtTime() {
		return curLastModtTime;
	}

	public void setCurLastModtTime(String curLastModtTime) {
		this.curLastModtTime = curLastModtTime;
	}

	public int getCurBrand() {
		return curBrand;
	}

	public void setCurBrand(int curBrand) {
		this.curBrand = curBrand;
	}

	public String getCurProvinceno() {
		return curProvinceno;
	}

	public void setCurProvinceno(String curProvinceno) {
		this.curProvinceno = curProvinceno;
	}

	public String getCurProvinceName() {
		return curProvinceName;
	}

	public void setCurProvinceName(String curProvinceName) {
		this.curProvinceName = curProvinceName;
	}

	public String getCurAreano() {
		return curAreano;
	}

	public void setCurAreano(String curAreano) {
		this.curAreano = curAreano;
	}

	public String getCurAreaName() {
		return curAreaName;
	}

	public void setCurAreaName(String curAreaName) {
		this.curAreaName = curAreaName;
	}

	public String getCurBossbindTime() {
		return curBossbindTime;
	}

	public void setCurBossbindTime(String curBossbindTime) {
		this.curBossbindTime = curBossbindTime;
	}

	public String getCurBosscanTime() {
		return curBosscanTime;
	}

	public void setCurBosscanTime(String curBosscanTime) {
		this.curBosscanTime = curBosscanTime;
	}

	public String getCur_BossbindTid() {
		return cur_BossbindTid;
	}

	public void setCur_BossbindTid(String cur_BossbindTid) {
		this.cur_BossbindTid = cur_BossbindTid;
	}

	public String getCurRecvsm1Time() {
		return curRecvsm1Time;
	}

	public void setCurRecvsm1Time(String curRecvsm1Time) {
		this.curRecvsm1Time = curRecvsm1Time;
	}

	public String getCurOrderid() {
		return curOrderid;
	}

	public void setCurOrderid(String curOrderid) {
		this.curOrderid = curOrderid;
	}

	public String getCurProductid() {
		return curProductid;
	}

	public void setCurProductid(String curProductid) {
		this.curProductid = curProductid;
	}

	public String getCurEmail() {
		return curEmail;
	}

	public void setCurEmail(String curEmail) {
		this.curEmail = curEmail;
	}

	public String getCurBirthday() {
		return curBirthday;
	}

	public void setCurBirthday(String curBirthday) {
		this.curBirthday = curBirthday;
	}

	public int getCurGender() {
		return curGender;
	}

	public void setCurGender(int curGender) {
		this.curGender = curGender;
	}

}
