<template>
    <div>
        <h1 class="user-title">系统用户管理</h1>
        <hr class="user-line"/>
        <div class="user-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="状态">
                    <el-select v-model="searchForm.sysState" placeholder="请选择">
                        <el-option
                                v-for="(value,key) in statusList"
                                :key="key"
                                :label="value"
                                :value="key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="角色">
                    <el-select v-model="searchForm.sysRoleId" placeholder="请选择">
                        <el-option
                                v-for="item in roleList"
                                :key="item.sysRoleId"
                                :label="item.sysRoleName"
                                :value="item.sysRoleId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="用户名">
                    <el-input  v-model="searchForm.sysUserName" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="公司">
                    <el-input  v-model="searchForm.sysUserCompany" placeholder=""></el-input>
                </el-form-item>
            </el-form>
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="负责省份">
                    <el-select v-model="searchForm.provinceCode" placeholder="请选择">
                        <el-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="创建时间">
                    <el-date-picker
                            v-model="searchForm.endTime"
                            type="date"
                            placeholder="创建时间">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="saveOrUpdateType('新增系统用户',{})">新增系统用户</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" >
            <el-table-column prop="sysUserName" label="用户名" />
            <el-table-column prop="sysStaffName" label="姓名" />
            <el-table-column prop="sysRoleName" label="角色" />
            <el-table-column prop="sysUserCompany" label="公司"/>
            <el-table-column prop="provinceName" label="负责省份"/>
            <el-table-column prop="sysMobileNumber" label="联系电话"/>
            <el-table-column prop="sysUserEmail" label="Email"/>
            <el-table-column prop="sysCreateTime" label="创建时间"/>
            <el-table-column prop="sysState" label="状态"/>
            <el-table-column prop="oper" label="操作" width="200px">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="outerVisible = true" >重置密码</el-button>
                    <el-button type="text" size="small" @click="saveOrUpdateType('修改系统用户',scope.row)" >编辑</el-button>
                    <el-button type="text" size="small" @click="delUser(scope.row)" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

        <div>
            <el-dialog title="新增系统用户" :visible.sync="addVisible" :before-close="handleClose"  >
                <el-form :model="sysUserFrom" :rules="rules" ref="sysUserFrom" class="demo-form-inline" label-width="25%"  style="width: 80%">
                    <el-form-item label="用户名" prop="sysUserName">
                        <el-input v-model="sysUserFrom.sysUserName"></el-input>
                    </el-form-item>
                    <el-form-item label="姓名" prop="sysStaffName">
                        <el-input v-model="sysUserFrom.sysStaffName" ></el-input>
                    </el-form-item>
                    <el-form-item label="公司" prop="sysUserCompany">
                        <el-input v-model="sysUserFrom.sysUserCompany" ></el-input>
                    </el-form-item>
                    <el-form-item label="角色" prop="sysRoleId">
                        <el-select v-model="sysUserFrom.sysRoleId" placeholder="请选择">
                            <el-option
                                    v-for="item in roleList"
                                    :key="item.sysRoleId"
                                    :label="item.sysRoleName"
                                    :value="item.sysRoleId">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="负责省份" prop="provinceCode">
                        <el-select v-model="sysUserFrom.provinceCode" placeholder="请选择">
                            <el-option
                                    v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="sysState">
                        <el-select v-model="sysUserFrom.sysState" placeholder="请选择">
                            <el-option
                                    v-for="item in statusList"
                                    :key="item.key"
                                    :label="item.value"
                                    :value="item.key">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item v-if="sysUserFrom.sysUserId===undefined" label="密码" prop="sysUserPassword">
                        <el-input v-model="sysUserFrom.sysUserPassword" type="password"></el-input>
                    </el-form-item>
                    <el-form-item v-if="sysUserFrom.sysUserId===undefined" label="确认密码" prop="confirmPassword">
                        <el-input v-model="sysUserFrom.confirmPassword" type="password"></el-input>
                    </el-form-item>
                    <el-form-item label="联系电话" prop="sysMobileNumber">
                        <el-input v-model="sysUserFrom.sysMobileNumber" ></el-input>
                    </el-form-item>
                    <el-form-item label="Email" prop="sysUserEmail">
                        <el-input v-model="sysUserFrom.sysUserEmail" ></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addVisible = false">取 消</el-button>
                    <el-button type="primary" @click="addOrUpdateUser('sysUserFrom')">确 定</el-button>
                </div>
            </el-dialog>
        </div>
        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
        </el-dialog>
</div>
</template>
<script src='./sysUserList.js'></script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }

</style>
