<template scope="scope">
    <div>
        <div class="user-titler">名片文本审核通过记录</div>
        <!--企业彩印-->
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="彩印类型">
                    <el-select v-model="searchReq.caiyinType" class="app-input" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in caiyinType"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="彩印ID">
                    <el-input v-model="searchReq.uuid" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="内容">
                    <el-input v-model="searchReq.content" class="app-input" size="small"></el-input>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="审核人">
                    <el-input v-model="searchReq.reviewer" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="审核时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchReq.timearr1"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-form-item label="企业名称">
                    <el-input v-model="searchReq.corpName" class="app-input" size="small"></el-input>
                </el-form-item>
                <el-form-item label="提交时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchReq.timearr"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
                 <el-form-item label="内容编号">
                    <el-input v-model="searchReq.contentID" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
					<el-button type="primary" @click="download()" size="small">导出EXCEL</el-button>
                    <el-button type="primary" :type="typeoff" :disabled="clickoff" size="small" @click="passVisible=true;rejectType=2;">批量撤销</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div>
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    @selection-change="handleSelectionChange"
                    :header-cell-class-name="tableheaderClassName">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column
                        prop="uuid"
                        label="彩印ID"
                        width="140">
                </el-table-column>
                <el-table-column
                        prop="content"
                        label="内容"
                        width="400"
                        >
                </el-table-column>
                <el-table-column
                        prop="contentType"
                        label="彩印类型"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="status"
                        label="审核意见"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="corpName"
                        label="企业名称"
                        width="100">
                </el-table-column>
                <el-table-column label="企业资质" width="100">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)" :style="hasCorpImage(scope.row.corpImage)?'':'color:#808080'">详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column label="其他资质" width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)" :style="hasOtherImage(scope.row.otherImage)?'':'color: #808080'">详情</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                        prop="submitDate"
                        label="提交时间"
                        width="200">
                </el-table-column>
                <el-table-column
                    prop="contentID"
                    label="内容编号"
                    width="200">
                 </el-table-column>
                <el-table-column
                        prop="templateIds"
                        label="异网模板编号"
                        width="350">
                </el-table-column>
                <el-table-column
                        prop="province"
                        label="省份"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="city"
                        label="地市"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="reviewer"
                        label="审核人"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="reviewDate"
                        label="审核时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        fixed="right"
                        label="操作"
                        width="120">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="passVisible=true;rowData=scope.row;rejectType=1;">撤销</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="block app-pageganit">
                <el-pagination v-show="pageTotal"
                               @size-change="handleSizeChange"
                               @current-change="handleCurrentChange"
                               :current-page="searchReq.pageIndex"
                               :page-sizes="[10, 20, 30, 50]"
                               :page-size="10"
                               layout="total, sizes, prev, pager, next, jumper"
                               :total="pageTotal"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
        <div>
            <el-dialog
                    width="30%"
                    title="撤销原因"
                    :visible.sync="passVisible"
                    append-to-body
                    :close-on-click-modal="false">
                <el-form label-width="80px" justify="center">
                    <el-form-item label="" v-show="radio==1">
                        <el-input v-model="rejectReq.undoReason"
                                  type="textarea"
                                  :rows="2"
                                  placeholder="" style="width: 250px;"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" style="text-align: right;">
                    <el-button @click="passVisible = false" size="small">取 消</el-button>
                    <el-button type="primary" size="small" @click="passVisible = false;rejectCheck(rowData);">确 定</el-button>
                </div>
            </el-dialog>
            <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
                <img style="width: 100%;" :src="corpImage" alt>
            </el-dialog>
            <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
              <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
                 <li >
                   <a :href="item" target="_blank">其他资质{{index+1}}</a>
                 </li>
              </ul>
            </el-dialog>
        </div>
    </div>
</template>
<script>
    import {postDownload} from './../../../servers/httpServer.js';
    import {dowandFile} from './../../../util/core.js';
    export default {
        data() {
            return {
                tableLoading:false,
                //驳回原因
                radio: '1',
                pageTotal: 0,//总条数
                clickoff:true,
                typeoff:'info',
                //类型
                Type:[
                    {
                        id:'ALL',
                        name:'全部',
                    },{
                        id:'ENTERPRISE',
                        name:'企业彩印',
                    },
                    {
                        id:'FEINNO',
                        name:'新媒彩印',
                    }
                ],
                caiyinType:[
                    {
                        id:0,
                        name:'主叫彩印',
                    },
                    {
                        id:1,
                        name:'被叫彩印',
                    },
                    {
                        id:2,
                        name:'热线彩印',
                    },
                    {
                        id:3,
                        name:'挂机短信',
                    },
                    {
                        id:4,
                        name:'挂机彩信',
                    },
                    {
                        id:5,
                        name:'群发短信',
                    }
                ],
                corpImageVisible: false,
                corpImage: "", //企业资质
                otherImageVisible: false,
                otherImage: [], //其他资质
                //查询条件
                searchReq: {
                    uuid:"",//内容id
                    corpId: "",//企业编号
                    corpName: "",//企业名称
                    caiyinType: "",
                    content: "",//彩印内容
                    submitStartDate: "",//提交时间
                    submitEndDate: "",
                    contentID:"",//内容编号
                    provinceId:'',//省份
                    cityId:'',//地市
                    type:'ALL',//企业彩印
                    status: '',//  #默认0 0未审核，1审核通过，2审核不通过，3,撤销';
                    timearr:[],//提交时间
                    timearr1:[],//审核时间
                    reviewStartDate:'',//审核时间
                    reviewEndDate:'',
                    reviewer:'',//审核人
                    submitter:'',//提交人
                    pageSize: 10,
                    pageIndex: 1
                },
                //数据表
                tableData: [],
                //操作列表
                rowData: "",
                checked: [],
                passVisible: false,
                //撤销单或批量
                rejectType:0,
                multipleSelection: [],
                //撤销请求参数
                rejectReq: {
                    corpInfoId:'',//id
                    corpInfoIds:[],//多id
                    undoReason:'',
                    reviewer:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName//操作者
                },
            };
        },
        created(){
            this.search();
        },
        watch:{
            'rejectReq.corpInfoIds'(){
                if(this.rejectReq.corpInfoIds.length){
                    this.clickoff = false;
                    this.typeoff = 'primary';
                }else{
                    this.clickoff = true;
                    this.typeoff = 'info'
                }
            },
            'passVisible'(){
                if(!this.passVisible){
                    this.rejectReq.undoReason = '';
                }
            }
        },
        methods: {
            //多选框
            handleSelectionChange(val) {
                this.rejectReq.corpInfoIds=[];
                for (var i = 0; i < val.length; i++) {
                    this.rejectReq.corpInfoIds.push(val[i].id);
                }
            },
            download: function() {
                if(this.searchReq.timearr){
                    this.searchReq.submitStartDate = this.searchReq.timearr[0];
                    this.searchReq.submitEndDate = this.searchReq.timearr[1];
                }else{
                    this.searchReq.submitStartDate='';
                    this.searchReq.submitEndDate='';
                }
                //审核时间
                if(this.searchReq.timearr1){
                    this.searchReq.reviewStartDate = this.searchReq.timearr1[0];
                    this.searchReq.reviewEndDate = this.searchReq.timearr1[1];
                }else{
                    this.searchReq.reviewStartDate='';
                    this.searchReq.reviewEndDate='';
                }
                this.searchReq.status=1
                postDownload(`${this.proxyUrl}/entContent/corp/content/exportExeclquery`,this.searchReq).then(function(res) {
                        dowandFile(res.data,'名片文本审核通过记录.xlsx');
                    });
            },
            searchBtn() {
              this.searchReq.pageIndex = 1;
              this.search()
            },
            //查询请求
            search: function() {
                this.tableLoading = true;
                //提交时间
                if(this.searchReq.timearr){
                    this.searchReq.submitStartDate = this.searchReq.timearr[0];
                    this.searchReq.submitEndDate = this.searchReq.timearr[1];
                }else{
                    this.searchReq.submitStartDate='';
                    this.searchReq.submitEndDate='';
                }
                //审核时间
                if(this.searchReq.timearr1){
                    this.searchReq.reviewStartDate = this.searchReq.timearr1[0];
                    this.searchReq.reviewEndDate = this.searchReq.timearr1[1];
                }else{
                    this.searchReq.reviewStartDate='';
                    this.searchReq.reviewEndDate='';
                }
                this.searchReq.status=1
                this.$http.post(`${this.proxyUrl}/entContent/corp/content/query`, JSON.stringify(this.searchReq), {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "contentType": "application/json",
                        "charset":"utf-8"
                    },
                    emulateJSON: true,
                    timeout: 5000,
                }).then(res => {
                    this.tableLoading = false;
                    var res = res.data;
                if(res.code==0){
                    this.tableData = res.data;
                    this.tableData.forEach(function (val,index) {
                        switch(val.contentType) {
                            case 'ENTERPRISE':
                                if(val.caiyinType==0){
                                    val.contentType = '主叫彩印';
                                }else if(val.caiyinType==1){
                                    val.contentType = '被叫彩印';
                                }else if(val.caiyinType==2){
                                    val.contentType = '热线彩印';
                                }else if(val.caiyinType==3){
                                    val.contentType = '挂机短信';
                                }else if(val.caiyinType==4){
                                    val.contentType = '挂机彩信';
                                }else if(val.caiyinType==5){
                                    val.contentType = '群发短信';
                                }else{
                                    val.contentType = '企业彩印'
                                }
                                break;
                            case 'FEINNO':
                                val.contentType = '新媒彩印'
                                break;
                            default:
                                break;
                        };
                        switch(val.status) {
                            case 1:
                                val.status = '通过'
                                break;
                            case 2:
                                val.status = '驳回'
                                break;
                            case 3:
                                val.status = '已撤销'
                              break;
                          case 4:
                            val.status = '初审通过'
                            break;
                          case 5:
                            val.status = '初审驳回'
                                break;
                            default:
                                break;
                        }
                    })
                    this.pageTotal = res.totalCount;
                }
            });
            },
            //判断是批量但是单操作，发相应请求
            rejectCheck(val){
                //1为单，2为多
                if(this.rejectType==1){
                    this.reject(val);
                }
                else if(this.rejectType==2){
                    this.rejectlist();
                }
            },
            showCorpImage(corpImage) {
                console.log(corpImage);
                this.corpImage = corpImage;
                this.corpImageVisible = true;
            },
            showOtherImage(otherImage) {
              this.otherImage = otherImage;
              this.otherImageVisible = true;
            },
            // 判断企业资质和其他资质是否有值，没有则字体颜色变成灰色
            hasCorpImage(corpImage){
                return !(corpImage == null || corpImage == "");
            },
            hasOtherImage(otherImage){
                return !(otherImage == null || otherImage.length == 0);
            },
            //撤销请求---单
            reject: function(val) {
                if(!this.rejectReq.undoReason){
                    this.$message.error("请填写撤销原因");
                    this.passVisible=true;
                    return false;
                }
                this.rejectReq.corpInfoId = val.id;
                this.$http
                    .post(`${this.proxyUrl}/entContent/audit/undo`, JSON.stringify(this.rejectReq))
                    .then(function(res) {
                        if (res.data.code == "0") {
                            this.$message.success("撤销成功");
                            this.search(this.searchReq);
                            this.rejectReq.corpInfoId = '';
                        } else{
                            this.$message("撤销失败");
                            this.rejectReq.corpInfoId = '';
                        }
                    });
            },
            //撤销请求---多
            rejectlist: function() {
                if(!this.rejectReq.undoReason){
                    this.$message.error("请填写撤销原因");
                    this.passVisible=true;
                    return false;
                }
                if(!this.rejectReq.corpInfoIds.length>0){
                    this.$message.error("请选择批量撤销的内容");
                    return false;
                }
                this.$http
                    .post(`${this.proxyUrl}/entContent/audit/undo/batch`, JSON.stringify(this.rejectReq))
                    .then(function(res) {
                        if (res.data.code == "0") {
                            this.$message.success("撤销成功");
                            this.search(this.searchReq);
                            this.rejectReq.corpInfoIds.length=0;
                        } else{
                            this.$message("撤销失败");
                            this.rejectReq.corpInfoIds.length=0;
                        }
                    });
            },
            handleSizeChange(val) {
                this.searchReq.pageIndex = 1;
                //每页条数
                this.searchReq.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                //当前页
                this.searchReq.pageIndex = val;
                this.search();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
        }
    };
</script>
<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .content-title{
        margin-top: 20px;
        margin-left: 20px;
        background-color: white;
    }
    .content-line{
        margin-top: 20px;
    }
    .user-search {
        width: 94%;
        margin: 0 auto;
        margin-top: 20px;
        margin-left: 20px;
    }
    .el-table {
        margin-left: 3%;
        margin-top: 3%;
        border: 1px solid #ecebe9;
    }
</style>
<style>
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
