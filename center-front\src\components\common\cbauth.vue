<template>
	 <div>
	 	<el-dialog
            width="40%"
            title="金库认证"
            :visible.sync="authenVisible"
            :close-on-click-modal="false"
            append-to-body>
             <el-form :model="cbReq" class="demo-form-inline" label-width="35%" >
                <el-form-item label="申请理由">
                    <el-input v-model="cbReq.authReason" type="textarea" :rows="2" :maxlength="100" style="width:215px;"></el-input>
                </el-form-item>
                <el-form-item label="协同人">
                    <el-select v-model="cbReq.cbManager" value-key="account" size="small" >
                            <el-option
                                    v-for="item in cbManagerList"
                                    :label="item.account"
                                    :value="item">
                                    <span style="float: left">{{ item.account }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 10px">{{ item.username }}</span>
                            </el-option>
                        </el-select>
                </el-form-item>
                <el-form-item label="授权方式">
                    <el-select v-model="cbReq.authenMode" size="small" >
                            <el-option
                                    v-for="item in authenModeList"
                                    :key="item"
                                    :label="item"
                                    :value="item">
                            </el-option>
                        </el-select>
                </el-form-item>

                <el-form-item label="授权条件">
                    <el-radio v-model="cbReq.authCondition" label="1">远程授权(有验证码方式)</el-radio>
                </el-form-item>
           
                <el-form-item style="text-align: right;margin-top:20px;">
                    <el-button  size="small" @click="authenVisible=false;">取 消</el-button>
                    <el-button type="primary" size="small" @click="doAgainCBAuth();">确 定</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>

        <el-dialog
            width="40%"
            title="金库认证"
            :visible.sync="authenAginVisible"
            :close-on-click-modal="false"
            append-to-body>
             <el-form :model="cbReq" class="demo-form-inline" label-width="45%" >
                <el-form-item label="申请理由">
                    {{cbReq.authReason}}
                </el-form-item>
                <el-form-item label="协同人">
                    {{cbReq.cbManager.username}}
                </el-form-item>
                <el-form-item label="协同人手机号码">
                    {{cbReq.cbManager.iphone}}
                </el-form-item>
                <el-form-item label="授权方式">
                   远程授权(有验证码方式)
                </el-form-item>
                <el-form-item label="验证方式">
                    <el-col v-if="cbReq.authenMode == 'smscha'" >短信挑战</el-col>
                    <el-col v-if="cbReq.authenMode == 'password'" >静态密码</el-col>
                    <el-col v-if="cbReq.authenMode == 'C200token'" >C200令牌卡认证</el-col>
                    <el-col v-if="cbReq.authenMode == 'timetoken'" >时间令牌卡</el-col>
                    <el-col v-if="cbReq.authenMode == 'finger'" >指纹认证方式</el-col>
                    <el-col v-if="cbReq.authenMode == 'worksheet_kefu'" >自动工单授权认证</el-col>
                </el-form-item>

                <el-form-item label="录入短信挑战" v-if="cbReq.authenMode == 'smscha'">
                    <el-input v-model="cbReq.password" :maxlength="50" style="width:200px;" size="small"></el-input>
                </el-form-item>
                <el-form-item style="text-align: right;margin-top:20px;">
                    <el-button  size="small" @click="authenAginVisible=false;">取 消</el-button>
                    <el-button type="primary" size="small" @click="doThirdCBAuth();">确 定</el-button>
                    
                </el-form-item>
            </el-form>
        </el-dialog>
	 </div>
</template>
<script>
export default {
	name: 'cbauth',
	props:['menuCode'],
	data() {
        return {
            authenVisible:false,
            authenAginVisible:false,
            cbReq:{
                authReason:'',
                cbManager:{account:'',username:'',iphone:''},
                authenMode:'',
                authCondition:'1',
                resultType:'',
                password:'',
                keyOpCode:this.menuCode
            },
            cbManagerList:[],
            authenModeList:[]
        }
    },
    methods: {
        openAuthenDailog:function(){
            this.$http
                .post(`${this.proxyUrl}/sys/cbauth/doCBAuth`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.resultCode == -1){
                        this.authenVisible = false;
                        this.$emit('buttonDisable', false);
                    }else if(res.data.resultCode == 0){
                        this.cbReq.resultType = res.data.resultType;
                        this.cbReq.sessionId = res.data.sessionId;
                        this.cbManagerList = res.data.cbManagerList;
                        this.authenModeList = res.data.authenModeList;
                        this.authenVisible = true;
                    }else{
                        this.$message.error(res.data.resultMsg);
                    }
                    
             })
        },
        doAgainCBAuth:function(){
            if(!this.cbReq.authReason || !this.cbReq.authReason.trim()){
              this.$message.warning("申请理由不能为空");
              return;
            }
            if(!this.cbReq.cbManager){
              this.$message.warning("协同人不能为空");
              return;
            }
            if(!this.cbReq.authenMode || !this.cbReq.authenMode.trim()){
              this.$message.warning("授权方式不能为空");
              return;
            }
            this.$http
                .post(`${this.proxyUrl}/sys/cbauth/doAgainCBAuth`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.resultCode == -1){
                        this.authenVisible = false;
                        this.$emit('buttonDisable', false);
                    }else if(res.data.resultCode == 0){
                        this.cbReq.resultType = res.data.resultType;
                        this.cbReq.sessionId = res.data.sessionId;
                        this.authenVisible = false;
                        this.authenAginVisible = true;
                    }else{
                        this.$message.error(res.data.resultMsg);
                    }
             })
        },
        doThirdCBAuth:function(){
            if(!this.cbReq.password || !this.cbReq.password.trim()){
              this.$message.warning("短信挑战不能为空");
              return;
            }
            this.$http
                .post(`${this.proxyUrl}/sys/cbauth/doThirdCBAuth`, JSON.stringify(this.cbReq), { emulateJSON: true })
                .then(function(res) {
                    if (res.data.resultCode == -1 || res.data.resultCode == 0){
                        this.authenVisible = false;
                        this.authenAginVisible = false;
                        this.$emit('buttonDisable', false);
                    }else{
                        this.$message.error(res.data.resultMsg);
                    }
             })
        }

    },
    mounted() {
        this.openAuthenDailog();
    }
}
</script>