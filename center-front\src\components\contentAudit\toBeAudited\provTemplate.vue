<template>
  <div class="provinces">
    <div class="user-titler">{{$route.name}}</div>
    <div class="app-norbox">
      <!--查询条件-->
      <div>
        <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" label-width="70px">
          <el-form-item label="模版ID">
            <el-input  v-model="searchForm.templateId" placeholder="" class="app-input"></el-input>
          </el-form-item>
          <el-form-item label="模版名称">
            <el-input  v-model="searchForm.templateName" placeholder="" class="app-input"></el-input>
          </el-form-item>
          <el-form-item label="分类">
            <el-select v-model="searchForm.categoryId" placeholder="请选择" class="app-input">
              <el-option
                      v-for="item in categoryList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" label-width="70px">
          <el-form-item label="省份">
            <el-select v-model="searchForm.provinceId" @change="querySearchRegionList" placeholder="请选择" class="app-input">
              <el-option
                      v-for="item in provinceList"
                      :key="item.provinceCode"
                      :label="item.provinceName"
                      :value="item.provinceCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="地市">
            <el-select v-model="searchForm.countryId" placeholder="请选择" class="app-input">
              <el-option
                      v-for="item in city"
                      :key="item.regionCode"
                      :label="item.regionName"
                      :value="item.regionCode">
              </el-option>
            </el-select>
          </el-form-item>
          <br>
          <el-form-item label="提交时间" style="margin-top: 10px;">
            <el-date-picker
                    v-model="searchForm.startTime"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item style="margin-top: 10px;">
            <el-button type="primary" @click="search(1)">查询</el-button>
          </el-form-item>
        </el-form>
        <el-form :inline="true" class="demo-form-inline" size="small">
          <el-form-item>
            <el-button :type="typeoff" :disabled="clickoff" size="small" @click="alowList('')">批量通过</el-button>
            <el-button :type="typeoff" :disabled="clickoff" size="small" @click="rejectList('')">批量驳回</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!--表格-->
      <el-table :data="tableData" border
                @selection-change="handleSelectionChange"
                :header-cell-class-name="tableheaderClassNameZ"
                class="app-tab02">
        <el-table-column
                type="selection"
                width="55">
        </el-table-column>
        <el-table-column prop="templateId" label="模版ID" />
        <el-table-column prop="templateName" label="模版名称" />
        <el-table-column prop="categoryName" label="分类" />
        <!--<el-table-column prop="sourceName" label="来源"/>-->
        <el-table-column prop="provinceName" label="省份"/>
        <el-table-column prop="countryName" label="地市"/>
        <el-table-column prop="sysUserEmail" label="模版内容">
          <template slot-scope="scope">
            <el-button type="text" @click="details(scope.row)" size="small">查看详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述"/>
        <el-table-column prop="submitCount" label="提交次数"/>
        <el-table-column prop="submitTime" label="提交时间"/>
        <el-table-column label="操作" width="110">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="alowList(scope.row)">通过</el-button>
            <el-button type="text" size="small" @click="rejectList(scope.row)">驳回</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!--分页-->
      <div class="block app-pageganit" v-show="total">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"  style="text-align: right;">
        </el-pagination>
      </div>
    </div>
    <!--查看详情-->
    <el-dialog
            title="模版详情"
            :visible.sync="dialogVisible"
            width="40%" :close-on-click-modal="false">
      <ul class="detbox">
        <li>
          <p>拨打号码提醒内容:</p>
          <p class="fontcol">{{callingRemindContent}}</p>
        </li>
        <li>
          <p>接听号码提醒内容:</p>
          <p class="fontcol">{{calledRemindContent}}</p>
        </li>
        <li>
          <p>描述:</p>
          <p class="fontcol">{{description}}</p>
        </li>
      </ul>
    </el-dialog>
    <!--通过-->
    <el-dialog
            width="30%"
            title="通过"
            :visible.sync="passVisible"
            append-to-body :close-on-click-modal="false">
      <div>是否通过该内容？</div>
      <div slot="footer" style="text-align: right;">
        <el-button @click="passVisible = false" size="small">取 消</el-button>
        <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
      </div>
    </el-dialog>
    <!--驳回-->
    <el-dialog
            width="30%"
            title="驳回"
            :visible.sync="rejectVisible"
            append-to-body :close-on-click-modal="false">
      <el-form :model="ReasonFrom" :rules="rules" ref="ReasonFrom" label-width="100px" justify="center">
        <el-form-item label="驳回原因" prop="rejectReason">
          <el-input v-model="ReasonFrom.rejectReason"
                    type="textarea"
                    :rows="2"
                    placeholder="" style="width: 200px;"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" style="text-align: right;">
        <el-button @click="rejectVisible = false;resetForm('ReasonFrom')"size="small">取 消</el-button>
        <el-button type="primary" @click="rejectbtn"size="small">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
    //    import editprovinces from './editprovinces'
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'provinces',
        data(){
            return{
                dialogVisible:false,
                rejectVisible:false,
                passVisible:false,
                clickoff:true,
                typeoff:'info',
                rowData:'',//操作列表
                //查询form对象定义
                searchForm: {
                    templateId:'', //模版id
                    templateName:'',//模版名称,
                    categoryId:'', //分类id
                    sourceId: '', //来源ID
                    provinceId: '',
                    countryId:'',
                    provinceName:'',
                    countryName:'',
                    startDate:'',
                    endDate:'',
                    startTime:'',//创建时间
                    pageSize:10,// 每页显示条数
                    pageNo:1, // 查询的页码
                    auditStatus:0,// 模板审核状态:待审核 0 ,通过 1,拒绝 ： 2,撤销 ： 3* ；； 0：未审核，1：通过，2：驳回
                },
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                city:[],
                tableData:[],
                currentPage: 1,
                total:0,
                sourceList:[],//来源
                categoryList:[
                    {
                        name:'诈骗',
                        id:1
                    },
                    {
                        name:'黄页',
                        id:2
                    },
                    {
                        name:'标记',
                        id:3
                    }
                ],//分类
                callingRemindContent:'',//主叫提醒
                calledRemindContent:'',//被叫提醒
                description:'',//描述
                checkFrom:{
                    auditNumList:[
                        {
                            templateId:'',//模版ID
                            auditStatus:'',//审核状态0:待审核,1:审核通过,2:审核驳回,3:撤销
                            auditReason:'',//原因
                            auditUserAccount:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//审核人
                        }
                    ]
                },//单个审核参数
                checkallFrom:{
                    auditNumList:[]
                },
                ReasonFrom:{
                    rejectReason:'',//驳回原因
                },
                rules: {
                    rejectReason: [
                        { required: true, message: '请输入驳回原因', trigger: 'blur' },
                    ]
                },
                corpInfoIds:[],//选择框
                allclick:false
            }
        },
        components: {
//            editprovinces
        },
        created(){
            this.qusourceList();
            this.search();
        },
        watch:{
            rejectVisible(){
                if(!this.rejectVisible){
                    this.resetForm('ReasonFrom');
                }
            },
            corpInfoIds(){
                if(this.corpInfoIds.length){
                    this.clickoff = false;
                    this.typeoff = 'primary';
                }else{
                    this.clickoff = true;
                    this.typeoff = 'info'
                }
            }
        },
        methods:{
            //多选框
            handleSelectionChange(val) {
                this.corpInfoIds=[];
                for (var i = 0; i < val.length; i++) {
                    this.corpInfoIds.push(val[i].templateId);
                }
            },
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search()
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search()
            },
            //查看详情
            details(row){
                let vm = this;
                vm.dialogVisible = true;
                vm.callingRemindContent = row.callingRemindContent;
                vm.calledRemindContent = row.calledRemindContent;
                vm.description = row.description;
            },
            //通过
            alowList(row){
                let vm = this;
                vm.passVisible = !vm.passVisible
                if(row){
                    this.rowData=row;
                    this.allclick = false;
                }else{
                    this.rowData='';
                    this.allclick = true;
                }
            },
            //通过审核
            passCheck(){
                let vm = this;
                if(this.allclick){
                    this.passAll(1);
                    return;
                }
                this.checkFrom.auditNumList[0].templateId=this.rowData.templateId;
                this.checkFrom.auditNumList[0].auditStatus = 1;//审核通过
                postHeader('auditNumTemplateProvince',JSON.stringify(this.checkFrom)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("通过成功");
                        this.search(1);
                    }else{
                        vm.$message.error("通过失败");
                    }
                })
            },
            //驳回
            rejectList(row){
                let vm = this;
                if(row){
                    this.rowData=row;
                    this.allclick = false;
                }else{
                    this.rowData='';
                    this.allclick = true;
                }
                this.rejectVisible = !this.rejectVisible;
            },
            //驳回审核
            rejectbtn() {
                let vm = this;
                this.$refs['ReasonFrom'].validate((valid) => {
                    if(valid){
                        if(this.allclick){
                            this.passAll(2);
                            return;
                        }
                        this.checkFrom.auditNumList[0].templateId=this.rowData.templateId;
                        this.checkFrom.auditNumList[0].auditStatus = 2;//驳回
                        this.checkFrom.auditNumList[0].auditReason = this.ReasonFrom.rejectReason;//驳回原因
                        postHeader('auditNumTemplateProvince',JSON.stringify(this.checkFrom)).then(res=>{
                            let data = res.data;
                            if(data.code==0){
                                vm.$message.success("驳回成功");
                                this.search(1);
                            }else{
                                vm.$message.error("驳回失败");
                            }
                        })
                        this.resetForm('ReasonFrom');
                        this.rejectVisible = !this.rejectVisible;
                    }
                })
            },
            //批量审核
            passAll(int){
                let vm = this;
                let auditNumList = [];
                this.corpInfoIds.forEach(item=>{
                    auditNumList.push({
                        templateId:item,//模版ID
                        auditStatus:int,//审核状态0:待审核,1:审核通过,2:审核驳回,3:撤销
                        auditReason:int==2?this.ReasonFrom.rejectReason:'',//原因
                        auditUserAccount:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//审核人
                    })
                })
                this.checkallFrom.auditNumList = auditNumList;
                console.log(this.checkallFrom.auditNumList)
                postHeader('auditNumTemplateProvince',JSON.stringify(this.checkallFrom)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        if(int==1){
                            vm.$message.success("通过成功");
                        }else{
                            vm.$message.success("驳回成功");
                        }
                        this.search(1);
                    }else{
                        if(int==1){
                            vm.$message.error("通过失败");
                        }else{
                            vm.$message.error("驳回失败");
                        }
                    }
                })
                vm.passVisible = false;
                vm.rejectVisible = false;
            },
            //查看来源
            qusourceList(){
                let vm = this;
                postHeader('querySource',JSON.stringify({categoryId:0})).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.sourceList = data.data.sourceList;
                }
            })
            },
            //查询地市
            querySearchRegionList(){
                var queryRegion={
                    provinceCode: this.searchForm.provinceId
                };
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.city=res.data;
                        this.searchForm.countryId='';
                    })
            },
            //查询请求
            search(pg) {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                postHeader('queryNumTemplateProvince', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.tableData = data.data.list;
                    if(vm.tableData&&vm.tableData.length){
                        vm.tableData.forEach(item=>{
                            switch(Number(item.auditStatus)){
                        case 0:
                            item.auditStatus = '未审核';
                            break;
                        case 1:
                            item.auditStatus = '已通过';
                            break;
                        case 2:
                            item.auditStatus = '驳回';
                            break;
                        case 3:
                            item.auditStatus = '已撤销';
                            break;
                        default:
                            break;
                        }
                    })
                    }
                    vm.total = data.data.total;
                }
            })
            },

            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style scoped>
  .detbox{
    margin-left: 30px;
    margin-right: 20px;
  }
  .detbox li{
    border-bottom: 1px solid #cccccc;
  }
  .fontcol{
    color: #cccccc;
  }
  .user-titler{
    font-size: 20px;
    padding-left: 24px;
    height: 56px;
    line-height: 56px;
    font-family: PingFangSC-Medium;
    color: #333333;
    letter-spacing: -0.57px;
    border-bottom: 1px solid #D9D9D9;
  }
</style>
<style>
  .el-table .table-head-thz{
    background-color: #F5F5F5;
  }
</style>
