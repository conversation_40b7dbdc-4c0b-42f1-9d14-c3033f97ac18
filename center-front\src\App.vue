<template>
  <div>
    <transition>
      <router-view></router-view>
    </transition>
  </div>
</template>
<script>
    export default {
        mounted () {
            sessionStorage.removeItem('token')
        }
    }
</script>
<style>
  html,body{
    width:100%;
    height:100%;
    box-sizing:border-box;
    /* background:#288cc3; */
  }
  #app {
    font-family: 'Microsoft YaHei','Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: left;
    color: #2c3e50;
    height:100%;
  }
  .main{
    width:100%;
    background-color: white;
    height:auto;
    min-height: 100vh;
    z-index:500;
    box-sizing: border-box;
  }

  .home-container {
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
  }

  .home-header {
    background-color: #20a0ff;
    color: #333;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: content-box;
    padding: 0px;
  }

  .home-aside {
    background-color: #545c64;
  }
  .home_title {
    color: #fff;
    font-size: 22px;
    display: inline;
    margin-left: 8px;
  }
  .home_userinfo {
    color: #fff;
    cursor: pointer;
    margin-right: 40px;
  }

</style>