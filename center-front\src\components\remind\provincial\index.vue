<template>
    <div class="provincial">
        <div>
            <div class="user-titler">{{$route.name}}</div>
            <div style="margin: 20px;">
                <!--查询条件-->
                <div>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline" size="small">
                        <el-form-item label="省份">
                            <el-select v-model="searchForm.provinceId" placeholder="请选择">
                                <el-option
                                        v-for="item in provinceList"
                                        :key="item.provinceCode"
                                        :label="item.provinceName"
                                        :value="item.provinceCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="时间">
                            <el-date-picker
                                    v-model="searchForm.startTime"
                                    value-format="yyyy-MM-dd"
                                    type="daterange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search(1)">查询</el-button>
                        </el-form-item>
                        <br>
                        <el-form-item class="app-add app-bottom">
                            <el-button type="primary" @click="addList">新增分省规则</el-button>
                        </el-form-item>
                        <el-form-item class="app-add app-bottom">
                            <el-button type="primary" plain @click="exportexcle">导出excel</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <!--表格-->
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ" class="app-tab02">
                    <el-table-column prop="ruleId" label="分省规则ID" />
                    <el-table-column prop="provinceName" label="适用省份" />
                    <el-table-column prop="categoryName" label="分类" />
                    <el-table-column prop="standardTypeName" label="标准类型" />
                    <el-table-column prop="sourceName" label="号码来源" />
                    <el-table-column prop="markTimes" label="标记次数" />
                    <el-table-column prop="createTime" label="生成时间" />
                    <el-table-column prop="sysRoleName" label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="editbtn(scope.row)">编辑</el-button>
                            <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                                <p style="margin: 10px;text-align:center">确定删除此项?</p>
                                <div style="margin: 10px;text-align:center">
                                    <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                    <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                                </div>
                                <div slot="reference">
                                    <el-button  type="text" size="small">删除</el-button>
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
        </div>
        <!--新增-->
        <div>
            <el-dialog title="新增分省规则" :visible.sync="addVisible"   :close-on-click-modal="false">
                <addprovincial :addVisible="addVisible" @addList="addList"></addprovincial>
            </el-dialog>
        </div>
        <!--编辑-->
        <div>
            <el-dialog title="编辑分省规则" :visible.sync="editVisible"   :close-on-click-modal="false">
                <editprovincial :editrow="editrow" :editVisible="editVisible" @editbtn="editbtn"></editprovincial>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    import addprovincial from './addprovincial';
    import editprovincial from './editprovincial';
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'provincial',
        data(){
            return{
                addVisible:false,
                editVisible:false,
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                //查询form对象定义
                searchForm: {
                    provinceId:'', //省份id
                    startTime:[],//创建时间
                    startDate:'',//开始时间
                    endDate:'',//结束时间
                    pageSize:10,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                tableData:[],
                currentPage: 1,
                total:0,
                editrow:''
            }
        },
        components: {
            addprovincial,
            editprovincial
        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            //查询请求
            search(pg) {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                postHeader('queryProvRuleList', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.provinceRules;
                        vm.total = data.data.total;
                    }
                })
            },
            //导出
            exportexcle(){
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                postDownloadHeader('exportProvRule',JSON.stringify(this.searchForm)).then(res=>{
                    dowandFile(res.data,'分省规则.xlsx');
                })
            },
            //删除
            deletebtn(row) {
                let vm = this;
                row.show = false;
                postHeader('deleteProvRule', JSON.stringify({id:row.id})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("删除成功");
                        this.search(1);
                    }else{
                        vm.$message.error("删除失败");
                    }
                })
            },
            //新增
            addList(){
                let vm = this;
                vm.addVisible = !vm.addVisible;
                if(!vm.addVisible){
                    this.search(1);
                }
            },
            //编辑
            editbtn(row){
                let vm = this;
                vm.editVisible = !vm.editVisible;
                if(!vm.editVisible){
                    this.search();
                }
                if(row){
                    this.editrow = row;
                }
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>
<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
