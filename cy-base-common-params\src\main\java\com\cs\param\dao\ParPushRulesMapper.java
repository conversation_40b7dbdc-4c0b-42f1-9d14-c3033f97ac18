package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParPushRulesCommon;
import com.cs.param.model.ParPushRulesModel;

@Repository
public interface ParPushRulesMapper {

	List<ParPushRulesModel> queryPageInfo(ParPushRulesCommon common) throws SQLException;

	Integer queryPageCount(ParPushRulesCommon common) throws SQLException;

}
