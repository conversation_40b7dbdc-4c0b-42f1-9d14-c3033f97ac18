<template scope="scope">
  <div>
    <div class="user-titler">咪咕名片</div>
    <!--广告彩印-->
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="手机号码">
              <el-input v-model="searchReq.mobile" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="姓名">
              <el-input v-model="searchReq.name" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="公司名称">
              <el-input v-model="searchReq.companyName" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column prop="id" label="ID" width="200"></el-table-column>
        <el-table-column prop="mobile" label="手机号码" width="200"></el-table-column>
        <el-table-column prop="name" label="姓名" width="100"></el-table-column>
        <el-table-column prop="companyName" label="公司名称" width="250"></el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="250"></el-table-column>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="goDetail(scope.row)"
            >详情</el-button>
            <el-button
              @click="rejectVisible=true;rowData=scope.row"
              type="text"
              size="small"
            >驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
    <div>
      <el-dialog
        width="30%"
        title="驳回"
        :visible.sync="rejectVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <div>是否驳回该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="rejectVisible = false;reject(rowData)">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from "moment";

export default {
  data() {
    return {
      tableLoading: false,
      //查询条件
      searchReq: {
        mobile: "",
        name: "",
        companyName: "",
        startTime: "",
        endTime: "",
        pageIndex: 1, //页码
        pageSize: 10, //一页的数量
        timearr: []
      },
      //数据表
      tableData: [],
      //操作列表
      rowData: "",
      pageTotal: 0, //总条数
      rejectVisible: false,
      //驳回请求参数
      rejectReq: {
        uuid: "",
        auditBy:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName //操作者
      },
    };
  },
  created() {
    this.search();
  },
  methods: {
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr) {
        this.searchReq.startTime = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYY-MM-DD') : '';
        this.searchReq.endTime = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYY-MM-DD') : '';
      } else {
        this.searchReq.startTime = "";
        this.searchReq.endTime = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      // searchReq.pageIndex = 1;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/get/card/list`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data || [];
            this.pageTotal = res.count;
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    handleSizeChange(val) {
      this.searchReq.pageIndex = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.pageIndex = val;
      this.search();
    },
    reject: function(val) {
      this.rejectReq.uuid = val.uuid;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/card/reject`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("驳回成功");
            this.rejectReq.uuid = "";
            this.search();
          } else {
            this.$message("驳回失败");
            this.rejectReq.uuid = "";
          }
        });
    },
    goDetail(row) {
      this.$router.push({ path: "miguCardDetails", query: { uuid: row.uuid} })
    }
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}

.zzWrap >>> .el-dialog__body {
  text-align: center;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
