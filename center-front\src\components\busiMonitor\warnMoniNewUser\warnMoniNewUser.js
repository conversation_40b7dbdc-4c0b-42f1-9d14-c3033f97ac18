import {warnMoniQuery,warnMoniExport,warnMoniSendSms,warnMoniSendEmail,queryCityList} from './warnMoniNewUserServer.js';
import {dowandFile,formDate} from './../../../util/core.js';
export default {
    data(){
        return{
            //查询条件
            warnMoni:{provinceId:'',cityId:'',type:'date',startDate:new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),endDate:new Date(),selectedOptions:[],page:{pageNo: 1,pageSize: 20,total: 0}},
            pickerOptions: {
                disabledDate: function (today) {
                    return today.getTime() > Date.now();
                }
            },

            //省份列表
            provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
            regionList:new Array(),//城市列表
            //数据列表
            accountPrewarningInfoList:new Array(),
            tableLoading:false,
        }
    },
    mounted(){
       // this.warnMoniSearch(1);
    },
    methods:{
        //判断省份城市等是否填写
        checkedParams(){
            if(this.warnMoni.provinceId===''){
                this.$message({
                    message:'省份不能为空',
                    type:'warning'
                })
                return false;
            }
            return true;
        },
        //查询
        warnMoniSearch(pageNo){
            this.warnMoni.page.pageNo=pageNo;
            if(!this.warnMoni.startDate){
                this.$message('请输入开始时间');
                return;
            }
            if(!this.warnMoni.endDate){
                this.$message('请输入结束时间');
                return;
            }
            if(new Date(this.warnMoni.startDate).getTime()>new Date(this.warnMoni.endDate).getTime()){
                this.$message('结束时间不得小于开始时间');
                return
            }
            let startDate='';
            let endDate='';
            if(this.warnMoni.type=='month'){
                startDate=formDate(this.warnMoni.startDate,'yyyy-MM');
                endDate=formDate(this.warnMoni.endDate,'yyyy-MM');
            }else{
                startDate=formDate(this.warnMoni.startDate,'yyyy-MM-dd');
                endDate=formDate(this.warnMoni.endDate,'yyyy-MM-dd');
            }
            let params={
                startTime:startDate,
                endTime:endDate,
                pageNo:this.warnMoni.page.pageNo,
                pageSize:this.warnMoni.page.pageSize,
                provinceId:this.warnMoni.provinceId,
                countyId:this.warnMoni.cityId
            }
            if(this.checkedParams()){
                warnMoniQuery('accountPrewarning',params).then(res=>{
                    if(res.code===0){
                        this.accountPrewarningInfoList=res.data.accountPrewarningInfoList;
                        this.warnMoni.page.total=res.data.total;
                    }
                })
            }
        },
        //根据省份查询城市列表
        selectProvince(){
            this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.warnMoni.provinceId},{emulateJSON:true})
                        .then((res)=>{
                            this.warnMoni.cityId='';
                            this.regionList=res.data;
            })
        },
        handleSizeChange(pagaeSize){
            this.warnMoni.page.pageSize=pagaeSize;
            this.warnMoniSearch(1);
        },
        //导出excel
        warnMoniDownload(){
            let startDate='';
            let endDate='';
            if(this.warnMoni.type=='month'){
                startDate=formDate(this.warnMoni.startDate,'yyyy-MM');
                endDate=formDate(this.warnMoni.endDate,'yyyy-MM');
            }else{
                startDate=formDate(this.warnMoni.startDate,'yyyy-MM-dd');
                endDate=formDate(this.warnMoni.endDate,'yyyy-MM-dd');
            }
            let params={
                startTime:startDate,
                endTime:endDate,
                pageNo:this.warnMoni.page.pageNo,
                pageSize:this.warnMoni.page.pageSize,
                provinceId:this.warnMoni.provinceId,
                countyId:this.warnMoni.cityId
            }
            if(this.checkedParams()){
                warnMoniExport('accountExportExcel',params).then(res=>{
                    dowandFile(res,'开销户预警.xlsx')
                })
            }
        },
        formatYoy(row, column){
            if(row.openYoy==undefined){
                return "";
            }else{
                return row.openYoy+"%";
            }
        },
        formatOffYoy(row, column){
            if(row.logoffYoy==undefined){
                return "";
            }else{
                return row.logoffYoy+"%";
            }
        },
        //下发短信
        sendSms(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:1};
            warnMoniSendSms('sendSMS',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        },
        //下发邮件
        sendEmail(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:1};
            warnMoniSendEmail('sendEmail',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        }
    },
    filters:{
        status(value){
            if(value==0){
                return '正常';
            }else{
                return '偏低';
            }
        }
    }
}