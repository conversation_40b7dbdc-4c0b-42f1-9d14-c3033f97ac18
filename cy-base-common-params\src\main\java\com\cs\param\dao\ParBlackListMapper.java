package com.cs.param.dao;

import com.cs.param.common.ParBlackListCommon;
import com.cs.param.model.ParBlackListModel;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;

/**
 * 系统黑名单DAO层
 *
 * <AUTHOR>
 * @date 2021/8/2 15:17
 */
@Repository
public interface ParBlackListMapper {


    Integer queryCount(ParBlackListCommon common);

    List<ParBlackListModel> queryPageInfos(ParBlackListCommon common);

    Integer insert(ParBlackListCommon common);

    Integer delBlackList(ParBlackListCommon common);

    Integer updateBlackList(ParBlackListCommon common);

    Integer insertBatch(List<ParBlackListCommon> list) throws SQLException;

    Integer delBatch(@Param("ids") Integer[] ids, @Param("phones") String[] phones);

    List<ParBlackListModel> queryDuplicate(List<ParBlackListCommon> list);
}
