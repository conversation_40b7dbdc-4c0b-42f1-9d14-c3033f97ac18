<template scope="scope">
    <div>
        <div class="user-titler">热线彩印固定模板统计</div>
        <!--企业彩印-->
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="彩印ID">
                            <el-input v-model="searchReq.uuid" class="inputWidth" size="small" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="彩印内容">
                            <el-input v-model="searchReq.subject" class="inputWidth" size="small" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="彩印类型">
                            <el-select
                                    v-model="searchReq.serviceId"
                                    class="inputWidth"
                                    placeholder="请选择"
                                    size="small"
                                    clearable
                            >
                                <el-option
                                        v-for="item in caiyinType"
                                        :key="item.id"
                                        :label="item.name"
                                        :value="item.id"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="创建时间">
                            <div class="block">
                                <el-date-picker
                                        v-model="searchReq.timearr"
                                        type="datetimerange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :default-time="['00:00:00', '23:59:59']"
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        size="small"
                                ></el-date-picker>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="内容编号">
                            <el-input v-model="searchReq.contentHashcode" size="small" class="inputWidth" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item>
                            <el-button type="primary" @click="searchBtn" size="small">查询</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div>
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    :header-cell-class-name="tableheaderClassName"
            >
                <el-table-column prop="uuid" label="彩印ID" width="140"></el-table-column>
                <el-table-column prop="subject" label="彩印内容" width="200"></el-table-column>
                <el-table-column prop="templateArgs" label="变量" width="200"></el-table-column>
                <el-table-column prop="deliverType" label="子业务类型" width="150"></el-table-column>
                <el-table-column prop="caiyinType" label="彩印类型" width="110"></el-table-column>
                <el-table-column prop="corpId" label="企业编号" width="100"></el-table-column>
                <el-table-column prop="corpName" label="企业名称" width="100"></el-table-column>
                <el-table-column prop="corpParentId" label="父企业编号" width="120"></el-table-column>
                <el-table-column prop="corpParentName" label="父企业名称" width="120"></el-table-column>
                <el-table-column prop="deliverTime" label="提交时间" width="200"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="200"></el-table-column>
                <el-table-column
                        prop="contentHashcode"
                        label="内容编号"
                        width="200">
                </el-table-column>
                <el-table-column prop="province" label="省份" width="200"></el-table-column>
                <el-table-column prop="city" label="地市" width="200"></el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="block app-pageganit">
                <el-pagination
                        v-show="pageTotal"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="searchReq.pageNo"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageTotal"
                        style="text-align: right;"
                ></el-pagination>
            </div>
        </div>
    </div>
</template>
<script>
    import moment from 'moment';

    export default {
        data() {
            return {
                tableLoading: false,
                pageTotal: 0, //总条数
                //查询条件
                searchReq: {
                    contentHashcode:"",//内容编号
                    corpId:"",
                    corpName:"",
                    corpParentId:"",
                    corpParentName:"",
                    serviceId: "",
                    uuid: "", //内容id
                    subject: "",
                    timearr: [], //提交时间
                    startTime: "",
                    endTime: "",
                    pageNo: 1, //页码
                    pageSize: 10 ,//一页的数量
                    provinceId:"",
                    cityId:"",
                    deliverType:""
                },
                //数据表
                tableData: [],
                //省份
                provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
                //地市
                city: [],
                caiyinType: [
                    {
                        id: '05100',
                        name: "省内版-主被叫彩印"
                    },
                    {
                        id: '01124',
                        name: "二级企业-主被叫彩印"
                    }
                ]
            };
        },
        created() {
            // this.formatData();
            this.search();
        },
        methods: {
            /*download: function() {
                if (this.searchReq.timearr) {
                    this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
                    this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
                } else {
                    this.searchReq.submitStart = "";
                    this.searchReq.submitEnd = "";
                }
                const { timearr, timearr1, ...searchReq } = this.searchReq;
                postDownload(`${this.proxyUrl}/entContent/corp/hot/content/reviewexportExecl`,searchReq).then(function(res) {
                    dowandFile(res.data,'热线彩印审核记录.xlsx');
                });
            },*/
            searchBtn() {
                this.searchReq.pageNo = 1;
                this.search()
            },
            //查询请求
            search: function() {
                this.tableLoading = true;
                //提交时间
                if (this.searchReq.timearr) {
                    this.searchReq.startTime = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYY-MM-DD HH:mm:ss') : '';
                    this.searchReq.endTime = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYY-MM-DD HH:mm:ss') : '';
                } else {
                    this.searchReq.startTime = "";
                    this.searchReq.endTime = "";
                }
                const { timearr, ...searchReq } = this.searchReq;
                this.$http
                    .post(
                        `${this.proxyUrl}/entContent/fixedTemplate/queryTemplate`,
                        JSON.stringify(searchReq),
                        {
                            headers: {
                                "X-Requested-With": "XMLHttpRequest",
                                contentType: "application/json",
                                charset: "utf-8"
                            },
                            emulateJSON: true,
                            timeout: 5000
                        }
                    )
                    .then(res => {
                        this.tableLoading = false;
                        var res = res.data;
                        if (res.code == 0) {
                            this.tableData = res.data;
                            this.formatData();
                            this.pageTotal = res.count;
                        } else {
                            this.tableData = [];
                            this.formatData();
                            this.pageTotal = 0;
                        }
                    });
            },
            //查询地市
            querySearchRegionList() {
                var queryRegion = {
                    provinceCode: this.searchReq.provinceId
                };
                this.$http
                    .post(`${this.proxyUrl}/param/regionMgt/getRegion`, queryRegion, {
                        emulateJSON: true
                    })
                    .then(function(res) {
                        this.city = res.data;
                        this.searchReq.cityId = "";
                    });
            },
            formatData() {
                this.tableData.forEach(function(val, index) {
                    val.createTime = (val.createTime != null) && moment(val.createTime).format("YYYY-MM-DD HH:mm:ss");
                    // 遍历子业务类型
                    switch (val.deliverType) {
                        case 1:
                            val.deliverType = "0001主叫屏显";
                            break;
                        case 2:
                            val.deliverType = "0001主叫屏显";
                            break;
                        case 3:
                            val.deliverType = "0011 屏显（主被叫）";
                            break;
                        case 4:
                            val.deliverType = "0100挂机短信";
                            break;
                        case 8:
                            val.deliverType = "1000 挂机彩信";
                            break;
                        case 16:
                            val.deliverType = "10000 增强彩信";
                            break;
                        case 7:
                            val.deliverType = "交互USSD";
                            break;
                        default:
                            break;
                    }
                    //遍历彩印类型
                    switch(val.serviceId){
                        case "01120":
                            val.caiyinType = "主叫彩印";
                            break;
                        case "01121":
                            val.caiyinType = "被叫彩印";
                            break;
                        case "01122":
                            val.caiyinType = "挂机短信";
                            break;
                        case "05100":
                            val.caiyinType = "省内版-主被叫彩印";
                            break;
                        case "05104":
                            val.caiyinType = "省内版-主叫彩印";
                            break;
                        case "05105":
                            val.caiyinType = "省内版-被叫彩印";
                            break;
                        case "05101":
                            val.caiyinType = "省内版-挂机短信";
                            break;
                        case "01124":
                            val.caiyinType = "二级企业-主被叫彩印";
                            break;
                        case "01123":
                            val.caiyinType = "二级企业-挂机彩漫";
                            break;
                        case "00120":
                            val.caiyinType = "交互彩印";
                            break;
                        default:
                            break;
                    }
                });
            },
            handleSizeChange(val) {
                this.searchReq.pageNo = 1;
                //每页条数
                this.searchReq.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                //当前页
                this.searchReq.pageNo = val;
                this.search();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }
        }
    };
</script>
<style scoped>
    .inputWidth {
        width: 160px !important;
    }
    .user-titler {
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #d9d9d9;
    }
    .content-title {
        margin-top: 20px;
        margin-left: 20px;
        background-color: white;
    }
    .content-line {
        margin-top: 20px;
    }
    .user-search {
        width: 94%;
        margin: 0 auto;
        margin-top: 20px;
        margin-left: 20px;
    }
    .el-table {
        margin-left: 3%;
        margin-top: 3%;
        border: 1px solid #ecebe9;
    }
</style>
<style>
    .el-table .table-head-th {
        background-color: #f5f5f5;
    }
</style>
