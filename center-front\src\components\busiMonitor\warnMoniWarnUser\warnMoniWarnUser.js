import {warnUserQuery,addWarnUser,updateWarnUser,deleteWarnUser} from './warnMoniWarnUserServer.js';
export default {
    data(){
        return{
            edit:false,
            isAdd:true,
            //省份列表
            provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
            regionList:new Array(),//城市列表
            searchRegionList: [], // 查询表单的城市列表
            userRegionList: [], // 用户表单的城市列表
            deleteListHident:false,//是否删除数据
            searchForm: {
                pageNo: 1,
                pageSize: 100,
                total: 0,
                ex_type: '',
                chargePersonName: '',
                exceptionType:'',
                phone: '',
                provinceId:'',
                cityId:'',
                selectedOptions: [],
            },
            userForm: {
                id:'',
                chargePersonName:'',
                phone: '',
                mail: '',
                provinceId:'',
                cityId:'',
                provinceName: '',
                exceptionType:'',
                selectedOptions:[]
            },
            exceptionTypeList:[
                {
                    key:'1',
                    value:'开销户异常'
                },
                {
                    key:'2',
                    value:'推送量异常'
                },{
                    key:'3',
                    value:'活跃数据异常'
                }
            ],
            tableData:new Array(),
            tableLoading: false,
            rules2:{
                chargePersonName: [
                    { required: true, message: '请输入负责人姓名', trigger: 'blur' },
                    { min: 2, max: 10, message: '长度在 2到 10个字符', trigger: 'blur' }
                ],
                phone:[
                    { required: true, message: '请输入手机号码', trigger: 'blur' },
                    { pattern: "^\\d+$", message: '手机号必须是数字', trigger: 'blur' },
                    { min: 11, max: 11, message: '请输入11位手机号码', trigger: 'blur' }
                ],
                mail:[
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur,change' }
                ],
                selectedOptions:[
                    { required: true, message: '请选择省份地区', trigger: 'change' }
                ],
                exceptionType:[
                    { required: true, message: '请选择异常分类', trigger: 'change' }
                ]
            }
        }
    },
    mounted(){
        //this.search(1);
    },
    methods:{
        //判断省份城市等是否填写
        checkedParams(){
            if(this.searchForm.provinceId===''){
                this.$message({
                    message:'省份不能为空',
                    type:'warning'
                })
                return false;
            }
            return true;
        },
        //查询
        search(pageNo){
            this.searchForm.pageNo=pageNo;
            // this.searchForm.provinceId = "01";
            let params={
                pageNo: this.searchForm.pageNo,
                pageSize: this.searchForm.pageSize,
                phone:this.searchForm.phone,
                chargePersonName:this.searchForm.chargePersonName,
                exceptionType:this.searchForm.exceptionType,
                provinceId:this.searchForm.provinceId,
                cityId:this.searchForm.cityId
            }
            if(this.checkedParams()){
                this.tableLoading=true;
                warnUserQuery('warningPersonList',params).then(res=>{
                    if(res.code===0){
                        this.tableData=res.data.warningPersonList;
                        this.searchForm.total=res.data.total;
                        this.tableLoading=false;
                    }
                })
            }
        },
        //根据每页显示的条数查询数据
        handleSizeChange(pageSize){
            this.searchForm.pageSize=pageSize;
            this.search(1);
        },
        //根据省份查询城市列表
        selectProvince(type){
            let params={};
            if(type==1){
                params={provinceCode:this.searchForm.provinceId};
                this.searchForm.cityId = '';
                this.searchRegionList = [];
            }else{
                params={provinceCode:this.userForm.provinceId};
                this.userForm.cityId = '';
                this.userRegionList = [];
            }

            if (!params.provinceCode) {
                this.regionList = [];
                this.searchRegionList = [];
                this.userRegionList = [];
                return;
            }

            this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,params,{emulateJSON:true})
                .then((res)=>{
                    if (res.data && Array.isArray(res.data)) {
                        if (type == 1) {
                            this.searchRegionList = res.data;
                            this.regionList = this.searchRegionList;
                        } else {
                            this.userRegionList = res.data;
                            this.regionList = this.userRegionList;
                        }
                        // 强制更新视图
                        this.$nextTick(() => {
                            this.$forceUpdate();
                        });
                    }
                })
                .catch(err => {
                    console.error('获取城市列表错误:', err);
                    this.$message.error('获取城市列表失败，请重试');
                });
        },
        //修改
        updateUser(list){
            list.exceptionType = String(list.exceptionType);
            this.userForm=list;
            this.edit=true;
            this.isAdd=false;
        },
        //新增或修改数据
        submitForm(){
            if(this.isAdd){
                // this.userForm.provinceId= "01";
                addWarnUser('addWarningPerson',this.userForm).then(res=>{
                    if(res.code===0){
                        this.$message(res.msg);
                        this.edit=false;
                        this.searchForm.provinceId = this.userForm.provinceId;
                        this.searchForm.chargePersonName = this.userForm.chargePersonName;
                        this.search(1);
                    }
                })
            }else{
                updateWarnUser('updateWarningPerson',this.userForm).then(res=>{
                    if(res.code===0){
                        this.$message(res.msg);
                        this.edit=false;
                        this.search(1);
                    }
                })
            }
        },
        //取消
        cancel(){
            this.edit = false;
            this.isAdd = true;
            // 重置表单验证状态
            if (this.$refs.userForm) {
                this.$refs.userForm.resetFields();
            }
            // 清空城市列表
            this.regionList = [];
            this.userRegionList = [];
            this.searchRegionList = [];
            // 重置表单数据
            this.userForm = {
                id:'',
                chargePersonName:'',
                phone: '',
                mail: '',
                provinceId:'',
                cityId:'',
                provinceName: '',
                exceptionType:'',
                selectedOptions:[]
            };
        },
        //删除
        deleteUser(list){
            deleteWarnUser('delWarningPerson',{warningPersonId:list.warningPersonId}).then(res=>{
                if(res.code===0){
                    this.$message('删除成功');
                    this.search(1);
                }
            })
        }
    }
}
