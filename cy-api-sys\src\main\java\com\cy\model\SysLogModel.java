
package com.cy.model;

import com.github.crab2died.annotation.ExcelField;

public class SysLogModel {
	private Integer sysLogId;
	
	@ExcelField(title = "操作者", order = 1)
	private String sysUseName;
	
	private Integer sysModuleId;
	@ExcelField(title = "操作时间", order = 3)
	private String sysUseTime;
	@ExcelField(title = "操作对象", order = 4)
	private String sysUseObject;
	@ExcelField(title = "操作内容", order = 5)
	private String sysUseContent;
	@ExcelField(title = "操作结果", order = 5)
	private String sysUseResult;
	@ExcelField(title = "IP", order = 5)
	private String sysUseIp;
	private String sysUseType;
	@ExcelField(title = "操作模块", order = 2)
	private String sysUseModule;

	public Integer getSysLogId() {
		return sysLogId;
	}

	public void setSysLogId(Integer sysLogId) {
		this.sysLogId = sysLogId;
	}

	public String getSysUseName() {
		return sysUseName;
	}

	public void setSysUseName(String sysUseName) {
		this.sysUseName = sysUseName;
	}

	public String getSysUseTime() {
		return sysUseTime;
	}

	public void setSysUseTime(String sysUseTime) {
		this.sysUseTime = sysUseTime;
	}

	public String getSysUseObject() {
		return sysUseObject;
	}

	public void setSysUseObject(String sysUseObject) {
		this.sysUseObject = sysUseObject;
	}

	public String getSysUseContent() {
		return sysUseContent;
	}

	public void setSysUseContent(String sysUseContent) {
		this.sysUseContent = sysUseContent;
	}

	public String getSysUseResult() {
		return sysUseResult;
	}

	public void setSysUseResult(String sysUseResult) {
		this.sysUseResult = sysUseResult;
	}

	public String getSysUseIp() {
		return sysUseIp;
	}

	public void setSysUseIp(String sysUseIp) {
		this.sysUseIp = sysUseIp;
	}

	public String getSysUseType() {
		return sysUseType;
	}

	public void setSysUseType(String sysUseType) {
		this.sysUseType = sysUseType;
	}

	public Integer getSysModuleId() {
		return sysModuleId;
	}

	public void setSysModuleId(Integer sysModuleId) {
		this.sysModuleId = sysModuleId;
	}

	public String getSysUseModule() {
		return sysUseModule;
	}

	public void setSysUseModule(String sysUseModule) {
		this.sysUseModule = sysUseModule;
	}

}
