.bodybg {
    width: 100%;
    height: 100%;
    background-size: cover;
    overflow: hidden;
}

h1 {
    font-size: 18px;
    text-indent: 24px;
    font-weight: normal;
}

.app-white {
    background: white;
}

.login-margin {
    width: 100%;
    height: 100vh;
    background-image: url('./../images/bg.png');
    background-size: 100%;
}

.login-warp-box {
    width:100%;
    max-width: 262px !important;
    height: auto !important;
    margin: 0 auto;
    overflow: hidden;
}

.login-wrap {
    width: 392px;
    margin-top: 3%;
}

.loing-form input {
    width: 95%;
    height: 42px !important;
    line-height: 42px !important;
    border-radius: 2px !important;
    margin-bottom: 15px;
    background: #fbfbfb;
    border: #e8e8ee solid 1px;
}
.user-login {
    margin:0;
  }
  .user-login h1{
      font-size: 25px;
      color: white;
      margin-bottom: 14px;
  }
  .user-login .ji<PERSON>yan input {
    width: 166px;
  }
  .yan-img {
    width: 89px;
    max-width: 113.4px !important;
    height: 42px;
    border: 1px solid #eaeaea;
    -webkit-border-radius: 3px;
    border-radius: 3px;
    margin-left: 12px;
    position: absolute;
    top: 0;
    left:158px;
  }
  .yan-img img {
    width: 100%;
    height: 100%;
    vertical-align: middle;
  }
  .login-tip{
      text-align: right;
      margin-bottom: 10px;
  }
  .user-login li {
    position: relative;
    margin-bottom: 14px;
  }
  .user-login li input {
    height: 42px!important;
    line-height: normal!important;
    border-radius: 3px;
    -ms-border-radius: 3px;
    -moz-border-radius: 3px;
    -o-border-radius: 3px;
    -webkit-border-radius: 3px;
    border-bottom: 1px solid #eaeaea;
    background: #fff!important;
    padding-left: 20px;
    width: 100%;
    font-family: "Microsoft YaHei", "微软雅黑", "宋体", "helvetica", "Hiragino Sans GB";
  }
  .user-login li input:focus {
    border: 1px solid #ffd435;
  }
  .user-login li p {
    color: #cc0000;
    text-align: left;
    margin-top: 5px;
  }
.login-restpass {
    width: 95%;
    display: inline-block;
    text-align: right;
}

.main-title {
    height: 36px;
    line-height: 36px;
}

.userpadding {
    padding: 5px 0;
    font-weight: normal;
}

.main-titlebox {
    border-radius: 0px !important;
    border: 1px solid #cccccc;
    height: 216px;
}

.main-titlewarp {
    height: 100px;
    line-height: 50px;
    color: white;
    border-right: #317daf solid 1px;
}
.main-titlewarp center{
    color:#000000
}
.main-titlewarp center p{
    font-size: 45px;
}
.ceshi{
    width:100%;
    height:110px;
    
}
.ceshi ul li{width:20%; 
    height:90px; 
    float:left; 
    list-style-type:none; 
    text-align:center;
    color: #000000;
    margin-left: 20px;
    line-height:80px; 
    border-right:1px solid #cccccc;
}
.ceshi ul li center p{
    color: #000000 !important;
}
.main-grid-title{
    margin-left: 0px !important;
    margin-right: 0px !important;
    height: 195px;
    margin-bottom: 20px !important;
}
.main-titlewarp:nth-last-child(1) {
    border-right: none;
}

.graid-box {
    margin-top: 14px;
    height: 380px !important;
    overflow: hidden;
}

.clear-margin {
    margin-left: 0px !important;
    margin-right: 0px !important;
}

.table-bar {
    background: #f5f9fb;
}

.noscroll div {
    overflow: hidden;
}

.el-pagination__total,
.el-pagination__sizes,
.btn-prev,
.el-pager,
.btn-next {
    float: left !important;
}

.el-pagination__jump {
    float: right !important;
}

.el-scrollbar__wrap {
    overflow-x: hidden !important;
}

.el-table td,
.el-table th.is-leaf {
    border-right: #d5d8de solid 1px;
}

.el-pagination {
    margin-top: 15px;
    margin-left: 0 !important;
    display:inline-block;
}

.user-title {
    margin-left: 0px !important;
}

.user-line {
    width: 100% !important;
    margin: 0 auto;
    margin-top: 3%;
    border-bottom: 1px solid #DDDFE6 !important;
}

.has-gutter {
    color: #333 !important;
}

.el-table th,
.el-table tr {
    background-color: #f5f5f5 !important;
}

.el-checkbox {
    color: #666 !important;
}
.el-checkbox+.el-checkbox {
    margin-left: 20px !important;
    color: #666 !important;
}
.el-form-item{
    margin-bottom: 0px !important;
}
.blue-btn {
    width: 106px !important;
    height: 28px !important;
}
.app-form-item .el-form-item{
    margin-bottom: 20px !important;
}
.el-dialog__header {
    border-bottom: #DDDFE6 solid 1px;
}

.user-search {
    margin-top: 10px;
    margin-bottom: 10px;
}

.user-info-label {
    color: #666 !important;
    font-size: 14px !important;
    font-weight: normal !important;
}

.app-bnt {
    width: 106px;
    height: 32px;
}

.app-search {
    width: 95%;
    margin: 15px auto;
}

.app-input {
    width: 222px !important;
}

.app-input-long {
    width: 400px;
}

.app-tab {
    width: 95% !important;
    margin: 0 auto !important;
}
.app-tab02 {
    width: 100% !important;
    margin: 15px auto !important;
}

.app-pageganit {
    width: 95% !important;
    margin: 0 auto !important;
    text-align: right !important;
}

.app-motitle {
    margin-top: 15px;
}

.app-motitle-box {
    height: 40px !important;
    line-height: 40px !important;
}

.app-motitle-int {
    text-indent: 24px;
}

.app-moselect {
    margin-top: 15px;
    margin-left: 24px;
}

.app-add {
    margin-top: 16px;
}

.app-bottom {
    margin-bottom: 10px;
}

.app-add-left {
    float: left;
    margin-left: 70px;
    margin-top: 20px;
}

.app-norbox {
    width: 95%;
    margin: 15px auto;
}

.app-norable {
    width: 100%;
    margin: 15px auto;
}

.arbthwarp {
    width: 95%;
    height: auto;
    margin: 15px auto;
}

.arbthbox {
    float: left;
    width: 250px;
    height: auto;
}

.arbthsub {
    width: 222px;
    height: 28px;
}

.arbthsub input {
    background: white;
    border: #2196F3 solid 1px;
    border-radius: 4px;
    height: 28px;
    line-height: 28px;
    width: 100%;
    text-indent: 15px;
}

.abrthcup {
    width: 222px;
    height: auto;
    min-height: 253px;
    background: white;
    border: #D9D9D9 solid 1px;
    border-radius: 4px;
    margin-top: 5px;
}

.abrthcup ul li {
    width: 100%;
    height: 32px;
    line-height: 32px;
    text-indent: 15px;
}

.abrthcup ul li:nth-child(odd) {
    background: #F7F7F7;
}

.abrthcup ul li a {
    display: block;
    font-size: 14px;
    color: #666;
}

.abrthcup ul li a:hover {
    background: #e8f4fd;
}

.abrthbtn {
    width: 106px;
    height: 28px;
    background: white;
    border: #D9D9D9 solid 1px;
    border-radius: 4px;
    margin-top: 15px;
}

.abrthbtn a {
    display: block;
    color: #666;
    font-size: 14px;
    line-height: 28px;
    text-indent: 8px;
}

.abrthbtn a:hover {
    color: #2196F3;
}

.abrthopen {
    float: left;
}

.abrthopen span,
.abrthopen i {
    display: inline-block;
}

.abrthopen span {
    padding-bottom: 10px;
}

.abrthopen img {
    margin-top: 5px;
}

.abrthopen a {
    height: 24px;
    line-height: 24px;
    display: block;
    color: #2196F3;
    font-size: 14px;
}

.el-table thead,
.el-table th {
    font-weight: normal !important;
}

.app-input02 {
    width: 130px;
}

.el-col-21 {
    width: 100% !important;
}

.el-table__body td {
    background: white !important;
}