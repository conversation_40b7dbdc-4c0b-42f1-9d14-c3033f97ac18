<template scope="scope">
  <div name="enhanceMassReviewList">
    <!--中央平台审核分类优化——修改增强群发审核记录为企业通知审核记录-->
    <div class="user-titler">企业通知审核记录</div>

    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-form-item label="彩印ID">
          <el-input v-model="searchReq.uuid" class="app-input" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input v-model="searchReq.text" class="app-input" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item label="主题名称">
          <el-input v-model="searchReq.subject" class="app-input" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-select
            v-model="searchReq.status"
            class="app-input"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option
              v-for="item in caiyinType"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-form-item label="企业名称">
          <el-input v-model="searchReq.submitter" class="app-input" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item label="提交时间">
          <div class="block">
            <el-date-picker
              v-model="searchReq.timearr"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="small"
            ></el-date-picker>
          </div>
        </el-form-item>
          <el-form-item label="内容编号">
          <el-input v-model="searchReq.contentID" class="app-input" clearable size="small"></el-input>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-form-item label="审核人">
          <el-input v-model="searchReq.reviewer" class="app-input" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item label="审核时间">
          <div class="block">
            <el-date-picker
              v-model="searchReq.timearr1"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="small"
            ></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchBtn" size="small">查询</el-button>
          <el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
        </el-form-item>
        <br>
        <el-form-item>
          <el-button
            type="primary"
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="passVisible=true;rejectType=2;"
          >批量撤销</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="uuid" label="彩印ID" width="140"></el-table-column>
        <el-table-column prop="text" label="彩印内容" width="140"></el-table-column>

        <!--中央平台审核分类优化-修改增彩内容为内容-->
        <el-table-column label="内容" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="toDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="敏感词"
                         width="200">
          <template slot-scope="scope">
            <div  v-html="scope.row.sensitiveWords"></div>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="驳回原因" width="300"></el-table-column>
        <el-table-column prop="submitter" label="企业名称" width="100"></el-table-column>
         <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)" :style="hasCorpImage(scope.row.corpImage)?'':'color:#808080'">详情</el-button>
          </template>
        </el-table-column>

        <!--中央平台审核分类优化-新增其他资质-->
        <el-table-column label="其他资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)" :style="hasOtherImage(scope.row.otherImage)?'':'color: #808080'">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="submitDate" label="提交时间" width="200"></el-table-column>

        <!--中央平台审核分类优化-新增彩印类型-->
        <el-table-column prop="serviceType" label="彩印类型" width="100"></el-table-column>

        <el-table-column prop="contentID" label="内容编号" width="200"></el-table-column>

        <el-table-column label="异网审核结果" width="400">
          <template slot-scope="scope">
            <div style="white-space:pre-wrap" >{{scope.row.templateIds}}</div>
          </template>
        </el-table-column>

        <!--中央平台审核分类优化-修改主题名称为主题，并放到审核人之前（原来在审核时间之后）-->
        <el-table-column prop="subject" label="主题" width="400"></el-table-column>
        <el-table-column prop="province" label="省份" width="100"></el-table-column>
        <el-table-column prop="city" label="地市" width="100"></el-table-column>
        <el-table-column prop="reviewer" label="审核人" width="100"></el-table-column>
        <el-table-column prop="reviewDate" label="审核时间" width="200"></el-table-column>

        <!--<el-table-column prop="subject" label="主题名称" width="400"></el-table-column>-->
        <el-table-column prop="status" label="审核意见" width="100"></el-table-column>
        <el-table-column prop="telecomReviewOpinion" label="电信审核意见" width="250">
          <template slot-scope="scope">
            <div style="white-space:pre-wrap">{{ scope.row.telecomReviewOpinion }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="unicomReviewOpinion" label="联通审核意见" width="250">
          <template slot-scope="scope">
            <div style="white-space:pre-wrap">{{ scope.row.unicomReviewOpinion }}</div>
          </template>
        </el-table-column>
<!--        <el-table-column fixed="right" label="操作" width="120">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button-->
<!--              type="text"-->
<!--              size="small"-->
<!--              @click="passVisible=true;rowData=scope.row;rejectType=1;"-->
<!--            >撤销</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.p"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>

    <div>
      <el-dialog
        width="30%"
        title="撤销原因"
        :visible.sync="passVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form label-width="80px" justify="center">
          <el-form-item label v-show="radio==1">
            <el-input
              v-model="rejectReq.undoReason"
              type="textarea"
              :rows="2"
              placeholder
              style="width: 250px;"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button
            type="primary"
            size="small"
            @click="passVisible = false;rejectCheck(rowData);"
          >确 定</el-button>
        </div>
      </el-dialog>
        <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
         <img style="width: 100%;" :src="corpImage" alt>
      </el-dialog>

      <!--中央平台审核分类优化-新增其他资质模态框-->
      <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
        <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
          <li >
            <a :href="item" target="_blank">其他资质{{index+1}}</a>
          </li>
        </ul>
      </el-dialog>

    </div>

    <el-dialog
        @open="exportClick"
        title="导出"
        :visible.sync="propVisible"
        :close-on-click-modal="false"
        width="45%">
      <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
        <el-form-item label="文件名" prop="fileName">
          <el-input v-model="addReq.fileName" type="input" size="small"
                    placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字"
                    style="width: 90%;"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256"
                    style="width: 90%;"></el-input>
        </el-form-item>
      </el-form>
      <div style=" margin-left: 80px; color: red;">
        导出后请到系统管理-导出文件下载对应文件
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="confirmExport">确定</el-button>
        <el-button @click="cancelExport">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import {postDownload} from './../../../servers/httpServer.js';
import {dowandFile} from './../../../util/core.js';
import {dealSensitiveWord} from './../../../util/core.js';
import axios from '../../../../node_modules/axios/dist/axios';

export default {
  data() {
    return {
      propVisible: false,
      addReq:{
        fileName: '',
        remark: ''
      },
      rules:{
        fileName: [
          { required: true, message: '请输入文件名', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
          { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
        ],
        remark: [
          { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
        ]
      },
      tableLoading: false,
      //驳回原因
      radio: "1",
      pageTotal: 0, //总条数
      clickoff: true,
      typeoff: "info",
      //类型
      Type: [
        {
          id: "ALL",
          name: "全部"
        },
        {
          id: "ENTERPRISE",
          name: "企业彩印"
        },
        {
          id: "FEINNO",
          name: "新媒彩印"
        }
      ],
      caiyinType: [
        {
          id: 4,
          name: "初审通过"
        },
        {
          id: 5,
          name: "初审驳回"
        }
      ],

      // 中央平台审核分类优化-新增彩印类型初始化变量
      serviceType:[
        {
          id:"01136",
          name:"企业彩印-增彩",
        },
        {
          id:"01144",
          name:"省内版企业彩印-增彩",
        },
        {
          id:"02114",
          name:"新媒彩印-增彩",
        },
        {
          id:"05106",
          name:"数媒-增彩",
        },

      // 新需求
        {
          id:"05299",
          name:"二级企业-增彩群发",
        },
        {
          id:"05300",
          name:"二级企业-屏显群发",
        },
        {
          id:"01150",
          name:"二级企业-挂机短信群发",
        },
        {
          id:"01123",
          name:"二级企业-挂机彩漫群发",
        }
      ],
      corpImageVisible: false,
      corpImage: "", //企业资质

      otherImageVisible: false,
      otherImage: [], //其他资质

      //查询条件
      searchReq: {
        contentID:"",//内容编号
        serviceId: "",
        subject: "",
        uuid: "", //内容id
        status: "", //  1驳回，2通过
        reviewer: "", //审核人
        submitter: "", //企业名称
        submitStart: "", //提交时间
        submitEnd: "",
        timearr: [], //提交时间
        timearr1: [], //审核时间
        reviewStart: "", //审核时间
        reviewEnd: "",
        text:"",
        pz: 10,
        p: 1
      },
      //数据表
      tableData: [],
      //操作列表
      rowData: "",
      checked: [],
      passVisible: false,
      //撤销单或批量
      rejectType: 0,
      multipleSelection: [],
      //撤销请求参数
      rejectReq: {
        corpInfoId: "", //id
        corpInfoIds: [], //多id
        undoReason: "",
        reviewer: JSON.parse(sessionStorage.getItem("userInfo")).sysUserName, //操作者
      }
    };
  },

  created() {
    this.search();
  },

  watch: {
    "rejectReq.corpInfoIds"() {
      if (this.rejectReq.corpInfoIds.length) {
        this.clickoff = false;
        this.typeoff = "primary";
      } else {
        this.clickoff = true;
        this.typeoff = "info";
      }
    },
    passVisible() {
      if (!this.passVisible) {
        this.rejectReq.undoReason = "";
      }
    }
  },

  methods: {
  	tableRowClassName({row, rowIndex}) {
		const writeList = ['111','101','110','011','001','010']
	    if(writeList.includes(row.platforms)){
	    	return 'success-row'
	    }
	    return '';
	  },
     showCorpImage(corpImage) {
      console.log(corpImage);
      this.corpImage = corpImage;
      this.corpImageVisible = true;
    },

    // 中央平台审核分类优化-新增其他资质函数
    showOtherImage(otherImage) {
      console.log(otherImage);
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    // 判断企业资质和其他资质是否有值，没有则字体颜色变成灰色
    hasCorpImage(corpImage){
      return !(corpImage == null || corpImage == "");
    },
    hasOtherImage(otherImage){
       return !(otherImage == null || otherImage.length == 0);
    },

    //多选框
    handleSelectionChange(val) {
      this.rejectReq.corpInfoIds = [];
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.corpInfoIds.push(val[i].id);
      }
    },
    exportClick(){
      this.$refs.addReqForm.resetFields();
    },
    confirmExport() {
      this.$refs.addReqForm.validate(valid => {
        if (valid) {
          this.propVisible = !this.propVisible;

          if (this.searchReq.timearr) {
            this.searchReq.submitStart = this.searchReq.timearr[0];
            this.searchReq.submitEnd = this.searchReq.timearr[1];
          } else {
            this.searchReq.submitStart = "";
            this.searchReq.submitEnd = "";
          }
          //审核时间
          if (this.searchReq.timearr1) {
            this.searchReq.reviewStart = this.searchReq.timearr1[0];
            this.searchReq.reviewEnd = this.searchReq.timearr1[1];
          } else {
            this.searchReq.reviewStart = "";
            this.searchReq.reviewEnd = "";
          }

          const vm = this;
          var req = {
            fileName: this.addReq.fileName,
            remark: this.addReq.remark,
            taskType: 7,
            params: JSON.stringify(this.searchReq)
          }
          axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then(function (res) {

            let data = res.data;
            if (data.code == 0) {
              vm.$message.success("系统将生成文件名为" + vm.addReq.fileName + "的文件");
            } else {
              vm.$message.error(data.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    cancelExport() {
      this.propVisible = !this.propVisible;
      this.$refs.addReqForm.resetFields();
    },
    searchBtn() {
      this.searchReq.p = 1;
      this.search()
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      //提交时间
      if (this.searchReq.timearr) {
        this.searchReq.submitStart = this.searchReq.timearr[0];
        this.searchReq.submitEnd = this.searchReq.timearr[1];
      } else {
        this.searchReq.submitStart = "";
        this.searchReq.submitEnd = "";
      }
      //审核时间
      if (this.searchReq.timearr1) {
        this.searchReq.reviewStart = this.searchReq.timearr1[0];
        this.searchReq.reviewEnd = this.searchReq.timearr1[1];
      } else {
        this.searchReq.reviewStart = "";
        this.searchReq.reviewEnd = "";
      }
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hangup/supper/review`,
          JSON.stringify(this.searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            // 测试数据
            const testData = [
              // {
              //   id: 0,
              //   hangupContentId: 22,
              //   uuid: "111x",
              //   subject: "名称",
              //   submitter: "张三",
              //   submitDate: 1550563601000,
              //   reviewer: "老大",
              //   reviewDate: 1550563601000,
              //   status: 1,
              //   reason: 3,
              //   serviceId: "0100021",
              //   frames: [
              //     {
              //       id: 24,
              //       seqNo: 1,
              //       text: "中国人民解放军s",
              //       image: "/data/uploadFile/image/200007/1552026296389.jpg",
              //       txtLocalPath:
              //         "02102_3_2019030814275932489/20190219164448/1.txt",
              //       imageLocalPath:
              //         "http://pic29.photophoto.cn/20131008/0020033001098472_b.jpg",
              //       video: "",
              //       videoLocalPath: "http://www.w3school.com.cn/i/movie.ogg"
              //     },
              //     {
              //       id: 25,
              //       seqNo: 2,
              //       text: "中国人",
              //       image: null,
              //       txtLocalPath:
              //         "02102_3_2019030814275932489/20190219164448/2.txt",
              //       imageLocalPath: "http://pic29.photophoto.cn/20131008/0020033001098472_b.jpg",
              //       video: "",
              //       videoLocalPath: "http://www.w3school.com.cn/i/movie.ogg"
              //     }
              //   ]
              // }
            ];
            this.tableData = res.data || testData;
            this.tableData &&
              this.tableData.forEach(val => {
                switch (val.status) {
                  case 0:
                    val.status = "未审核";
                    break;
                  case 1:
                    val.status = "驳回";
                    break;
                  case 2:
                    val.status = "已通过";
                    break;
                  case 3:
                    val.status = "已撤销";
                    break;
                  case 4:
                    val.status = "初审通过";
                    break;
                  case 5:
                    val.status = "初审驳回";
                    break;
                  default:
                    break;
                }

                switch (val.reason) {
                  case 1:
                    val.reason = "无效";
                    break;
                  case 2:
                    val.reason = "无企业签名";
                    break;
                  case 3:
                    val.reason =
                      "企业签名不准确，请使用营业执照上的企业名称作为企业签名";
                    break;
                  case 4:
                    val.reason = "企业签名不完整，请完善签名保证签名的唯一性";
                    break;
                  case 5:
                    val.reason = "不符合主叫规则";
                    break;
                  case 6:
                    val.reason = "主叫仅做身份识别，不得加载商业信息";
                    break;
                  case 7:
                    val.reason = "语境不符合主叫场景";
                    break;
                  case 8:
                    val.reason = "语境不符合被叫场景";
                    break;
                  case 9:
                    val.reason = "不得含有变量";
                    break;
                  case 10:
                    val.reason = "禁带网址链接";
                    break;
                  case 11:
                    val.reason = "不得带有保证性词语";
                    break;
                  case 12:
                    val.reason = "所述经营范围与营业执照不一致";
                    break;
                  case 13:
                    val.reason =
                      "彩印内容不能出现具体金额、折扣价、满减价、百分比等此类价格类描述";
                    break;
                  case 14:
                    val.reason =
                      "因涉及敏感行业，请提供经营资质至************予以审核";
                    break;
                  case 15:
                    val.reason = "无标点符号";
                    break;
                  case 16:
                    val.reason = "标点使用有误";
                    break;
                  default:
                    break;
                }

                //修改4-新增彩印类型判断函数
                switch (val.serviceId) {
                  case "01136":
                    val.serviceType = "企业彩印-增彩";
                    break;
                  case "01144":
                    val.serviceType = "省内版企业彩印-增彩";
                    break;
                  case "02114":
                    val.serviceType = "新媒彩印-增彩";
                    break;
                  case "05106":
                    val.serviceType = "数媒-增彩";
                    break;
                  case "05299":
                    val.serviceType = "二级企业-增彩群发";
                    break;
                  case "05300":
                    val.serviceType = "二级企业-屏显群发";
                    break;
                  case "01150":
                    val.serviceType = "二级企业-挂机短信群发";
                    break;
                  case "01123":
                    val.serviceType = "二级企业-挂机彩漫群发";
                    break;
                  default:
                    val.serviceType = "";
                    break;
                }
                val.submitDate = (val.submitDate != null) && moment(val.submitDate).format("YYYY-MM-DD HH:mm:ss");
                val.reviewDate = (val.reviewDate != null) && moment(val.reviewDate).format("YYYY-MM-DD HH:mm:ss");

                let sensitiveWords = "";
                sensitiveWords = dealSensitiveWord(sensitiveWords,val,"03");
                sensitiveWords = dealSensitiveWord(sensitiveWords,val,"02");
                sensitiveWords = dealSensitiveWord(sensitiveWords,val,"01");

                val.sensitiveWords = sensitiveWords;


              });
            this.pageTotal = res.count;
          }
        });
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val) {
      //1为单，2为多
      if (this.rejectType == 1) {
        this.reject(val);
      } else if (this.rejectType == 2) {
        this.rejectlist();
      }
    },
    //撤销请求---单
    reject: function(val) {
      if (!this.rejectReq.undoReason) {
        this.$message.error("请填写撤销原因");
        this.passVisible = true;
        return false;
      }
      this.rejectReq.corpInfoId = val.id;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/undo`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("撤销成功");
            this.search(this.searchReq);
            this.rejectReq.corpInfoId = "";
          } else {
            this.$message("撤销失败");
            this.rejectReq.corpInfoId = "";
          }
        });
    },
    //撤销请求---多
    rejectlist: function() {
      if (!this.rejectReq.undoReason) {
        this.$message.error("请填写撤销原因");
        this.passVisible = true;
        return false;
      }
      if (!this.rejectReq.corpInfoIds.length > 0) {
        this.$message.error("请选择批量撤销的内容");
        return false;
      }
      this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/undo/batch`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("撤销成功");
            this.search(this.searchReq);
            this.rejectReq.corpInfoIds.length = 0;
          } else {
            this.$message("撤销失败");
            this.rejectReq.corpInfoIds.length = 0;
          }
        });
    },
    handleSizeChange(val) {
      this.searchReq.p = 1;
      //每页条数
      this.searchReq.pz = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.p = val;
      this.search();
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },

    // 跳转详情页
    toDetail(row) {
      sessionStorage.setItem("massDetail", row && JSON.stringify(row));
      this.$router.push("massDetail");
    },
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
.el-table .success-row td{
  background: #c5e0b3 !important;
}
</style>
