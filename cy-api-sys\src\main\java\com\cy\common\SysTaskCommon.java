
package com.cy.common;

public class SysTaskCommon {
	private Integer sysTaskId;
	private String sysTaskName;
	private String sysTaskState;
	private String sysTriggerPeople;
	private String sysTiggerTime;
	private String sysStartTime;
	private String sysTaskProgress;
	private String sysRemainingTime;
	private String sysTaskDesc;
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private String startTime;// 开始时间
	private String endTime;// 结束时间

	public Integer getSysTaskId() {
		return sysTaskId;
	}

	public void setSysTaskId(Integer sysTaskId) {
		this.sysTaskId = sysTaskId;
	}

	public String getSysTaskName() {
		return sysTaskName;
	}

	public void setSysTaskName(String sysTaskName) {
		this.sysTaskName = sysTaskName;
	}

	public String getSysTaskState() {
		return sysTaskState;
	}

	public void setSysTaskState(String sysTaskState) {
		this.sysTaskState = sysTaskState;
	}

	public String getSysTriggerPeople() {
		return sysTriggerPeople;
	}

	public void setSysTriggerPeople(String sysTriggerPeople) {
		this.sysTriggerPeople = sysTriggerPeople;
	}

	public String getSysTiggerTime() {
		return sysTiggerTime;
	}

	public void setSysTiggerTime(String sysTiggerTime) {
		this.sysTiggerTime = sysTiggerTime;
	}

	public String getSysStartTime() {
		return sysStartTime;
	}

	public void setSysStartTime(String sysStartTime) {
		this.sysStartTime = sysStartTime;
	}

	public String getSysTaskProgress() {
		return sysTaskProgress;
	}

	public void setSysTaskProgress(String sysTaskProgress) {
		this.sysTaskProgress = sysTaskProgress;
	}

	public String getSysTaskDesc() {
		return sysTaskDesc;
	}

	public void setSysTaskDesc(String sysTaskDesc) {
		this.sysTaskDesc = sysTaskDesc;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getSysRemainingTime() {
		return sysRemainingTime;
	}

	public void setSysRemainingTime(String sysRemainingTime) {
		this.sysRemainingTime = sysRemainingTime;
	}

	@Override
	public String toString() {
		return "SysTaskCommon [sysTaskId=" + sysTaskId + ", sysTaskName=" + sysTaskName + ", sysTaskState="
				+ sysTaskState + ", sysTriggerPeople=" + sysTriggerPeople + ", sysTiggerTime=" + sysTiggerTime
				+ ", sysStartTime=" + sysStartTime + ", sysTaskProgress=" + sysTaskProgress
				+ ", sysRemainingTime=" + sysRemainingTime + ", sysTaskDesc=" + sysTaskDesc + ", pageSize="
				+ pageSize + ", pageNum=" + pageNum + ", startTime=" + startTime + ", endTime=" + endTime
				+ "]";
	}

}
