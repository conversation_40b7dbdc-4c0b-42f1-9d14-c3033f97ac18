package com.cy.content.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


public class ContentCommon implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 彩印id
	 */
	private String csId;
	/**
	 * 用户id，手机号码
	 */
	private String phone;
	/**
	 * 状态
	 */
	private String status;
	
	private int csStatus;
	
	private int auditStatus;
	/**
	 * 彩印来源
	 */
	private int csSource;
	/**
	 * 彩印内容
	 */
	private String csContent;
	/**
	 * 彩印编号
	 */
	private String csContentNo;
	/**
	 * 彩印来源：0：其他 1：WEB 2：WAP 3：客户端 4：微信
	 */
	private int csSubmitType;
	/**
	 * 内容说明或描述
	 */
	private String csRemark;
	/**
	 * 起始时间
	 */
	private String startTime;
	/**
	 * 结束时间
	 */
	private String endTime;
	/**
	 * 彩印类别名称
	 */
	private String csGroupName;
	/**
	 * 彩印类别ID
	 */
	private String csGroupId;
	/**
	 * 彩印类别名称
	 */
	private String csLabelName;
	/**
	 * 彩印类别ID
	 */
	private String csLabelId;
	/**
	 * 彩印编号集合
	 */
	private List<String> csContentNos;
	
	private String csSubmitUser;
	private String csSubmitTime;
	
	/**
	 * 彩印类型：1用户DIY,2个人彩印，3彩印盒
	 */
	private String csType;
	/**
	 * （彩印类型：彩印编号）
	 */
	private Map<String,Integer> csNoCsType;
	
	private List<CsOffModel> csOffModels;
	
	private CsOffModel csOffModel;
	
	private int pageNum = 1;// 查询的页码
	
	private int pageSize = 20;// 每页显示条数
	
	private int startNum;//起始查询行
	
	private String auditor;
	private String auditorTime;
	private String auditStartTime;
	private String auditEndTime;
	
	/**
	 * 彩印盒唯一编号
	 */
	private String csPackageNo;
	/**
	 * 彩印盒名称
	 */
	private String csPackageName;
	/**
	 * 子业务编码
	 */
	private String csPkgServiceCode;
	/**
	 * 彩印盒ID
	 */
	private String csPackageId;
	/**
	 * 分类id
	 */
	private String csPkgGroupId;
	/**
	 * 标签id
	 */
	private String csPkgLabelId;
	/**
	 * 彩印盒状态
	 * 0：待上架
		1：上架
		2：删除
		3：下架
	 */
	private String csPackageStatus;

	/**
	 * 来自接口调用查询
	 */
	private boolean isInterface;
	/**
	 * 彩印盒里的彩印内容
	 */
	private String  csPkgContent1,csPkgContent2,csPkgContent3,csPkgContent4,csPkgContent5,csPkgContent6,csPkgContent7,csPkgContent8,csPkgContent9,csPkgContent10,
                	csPkgContent11,csPkgContent12,csPkgContent13,csPkgContent14,csPkgContent15,csPkgContent16,csPkgContent17,csPkgContent18,csPkgContent19,csPkgContent20,
                	csPkgContent21,csPkgContent22,csPkgContent23,csPkgContent24,csPkgContent25,csPkgContent26,csPkgContent27,csPkgContent28,csPkgContent29,csPkgContent30;
	
	private String  csPkgId1,csPkgId2,csPkgId3,csPkgId4,csPkgId5,csPkgId6,csPkgId7,csPkgId8,csPkgId9,csPkgId10,
                	csPkgId11,csPkgId12,csPkgId13,csPkgId14,csPkgId15,csPkgId16,csPkgId17,csPkgId18,csPkgId19,csPkgId20,
                	csPkgId21,csPkgId22,csPkgId23,csPkgId24,csPkgId25,csPkgId26,csPkgId27,csPkgId28,csPkgId29,csPkgId30;
	private Integer serviceType;
	public String getCsPkgServiceCode() {
	    return csPkgServiceCode;
	}
	public void setCsPkgServiceCode(String csPkgServiceCode) {
	    this.csPkgServiceCode = csPkgServiceCode;
	}
	public void setAuditEndTime(String auditEndTime) {
	    this.auditEndTime = auditEndTime;
	}
	public String getCsSubmitTime() {
	    return csSubmitTime;
	}
	public void setCsSubmitTime(String csSubmitTime) {
	    this.csSubmitTime = csSubmitTime;
	}
	public String getAuditor() {
	    return auditor;
	}
	public void setAuditor(String auditor) {
	    this.auditor = auditor;
	}
	public String getAuditorTime() {
	    return auditorTime;
	}
	public void setAuditorTime(String auditorTime) {
	    this.auditorTime = auditorTime;
	}
	public String getCsSubmitUser() {
	    return csSubmitUser;
	}
	public void setCsSubmitUser(String csSubmitUser) {
	    this.csSubmitUser = csSubmitUser;
	}
	private List<String> pkgIds;
	

	public List<CsOffModel> getCsOffModels() {
		return csOffModels;
	}
	public void setCsOffModels(List<CsOffModel> csOffModels) {
		this.csOffModels = csOffModels;
	}
	public CsOffModel getCsOffModel() {
		return csOffModel;
	}
	public void setCsOffModel(CsOffModel csOffModel) {
		this.csOffModel = csOffModel;
	}
	public String getCsId() {
		return csId;
	}
	public void setCsId(String csId) {
		this.csId = csId;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public String getCsContent() {
		return csContent;
	}
	public void setCsContent(String csContent) {
		this.csContent = csContent;
	}
	public int getAuditStatus() {
		return auditStatus;
	}
	public void setAuditStatus(int auditStatus) {
		this.auditStatus = auditStatus;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getCsGroupName() {
		return csGroupName;
	}
	public void setCsGroupName(String csGroupName) {
		this.csGroupName = csGroupName;
	}
	public String getCsGroupId() {
		return csGroupId;
	}
	public void setCsGroupId(String csGroupId) {
		this.csGroupId = csGroupId;
	}
	public String getCsLabelName() {
		return csLabelName;
	}
	public void setCsLabelName(String csLabelName) {
		this.csLabelName = csLabelName;
	}
	
	public String getCsLabelId() {
		return csLabelId;
	}
	public void setCsLabelId(String csLabelId) {
		this.csLabelId = csLabelId;
	}
	public String getCsContentNo() {
		return csContentNo;
	}
	public void setCsContentNo(String csContentNo) {
		this.csContentNo = csContentNo;
	}
	public int getPageNum() {
		return pageNum;
	}
	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}
	public int getPageSize() {
		return pageSize;
	}
	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	public int getStartNum() {
		return startNum;
	}
	public void setStartNum(int startNum) {
		this.startNum = startNum;
	}
	public void setStartPage(){
	    this.startNum = (pageNum-1)*pageSize;
	}
	public List<String> getCsContentNos() {
		return csContentNos;
	}
	public void setCsContentNos(List<String> csContentNos) {
		this.csContentNos = csContentNos;
	}
	public String getCsType() {
		return csType;
	}
	public void setCsType(String csType) {
		this.csType = csType;
	}
	
	public Map<String, Integer> getCsNoCsType() {
		return csNoCsType;
	}
	public void setCsNoCsType(Map<String, Integer> csNoCsType) {
		this.csNoCsType = csNoCsType;
	}
	public int getCsSubmitType() {
		return csSubmitType;
	}
	public void setCsSubmitType(int csSubmitType) {
		this.csSubmitType = csSubmitType;
	}
	public String getCsRemark() {
		return csRemark;
	}
	public void setCsRemark(String csRemark) {
		this.csRemark = csRemark;
	}
	public int getCsStatus() {
		return csStatus;
	}
	public void setCsStatus(int csStatus) {
		this.csStatus = csStatus;
	}
	public int getCsSource() {
		return csSource;
	}
	public void setCsSource(int csSource) {
		this.csSource = csSource;
	}
	public String getAuditStartTime() {
	    return auditStartTime;
	}
	public void setAuditStartTime(String auditStartTime) {
	    this.auditStartTime = auditStartTime;
	}
	public String getAuditEndTime() {
	    return auditEndTime;
	}
	public void setAuditEndTtime(String auditEndTime) {
	    this.auditEndTime = auditEndTime;
	}
	public String getCsPackageNo() {
		return csPackageNo;
	}
	public void setCsPackageNo(String csPackageNo) {
		this.csPackageNo = csPackageNo;
	}
	public String getCsPackageName() {
		return csPackageName;
	}
	public void setCsPackageName(String csPackageName) {
		this.csPackageName = csPackageName;
	}
	public String getCsPackageId() {
		return csPackageId;
	}
	public void setCsPackageId(String csPackageId) {
		this.csPackageId = csPackageId;
	}
	public String getCsPkgGroupId() {
		return csPkgGroupId;
	}
	public void setCsPkgGroupId(String csPkgGroupId) {
		this.csPkgGroupId = csPkgGroupId;
	}
	public String getCsPkgLabelId() {
		return csPkgLabelId;
	}
	public void setCsPkgLabelId(String csPkgLabelId) {
		this.csPkgLabelId = csPkgLabelId;
	}
	public String getCsPackageStatus() {
		return csPackageStatus;
	}
	public void setCsPackageStatus(String csPackageStatus) {
		this.csPackageStatus = csPackageStatus;
	}
	public String getCsPkgContent1() {
		return csPkgContent1;
	}
	public void setCsPkgContent1(String csPkgContent1) {
		this.csPkgContent1 = csPkgContent1;
	}
	public String getCsPkgContent2() {
		return csPkgContent2;
	}
	public void setCsPkgContent2(String csPkgContent2) {
		this.csPkgContent2 = csPkgContent2;
	}
	public String getCsPkgContent3() {
		return csPkgContent3;
	}
	public void setCsPkgContent3(String csPkgContent3) {
		this.csPkgContent3 = csPkgContent3;
	}
	public String getCsPkgContent4() {
		return csPkgContent4;
	}
	public void setCsPkgContent4(String csPkgContent4) {
		this.csPkgContent4 = csPkgContent4;
	}
	public String getCsPkgContent5() {
		return csPkgContent5;
	}
	public void setCsPkgContent5(String csPkgContent5) {
		this.csPkgContent5 = csPkgContent5;
	}
	public List<String> getPkgIds() {
		return pkgIds;
	}
	public void setPkgIds(List<String> pkgIds) {
		this.pkgIds = pkgIds;
	}
	
	public String getCsPkgContent6() {
	    return csPkgContent6;
	}
	public void setCsPkgContent6(String csPkgContent6) {
	    this.csPkgContent6 = csPkgContent6;
	}
	public String getCsPkgContent7() {
	    return csPkgContent7;
	}
	public void setCsPkgContent7(String csPkgContent7) {
	    this.csPkgContent7 = csPkgContent7;
	}
	public String getCsPkgContent8() {
	    return csPkgContent8;
	}
	public void setCsPkgContent8(String csPkgContent8) {
	    this.csPkgContent8 = csPkgContent8;
	}
	public String getCsPkgContent9() {
	    return csPkgContent9;
	}
	public void setCsPkgContent9(String csPkgContent9) {
	    this.csPkgContent9 = csPkgContent9;
	}
	public String getCsPkgContent10() {
	    return csPkgContent10;
	}
	public void setCsPkgContent10(String csPkgContent10) {
	    this.csPkgContent10 = csPkgContent10;
	}
	public String getCsPkgContent11() {
	    return csPkgContent11;
	}
	public void setCsPkgContent11(String csPkgContent11) {
	    this.csPkgContent11 = csPkgContent11;
	}
	public String getCsPkgContent12() {
	    return csPkgContent12;
	}
	public void setCsPkgContent12(String csPkgContent12) {
	    this.csPkgContent12 = csPkgContent12;
	}
	public String getCsPkgContent13() {
	    return csPkgContent13;
	}
	public void setCsPkgContent13(String csPkgContent13) {
	    this.csPkgContent13 = csPkgContent13;
	}
	public String getCsPkgContent14() {
	    return csPkgContent14;
	}
	public void setCsPkgContent14(String csPkgContent14) {
	    this.csPkgContent14 = csPkgContent14;
	}
	public String getCsPkgContent15() {
	    return csPkgContent15;
	}
	public void setCsPkgContent15(String csPkgContent15) {
	    this.csPkgContent15 = csPkgContent15;
	}
	public String getCsPkgContent16() {
	    return csPkgContent16;
	}
	public void setCsPkgContent16(String csPkgContent16) {
	    this.csPkgContent16 = csPkgContent16;
	}
	public String getCsPkgContent17() {
	    return csPkgContent17;
	}
	public void setCsPkgContent17(String csPkgContent17) {
	    this.csPkgContent17 = csPkgContent17;
	}
	public String getCsPkgContent18() {
	    return csPkgContent18;
	}
	public void setCsPkgContent18(String csPkgContent18) {
	    this.csPkgContent18 = csPkgContent18;
	}
	public String getCsPkgContent19() {
	    return csPkgContent19;
	}
	public void setCsPkgContent19(String csPkgContent19) {
	    this.csPkgContent19 = csPkgContent19;
	}
	public String getCsPkgContent20() {
	    return csPkgContent20;
	}
	public void setCsPkgContent20(String csPkgContent20) {
	    this.csPkgContent20 = csPkgContent20;
	}
	public String getCsPkgContent21() {
	    return csPkgContent21;
	}
	public void setCsPkgContent21(String csPkgContent21) {
	    this.csPkgContent21 = csPkgContent21;
	}
	public String getCsPkgContent22() {
	    return csPkgContent22;
	}
	public void setCsPkgContent22(String csPkgContent22) {
	    this.csPkgContent22 = csPkgContent22;
	}
	public String getCsPkgContent23() {
	    return csPkgContent23;
	}
	public void setCsPkgContent23(String csPkgContent23) {
	    this.csPkgContent23 = csPkgContent23;
	}
	public String getCsPkgContent24() {
	    return csPkgContent24;
	}
	public void setCsPkgContent24(String csPkgContent24) {
	    this.csPkgContent24 = csPkgContent24;
	}
	public String getCsPkgContent25() {
	    return csPkgContent25;
	}
	public void setCsPkgContent25(String csPkgContent25) {
	    this.csPkgContent25 = csPkgContent25;
	}
	public String getCsPkgContent26() {
	    return csPkgContent26;
	}
	public void setCsPkgContent26(String csPkgContent26) {
	    this.csPkgContent26 = csPkgContent26;
	}
	public String getCsPkgContent27() {
	    return csPkgContent27;
	}
	public void setCsPkgContent27(String csPkgContent27) {
	    this.csPkgContent27 = csPkgContent27;
	}
	public String getCsPkgContent28() {
	    return csPkgContent28;
	}
	public void setCsPkgContent28(String csPkgContent28) {
	    this.csPkgContent28 = csPkgContent28;
	}
	public String getCsPkgContent29() {
	    return csPkgContent29;
	}
	public void setCsPkgContent29(String csPkgContent29) {
	    this.csPkgContent29 = csPkgContent29;
	}
	public String getCsPkgContent30() {
	    return csPkgContent30;
	}
	public void setCsPkgContent30(String csPkgContent30) {
	    this.csPkgContent30 = csPkgContent30;
	}
	public String getCsPkgId1() {
	    return csPkgId1;
	}
	public void setCsPkgId1(String csPkgId1) {
	    this.csPkgId1 = csPkgId1;
	}
	public String getCsPkgId2() {
	    return csPkgId2;
	}
	public void setCsPkgId2(String csPkgId2) {
	    this.csPkgId2 = csPkgId2;
	}
	public String getCsPkgId3() {
	    return csPkgId3;
	}
	public void setCsPkgId3(String csPkgId3) {
	    this.csPkgId3 = csPkgId3;
	}
	public String getCsPkgId4() {
	    return csPkgId4;
	}
	public void setCsPkgId4(String csPkgId4) {
	    this.csPkgId4 = csPkgId4;
	}
	public String getCsPkgId5() {
	    return csPkgId5;
	}
	public void setCsPkgId5(String csPkgId5) {
	    this.csPkgId5 = csPkgId5;
	}
	public String getCsPkgId6() {
	    return csPkgId6;
	}
	public void setCsPkgId6(String csPkgId6) {
	    this.csPkgId6 = csPkgId6;
	}
	public String getCsPkgId7() {
	    return csPkgId7;
	}
	public void setCsPkgId7(String csPkgId7) {
	    this.csPkgId7 = csPkgId7;
	}
	public String getCsPkgId8() {
	    return csPkgId8;
	}
	public void setCsPkgId8(String csPkgId8) {
	    this.csPkgId8 = csPkgId8;
	}
	public String getCsPkgId9() {
	    return csPkgId9;
	}
	public void setCsPkgId9(String csPkgId9) {
	    this.csPkgId9 = csPkgId9;
	}
	public String getCsPkgId10() {
	    return csPkgId10;
	}
	public void setCsPkgId10(String csPkgId10) {
	    this.csPkgId10 = csPkgId10;
	}
	public String getCsPkgId11() {
	    return csPkgId11;
	}
	public void setCsPkgId11(String csPkgId11) {
	    this.csPkgId11 = csPkgId11;
	}
	public String getCsPkgId12() {
	    return csPkgId12;
	}
	public void setCsPkgId12(String csPkgId12) {
	    this.csPkgId12 = csPkgId12;
	}
	public String getCsPkgId13() {
	    return csPkgId13;
	}
	public void setCsPkgId13(String csPkgId13) {
	    this.csPkgId13 = csPkgId13;
	}
	public String getCsPkgId14() {
	    return csPkgId14;
	}
	public void setCsPkgId14(String csPkgId14) {
	    this.csPkgId14 = csPkgId14;
	}
	public String getCsPkgId15() {
	    return csPkgId15;
	}
	public void setCsPkgId15(String csPkgId15) {
	    this.csPkgId15 = csPkgId15;
	}
	public String getCsPkgId16() {
	    return csPkgId16;
	}
	public void setCsPkgId16(String csPkgId16) {
	    this.csPkgId16 = csPkgId16;
	}
	public String getCsPkgId17() {
	    return csPkgId17;
	}
	public void setCsPkgId17(String csPkgId17) {
	    this.csPkgId17 = csPkgId17;
	}
	public String getCsPkgId18() {
	    return csPkgId18;
	}
	public void setCsPkgId18(String csPkgId18) {
	    this.csPkgId18 = csPkgId18;
	}
	public String getCsPkgId19() {
	    return csPkgId19;
	}
	public void setCsPkgId19(String csPkgId19) {
	    this.csPkgId19 = csPkgId19;
	}
	public String getCsPkgId20() {
	    return csPkgId20;
	}
	public void setCsPkgId20(String csPkgId20) {
	    this.csPkgId20 = csPkgId20;
	}
	public String getCsPkgId21() {
	    return csPkgId21;
	}
	public void setCsPkgId21(String csPkgId21) {
	    this.csPkgId21 = csPkgId21;
	}
	public String getCsPkgId22() {
	    return csPkgId22;
	}
	public void setCsPkgId22(String csPkgId22) {
	    this.csPkgId22 = csPkgId22;
	}
	public String getCsPkgId23() {
	    return csPkgId23;
	}
	public void setCsPkgId23(String csPkgId23) {
	    this.csPkgId23 = csPkgId23;
	}
	public String getCsPkgId24() {
	    return csPkgId24;
	}
	public void setCsPkgId24(String csPkgId24) {
	    this.csPkgId24 = csPkgId24;
	}
	public String getCsPkgId25() {
	    return csPkgId25;
	}
	public void setCsPkgId25(String csPkgId25) {
	    this.csPkgId25 = csPkgId25;
	}
	public String getCsPkgId26() {
	    return csPkgId26;
	}
	public void setCsPkgId26(String csPkgId26) {
	    this.csPkgId26 = csPkgId26;
	}
	public String getCsPkgId27() {
	    return csPkgId27;
	}
	public void setCsPkgId27(String csPkgId27) {
	    this.csPkgId27 = csPkgId27;
	}
	public String getCsPkgId28() {
	    return csPkgId28;
	}
	public void setCsPkgId28(String csPkgId28) {
	    this.csPkgId28 = csPkgId28;
	}
	public String getCsPkgId29() {
	    return csPkgId29;
	}
	public void setCsPkgId29(String csPkgId29) {
	    this.csPkgId29 = csPkgId29;
	}
	public String getCsPkgId30() {
	    return csPkgId30;
	}
	public void setCsPkgId30(String csPkgId30) {
	    this.csPkgId30 = csPkgId30;
	}
	
	
	
	public String getDiyParam() {
		return  "phone:" + this.phone + ","
				+"status:" + this.status + ","
				+"csContent:" + this.csContent + ","
				+"startTime:" + this.startTime + ","
				+"endTime:" + this.endTime + ","
				;
	}
	
	public String getGroupParam(){
		return  "csGroupId:" + this.csGroupId + ","
				+"csGroupName:" + this.csGroupName + ","
				;
	}
	
	public String getLabelParam(){
		return  "csLabelId:" + this.csLabelId + ","
				+"csLabelName:" + this.csLabelName + ","
				;
	}
	public String getTextInfoParam(){
		return  "csContent:" + this.csContent + ","
				+"csContentNo:" + this.csContentNo + ","
				+"startTime:" + this.startTime + ","
				+"endTime:" + this.endTime + ","
				+"pageSize :" + this.pageSize  + ","
				+"pageNum  :" + this.pageNum   + ","
				;
	}
	
	public String getCsOffParam(){
		return  "csId:" + this.csId + ","
				+"csType:" + this.csType + ","
				;
	}
	public String getPackageParam(){
		return  "csPackageName:" + this.csPackageName + ","
				+"csPackageNo:" + this.csPackageNo + ","
				+"csPackageId:" + this.csPackageId + ","
				+"startTime:" + this.startTime + ","
				+"endTime:" + this.endTime + ","
				+"csPackageStatus:" + this.csPackageStatus + ","
				+"csPkgGroupId:" + this.csPkgGroupId + ","
				+"csPkgLabelId:" + this.csPkgLabelId + ","
				+"csPkgContent1:" + this.csPkgContent1 + ","
				+"csPkgContent2:" + this.csPkgContent2 + ","
				+"csPkgContent3:" + this.csPkgContent3 + ","
				+"csPkgContent4:" + this.csPkgContent4 + ","
				+"csPkgContent5:" + this.csPkgContent5 + ","
				;
	}
	public String getPkgNoParam(){
		return  "csPackageNo:" + this.csPackageNo
				;
	}

	public boolean isInterface() {
		return isInterface;
	}

	public void setInterface(boolean anInterface) {
		isInterface = anInterface;
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}
}
