/*---------------------------------------------------------通用CSS开始----------------------------------------------- */
/***
*author:xiaomage
*time:20160412
*param:common
*/
@charset "utf-8";

/* ------------------------------------全局定义---------------------------------------- */
/* 将具有默认margin和padding的标记置零，所有标记的margin、padding都在使用时具体定义 */
* {
    margin: 0;
    padding: 0;
}

/* 修正IE5.x和IE6的斜体溢出bug */
* html body {
    overflow: visible;
}

* html iframe, * html frame {
    overflow: auto;
}

* html frameset {
    overflow: hidden;
}

/* 常用标签，基本标签默认样式取消，HTML标签，取消基本标签默认样式，防止不同浏览器显示效果不同，text-align:center; 解决不同浏览器居中问题 */
body {
    color: #000;
    background-color: #ededed;
    font-family: "PingFang SC", "Hiragino Sans GB", "Helvetica Neue", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    text-align: left;
}

body, ul, ol, li, p, h1, h2, h3, h4, h5, h6, form, fieldset, table, td, img, div, tr {
    margin: 0;
    padding: 0;
}

input, select {
    font-size: 12px;
    vertical-align: middle;
}

select {
    color: #555555;
    background-color: #ffffff;
    background-image: none;
}

/* 设置内容左对齐，恢复因BODY设置剧中产生的继承 */
/*body div{ text-align:left;}*/

/* 标签属性，textarea,input 强制输入时内容超出时换行 */
textarea, input {
    word-wrap: break-word;
    word-break: break-all;
    padding: 0px;
}

/* 清除ul列表标记的样式，ul列表更多的用在不需要列表前置符号的样式里 */
li {
    list-style-type: none;
}

/* 定义图片边框，当图片作为链接内容被填充时，会有默认边框出现，重定义掉 */
img {
    border: 0 none;
}

/* 定义默认的链接样式，仅仅是作为默认样式提供，可以在各自的实例中覆盖掉 */
a:link, a:visited {
    color: #04C;
    text-decoration: none;
}

a:hover {
    text-decoration: none !important;
}

/* 去掉链接的虚线框 */
a {
    outline: none;
}

/* 定义H系列标签，覆盖H系列标签默认属性 */
h1 {
    font-size: 24px;
}

h2 {
    font-size: 20px;
}

h3 {
    font-size: 18px;
}

h4 {
    font-size: 16px;
}

h5 {
    font-size: 14px;
}

h6 {
    font-size: 12px;
}

/* ------------------------------------通用属性定义---------------------------------------- */

/* 鼠标样式 */
.pointer {
    cursor: pointer;
}

/* 取消边框 */
.NoBorder {
    border: 0 none;
}

/* 文本对齐方式 */
.t_l {
    text-align: left;
}

.t_c {
    text-align: center;
}

.t_r {
    text-align: right;
}

/*错误文本*/
.error {
    color: #d50049 !important;
}

/* ------------------------------------通用容器定义---------------------------------------- */

/* 容器 */
.wrapper {
    clear: both;
}

/* 隐藏元素，当元素内容内容超出元素height 或 width 时，隐藏之 */
.o-hidden {
    overflow: hidden;
}

/*
* 隐藏元素，visibility可以隐藏元素，但是还是会在布局中占位 */
.invisible {
    visibility: hidden;
}

/* 从页面布局上隐藏元素，从布局上隐藏元素 */
.hidden {
    display: none;
}

.block {
    display: block;
}

/* ------------------------------------清理元素--------------------------------------- */

/* 清理浮动元素,当浮动换行时后面元素不希望浮动，添加此属性，防止IE BUG */
/*.clear{ clear:both; height:0px; width:100%; font-size:1px; line-height:0px; visibility:hidden; overflow:hidden;}*/
.clear:after {
    content: '';
    clear: both;
    display: block;
    height: 0;
}

.clear {
    *zoom: 1;
}

/* ------------------------------------常用样式定义---------------------------------------- */

/* 颜色功能定义，由小写C开头,表示color */
.cRed {
    color: #F00 !important;
}

.cWhite {
    color: #FFF !important;;
}

.cGreen {
    color: #0F0 !important;;
}

.cGray {
    color: #666 !important;;
}

.cBlue {
    color: #00F !important;;
}

.cblack {
    color: #000 !important;;
}

/* 定义某个项目常用颜色 */
.c001 {
    color: #663;
}

/* 定义字体样式，由大写字母F开头,表示FONT */

/* 字体样式 */
.FB {
    font-weight: bold;
}

.FN {
    font-weight: normal;
}

.FI {
    font-style: italic;
}

/* 字体大小 */
.F10 {
    font-size: 10px;
}

.F11 {
    font-size: 11px;
}

.F12 {
    font-size: 12px;
}

.F13 {
    font-size: 13px;
}

.F14 {
    font-size: 14px;
}

.F16 {
    font-size: 16px;
}

.F18 {
    font-size: 18px;
}

/*--------------------------------------------------------------通用CSS结束----------------------------------------- */
/*路由过渡效果*/
.fade-enter-active, .fade-leave-active {
    transition: opacity .8s ease-in;
}

.fade-enter, .fade-leave-active {
    opacity: 0
}

.slide-fade-enter-active {
    transition: all .3s ease-in;
}

.slide-fade-leave-active {
    transition: all .8s cubic-bezier(3.0, 1.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-active {
    transform: translateY(-100px);
    opacity: 0;
}

.slide-left-enter, .slide-right-leave-active {
    opacity: 0;
    -webkit-transform: translate(30px, 0);
    transform: translate(30px, 0);
}

.slide-left-leave-active, .slide-right-enter {
    opacity: 0;
    -webkit-transform: translate(-30px, 0);
    transform: translate(-30px, 0);
}

/** 弹出窗样式 **/
.el-message-box__title {
    text-align: center;
    font-size: 23px;
    border-bottom: 1px solid #e5e9f1;
    font-weight: 700;
    color: #1f2d3d;
    padding-bottom: 16px;
}

.el-message-box__message {
    line-height: 36px;
    font-size: 16px;
}

.el-message-box__message p {
    text-align: center;
}

.el-message-box__btns {
    text-align: center !important;
}

.el-message-box {
    width: 300px !important;
}

div.cell {
    text-align: center;
}

/*------------------------------------------------------------------
Loader holder / .loader-holder
-------------------------------------------------------------------*/

.loader-holder {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: #fff;
    position: absolute;
}

.loader {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 48.2842712474619px;
    height: 48.2842712474619px;
    margin-left: -24.14213562373095px;
    margin-top: -24.14213562373095px;
    border-radius: 100%;
    -webkit-animation-name: loader;
    animation-name: loader;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: 4s;
    animation-duration: 4s;
}

.loader .side {
    display: block;
    width: 6px;
    height: 20px;
    background-color: #20A0FF;
    margin: 2px;
    position: absolute;
    border-radius: 50%;
    -webkit-animation-duration: 1.5s;
    animation-duration: 1.5s;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease;
    animation-timing-function: ease;
}

.loader .side:nth-child(1),
.loader .side:nth-child(5) {
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-animation-name: rotate0;
    animation-name: rotate0;
}

.loader .side:nth-child(3),
.loader .side:nth-child(7) {
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-animation-name: rotate90;
    animation-name: rotate90;
}

.loader .side:nth-child(2),
.loader .side:nth-child(6) {
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-animation-name: rotate45;
    animation-name: rotate45;
}

.loader .side:nth-child(4),
.loader .side:nth-child(8) {
    -webkit-transform: rotate(135deg);
    -ms-transform: rotate(135deg);
    transform: rotate(135deg);
    -webkit-animation-name: rotate135;
    animation-name: rotate135;
}

.loader .side:nth-child(1) {
    top: 24.14213562373095px;
    left: 48.2842712474619px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(2) {
    top: 41.21320343109277px;
    left: 41.21320343109277px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(3) {
    top: 48.2842712474619px;
    left: 24.14213562373095px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(4) {
    top: 41.21320343109277px;
    left: 7.07106781636913px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(5) {
    top: 24.14213562373095px;
    left: 0;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(6) {
    top: 7.07106781636913px;
    left: 7.07106781636913px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(7) {
    top: 0;
    left: 24.14213562373095px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

.loader .side:nth-child(8) {
    top: 7.07106781636913px;
    left: 41.21320343109277px;
    margin-left: -3px;
    margin-top: -10px;
    -webkit-animation-delay: 0;
    animation-delay: 0;
}

@-webkit-keyframes rotate0 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    60% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    100% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

@keyframes rotate0 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    60% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    100% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }
}

@-webkit-keyframes rotate90 {
    0% {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    60% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
    }

    100% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
    }
}

@keyframes rotate90 {
    0% {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }

    60% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
    }

    100% {
        -webkit-transform: rotate(270deg);
        transform: rotate(270deg);
    }
}

@-webkit-keyframes rotate45 {
    0% {
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
    }

    60% {
        -webkit-transform: rotate(225deg);
        transform: rotate(225deg);
    }

    100% {
        -webkit-transform: rotate(225deg);
        transform: rotate(225deg);
    }
}

@keyframes rotate45 {
    0% {
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
    }

    60% {
        -webkit-transform: rotate(225deg);
        transform: rotate(225deg);
    }

    100% {
        -webkit-transform: rotate(225deg);
        transform: rotate(225deg);
    }
}

@-webkit-keyframes rotate135 {
    0% {
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg);
    }

    60% {
        -webkit-transform: rotate(315deg);
        transform: rotate(315deg);
    }

    100% {
        -webkit-transform: rotate(315deg);
        transform: rotate(315deg);
    }
}

@keyframes rotate135 {
    0% {
        -webkit-transform: rotate(135deg);
        transform: rotate(135deg);
    }

    60% {
        -webkit-transform: rotate(315deg);
        transform: rotate(315deg);
    }

    100% {
        -webkit-transform: rotate(315deg);
        transform: rotate(315deg);
    }
}

@-webkit-keyframes loader {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

@keyframes loader {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/*div.pswp__bg{*/
/*opacity: 0.8 !important; !**画廊背景颜色**!*/
/*}*/
button.pswp__button--share {
    display: none;
}

.el-table {
    width: 100% ;
    border: 1px solid #eaeaea !important;
    box-sizing: border-box !important;
    border-collapse: collapse !important;
}
.el-table tr:last-child td {
    border-bottom: none !important;
}
.el-table td {
    font-size: 12px;
    color: #666 !important;
    cursor: pointer !important;
}

.el-table th {
    background: #f9f9f9 !important;
    font-weight: bold !important;
    color: #666 !important;
}

.el-table a {
    color: #666 !important;
    margin-left: 5px !important;
}

.el-table tr:hover td {
    background: #e7f2ff !important;
}

.el-table tr:hover a {
    color: #1c7ce7 !important;
}

.el-table th,
.el-table td {
    height: 40px !important;
    line-height: 40px !important ;
    padding: 0 10px !important;
    box-sizing: border-box !important;
    text-align: left !important;
    border-bottom: 1px solid #eaeaea !important;
}
.el-table tr:hover .fddv3-icon-view:before {
    background-position: -226px -16px !important;
}

.el-table tr:hover .fddv3-icon-sign:before {
    background-position: -244px -16px !important;
}

.el-table tr:hover .fddv3-icon-qx:before {
    background-position: -559px -170px !important;
}

.el-table tr:hover .fddv3-icon-hammer:before {
    background-position: -373px -132px !important;
}

.el-table tr:hover .fddv3-icon-putup:before {
    background-position: -414px -131px !important;
}

.el-table tr:hover .fddv3-icon-delete:before {
    background-position: -348px -98px !important;
}

.el-table tr:hover .fddv3-icon-sp:before {
    background-position: -576px -17px !important;
}

.el-table tr:hover .fddv3-icon-send:before {
    background-position: -493px -171px !important;
}

.el-table tr:hover .fddv3-icon-down:before {
    background-position: -261px -17px !important;
}

.el-table tr:hover .fddv3-icon-tip:before {
    background-position: -530px -18px !important;
}

.el-table tr:hover .fddv3-icon-del:before {
    background-position: -328px -16px !important;
}

.el-table tr:hover .fddv3-icon-edit:before {
    background-position: -487px -18px !important;
}

.el-table tr:hover .fddv3-icon-move:before {
    background-position: -475px 1px !important;
}

.el-table tr:hover .fddv3-icon-cancel:before {
    background-position: -556px 1px !important;
}

.el-table tr:hover .i-status-payment {
    color: #3896ff;
}

div[role='tooltip']{
    min-width:20px;
    max-width:300px !important;
}
.user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
}
::-webkit-scrollbar{
    width: 5px !important;
    height: 16px !important;
    background-color: #CCCC !important;
}
::-webkit-scrollbar-thumb{
    background-color:#cccc !important;
    border-radius:1px !important;
}
::-webkit-scrollbar-track{
    border-radius: 3px !important;
    background-color: #F5F5F5 !important;
}
.user-title {
    padding: 10px 0px 0px 0px !important;
}