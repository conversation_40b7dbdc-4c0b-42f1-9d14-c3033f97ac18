package com.cy.content.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

@JacksonXmlRootElement(localName = "Request")
public class GetContentByLabelOrGroupReq {
    @JacksonXmlProperty(localName = "ContentGroupID")
    private String ContentGroupID;
    @JacksonXmlProperty(localName = "ContentLabelID")
    private String ContentLabelID;

    public String getContentGroupID() {
        return ContentGroupID;
    }

    public void setContentGroupID(String contentGroupID) {
        ContentGroupID = contentGroupID;
    }

    public String getContentLabelID() {
        return ContentLabelID;
    }

    public void setContentLabelID(String contentLabelID) {
        ContentLabelID = contentLabelID;
    }

    @Override
    public String toString() {
        return "GetContentByLabelOrGroupReq{" +
                "ContentGroupID='" + ContentGroupID + '\'' +
                ", ContentLabelID='" + ContentLabelID + '\'' +
                '}';
    }
}
