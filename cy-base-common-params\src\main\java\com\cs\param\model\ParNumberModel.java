
package com.cs.param.model;

public class ParNumberModel {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String phoneSection;
	private String optCode;
	private String countryCode;
	private String startSuffix;
	private String endSuffix;
	private String brandCode;
	private String netCode;
	private String provinceCode;
	private String provinceName;
	private String regionCode;
	private String regionName;
	private String hlrAddr;
	private String terminalCode;
	private String brandName;
	private String terminalType;
	private String netName;
	private String status;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPhoneSection() {
		return phoneSection;
	}

	public void setPhoneSection(String phoneSection) {
		this.phoneSection = phoneSection;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getOptCode() {
		return optCode;
	}

	public void setOptCode(String optCode) {
		this.optCode = optCode;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getStartSuffix() {
		return startSuffix;
	}

	public void setStartSuffix(String startSuffix) {
		this.startSuffix = startSuffix;
	}

	public String getEndSuffix() {
		return endSuffix;
	}

	public void setEndSuffix(String endSuffix) {
		this.endSuffix = endSuffix;
	}

	public String getBrandCode() {
		return brandCode;
	}

	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}

	public String getNetCode() {
		return netCode;
	}

	public void setNetCode(String netCode) {
		this.netCode = netCode;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getHlrAddr() {
		return hlrAddr;
	}

	public void setHlrAddr(String hlrAddr) {
		this.hlrAddr = hlrAddr;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(String terminalType) {
		this.terminalType = terminalType;
	}

	public String getNetName() {
		return netName;
	}

	public void setNetName(String netName) {
		this.netName = netName;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
