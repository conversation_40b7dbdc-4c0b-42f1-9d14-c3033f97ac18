<template>
    <div>
        <h1 class="user-title">业务监控</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :inline="true" :model="procMoni" size="small">
                <el-row>
                    <el-col :span="24">
                        <el-form-item>
                            <el-radio-group v-model="procMoni.days" size="small" @change="procMoni.dateTime=[];">
                                <el-radio-button label="yesterday">昨天</el-radio-button>
                                <el-radio-button label="7">最近7天</el-radio-button>
                                <el-radio-button label="30">最近30天</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker v-model="procMoni.dateTime"  type="daterange" range-separator="至"
                                            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 230px;"  @change="procMoni.days=''"/>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="procMoniSearch()">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                                <el-button type="primary" plain @click="procMoniDownload()">导出excel</el-button>
                            </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-container class="procMoni-view app-motitle">
                <el-header class="procMoni-header app-motitle-box"><div class="procMoni-div app-motitle-int"><B>全国数据</B></div></el-header>
                <el-main style="padding:0">
                   <div class="procMoni-centen menu">
                       <ul>
                            <li>
                                 <dl>
                                    <dt>有效用户数</dt>
                                    <dd>{{fmoney(procMoniList.effectUser,0)}}</dd>
                                </dl>
                            </li>
                            <li>
                                <dl>
                                    <dt>新增用户数</dt>
                                    <dd>{{fmoney(procMoniList.addUser,0)}}</dd>
                                </dl>
                            </li>
                            <li>
                                <dl>
                                    <dt>退订用户数</dt>
                                    <dd>{{fmoney(procMoniList.unsubscribeUser,0)}}</dd>
                                </dl>
                            </li>
                            <li>
                                <dl>
                                    <dt>彩印投递次数</dt>
                                    <dd>{{fmoney(procMoniList.cyDeliveryCount,0)}}</dd>
                                </dl>
                            </li>
                            <li>
                                <dl>
                                    <dt>彩印修改次数</dt>
                                    <dd>{{fmoney(procMoniList.cyModifyCount,0)}}</dd>
                                </dl>
                            </li>
                       </ul>
                   </div>
                   <div>
                       <div class="app-moselect">
                            <label>指标</label>
                            <el-select v-model="procMoni.selectCount" placeholder="请选择" class="procMoni-select" @change="selectCount()">
                                <el-option v-for="list of procMoniTypeList" :key="list.value" :label="list.value" :value="list.label"></el-option>
                            </el-select>
                       </div>
                       <el-row :gutter="2">
                            <el-col :span="14">
                               <div id="myChart" style="width:100%;height:400px;"></div>
                            </el-col>
                            <el-col :span="10">
                                <el-table :data="procMoniList.monitorDetail" border width='500' height="443" v-loading="loading">
                                    <el-table-column prop="provinceName" sortable label="省份"></el-table-column>
                                    <el-table-column :prop="procMoniTypeList[procMoni.selectCount-1].name" sortable :label="procMoniTypeList[procMoni.selectCount-1].value"></el-table-column>
                                    <el-table-column label="占比">
                                        <template slot-scope="scope" >
                                            <span v-if="procMoniList[procMoniTypeList[procMoni.selectCount-1].type]!=0">
                                                {{((scope.row[procMoniTypeList[procMoni.selectCount-1].name]/procMoniList[procMoniTypeList[procMoni.selectCount-1].type])*100).toFixed(2)+'%'}}
                                            </span>
                                            <span v-else>
                                                {{'0.00%'}}
                                            </span>
                                        </template>

                                    </el-table-column>
                                </el-table>
                            </el-col>
                        </el-row>
                   </div>
                </el-main>
            </el-container>
            <el-container class="procMoni-view">
                <el-header class="procMoni-header"><div class="procMoni-div pp-motitle-box"><B>各省数据列表</B></div></el-header>
                <el-main style="padding:0">
                    <el-table :data="procMoniList.monitorDetail" border style="width: 100%;font-size:12px;" v-loading="loading">
                        <el-table-column prop="provinceName" label="省份" width="120"/>
                        <el-table-column prop="effectUser" label="有效用户数" width="120"/>
                        <el-table-column prop="payUser" label="付费用户数" width="120"/>
                        <el-table-column prop="freeUser" label="免费用户数" width="120"/>
                        <el-table-column prop="addUser" label="新增用户数" width="120"/>
                        <el-table-column prop="newPayUser" label="新增付费用户数" width="130"/>
                        <el-table-column prop="newFreeUser" label="新增免费用户数" width="130"/>
                        <el-table-column prop="unsubscribeUser" label="退订用户数" width="120"/>
                        <el-table-column prop="unsubPayUser" label="退订付费用户数" width="130"/>
                        <el-table-column prop="unsubFreeUser" label="退订免费用户数" width="130"/>
                        <el-table-column prop="cyDeliveryCount" label="彩印投递次数" width="120"/>
                        <el-table-column prop="cyModifyCount" label="彩印修改次数" width="120"/>
                    </el-table>
                </el-main>
            </el-container>
         </div>
    </div>
</template>
<script src='./procMoni.js'></script>
<style scoped>
    .user-line {
    /* width: 94%; */
    margin: 0 auto;
    margin-top: 3%;
    margin-bottom: 3%;
    border-bottom: 1px solid #439ae6;
    }
    .procMoni-view{
        border:1px solid #CCCCCC;
    }
    .procMoni-header{
        height: 40px!important;
        padding: 0 !important;
        border-bottom: 1px solid #CCCCCC;
    }
    .procMoni-div{
        height: 40px;
        line-height: 40px;
        text-indent: 24px;
        background-color: #EEEEEE;
        width: 100%;
    }
    .procMoni-centen{
        padding-top:30px !important;
        border-bottom: 1px solid #CCCCCC;
        height: 100px;
    }
    .procMoni-centen ul{
        list-style:none; /* 去掉ul前面的符号 */
        margin: 0px; /* 与外界元素的距离为0 */
        padding: 0px; /* 与内部元素的距离为0 */
        width: auto; /* 宽度根据元素内容调整 */
    }
    .procMoni-centen ul li{
        float:left;
        width: 19%;
        height:80px !important;
        list-style-type:none; 
        text-align:center;  
        border-right:1px solid #DDDDDD;
    }
    .procMoni-centen ul li dl{
        margin: 12px;
    }
    .procMoni-centen ul li dl dt{
        font-size: 17px;
        color: #cccccc;
    }
    .procMoni-centen ul li dl dd{
        margin: 12px;
        font-size: 20px;
        font-weight: bold;
    }
    .procMoni-select{
        text-align: left !important;
    }
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 3%;
    }
</style>
