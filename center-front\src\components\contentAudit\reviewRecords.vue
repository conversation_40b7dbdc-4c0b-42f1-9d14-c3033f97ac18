<template scope="scope">
    <div>
        <!--<h1 class="user-title">批量下架内容</h1>-->
        <!--<hr class="user-line"/>-->
        <!--用户管理-->
        <el-tabs v-model="activeName" class="user-title">
          <!--已通过审核记录-->
          <el-tab-pane label="已通过审核记录" name="passRecords">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="类型">
                  <el-select v-model="searchReq.svMold" placeholder="请选择">
                    <el-option
                        v-for="item in statusData"
                        :key="item.csTypeId"
                        :label="item.csTypeName"
                        :value="item.csTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="内容ID">
                  <el-input v-model="searchReq.svId"></el-input>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="内容">
                  <el-input v-model="searchReq.svCard "></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px"
                    />
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search(searchReq)">查询</el-button>
                </el-form-item>
              </el-form>

            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    prop="csGroupName"
                    label="类型">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="内容ID">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="内容">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="审核意见">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="审核时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="撤销原因">
                          <el-input type="textarea" v-model="rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;edit">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true;addDisable(scope);tableData[scope.$index].isDisabled=true" type="text" size="small" :disabled="tableData[scope.$index].isDisabled">撤销</el-button>
                    <!--<el-button type="text" size="small" v-show="revoke" disabled>撤销</el-button>-->
                  </template>
                </el-table-column>
              </el-table>
               <!-- 分页 -->
              <div class="block app-pageganit">
              <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="tableData.pageNum"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="pageTotal"  style="text-align: right;">
              </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <!--审核明细表-->
          <el-tab-pane label="审核明细表" name="reviewDetails">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="类型">
                  <el-select v-model="searchReq.csType" placeholder="请选择">
                    <el-option
                        v-for="item in statusData"
                        :key="item.csTypeId"
                        :label="item.csTypeName"
                        :value="item.csTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="内容ID">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="审核意见">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="内容">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="审核人">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search()">查询</el-button>
                </el-form-item>
              </el-form>

            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="类型">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="内容ID">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="内容">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    label="审核意见">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="驳回原因">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="审核时间">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="撤销原因">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="撤销时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input type="textarea" v-model="rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;edit">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="block app-pageganit">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="10"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableData.pageTotal"  style="text-align: right;">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
import {formDate} from './../../util/core.js';
export default {
  //        name: 'textCS',
  data() {
    return {
      pageNum:1,
      disabled: false,
      statusData:[],//类型下拉栏变量
      rejectReason: "", //驳回原因
      activeName: "passRecords",
      // csTextStatus:[{'1':'上架',"2":'待上架','4':'下架'}],
      checked: [],
      editVisible: false,
      // tableData: [
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "1",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "2",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "4",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   },
      //   {
      //     isDisabled: false,
      //     csTextInfoNo: "1231",
      //     csGroupName: "asd",
      //     csLabelName: "asdas",
      //     csTextContent: "sdfsd",
      //     csTextStatus: "1",
      //     csSubmitTime: "2323",
      //     auditor: "sdfsdf",
      //     passTime: "asdas",
      //     useNumber: "asdasd"
      //   }
      // ],
      tableData:[],
      multipleSelection: [],
      dateTime:[],
      //请求数据
      //查询
      searchReq: {
        svMold:"",
        svId: "",
        svCard : "",
        startTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1
      },
      request: {},
      delRequest: {
        index: ""
      }
    };
  },
  //        created(){
  //          console.log(this.$route.params.index);
  //          this.activeName=this.$route.params.index;
  //        },
  mounted() {
    this.mold();
  },
  methods: {
    addDisable: function(scope) {
      //            console.log(button);
      //            console.log($(`.revoke${scope.$index}`).attr("disabled"));
      //            $(`.revoke${scope.$index}`).attr("disabled");
      const button = document.querySelector(`.revoke${scope.$index}`);
      //              button.disabled=true;
      //            console.log(button.disabled);
      console.log(button);
    },
    check: function(index, row) {
      //              console.log(index,row);
      //              console.log(!(row.csTextStatus==2));
      console.log(this.checked);
    },
    //----------------------已通过审核记录--------------------------
    //类型下拉栏选项请求
    mold() {
      this.$http
        .get(`${this.proxyUrl}/csOff/getCsType`, { emulateJSON: true })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.statusData = res.data.datas;
          } else if (res.data.resStatus == "1") {
            console.log("请求失败");
          }
        });
    },
    //多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.checked = true;
      console.log(val);
    },
    //查询请求
    search: function(request) {
      // console.log(request);
      if(this.dateTime && this.dateTime.length>0){
        request.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        request.endTIme=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        request.startTime='';
        request.endTime='';
      }
      this.$http
        .post(`${this.proxyUrl}/auditRecord/getPassAuditRecord`, request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data;
          console.log(this.tableData);
        });
    },
    //删除请求
    deltr: function() {
      console.log("删除成功");
      console.log(!tnis.tableData.csTextStatus == "2");
      //               this.$http
      //                   .post(`${this.proxyUrl}/csText/delCsTextInfo`,delRequest,{emulateJSON:true})
      //                   .then(function(res){
      //                     console.log(res);
      //                   })
    },
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTIme=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.$http
          .post(`${this.proxyUrl}/csText/getCsText`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
      })
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTIme=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
       this.$http
          .post(`${this.proxyUrl}/csText/getCsText`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
        })
    },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-search3 {
  width: 94%;
  margin-top: 3%;
}
.el-table {
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
</style>
