package com.cy.content;

import com.cy.content.model.CsMphSgpkgRecord;
import com.cy.content.model.MphSgpkgCheckReq;
import com.cy.content.model.MphSgpkgCheckRsp;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

public interface MphSgpkgRecordService {
    @PostMapping("/mphSgpkgService/mphSgpkgCheck")
    MphSgpkgCheckRsp mphSgpkgCheck(MphSgpkgCheckReq req);


    @PostMapping("/mphSgpkgService/mphSgpkgQuery")
    List<CsMphSgpkgRecord> mphSgpkgQuery(CsMphSgpkgRecord record);
}
