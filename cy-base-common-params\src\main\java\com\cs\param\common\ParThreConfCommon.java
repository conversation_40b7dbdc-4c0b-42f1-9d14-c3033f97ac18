
package com.cs.param.common;

public class ParThreConfCommon {
	private Integer id;// 阈值配置id
	private String provinceCode;// 省份code
	private String regionCode;// 地区code
	private String createTime;// 创建时间
	private String perPushTime;// 个人彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
	private String perPushMaxNum;// 个人彩印，对同一彩印接收方每天最大推送次数,单位：次数
	private String perAllPushMaxNum;// 个人彩印，每天最大推送次数（整个彩印）,单位：次数
	private String perDelayTime;
	private String bussPushTime;// 企业彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
	private String bussPushMaxNum;// 企业彩印，对同一彩印接收方每天最大推送次数,单位：次数
	private String bussAllPushMaxNum;// 企业彩印，每天最大推送次数（整个彩印）,单位：次数
	private String bussDelayTime;
	private String mediaPushTime;// 新媒彩印，同一彩印接收方的推送时间间隔,单位min（分钟）
	private String mediaPushMaxNum;// 媒彩印，对同一彩印接收方每天最大推送次数,单位：次数
	private String mediaAllPushMaxNum;// 新媒彩印，每天最大推送次数（整个彩印）,单位：次数
	private String mediaDelayTime;

	private String isDelete;// 标识是否为删除数据：0否，1是 默认为0
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getPerPushTime() {
		return perPushTime;
	}

	public void setPerPushTime(String perPushTime) {
		this.perPushTime = perPushTime;
	}

	public String getPerPushMaxNum() {
		return perPushMaxNum;
	}

	public void setPerPushMaxNum(String perPushMaxNum) {
		this.perPushMaxNum = perPushMaxNum;
	}

	public String getPerAllPushMaxNum() {
		return perAllPushMaxNum;
	}

	public void setPerAllPushMaxNum(String perAllPushMaxNum) {
		this.perAllPushMaxNum = perAllPushMaxNum;
	}

	public String getBussPushTime() {
		return bussPushTime;
	}

	public void setBussPushTime(String bussPushTime) {
		this.bussPushTime = bussPushTime;
	}

	public String getBussPushMaxNum() {
		return bussPushMaxNum;
	}

	public void setBussPushMaxNum(String bussPushMaxNum) {
		this.bussPushMaxNum = bussPushMaxNum;
	}

	public String getBussAllPushMaxNum() {
		return bussAllPushMaxNum;
	}

	public void setBussAllPushMaxNum(String bussAllPushMaxNum) {
		this.bussAllPushMaxNum = bussAllPushMaxNum;
	}

	public String getMediaPushTime() {
		return mediaPushTime;
	}

	public void setMediaPushTime(String mediaPushTime) {
		this.mediaPushTime = mediaPushTime;
	}

	public String getMediaPushMaxNum() {
		return mediaPushMaxNum;
	}

	public void setMediaPushMaxNum(String mediaPushMaxNum) {
		this.mediaPushMaxNum = mediaPushMaxNum;
	}

	public String getMediaAllPushMaxNum() {
		return mediaAllPushMaxNum;
	}

	public void setMediaAllPushMaxNum(String mediaAllPushMaxNum) {
		this.mediaAllPushMaxNum = mediaAllPushMaxNum;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getPerDelayTime() {
		return perDelayTime;
	}

	public void setPerDelayTime(String perDelayTime) {
		this.perDelayTime = perDelayTime;
	}

	public String getBussDelayTime() {
		return bussDelayTime;
	}

	public void setBussDelayTime(String bussDelayTime) {
		this.bussDelayTime = bussDelayTime;
	}

	public String getMediaDelayTime() {
		return mediaDelayTime;
	}

	public void setMediaDelayTime(String mediaDelayTime) {
		this.mediaDelayTime = mediaDelayTime;
	}

}
