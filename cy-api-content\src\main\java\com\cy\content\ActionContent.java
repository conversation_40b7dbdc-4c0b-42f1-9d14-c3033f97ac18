package com.cy.content;

import com.cy.content.Common.Result;
import com.cy.content.model.ActInfoModel;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface ActionContent {

    /**
     * 创建活动
     */
    @PostMapping("/action/create")
    public Result createAction(@RequestBody String activityName);
    /**
     *查询活动
     */
    @PostMapping("/action/query")
    public Result queryAction(@RequestBody ActInfoModel actInfo);

    /**
     * 修改活动
     */
    @PostMapping("/action/mod")
    public Result modAction(@RequestBody ActInfoModel actInfo);


    /**
     * 导出活动
     */
        @PostMapping("/action/export")
    public List<ActInfoModel> exportAction(@RequestBody ActInfoModel actInfo);

    /**
     * 活动数据同步接口
     * @param list
     */
    @PostMapping("/action/syncAct")
    public Result syncAct(@RequestBody List<ActInfoModel> list);
}
