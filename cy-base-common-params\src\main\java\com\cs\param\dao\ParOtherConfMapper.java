package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParOtherConfCommon;
import com.cs.param.model.ParOtherConfModel;

@Repository
public interface ParOtherConfMapper {

	int updateParOtherConfByPK(ParOtherConfCommon common) throws SQLException;

	int deleteParOtherConfByPK(ParOtherConfCommon common) throws SQLException;

	ParOtherConfModel getParOtherConfDetailById(ParOtherConfCommon common) throws SQLException;

	List<ParOtherConfModel> queryPageInfo(ParOtherConfCommon common) throws SQLException;

	Integer queryPageCount(ParOtherConfCommon common) throws SQLException;

	Integer isExistWithoutCity(ParOtherConfCommon common) throws SQLException;

	int addParOtherConf(ParOtherConfCommon common) throws SQLException;

}
