package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParSmsInsCommon;
import com.cs.param.model.ParSmsInsModel;

@Repository
public interface ParSmsInsMapper {

	int updateParSmsInsByPK(ParSmsInsCommon common) throws SQLException;

	int deleteParSmsInsByPK(ParSmsInsCommon common) throws SQLException;

	int openOrCloseStatusById(ParSmsInsCommon common) throws SQLException;

	ParSmsInsModel queryStatusById(ParSmsInsCommon common) throws SQLException;

	List<ParSmsInsModel> queryPageInfo(ParSmsInsCommon common) throws SQLException;

	Integer queryPageCount(ParSmsInsCommon common) throws SQLException;

}
