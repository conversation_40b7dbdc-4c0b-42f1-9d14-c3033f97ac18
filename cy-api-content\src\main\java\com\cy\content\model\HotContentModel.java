package com.cy.content.model;

import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
public class HotContentModel {
    // 热线内部唯一标识
    private Integer id;

    // 彩印唯一编号，不为空的编号不能重复
    private String hcNumber;

    // 业务类型，1表示热线彩印
    private Integer servType;

    // 投递类型，1表示闪信，2表示短信
    private String deliveryType;

    // 彩印内容或模板内容
    private String content;

    // 内容类型，1表示普通内容
    private Integer contentType;

    // 审核状态：待提交审核、审核中、审核通过、审核驳回
    private String approveStatus;

    // 审核意见
    private String approveIdea;

    // 企业唯一标识
    private Integer enterpriseId;

    // 企业名称
    private String enterpriseName;

    // 行业类型
    private String industryType;

    // 签名
    private String sign;

    // 审核时间
    private Date auditTime;

    // 内容支持的运营商：1移动，2联通，3电信，多个时用逗号分割，默认支持移动
    private String operator;

    // 联通审核状态：未使用、待提交审核、审核中、审核通过、审核驳回
    private String unicomApproveStatus;

    // 联通审核意见
    private String unicomApproveIdea;

    // 联通审核时间
    private Date unicomApproveTime;

    // 电信审核状态：未使用、待提交审核、审核中、审核通过、审核驳回
    private String telecomApproveStatus;

    // 电信审核意见
    private String telecomApproveIdea;

    // 电信审核时间
    private Date telecomApproveTime;

    // 资质上传 URL，支持批量上传，以分号分隔
    private String certificateUrl;

    // 创建时间，不能为空
    private Date createTime;

    // 更新时间，不能为空
    private Date updateTime;

    /**
     * 电信投递通道，5：号百；8：彩讯
     */
    private String telecomDeliveryWay;

    /**
     * 联通投递通道，6：联通在线；8：彩讯
     */
    private String unicomDeliveryWay;
}
