<template scope="scope">
  <div>
    <div class="user-titler">热线彩印</div>
    <!--企业彩印-->
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="企业编号">
              <el-input v-model="searchReq.corpId" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业名称">
              <el-input v-model="searchReq.corpName" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="彩印类型">
              <el-select
                v-model="searchReq.serviceId"
                placeholder="请选择"
                size="small"
                style="width:150px;"
                clearable
              >
                <el-option
                  v-for="item in caiyinType"
                  :label="item.name"
                  :value="item.id"
                  :key="item.id"
                >{{item.name}}</el-option>
                <!-- <el-option label="主叫彩印" value>主叫彩印</el-option>
                <el-option label="被叫彩印" value>被叫彩印</el-option>
                <el-option label="挂机短信" value>挂机短信</el-option>-->
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="彩印内容">
              <el-input v-model="searchReq.subject" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="省份">
              <el-select
                v-model="searchReq.provinceId"
                clearable
                placeholder="请选择"
                size="small"
                @change="querySearchRegionList"
                class="app-input"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.provinceName"
                  :label="item.provinceName"
                  :value="item.provinceCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地市">
              <el-select
                v-model="searchReq.cityId"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option
                  v-for="item in city"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="提交时间">
          <div class="block">
            <el-date-picker
              v-model="searchReq.timearr"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="small"
            ></el-date-picker>
          </div>
        </el-form-item>
         <el-form-item label="内容编号">
          <el-input v-model="searchReq.contentID" size="small" class="app-input"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchReq.p = 1;search()" size="small">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button
            type="primary"
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="passVisible=true;passType=2;"
          >批量通过</el-button>
          <el-button
            type="primary"
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="rejectVisible=true;rejectType=2;"
          >批量驳回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="uuid" label="彩印ID" width="200"></el-table-column>
        <el-table-column  label="彩印内容" width="200">
          <template slot-scope="scope">
            <div v-html="scope.row.subject"></div>
          </template>
        </el-table-column>
        <el-table-column label="敏感词"
                         width="200">
          <template slot-scope="scope">
            <div  v-html="scope.row.sensitiveWords"></div>
          </template>
        </el-table-column>
        <el-table-column label="热线号码" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small " @click="showPhoneDetail(scope.row.phones)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="caiyinType" label="彩印类型" width="100"></el-table-column>
        <el-table-column prop="corpName" label="企业名称" width="200"></el-table-column>
        <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)" :style="hasCorpImage(scope.row.corpImage)?'':'color:#808080'">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="其他资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)" :style="hasOtherImage(scope.row.otherImage)?'':'color: #808080'">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="insertTime" label="提交时间" width="200"></el-table-column>
          <el-table-column
                prop="contentID"
                label="内容编号"
                width="200">
        </el-table-column>
        <el-table-column
                prop="templateIds"
                label="异网模板编号"
                width="350">
        </el-table-column>
        <el-table-column prop="corpId" label="企业编号" width="120"></el-table-column>
        <el-table-column prop="province" label="省份" width="100"></el-table-column>
        <el-table-column prop="city" label="地市" width="100"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="passVisible=true;rowData=scope.row;passType=1;"
            >通过</el-button>
            <el-button
              @click="rejectVisible=true;rowData=scope.row;rejectType=1;"
              type="text"
              size="small"
            >驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.p"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
    <div>
      <el-dialog
        width="30%"
        title="通过"
        :visible.sync="passVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <div>是否通过该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        width="30%"
        title="驳回"
        :visible.sync="rejectVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <el-form label-width="80px" justify="center">
          <el-form-item label="驳回原因">
            <el-radio v-model="radio" label="1">手动输入</el-radio>
            <el-radio v-model="radio" label="2">系统预设</el-radio>
          </el-form-item>
          <el-form-item label v-show="radio==1">
            <!--驳回原因字数无限制-->
            <el-input
              v-model="rejectReq.reason"
              type="textarea"
              :rows="2"
              placeholder
              style="width: 200px;"
              clearable
            ></el-input>
            <p v-if="rejectReq.reason.length > 1024" style="color: red">不能超过1024个字</p>
          </el-form-item>
          <el-form-item label v-show="radio==2">
            <el-select v-model="rejectReq.reason" clearable placeholder="请选择" size="small">
              <el-option
                v-for="item in refuse"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false" size="small">取 消</el-button>
          <el-button
            type="primary"
            @click="rejectVisible = false;rejectCheck(rowData)"
            size="small"
            :disabled="rejectReq.reason.length > 1024"
          >确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog title="热线号码" width="30%" :visible.sync="phonesVisible">
        <el-table :data="phonesData">
          <el-table-column type="index" label="序号" width="120"></el-table-column>
          <el-table-column prop="phone" label="热线号码"></el-table-column>
        </el-table>
      </el-dialog>
      <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
        <img style="width: 100%;" :src="corpImage" alt>
      </el-dialog>
      <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
        <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
           <li >
             <a :href="item" target="_blank">其他资质{{index+1}}</a>
           </li>
        </ul>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from 'moment';
import {dealSensitiveWord} from './../../../util/core.js';

export default {
  data() {
    return {
      tableLoading: false,
      //查询条件
      searchReq: {
        serviceId: "",
        contentID:"",//内容编号
        corpId: "",
        corpName: "",
        subject: "",
        uuid: "",
        provinceId: "",
        cityId: "",
        start: "",
        end: "",
        p: 1, //页码
        pz: 10, //一页的数量
        timearr: []
      },
      typeoff: "info",
      clickoff: true,
      //省份
      provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
      //地市
      city: [],
      //数据表
      tableData: [],
      //操作列表
      rowData: "",
      pageTotal: 0, //总条数
      passVisible: false,
      rejectVisible: false,
      radio: "1",
      //通过请求参数
      passReq: {
        ids: [],
        type: 2,
        submitter: sessionStorage.getItem("userInfo") && JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        reviewer: sessionStorage.getItem("userInfo") && JSON.parse(sessionStorage.getItem("userInfo")).sysUserName //操作者
      },
      //驳回请求参数
      rejectReq: {
        ids: [],
        type: 1,
        reason: "", //驳回原因
        submitter: sessionStorage.getItem("userInfo") && JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        reviewer: sessionStorage.getItem("userInfo") && JSON.parse(sessionStorage.getItem("userInfo")).sysUserName //操作者
      },
      phonesVisible: false,
      phonesData: [],
      corpImageVisible: false,
      corpImage: "", //企业资质
      otherImageVisible: false,
      otherImage: [], //其他资质
      //彩印类型
      caiyinType: [
        {
          id: '01120',
          name: "主叫彩印"
        },
        {
          id: '01121',
          name: "被叫彩印"
        },
        {
          id: '01122',
          name: "挂机短信"
        },
        {
          id: '05100',
          name: "省内版-主被叫彩印"
        },
        {
          id: '05104',
          name: "省内版-主叫彩印"
        },
        {
          id: '05105',
          name: "省内版-被叫彩印"
        },
        {
          id: '05101',
          name: "省内版-挂机短信"
        },
        {
          id: '01124',
          name: "二级企业-主被叫彩印"
        },   {
          id: '01123',
          name: "二级企业-挂机彩漫"
        },
        {
          id: '00120',
          name : "交互彩印"       
        }
      ],
      //系统驳回原因
      refuse: [],
      clickPassFlag: false,
	  clickRejectFlag: false
    };
  },
  created() {
    this.search();
    this.refuse=JSON.parse(sessionStorage.getItem("refuseList"))
  },
  watch: {
    "rejectReq.ids"() {
      if (this.rejectReq.ids.length) {
        this.clickoff = false;
        this.typeoff = "primary";
      } else {
        this.clickoff = true;
        this.typeoff = "info";
      }
    },
    radio() {
      if (this.radio == 2 || this.radio == 1) {
        this.rejectReq.reason = "";
      }
    },
    rejectVisible() {
      if (!this.rejectVisible) {
        this.rejectReq.reason = "";
      }
    }
  },
  methods: {
  	tableRowClassName({row, rowIndex}) {
		const writeList = ['111','101','110','011','001','010']
	    if(writeList.includes(row.platforms)){
	    	return 'success-row'
	    }
	  	return '';
	},
    searchBtn() {
      this.searchReq.p = 1;
      this.search()
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr) {
        this.searchReq.start = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
        this.searchReq.end = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
      } else {
        this.searchReq.start = "";
        this.searchReq.end = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/query`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data || [];
            this.tableData.forEach(val => {
              if (val.serviceId == '01120') {
                val.caiyinType = "主叫彩印";
              } else if (val.serviceId == '01121') {
                val.caiyinType = "被叫彩印";
              } else if (val.serviceId == '01122') {
                val.caiyinType = "挂机短信";
              } else if (val.serviceId == '05100') {
                val.caiyinType = "省内版-主被叫彩印";
              } else if (val.serviceId == '05104') {
                val.caiyinType = "省内版-主叫彩印";
              } else if (val.serviceId == '05105') {
                val.caiyinType = "省内版-被叫彩印";
              } else if (val.serviceId == '05101') {
                val.caiyinType = "省内版-挂机短信";
              }else if (val.serviceId == '01124'){
                val.caiyinType = "二级企业-主被叫彩印";
              }else if (val.serviceId == '01123'){
                val.caiyinType = "二级企业-挂机彩漫";
              }else if (val.serviceId == '00120'){
                val.caiyinType = "交互彩印";
              }
              val.insertTime = (val.insertTime != null) && moment(val.insertTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );

              let sensitiveWords = "";
              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"03",true);
              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"02",true);
              sensitiveWords =dealSensitiveWord(sensitiveWords,val,"01",true);
              val.sensitiveWords = sensitiveWords;
            });
            this.pageTotal = res.count;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    //查询地市
    querySearchRegionList() {
      var queryRegion = {
        provinceCode: this.searchReq.provinceId
      };
      this.$http
        .post(`${this.proxyUrl}/param/regionMgt/getRegion`, queryRegion, {
          emulateJSON: true
        })
        .then(function(res) {
          this.city = res.data;
          this.searchReq.cityId = "";
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    //多选框
    handleSelectionChange(val) {
      this.rejectReq.ids = [];
      this.passReq.ids = [];
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.ids.push(val[i].id);
        this.passReq.ids.push(val[i].id);
      }
    },
    handleSizeChange(val) {
      this.searchReq.p = 1;
      //每页条数
      this.searchReq.pz = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.p = val;
      this.search();
    },
    showPhoneDetail(phones) {
      this.phonesData = (phones || []).map(item => ({ phone: item }));
      this.phonesVisible = true;
    },
    showCorpImage(corpImage) {
      console.log(corpImage);
      this.corpImage = corpImage;
      this.corpImageVisible = true;
    },
    showOtherImage(otherImage) {
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    // 判断企业资质和其他资质是否有值，没有则字体颜色变成灰色
    hasCorpImage(corpImage){
      return !(corpImage == null || corpImage == "");
    },
    hasOtherImage(otherImage){
      return !(otherImage == null || otherImage.length == 0);
    },
    //判断是批量还是单操作，发相应请求
    passCheck(val) {
      if(this.clickPassFlag) {
        return
      }
      this.clickPassFlag = true;
      //1为单，2为多
      if (this.passType == 1) {
        this.pass(val);
      } else if (this.passType == 2) {
        this.passlist();
      }
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val) {
      if(this.clickRejectFlag) {
        return
      }
      this.clickRejectFlag = true;
      //1为单，2为多
      if (this.rejectType == 1) {
        this.reject(val);
      } else if (this.rejectType == 2) {
        this.rejectlist();
      }
    },
    //通过请求---单
    pass: function(val) {
      this.passReq.ids = [val.id];
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/pass`,
          JSON.stringify(this.passReq)
        )
        .then(function(res) {
          loading.close();
          this.clickPassFlag = false;
          if (res.data.code == "0") {
            this.$message.success("通过成功");
            this.search();
            this.passReq.ids = [];
          } else {
            this.$message("通过失败");
            this.passReq.ids = [];
          }
        });
    },
    //通过请求---多
    passlist: function() {
      if (!this.passReq.ids.length > 0) {
        this.$message.error("请选择批量通过的内容");
        this.clickPassFlag = false;
        return false;
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/pass`,
          JSON.stringify(this.passReq)
        )
        .then(function(res) {
          loading.close();
          this.clickPassFlag = false;
          if (res.data.code == "0") {
            let reslist = res.data.data;
            let successCount = 0;
            let failCount = 0;
            let duplicateCount = 0;
            reslist.forEach(list => {
              if (list.auditStatus==1) {
                successCount++;
              } else if (list.auditStatus==2){
                duplicateCount++;
              } else {
                failCount++;
              }
            });
            this.$message.success(
              `审核通过成功记录${successCount}条，失败记录${failCount}条，重复审核记录${duplicateCount}条，详细情况请查询企业明细`
            );
            this.search();
            this.passReq.ids = [];
          } else {
            this.$message("通过失败");
            this.passReq.ids = [];
          }
        });
    },
    //驳回请求---单
    reject: function(val) {
      if (!this.rejectReq.reason) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        this.clickRejectFlag = false;
        return false;
      }
      this.rejectReq.ids = [val.id];
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/reject`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          loading.close();
          this.clickRejectFlag = false;
          if (res.data.code == "0") {
            this.$message.success("驳回成功");
            this.rejectReq.ids = [];
            this.search();
          } else {
            this.$message("驳回失败");
            this.rejectReq.ids = [];
          }
        });
    },
    //驳回请求---多
    rejectlist: function() {
      if (!this.rejectReq.reason) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        this.clickRejectFlag = false;
        return false;
      }
      if (!this.rejectReq.ids.length > 0) {
        this.$message.error("请选择批量驳回的内容");
        this.clickRejectFlag = false;
        return false;
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hot/content/reject`,
          JSON.stringify(this.rejectReq)
        )
        .then(function(res) {
          loading.close();
          this.clickRejectFlag = false;
          if (res.data.code == "0") {
            let reslist = res.data.data;
            let successCount = 0;
            let failCount = 0;
            let duplicateCount = 0;
            reslist.forEach(list => {
              if (list.auditStatus==1) {
                successCount++;
              } else if (list.auditStatus==2){
                duplicateCount++;
              } else {
                failCount++;
              }
            });
            this.$message.success(
              `驳回成功记录${successCount}条，失败记录${failCount}条，重复审核记录${duplicateCount}条，详细情况请查询企业明细`
            );
            this.search();
            this.rejectReq.ids = [];
          } else {
            this.$message("驳回失败");
            this.rejectReq.ids = [];
          }
        });
    }
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  /* margin-left: 3%; */
  /* margin-top: 3%; */
  border: 1px solid #ecebe9;
}


.zzWrap>>>.el-dialog__body {
  text-align: center;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
.el-table .success-row td{
  background: #c5e0b3 !important;
}
</style>
