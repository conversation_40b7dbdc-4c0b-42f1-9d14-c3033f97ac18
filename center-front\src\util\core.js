import axios from 'axios';
import routers from './../router/index.js';
//验证码倒数计时
export function restTime(id){
    var resetbtn = document.getElementById(id),
		count  = 30,iTimer=0,
		ibtn   = true;
    if(ibtn){
        ibtn = false;
        clearInterval(iTimer);
        resetbtn.disabled=true;
        iTimer=setInterval(()=>{
            if(count == 1){
                resetbtn.innerHTML = "重新发送";
                clearInterval(iTimer);
                ibtn = true;
                resetbtn.disabled=false;
                return false;
            }
            resetbtn.innerHTML = "发送("+--count + ')';
        })
    }
}
//金额格式化
export function fmoney(s, n) {
	n = n > 0 && n <= 20 ? n : 2;
	s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
	var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
	var t = "";
	for (var i = 0; i < l.length; i++) {
		t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    }
    if(s==0){
        return 0;
    }else if(!s){
        return 0;
    }else if(s>0){
        return t.split("").reverse().join("");
    }
	
}

//用*替换手机号码前6位
export function replacePhone(val){
    if(val.length==11&&val.charAt(0) == '1'){
        return val.replace(val.substr(0,6),'******');
    }
    return val;
}

//计算时间差相隔天数
export function surplusDay(startData,endDtata){
    return (new Date(endDtata).getTime()-new Date(startData).getTime())/(24 * 60 * 60 * 1000);
}
//计算时间差 耗时
export function timeConsuming(startData,endDtata){
    let dateTime = new Date(endDtata).getTime()-new Date(startData).getTime();
    let hour = new Date(dateTime).getHours();
    let minutes = new Date(dateTime).getMinutes();
    return hour+"时"+minutes+"分钟";
}
//日期格式化
export function formDate(date,format){
    var args = {
        "M+": date.getMonth() + 1,
        "d+": date.getDate(),
        "h+": date.getHours(),
        "m+": date.getMinutes(),
        "s+": date.getSeconds(),
        "q+": Math.floor((date.getMonth() + 3) / 3),  //quarter
        "S": date.getMilliseconds()
    };
    if (/(y+)/.test(format))
        format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var i in args) {
        var n = args[i];
        if (new RegExp("(" + i + ")").test(format))
            format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length));
    }
    return format;
}
//404
export function errorFour(){
    routers.push({ path: '/404' });
}
//500
export function errorFive(){
    routers.push({ path: '/500' });
}
//session超时
export function sessionOut(){
    routers.push({ path: '/login' });
}
//阻止默认事件及冒泡
export function stopUp(e) {
    var e = e || window.event;
    if (e.preventDefault) {
        e.preventDefault();
    } else {
        e.returnValue = false;
    };
    if (event && event.stopPropagation) {
        event.stopPropagation();
    } else {
        window.event.cancelBubble = true;
    }
}
//图片上传
export function imgUpload(e,size){
    let files = e.target.files || e.dataTransfer.files;
    if(!files.length){
      return false;
    }
    let fileType=files[0].name.substring(files[0].name.lastIndexOf('.')+1,files[0].name.length);
    if(fileType!=='jpg' && fileType!=='jpeg' && fileType!=='png'){
        showError('请上传jpg,jpeg,png等格式文件！');
        return false;
    }
    if((files[0].size/1024/1024)>=size){
        showError('文件大小不得超过'+size+'M');
        return false;
    }else if((files[0].size/1024)===0){
        showError('文件大小不得小于0KB!');
        return false;
    }
    return files[0];
}
//文件上传
export function fileUpload(e,size){
    let files = e.target.files || e.dataTransfer.files;
    if(!files.length){
      return false;
    }
    let fileType=files[0].name.substring(files[0].name.lastIndexOf('.')+1,files[0].name.length);
    if (fileType !== 'docx' && fileType !== 'doc' && fileType !== 'pdf' && fileType !== 'wps' && fileType !== 'xlsx' && fileType !== 'xls') {
        showError("请上传'.docx,.pdf,.doc,.wps,.xlsx,.xls'等格式文件!");
        return false;
    }
    if((files[0].size/1024/1024)>=size){
        showError('文件大小不得超过'+size+'M');
        return false;
    }else if((files[0].size/1024)===0){
        showError('文件大小不得小于0KB!');
        return false;
    }
    return files[0];
}
//文件或图片上传
export function fileOrImgUpload(e,size){
    let files = e.target.files || e.dataTransfer.files;
    if(!files.length){
      return false;
    }
    let fileType=files[0].name.substring(files[0].name.lastIndexOf('.')+1,files[0].name.length);
    if (fileType !== 'docx' && fileType !== 'doc' && fileType !== 'pdf' && fileType !== 'wps' && fileType !== 'xlsx' && fileType !== 'xls' && fileType!=='png' && fileType!=='jpeg' && fileType!=='jpg') {
        showError("请上传'.docx,.pdf,.doc,.wps,.xlsx,.xls,png,jpeg,jpg'等格式文件!");
        return false;
    }
    if((files[0].size/1024/1024)>=size){
        showError('文件大小不得超过'+size+'M');
        return false;
    }else if((files[0].size/1024)===0){
        showError('文件大小不得小于0KB!');
        return false;
    }
    return files[0];
}
//上传excel表格
export function excelUpload(e,size){
    let files = e.target.files || e.dataTransfer.files;
    if(!files.length){
      return false;
    }
    let fileType=files[0].name.substring(files[0].name.lastIndexOf('.')+1,files[0].name.length);
    if (fileType !== 'xlsx' && fileType !== 'xls') {
        showError("请上传'.xlsx,.xls'等格式文件!");
        return false;
    }
    if((files[0].size/1024/1024)>=size){
        showError('文件大小不得超过'+size+'M');
        return false;
    }else if((files[0].size/1024)===0){
        showError('文件大小不得小于0KB!');
        return false;
    }
    return files[0];
}
//下载文件
export function dowandFile(res,fileName){
    var blob = new Blob([res]);
    if('download' in document.createElement('a')){
        var a = window.document.createElement('a');
        var url = window.URL.createObjectURL(blob);
        a.href = url;
        a.download = fileName;
        a.click();
        window.URL.revokeObjectURL(url);
    }else{
        navigator.msSaveBlob(blob, fileName)
    }
}
//敏感词替换
export function dealSensitiveWord(sensitiveWords,val,grade,isSubject) {

    String.prototype.replaceAllTxt = function replaceAll(search, replace) { return this.split(search).join(replace); }


    let stringGrade = "";
    let color = "";
    if("01" === grade){
        stringGrade = "一级：";
        color ="red";
    }
    if("02" === grade){
        stringGrade = "二级：";
        color ="orange";
    }
    if("03" === grade){
        stringGrade = "三级：";
        color ="yellow";
    }
    if(val.sensitiveWordMap&&val.sensitiveWordMap[grade]){
        for(let i = 0;i<val.sensitiveWordMap[grade].length;i++){
            if(val.content){
                val.content = val.content.replaceAllTxt(val.sensitiveWordMap[grade][i],"<span style='background-color: "+color+"'>" + val.sensitiveWordMap[grade][i] + "</span>");
            }
            if(val.frames!=null){
                val.frames.forEach(frame => {
                    if(frame.text){
                        frame.text = frame.text.replaceAllTxt(val.sensitiveWordMap[grade][i],"<span style='background-color: "+color+"'>" + val.sensitiveWordMap[grade][i] + "</span>");
                    }
                });
            }
            if(isSubject&&val.subject){
                val.subject = val.subject.replaceAllTxt(val.sensitiveWordMap[grade][i],"<span style='background-color: "+color+"'>" + val.sensitiveWordMap[grade][i] + "</span>");

            }
        }
        sensitiveWords =  stringGrade + val.sensitiveWordMap[grade].join("，") + "；</br>" + sensitiveWords;
    }
    return sensitiveWords;
}