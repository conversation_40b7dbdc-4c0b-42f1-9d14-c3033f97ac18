
package com.cy.common;

import java.util.Arrays;

public class SysRoleCommon {
	private Integer sysRoleId; // 角色id
	private String sysRoleName; // 角色名称
	private String sysGrleDate; // 角色创建时间
	private String sysDescs; // 角色备注
	private Integer[] sysResourcesIds; // 资源id
	private String sysRoleIdStr; // 角色id,String类型
	
	
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getSysRoleId() {
		return sysRoleId;
	}

	public void setSysRoleId(Integer sysRoleId) {
		this.sysRoleId = sysRoleId;
	}

	public String getSysRoleName() {
		return sysRoleName;
	}

	public void setSysRoleName(String sysRoleName) {
		this.sysRoleName = sysRoleName;
	}

	public String getSysGrleDate() {
		return sysGrleDate;
	}

	public void setSysGrleDate(String sysGrleDate) {
		this.sysGrleDate = sysGrleDate;
	}

	public String getSysDescs() {
		return sysDescs;
	}

	public void setSysDescs(String sysDescs) {
		this.sysDescs = sysDescs;
	}

	public String getSysRoleIdStr() {
		return sysRoleIdStr;
	}

	public void setSysRoleIdStr(String sysRoleIdStr) {
		this.sysRoleIdStr = sysRoleIdStr;
	}

	public Integer[] getSysResourcesIds() {
		return sysResourcesIds;
	}

	public void setSysResourcesIds(Integer[] sysResourcesIds) {
		this.sysResourcesIds = sysResourcesIds;
	}
	

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	@Override
	public String toString() {
		return "SysRoleCommon [sysRoleId=" + sysRoleId + ", sysRoleName=" + sysRoleName + ", sysGrleDate="
				+ sysGrleDate + ", sysDescs=" + sysDescs + ", sysResourcesIds="
				+ Arrays.toString(sysResourcesIds) + ", sysRoleIdStr=" + sysRoleIdStr + "]";
	}

}
