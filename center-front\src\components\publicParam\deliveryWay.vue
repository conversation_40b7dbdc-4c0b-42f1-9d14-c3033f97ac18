<template>
  <div v-loading="loading">
    <!-- 标题 -->
    <h1 class="user-title">异网通道管理</h1>
    <div class="user-line"></div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" type="card" @tab-click="handleMainTabClick">
      <el-tab-pane label="默认设置" name="default">
        <div class="user-search" style="margin-top: 10px; padding-left: 24px !important">
          <!-- 联通 -->
          <div class="operator-group">
            <span class="operator-label">联通</span>
            <el-radio-group v-model="selectedUnicomChannel">
              <el-radio v-for="channel in unicomChannels" :key="channel.wayType" :label="channel.wayName">{{ channel.wayName }}</el-radio>
            </el-radio-group>
          </div>

          <!-- 电信 -->
          <div class="operator-group">
            <span class="operator-label">电信</span>
            <el-radio-group v-model="selectedTelecomChannel">
              <el-radio v-for="channel in telecomChannels" :key="channel.wayType" :label="channel.wayName">{{ channel.wayName }}</el-radio>
            </el-radio-group>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="定制设置" name="custom">
        <el-tabs type="border-card" v-if="channelSettings.length > 0" :value="activeChannelType"
                 @tab-click="handleChannelTabChange">
          <el-tab-pane v-for="channel in channelSettings" :key="channel.wayType" :label="channel.wayName" :name="String(channel.wayType)">
            <div class="custom-settings-container">
              <!-- 支持的运营商 -->
              <div class="setting-group">
                <h4>运营商</h4>
                <el-checkbox-group v-model="channel.selectedPlatforms">
                  <el-checkbox
                      v-for="platform in channel.availablePlatforms"
                      :key="platform.value"
                      :label="platform.value"
                  >
                    {{ platform.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>

              <!-- 支持的业务类型 -->
              <div class="setting-group">
                <h4>业务类型</h4>
                <el-checkbox-group v-model="channel.selectedBusinessTypes">
                  <el-checkbox
                      v-for="businessType in channel.availableBusinessTypes"
                      :key="businessType.value"
                      :label="businessType.value"
                  >
                    {{ businessType.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div v-else class="no-data">暂无定制设置数据</div>
      </el-tab-pane>
    </el-tabs>

    <!-- 保存按钮 -->
    <div class="save-button">
      <el-button type="primary" @click="saveSettings">保存</el-button>
    </div>
  </div>
</template>

<script>
// 运营商映射
const PLATFORM_MAP = {
  '2': { value: '2', label: '联通' },
  '3': { value: '3', label: '电信' }
}

// 业务类型映射 (根据实际情况调整)
const BUSINESS_TYPE_MAP = {
  '1': { value: '1', label: '名片号/视宣号' },
  '2': { value: '2', label: '热线彩印' },
  '3': { value: '3', label: '直投闪信' }
}
export default {
  name: "deliveryWay",
  data() {
    return {
      loading: false, // 加载状态
      activeTab: "default", // 当前激活的标签页
      selectedUnicomChannel: "", // 联通选择的通道
      selectedTelecomChannel: "", // 电信选择的通道
      unicomChannels: [], // 联通通道列表
      telecomChannels: [], // 电信通道列表
      allChannels: [], // 所有通道列表
      channelSettings: [], // 定制设置通道配置
      // 新增一个变量来跟踪当前活动的通道标签页
      activeChannelType: '',
    };
  },
  mounted() {
    this.fetchDefaultSettings();
    this.fetchCustomSettings();
  },
  methods: {
    // 主标签页切换事件处理
    handleMainTabClick(tab) {
      if (tab.name === 'default') {
        this.fetchDefaultSettings();
      } else if (tab.name === 'custom') {
        this.fetchCustomSettings();
      }
    },
    // 监听通道标签页变化
    handleChannelTabChange(tab) {
      // 从tab对象中获取name属性
      this.activeChannelType = tab.name;
    },
    // 获取默认设置数据
    async fetchDefaultSettings() {
      this.loading = true;
      try {
        const response = await this.$http.post("/content/person/deliveryWay/queryPersonDeliveryWay", {
          isDefault: 0
        });
        const data = response.data.datas;

        // 存储所有通道数据
        this.allChannels = data.map(item => ({
          id: item.id,
          platforms: item.platforms,
          wayType: item.wayType,
          isDefault: item.isDefault,
          isUsed: item.isUsed,
          wayName: item.wayName,
        }));

        // 过滤联通和电信的通道
        this.unicomChannels = this.allChannels.filter(item => item.platforms === "2");
        this.telecomChannels = this.allChannels.filter(item => item.platforms === "3");

        // 初始化默认选中的通道
        this.selectedUnicomChannel = this.findDefaultSelectedChannel(this.unicomChannels);
        this.selectedTelecomChannel = this.findDefaultSelectedChannel(this.telecomChannels);

      } catch (error) {
        console.error("获取默认设置数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 获取定制设置数据
    async fetchCustomSettings(type) {
      this.loading = true;
      try {
        const response = await this.$http.post("/content/person/deliveryWay/queryPersonDeliveryWay", {
          isDefault: 1
        });
        const data = response.data.datas;

        // 处理定制设置数据
        this.processChannelSettings(data);
        // 设置默认选中的第一个标签页
        if (!type && this.channelSettings.length > 0) {
          this.activeChannelType = String(this.channelSettings[0].wayType); // 转换为字符串
        }
      } catch (error) {
        console.error("获取定制设置数据失败:", error);
      } finally {
        this.loading = false;
      }
    },

    // 更新后的处理通道设置数据方法
    processChannelSettings(data) {
      // 初始化空数组
      this.channelSettings = [];

      // 检查数据是否有效
      if (!Array.isArray(data)) {
        console.error("无效的通道数据:", data);
        return;
      }

      try {
        // 先按通道类型分组
        const channelMap = {};

        data.forEach(item => {
          if (!item || !item.wayType) return;

          if (!channelMap[item.wayType]) {
            channelMap[item.wayType] = {
              wayType: item.wayType,
              wayName: item.wayName || `通道类型 ${item.wayType}`,
              channels: []
            };
          }

          // 确保只添加有效数据，使用 servType 作为业务类型字段
          if (item.platforms || item.servType) {
            channelMap[item.wayType].channels.push({
              ...item,
              platforms: item.platforms || '',
              servType: item.servType || '' // 使用 servType 字段
            });
          }
        });

        // 转换为通道设置数组
        this.channelSettings = Object.values(channelMap).map(channelGroup => {
          const platformSet = new Set();
          const businessTypeSet = new Set();

          // 收集所有支持的运营商和业务类型
          channelGroup.channels.forEach(channel => {
            if (channel.platforms) {
              platformSet.add(channel.platforms);
            }

            // 使用 servType 作为业务类型字段
            if (channel.servType) {
              // 假设 servType 是单个值，不是逗号分隔的
              businessTypeSet.add(channel.servType);
            }
          });

          // 转换为可用选项
          const availablePlatforms = Array.from(platformSet)
              .map(p => PLATFORM_MAP[p] || { value: p, label: `运营商(${p})` })
              .filter(Boolean);

          const availableBusinessTypes = Array.from(businessTypeSet)
              .map(t => BUSINESS_TYPE_MAP[t] || { value: t, label: `业务(${t})` })
              .filter(Boolean);

          // 获取当前选中的项(isUsed=1的项)
          const selectedPlatforms = channelGroup.channels
              .filter(c => c.isUsed === 1 && c.platforms)
              .map(c => c.platforms);

          // 使用 servType 作为业务类型字段
          const selectedBusinessTypes = channelGroup.channels
              .filter(c => c.isUsed === 1 && c.servType)
              .map(c => c.servType)
              .filter(Boolean);

          return {
            ...channelGroup,
            availablePlatforms,
            availableBusinessTypes,
            selectedPlatforms: [...new Set(selectedPlatforms)],
            selectedBusinessTypes: [...new Set(selectedBusinessTypes)],
            originalChannels: [...channelGroup.channels]
          };
        });

      } catch (error) {
        console.error("处理通道设置数据出错:", error);
        this.$message.error("处理通道配置出错");
        this.channelSettings = [];
      }
    },
    // 修改后的保存定制设置方法
    saveCustomSettings() {
      if (!this.activeChannelType) {
        this.$message.warning("请先选择要保存的通道");
        return;
      }

      // 使用activeChannelType查找当前通道
      const currentChannel = this.channelSettings.find(
          channel =>  String(channel.wayType) === this.activeChannelType
      );

      if (!currentChannel) {
        this.$message.error("找不到当前通道的配置");
        return;
      }

      const saveData = [];

      // 只处理当前通道的数据
      currentChannel.originalChannels.forEach(originalChannel => {
        const isPlatformSelected = currentChannel.selectedPlatforms.includes(originalChannel.platforms);
        const isBusinessTypeSelected = currentChannel.selectedBusinessTypes.includes(originalChannel.servType);

        saveData.push({
          id: originalChannel.id,
          wayType: currentChannel.wayType,
          platforms: originalChannel.platforms || '',
          servType: originalChannel.servType || '',
          isDefault: 1,
          isUsed: (isPlatformSelected && isBusinessTypeSelected) ? 1 : 2
        });
      });

      this.sendSaveRequest(saveData, "定制设置");
    },
    sendSaveRequest(data, type) {
      const requestPayload = {
        deliveryWays: data,
      };

      this.$http.post("/content/person/deliveryWay/syncPersonDeliveryWay", requestPayload)
          .then(response => {
            if (response.data.resStatus === 0) {
              this.$message.success(`${type}保存成功！`);
              // 保存成功后重新加载数据
              if (type === "默认设置") {
                this.fetchDefaultSettings();
              } else {
                this.fetchCustomSettings(this.activeChannelType);
              }
            } else {
              this.$message.error(response.data.resText);
            }
          })
          .catch(error => {
            console.error(`保存${type}失败:`, error);
            this.$message.error(`${type}保存失败，请重试！`);
          });
    },
    // 辅助方法：找到 isUsed = 1 的通道，如果没有则返回第一个通道的 wayName
    findDefaultSelectedChannel(channels) {
      // 找到 isUsed = 1 的通道
      const selectedChannel = channels.find(channel => channel.isUsed === 1);

      // 如果找到 isUsed = 1 的通道，则返回其 wayName
      if (selectedChannel) {
        return selectedChannel.wayName;
      }

      // 如果未找到，则返回第一个通道的 wayName
      return (channels[0] && channels[0].wayName) || "";
    },

    // 保存设置
    saveSettings() {
      if (this.activeTab === "default") {
        this.saveDefaultSettings();
      } else {
        this.saveCustomSettings();
      }
    },

    saveDefaultSettings() {
      this.allChannels.forEach(channel => {
        if (channel.platforms === "2" && channel.wayName === this.selectedUnicomChannel) {
          channel.isUsed = 1;
        } else if (channel.platforms === "3" && channel.wayName === this.selectedTelecomChannel) {
          channel.isUsed = 1;
        } else {
          channel.isUsed = 2;
        }
      });

      const saveData = this.allChannels.map(channel => ({
        id: channel.id,
        platforms: channel.platforms,
        wayType: channel.wayType,
        isDefault: channel.isDefault,
        isUsed: channel.isUsed,
      }));

      this.sendSaveRequest(saveData, "默认设置");
    },




  }
};
</script>

<style scoped>
.custom-settings-container {
  padding: 20px;
}

.setting-group {
  margin-bottom: 30px;
}

.setting-group h4 {
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.no-data {
  text-align: center;
  padding: 50px;
  color: #999;
}

.operator-group {
  margin-bottom: 20px;
}

.operator-label {
  display: inline-block;
  width: 60px;
  font-weight: bold;
}

.save-button {
  text-align: center;
  margin-top: 20px;
}
</style>