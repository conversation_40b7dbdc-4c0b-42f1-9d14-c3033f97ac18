import {path} from '../../api/path'
import router from '../../router';
import {getLeftMenu} from '../../api/initInfo'

export default {
    state: {
        list: []
    },

    mutations: {
        setMenuList: (state, list) => {
            state.list = list
        }
    },

    actions: {
         getMenuList: ({commit, state}, token) => {
             getLeftMenu(token).then(res => {
                if (res.body.result === 'success') {
                    // sessionStorage.setItem('menuList', JSON.stringify(res.body.data.list))
                    commit('setMenuList', res.body.data.list)
                    router.push('/list')
                }
            })
        }
    }
}