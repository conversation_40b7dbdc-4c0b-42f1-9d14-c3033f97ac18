<template>
<div>
    <h1 class="user-title">批量修改用户信息</h1>
    <div class="user-line"></div>
    <div style="margin:10px 0 0 24px;">
      <el-form style="margin-top:15px;" >
          <el-form-item label="上传用户号码:">
            <el-upload
                    class="upload-demo"
                    ref="upload"
                    action=""
                    :auto-upload='false'
                    :limit="1"
                    :on-remove='fileRemove'
                    :on-change="handleChange"
                    accept=".xls, .xlsx">
              <el-button size="mini" type="primary" style="font-size:14px;">上传excel表</el-button>
              <div slot="tip" class="el-upload__tip"></div>
            </el-upload>
          </el-form-item>
        <el-form-item>
          <a :href="`${this.proxyUrl}/user/batchMod/getTemplate`">下载模板</a><span style="color: red;margin-left: 15px">注:每次批设号码不能超过10万</span>
        </el-form-item>
        </el-form>
    </div>

    <div style="margin:20px 0 30px 24px; width: 100%" >
      <b style="font-size:14px;">请选择需要编辑的业务</b>
      <div style="margin: 30px 0;"></div>
      <el-checkbox-group v-model="checkedBusiness">
        <el-checkbox v-for="item in business" :label="item" :key="item">{{item}}</el-checkbox>
      </el-checkbox-group>
      <div class="user-search2" style="padding-left: 0!important;">
        
        <el-form style="margin-top:15px;" v-if="checkedBusiness.indexOf('彩印推送业务')>=0">
          <el-form-item label="彩印推送业务:">
            <el-checkbox-group v-model="checkedPushBusiness">
              <b>彩印推送业务</b><br>
              <el-checkbox v-for="item in pushBusiness" :label="item.name" :key="item.name">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <el-form style="margin-top:15px;" v-if="checkedBusiness.indexOf('彩印拒接业务')>=0">
          <el-form-item label="彩印拒接业务:"><br>
            <el-checkbox-group v-model="checkedRejectionBusiness">
              <el-checkbox v-for="item in rejectionBusiness" :label="item.name" :key="item.name">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <el-form style="margin-top:15px;" v-if="checkedBusiness.indexOf('彩印接收类型')>=0">
          <el-form-item label="彩印接收类型:"><br>
            <el-select v-model="receiveType"  placeholder="(默认)系统自动选择" size="small" style="width:354px;">
              <el-option value="0" label="默认"></el-option>
              <el-option value="1" label="USSD短信发送"></el-option>
              <el-option value="2" label="免提短信(闪信)发送"></el-option>
              <el-option value="3" label="普通短信发送"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-form style="margin-top:15px;" v-if="checkedBusiness.indexOf('个人彩印内容')>=0">
          <el-form-item label="个人彩印内容:"><br>
            <div>
              <div>
                <el-checkbox label="修改内容不推送" :checked="false" true-label="1" false-label="0" v-model="contentStatus"></el-checkbox>
                <el-checkbox label="存在个人彩印DIY内容的用户不设置" :checked="false" true-label="1" false-label="0"  v-model="cyDiyStatus"></el-checkbox>
              </div>
              <el-radio v-model="personPrintType" label="1">文本彩印</el-radio>
             <!--  <el-radio v-model="personPrintType" label="2">彩印盒</el-radio> -->
               <el-radio v-model="personPrintType" label="2">彩印盒</el-radio>
              <div v-if="personPrintType==0 || personPrintType==1" style="margin-top:15px;">
                <el-input v-model="crCyContent" type="textarea"  max="50" size="small" style="width: 354px;overflow-y:visible;"></el-input>
              </div>

              <div v-if="personPrintType==2" style="margin-top:15px;">
                  <el-select
                    v-model="value9"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请输入彩印盒名称"
                    :remote-method="remoteMethod"
                    :loading="loading"
                    size='100'>
                    <el-option
                      v-for="item in options4"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id">
                      <span style="float: left">{{ item.name }}</span>
                      <span style="float: right; color: #8492a6; font-size: 10px">{{ item.id }}</span>
                    </el-option>
                  </el-select>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <el-button type="primary" :disabled="disabled" style="margin-top:15px;width:106px;height:28px;" @click="submitForm()" size="small" class="app-bnt">提 交</el-button>
      </div>
    </div>
</div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
export default {
  data() {
    return {
      options4: [],
      value9: [],
      list: [],
      loading: false,
      disabled:false,
      request:{},

      personPrintType:'1',
      receiveType:'0',
      dialogFormVisible:false,
      statusData:[],
      checkedBusiness: [],
      checkedPushBusiness: [],
      checkedRejectionBusiness: [],
      business: ['彩印推送业务', '彩印拒接业务', '彩印接收类型', '个人彩印内容'],
      pushBusiness: [{name:'个人彩印',value:'sendPersonStatus'}, {name:'新媒彩印',value:'sendMediaStatus'}, {name:'企业彩印',value:'sendCdpStatus'}],
      rejectionBusiness: [{name:'个人彩印',value:'accpetPersonStatus'}, {name:'提醒彩印',value:'accpetRemindStatus'}, {name:'企业彩印',value:'accpetCdpStatus'}],
      file:'',
      crCyContent:'',
      box:'',
      boxId:'',
      contentStatus: 0,
      cyDiyStatus: 0
    };
  },
    mounted(){
    //this.queryPkgContend();
    // this.list = this.states.map(item => {
    //     return { value: item, label: item };
    //   });
  },
  methods: {
    remoteMethod(query) {
        if (query !== '') {
           this.loading = true;
           this.request.pkgId=query;//pkgName
          // setTimeout(() => {
          //   this.loading = false;
          //   this.options4 = this.list.filter(item => {
          //     return item.label.toLowerCase()
          //       .indexOf(query.toLowerCase()) > -1;
          //   });
          // }, 200);
          post('/user/batchMod/getPkgContent',this.request,{ emulateJSON: true}).then(res=>{
             if(res.status===200){
               this.options4=res.data;
             }
             this.loading = false;
          });
        } else {
          this.loading = false;
          this.options4 = [];
        }
      },
    //查询彩印盒列表数据
    queryPkgContend(){
      post('/user/batchMod/getPkgContent',{}).then(res=>{
         if(res.status===200){
           this.statusData=res.data;
         }
      });
    },
    //获取选中的值
    selectClickStatu(list){
        this.box=list.name;
        this.boxId=list.id;
    },
    //删除上传的文件
    fileRemove(file){

    },
    //上传文件
    handleChange(file,fileList){
      this.file=file.raw;
    },
    handleCheckedCitiesChange(value){
      console.log(value);
    },
    downloadTemplate:function(){
        window.location.href=`${this.proxyUrl}/user/batchMod/getTemplate`;
    },

    //提交批量用户信息
    clear(){
      this.$refs.upload.clearFiles();
      this.crCyContent = '';
      this.file = '';
      this.contentStatus = 0;
      this.cyDiyStatus = 0;
    },
    submitForm(){
      
      this.disabled = true;
      this.boxId=this.value9;
      console.log(this.boxId);
      if(!this.file){
        this.$message({
          message:'请上传excel文件',
          type:'warning'
        })
        this.disabled = false;
        return false;
      }

        if(this.checkedBusiness.length === 0){
            this.$message({
                message:'请选择需要编辑的业务',
                type:'warning'
            })
            this.disabled = false;
            return false;
        }

        if(this.checkedBusiness.indexOf('个人彩印内容')>=0 && this.personPrintType == 1 && this.crCyContent.length === 0){
            this.$message({
                message:'请输入个人彩印内容',
                type:'warning'
            })
            this.disabled = false;
            return false;
        }

        if(this.checkedBusiness.indexOf('个人彩印内容')>=0 && this.personPrintType == 2 && this.boxId == ''){
            this.$message({
                message:'请输选择彩印盒',
                type:'warning'
            })
            this.disabled = false;
            return false;
        }
      let formData=new FormData();
        formData.append('tsStatus',this.checkedBusiness.indexOf('彩印推送业务') >=0 ?  '1' : '0');
        formData.append('jjStatus',this.checkedBusiness.indexOf('彩印拒接业务') >=0 ?  '1' : '0');
        formData.append('jsStatus',this.checkedBusiness.indexOf('彩印接收类型') >=0 ?  '1' : '0');
        formData.append('grStatus',this.checkedBusiness.indexOf('个人彩印内容') >=0 ?  '1' : '0');

        formData.append('sendPersonStatus',this.checkedPushBusiness.indexOf('个人彩印') >=0 ?  '1' : '0');
        formData.append('sendMediaStatus',this.checkedPushBusiness.indexOf('新媒彩印') >=0 ?  '1' : '0');
        formData.append('sendCdpStatus',this.checkedPushBusiness.indexOf('企业彩印') >=0 ?  '1' : '0');

        formData.append('accpetPersonStatus',this.checkedRejectionBusiness.indexOf('个人彩印')>=0 ? '0':'1');
        formData.append('accpetRemindStatus',this.checkedRejectionBusiness.indexOf('提醒彩印')>=0 ? '0':'1');
        formData.append('accpetCdpStatus',this.checkedRejectionBusiness.indexOf('企业彩印')>=0 ? '0':'1');
        
        formData.append('contentStatus', this.contentStatus);
        formData.append('cyDiyStatus', this.cyDiyStatus);

      // for(let i=0;i<this.pushBusiness.length;i++){
      //   for(let j=0;j<this.checkedPushBusiness.length;j++){
      //     if(this.pushBusiness[i].name===this.checkedPushBusiness[j]){
      //       formData.append(this.pushBusiness[i].value,1)
      //     }else{
      //       formData.append(this.pushBusiness[i].value,0)
      //     }
      //   }
      // }
      // for(let i=0;i<this.rejectionBusiness.length;i++){
      //   for(let j=0;j<this.checkedRejectionBusiness.length;j++){
      //     if(this.rejectionBusiness[i].name===this.checkedRejectionBusiness[j]){
      //       formData.append(this.rejectionBusiness[i].value,1)
      //     }else{
      //      formData.append(this.rejectionBusiness[i].value,0)
      //     }
      //   }
      // }

      formData.append('file',this.file);
      formData.append('receiveType',this.receiveType);
      formData.append('personPrintType',this.personPrintType);
      if(this.personPrintType==1){
          formData.append('crCyContent',this.crCyContent);
      }else{
        formData.append('boxId',this.boxId);
      }
      post('/user/batchMod/batchUser',formData).then(res=>{
        if(res.data.status== 0){
          this.clear();
          this.$message({ message:'修改信息发送成功',type:'success'});
        }else{
            this.$message({ message:res.data.resText,type:'error'});
        }
          this.disabled = false;
      })
    }
  }
};
</script>
<style scoped>
  label{
    font-weight: bold;
    font-size:16px !important;
  }
.fl {
  float: left;
}
.user-title {
     padding: 10px 0px 0px 0px;
}
.user-line {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border-bottom: 1px solid #439ae6;
}
.user-search2 {
  width: 40%;
  margin-left: 0;
}
/* 弹窗checkbox样式 */
.el-form-item__content{
  margin:0 !important;
}
</style>
