
package com.cs.param.model;

public class ParThreConfModel {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String provinceCode;
	private String regionCode;
	private String createTime;
	private String perPushTime;
	private String perPushMaxNum;
	private String perAllPushMaxNum;
	private String perDelayTime;
	private String bussPushTime;
	private String bussPushMaxNum;
	private String bussAllPushMaxNum;
	private String bussDelayTime;
	private String mediaPushTime;
	private String mediaPushMaxNum;
	private String mediaAllPushMaxNum;
	private String mediaDelayTime;
	private String isDelete;
	private String provinceName;
	private String regionName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getPerPushTime() {
		return perPushTime;
	}

	public void setPerPushTime(String perPushTime) {
		this.perPushTime = perPushTime;
	}

	public String getPerPushMaxNum() {
		return perPushMaxNum;
	}

	public void setPerPushMaxNum(String perPushMaxNum) {
		this.perPushMaxNum = perPushMaxNum;
	}

	public String getPerAllPushMaxNum() {
		return perAllPushMaxNum;
	}

	public void setPerAllPushMaxNum(String perAllPushMaxNum) {
		this.perAllPushMaxNum = perAllPushMaxNum;
	}

	public String getBussPushTime() {
		return bussPushTime;
	}

	public void setBussPushTime(String bussPushTime) {
		this.bussPushTime = bussPushTime;
	}

	public String getBussPushMaxNum() {
		return bussPushMaxNum;
	}

	public void setBussPushMaxNum(String bussPushMaxNum) {
		this.bussPushMaxNum = bussPushMaxNum;
	}

	public String getBussAllPushMaxNum() {
		return bussAllPushMaxNum;
	}

	public void setBussAllPushMaxNum(String bussAllPushMaxNum) {
		this.bussAllPushMaxNum = bussAllPushMaxNum;
	}

	public String getMediaPushTime() {
		return mediaPushTime;
	}

	public void setMediaPushTime(String mediaPushTime) {
		this.mediaPushTime = mediaPushTime;
	}

	public String getMediaPushMaxNum() {
		return mediaPushMaxNum;
	}

	public void setMediaPushMaxNum(String mediaPushMaxNum) {
		this.mediaPushMaxNum = mediaPushMaxNum;
	}

	public String getMediaAllPushMaxNum() {
		return mediaAllPushMaxNum;
	}

	public void setMediaAllPushMaxNum(String mediaAllPushMaxNum) {
		this.mediaAllPushMaxNum = mediaAllPushMaxNum;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getPerDelayTime() {
		return perDelayTime;
	}

	public void setPerDelayTime(String perDelayTime) {
		this.perDelayTime = perDelayTime;
	}

	public String getBussDelayTime() {
		return bussDelayTime;
	}

	public void setBussDelayTime(String bussDelayTime) {
		this.bussDelayTime = bussDelayTime;
	}

	public String getMediaDelayTime() {
		return mediaDelayTime;
	}

	public void setMediaDelayTime(String mediaDelayTime) {
		this.mediaDelayTime = mediaDelayTime;
	}

}
