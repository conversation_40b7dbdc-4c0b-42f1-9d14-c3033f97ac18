<template>
    <div>
        <h1 class="user-title">异步任务管理</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="任务名称">
                    <el-input v-model="searchForm.sysTaskName" clearable placeholder="" size="small" :maxlength="40" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="发起者">
                    <el-input v-model="searchForm.sysTriggerPeople" clearable placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="任务状态">
                    <el-select v-model="searchForm.sysTaskState" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in sysTaskStateList"
                                :key="item.id"
                                :label="item.value"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="发起时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchForm.createTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
                  class="app-tab" :header-cell-class-name="tableheaderClassName">
            <!--<el-table-column type="index" label="序号" width="50px"/>-->
            <el-table-column prop="sysTaskName" label="名称" width="180" :show-overflow-tooltip="true"/>
            <el-table-column prop="sysFileName" label="上传文件名" width="180" :show-overflow-tooltip="true"/>
            <el-table-column prop="sysTaskState" label="状态" :formatter="formatStatus"/>

            <el-table-column prop="sysTriggerPeople" label="发起者" width="140"/>
            <el-table-column prop="sysStartTime" label="发起时间" width="180"/>
            <el-table-column prop="sysEndTime" label="结束时间" width="180"/>
            <el-table-column prop="sysTaskDesc" label="任务描述" width="180" :show-overflow-tooltip="true"/>
            <el-table-column prop="oper" label="执行结果" width="120" fixed="right">
                <template slot-scope="scope" >
                    <el-button  v-show="scope.row.sysTaskState == 1 ||scope.row.sysTaskState == 0" type="text" size="small" @click="downloadResult(scope.row.sysTaskId)" >执行结果</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

    </div>


</template>
<script>
    export default {
        data() {
            return {
                //查询form对象定义
                searchForm: {
                    sysTaskName:'',
                    sysTaskState:'',
                    sysTriggerPeople: '',
                    sysTaskDesc: '',
                    startTime: '',//发起时间
                    endTime:'',//发起时间
                    createTime:'',
                    pageSize:10,
                    pageNum :1 // 查询的页码

                },
                sysTaskStateList:[
                    {
                        id:'0',
                        value:'进行中'
                    },
                    {
                        id:'1',
                        value:'成功'
                    }
                    ,
                    {
                        id:'2',
                        value:'失败'
                    }
                ],
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
            this.search(this.searchForm);
        },
        methods: {
            //查询请求
            search:function(searchForm){
                if(searchForm.createTime != ''){
                    searchForm.startTime=searchForm.createTime[0];
                    searchForm.endTime=searchForm.createTime[1];
                }
                this.$http.post(`${this.proxyUrl}/sys/sysTask/getSysTaskPage`,searchForm,{emulateJSON:true})
                        .then(function(res){
                            this.currentPage=res.data.pageNum;
                            this.total=res.data.pageTotal;
                            this.tableData=res.data.datas;
                        })
            },
            formatStatus:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return '进行中';
                }else if (cellValue === "1"){
                    return '成功';
                }else if (cellValue === "2"){
                    return '失败';
                }
            },
            downloadResult:function (taskId) {
                window.location.href=`${this.proxyUrl}/user/batchResult/getErrorContents?batchNo=`+taskId;
            },

            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
