package com.cy.user.api.request;


import java.util.List;

public class QueryCallLogRsponse {
    //返回码，200表示成功，其他表示失败
    private Integer status;
    //错误信息
    private String errorMsg;
    //白名单号码
    private List<String> whiteListNum;

    private long totalCount;

    @Override
    public String toString() {
        return "QueryCallLogRsponse{" +
                "status=" + status +
                ", errorMsg='" + errorMsg + '\'' +
                ", whiteListNum=" + whiteListNum +
                ", totalCount=" + totalCount +
                '}';
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<String> getWhiteListNum() {
        return whiteListNum;
    }

    public void setWhiteListNum(List<String> whiteListNum) {
        this.whiteListNum = whiteListNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }


    public QueryCallLogRsponse() {
        super();
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    protected void finalize() throws Throwable {
        super.finalize();
    }

}
