<template scope="scope">
  <div v-loading="loading">
    <h1 class="user-title">文本彩印</h1>
    <div class="user-line"></div>
    <!-- 表单 -->
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="彩印内容">
          <el-input
            v-model="searchReq.csContent"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印ID">
          <el-input
            v-model="searchReq.csContentNo"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-select
            v-model="searchReq.contentType"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option
              v-for="item in contentTypeData"
              :key="`${item.cyType}_form`"
              :label="item.name"
              :value="item.cyType"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchReq.csStatus"
            placeholder="请选择"
            size="small"
            clearable
          >
            <el-option
              v-for="item in statusData"
              :key="item.csStatusNo"
              :label="item.csStatusName"
              :value="item.csStatusNo"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容标签">
          <el-input
            v-model="searchReq.csLabelName"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="业务类型">
          <el-select v-model="searchReq.serviceType" placeholder="请选择">
            <el-option label="个人彩印" :value="1"></el-option>
            <el-option label="名片号" :value="2"></el-option>
            <el-option label="全部" :value="null"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="提交时间">
          <div class="block">
            <el-date-picker
              v-model="dateTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              style="width: 355px"
              size="small"
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="
              searchReq.pageNum = 1;
              search(searchReq);
            "
            size="small"
            class="app-bnt"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button
            type="primary"
            @click="locationHref('/creatMaterial')"
            size="small"
            >新建文本彩印</el-button
          >
          <el-button type="primary" @click="importTextCY()" size="small"
            >导入文本彩印</el-button
          >
          <el-button type="primary" plain @click="exportText()" size="small"
            >导出excel</el-button
          >
          <!-- <el-button type="info" @click="delAny" size="small">批量删除</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="contentType" label="内容类型" width="240">
          <template slot-scope="scope">
            {{ getContentName(scope.row.contentType) }}
          </template>
        </el-table-column>
        <el-table-column prop="csNumber" label="彩印ID" width="240">
        </el-table-column>
        <el-table-column prop="csGroupName" label="内容分类" width="200">
        </el-table-column>
        <el-table-column
          prop="csLabelName"
          label="内容标签"
          width="200"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="csTextContent"
          label="彩印内容"
          width="400"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column prop="csStatusName" label="状态" width="100">
        </el-table-column>
        <el-table-column prop="csSubmitTime" label="提交时间" width="200">
        </el-table-column>
        <el-table-column prop="auditor" label="审核人" width="200">
        </el-table-column>
        <el-table-column prop="passTime" label="通过时间" width="200">
        </el-table-column>
        <el-table-column prop="useNumber" label="使用人数" width="100">
        </el-table-column>
        <el-table-column prop="telecomDeliveryWay" label="电信投递通道" width="200">
          <template slot-scope="scope">
            {{ getDeliveryWayName(scope.row.telecomDeliveryWay) }}
          </template>
        </el-table-column>
        <el-table-column prop="unicomDeliveryWay" label="联通投递通道" width="200">
          <template slot-scope="scope">
            {{ getDeliveryWayName(scope.row.unicomDeliveryWay) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              v-show="(scope.row.csTextStatus == 1) & (scope.row.tempId != 5)"
              @click="openModify(scope.row)"
              type="text"
              size="small"
              >编辑</el-button
            >
            <el-button
              v-show="scope.row.tempId != 5"
              @click="
                delVisible = true;
                delRequest.csContentNos.length = 0;
                delRequest.csContentNos[0] = scope.row.csTextInfoNo;
              "
              type="text"
              size="small"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 编辑 -->
      <el-dialog
        title="编辑彩印内容"
        :visible.sync="updateVisible"
        :close-on-click-modal="false"
        @close="closeUpdate"
      >
        <el-form label-position="left" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label=" 彩印ID">
                {{ updateRequest.csContentNo }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="业务类型">
                <el-select
                  v-model="updateRequest.serviceType"
                  disabled
                  placeholder="请选择"
                  size="small"
                >
                  <el-option label="个人彩印" :value="1"></el-option>
                  <el-option label="名片号" :value="2"></el-option>
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="内容分类">
                <el-select
                  v-model="updateRequest.csGroupId"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in groupData"
                    :key="item.groupId"
                    :label="item.groupName"
                    :value="item.groupId"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="内容类型">
                <el-select
                  v-model="updateRequest.contentType"
                  placeholder="请选择"
                  size="small"
                >
                  <el-option
                    v-for="item in contentTypeData"
                    :key="`${item.cyType}_edit`"
                    :label="item.name"
                    :value="item.cyType"
                    :disabled="true"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item label="内容标签">
                <el-button type="info" @click="visible = true" size="small"
                  >添加标签</el-button
                >
                &nbsp;{{ ckLabelNames }}

                <el-dialog
                  title="添加标签"
                  :visible.sync="visible"
                  :close-on-click-modal="false"
                  append-to-body
                >
                  <div style="height: 300px; overflow: auto">
                    <el-form
                      class="demo-form-inline"
                      label-width="160px"
                      justify="center"
                    >
                      <el-form-item>
                        <el-checkbox-group v-model="ckLabelIdArray">
                          <el-checkbox
                            @change="labelChange(item.liName)"
                            v-for="(item, index) in labelData"
                            :label="item.liId"
                            :key="`${item.liName}_${index}`"
                            style="display: inline-block; margin-left: 30px"
                            >{{ item.liName }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>
                    </el-form>
                  </div>
                  <div
                    slot="footer"
                    class="dialog-footer"
                    style="text-align: right"
                  >
                    <span>{{ "已选" + ckLabelIdArray.length + "个标签" }}</span>
                    <el-button @click="visible = false" size="small"
                      >取消</el-button
                    >
                    <el-button
                      type="primary"
                      @click="
                        subCheckLabel();
                        visible = false;
                      "
                      size="small"
                      >确认</el-button
                    >
                  </div>
                </el-dialog>
              </el-form-item></el-col
            >
            <el-col :span="12">
              <el-form-item label="彩印内容">
                <el-input
                  type="textarea"
                  autosize
                  v-model="updateRequest.csContent"
                  clearable
                  :maxlength="50"
                  style="width: 210px"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>
          <el-form-item label="运营商">
            <el-checkbox-group v-model="updateRequest.operator">
              <el-checkbox
                :label="`${item.value}`"
                v-for="item in operatorList"
                :key="`${item.value}_operator`"
                :disabled="item.disabled"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="资质上传">
            <el-upload
              class="upload-demo"
              ref="upload"
              action=""
              :auto-upload="false"
              :on-change="handleChangeImg"
              :on-remove="handleRemoveImg"
              accept="image/jpeg,image/jpg,image/png,image/bmp"
              list-type="picture-card"
              :file-list="imgList"
            >
              <i class="el-icon-plus"></i>
            </el-upload>
            <div class="tips-wrap">
              <p class="red">运营商选择异网时必填</p>
              <p class="grey">
                最多只能上传6张图片，仅支持jpg、bmp、png、jpeg格式
              </p>
            </div>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer" style="text-align: right">
          <el-button @click="closeUpdate()" size="small">关闭</el-button>
          <el-button @click="updatetr()" size="small" type="primary"
            >保存</el-button
          >
        </div>
      </el-dialog>

      <!-- 弹窗 -->
      <div>
        <el-dialog
          width="30%"
          title="删除"
          :visible.sync="delVisible"
          :close-on-click-modal="false"
          append-to-body
        >
          <span style="font-size: 20px"
            >内容删除后，用户当前设置将失效，请确认？</span
          >
          <div slot="footer" class="dialog-footer" style="text-align: right">
            <el-button @click="delVisible = false" size="small"
              >取 消</el-button
            >
            <el-button
              type="primary"
              @click="
                delVisible = false;
                deltr();
              "
              size="small"
              >确 定</el-button
            >
          </div>
        </el-dialog>
      </div>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="导入文本彩印"
      :visible.sync="importCYHiddent"
      :close-on-click-modal="false"
      width="50%"
      @close="clearUpload"
    >
      <el-form class="demo-form-inline" label-position="left" label-width="100px">
        <el-form-item label="文本彩印文件">
          <div class="file-box">
            <el-input v-model="fileName" disabled="" size="small"></el-input>
            <el-upload
              class="upload-demo"
              ref="upload"
              action=""
              :auto-upload="false"
              :on-change="handleChange"
              :on-remove="handleRemove"
              accept=".xls, .xlsx"
              :show-file-list="false"
            >
              <el-button type="primary" size="small">上传excel表</el-button>
            </el-upload>
            <a class="temp-down" href="javaScript:;" @click="getTemp()"
              >下载模板文件</a
            >
          </div>
        </el-form-item>
        <el-form-item label="资质上传">
          <el-upload
            ref="upload"
            action=""
            :auto-upload="false"
            :on-change="handleChangeImg"
            :on-remove="handleRemoveImg"
            accept="image/jpeg,image/jpg,image/png,image/bmp"
            list-type="picture-card"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
          <div class="tips-wrap">
            <p class="grey">
              最多只能上传6张图片，仅支持jpg、bmp、png、jpeg格式
            </p>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer" style="text-align: right">
        <el-button @click="clearUpload()" size="small">取 消</el-button>
        <el-button type="primary" @click="submitForm()" size="small"
          >确 定</el-button
        >
      </div>
      <!-- 错误信息 -->
      <el-dialog
        width="40%"
        title="错误信息"
        :visible.sync="innerVisible"
        :close-on-click-modal="false"
        append-to-body
        @close="errorList = []"
      >
      <div class="error-list-wrap">
        <p class="error-item" v-for="(item, index) in errorList" :key="index">
          <span style="color: red">{{ item }}</span>
        </p>
      </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import { postDownload } from './../../../../servers/httpServer.js';
import { dowandFile, formDate } from './../../../../util/core.js';
import { post } from './../../../../servers/httpServer.js';
export default {
  name: "textCS",
  data () {
    return {
      loading: false,
      value3: '',
      tableLoading: false,
      pageTotal: 0,
      statusData: [{ csStatusNo: 1, csStatusName: '已上架' }, { csStatusNo: 4, csStatusName: '已下架' }], //状态下拉框选项
      checked: [],
      updateVisible: false,
      delVisible: false,
      tableData: [],
      multipleSelection: [],
      contentTypeData: [
        { "name": "文本彩印", "cyType": "1" },
        { "name": "挂机短信", "cyType": "2" },
        { "name": "企业视彩号主叫彩印", "cyType": "3" },
        { "name": "企业视彩号被叫彩印", "cyType": "4" },
        { "name": "主叫文本彩印", "cyType": "5" },
        { "name": "被叫文本彩印", "cyType": "6" },
        { "name": "直投闪短信", "cyType": "7" }
      ],
      dateTime: [],
      //请求数据
      //查询
      searchReq: {
        csContent: "",
        csContentNo: "",
        csLabelName: "",
        startTime: "",
        endTime: "",
        csStatus: "",
        serviceType: null,// 1:个人彩印 2:名片号 -1:全部
        pageSize: 10,
        pageNum: 1
      },
      file: "",//上传文件流
      fileName: '',//文件名
      importCYHiddent: false,//导入彩印盒
      //导出
      exportReq: {
        csContent: "",
        csContentNo: "",
        startTime: "",
        endTime: "",
        csStatus: "",
        pageSize: 0,
        pageNum: 1
      },
      ckLabelNames: '',
      ckLabelNameArray: [],
      ckLabelIdArray: [],
      groupData: [], //内容分类变量
      labelData: [], //内容标签变量
      visible: false,
      updateRequest: {
        csId: "",
        csContentNo: "",
        csContent: "",
        csGroupId: "",
        csLabelId: "",
        serviceType: 1, // 1:个人彩印 2:名片号
        operator: [1],// 1:移动 2:联通 3:电信
        qualificationsFiles: "", //资质文件
      },
      imgList: [], //资质文件
      //删除
      delRequest: {
        csContentNos: []
      },
      request: {
        csGroupName: "",
        csLabelName: "",
        serviceType: -1, // -1:全部 1:个人彩印 2:名片号
      },
      operatorList: [
        {
          label: "移动",
          value: 1,
          disabled: true
        },
        {
          label: "联通",
          value: 2,
          disabled: false
        },
        {
          label: "电信",
          value: 3,
          disabled: false
        }
      ],
      innerVisible: false, // 批量上传错误信息弹窗
      errorList: [],// 批量上传错误信息列表
    };
  },
  mounted () {
    // this.statusSilde();
    // this.groupDatas();
    // this.labelDatas();
  },
  methods: {
    closeUpdate () {
      this.updateVisible = false;
      this.updateRequest = {
        csId: "",
        csContentNo: "",
        csContent: "",
        csGroupId: "",
        csLabelId: "",
        serviceType: 1, // 1:个人彩印 2:名片号
        operator: [1],// 1:移动 2:联通 3:电信
        qualificationsFiles: "", //资质文件
      };
      this.imgList = [];
    },
    handleChangeImg (file, fileList) {
      var typeArr = ["image/jpeg", "image/jpg", "image/png", "image/bmp"];
      if (!typeArr.includes(file.raw.type)) {
        this.$message.error("请上传jpg、png、jpeg、bmp格式图片");
        fileList.pop();
        return;
      }
      if (fileList.length > 6) {
        this.$message.error("最多只能上传6张图片");
        fileList.pop();
        return;
      }
      // 限制所有图片加起来不能超过100M
      var totalSize = 0;
      for (var i = 0; i < fileList.length; i++) {
        // 如果是base64格式，则用base64的长度计算，否则用file的size属性
        console.log(fileList[i], "xxxx");
        if (fileList[i].url && fileList[i].url.indexOf("data:image") === 0) {
          totalSize += fileList[i].url.length;
        } else {
          totalSize += fileList[i].size;
        }
      }
      console.log(totalSize / (1024 * 1024), "图片总大小");
      if (totalSize > 10 * 1024 * 1024) {
        this.$message.error("图片总大小不能超过10M");
        fileList.pop();
        return;
      }
      this.imgList.push(file);
    },
    handleRemoveImg (file, fileList) {
      console.log(file,"来衡量的说服力");
      console.log(fileList, "李经理回来的实例化");
      this.imgList.map((item,index) => {
        if( item.uid  === file.uid) {
          // return item !== file;
          // return item.uid  !== file.uid;
          this.imgList.splice(index, 1);
        }
      });
      // this.imgList = fileList;
    },
    getContentName: function (contentType) {
      if (contentType == null || contentType == 1) {
        return "文本彩印";
      } else if (contentType == 2) {
        return "挂机短信";
      } else if (contentType == 3) {
        return "企业视彩号主叫彩印";
      } else if (contentType == 4) {
        return "企业视彩号被叫彩印";
      } else if (contentType == 5) {
        return "主叫文本彩印";
      } else if (contentType == 6) {
        return "被叫文本彩印";
      } else if (contentType == 7) {
        return "直投闪短信";
      }
    },
    getDeliveryWayName: function (deliveryWay) {
      if (deliveryWay == null) {
        return "-";
      } else if (deliveryWay == 2) {
        return "东盟";
      } else if (deliveryWay == 5) {
        return "号百";
      } else if (deliveryWay == 6) {
        return "联通在线";
      } else if (deliveryWay == 8) {
        return "彩讯";
      }
    },
    groupDatas: function () {
      return new Promise((resolve, reject) => {
        this.$http
          .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
            emulateJSON: true
          })
          .then(function (res) {
            this.groupData = res.data;
            resolve(res.data);
          })
          .catch(function (err) {
            console.log(err);
            reject(err);
          });
      })
    },
    //内容标签选项请求
    labelDatas: function () {
      return new Promise((resolve, reject) => {
        this.$http
          .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
            emulateJSON: true
          })
          .then(function (res) {
            this.labelData = res.data;
            resolve(res.data);
          }).catch(function (err) {
            console.log(err);
            reject(err);
          });
      })
    },
    clearUpload () {
      this.importCYHiddent = false;
      this.$refs.upload.clearFiles();
      this.file = '';
      this.imgList = [];
    },
    check (vm) {
      if (!vm.searchReq.startTime) {
        //vm.$message.error("开始日期不能为空");
        return true;
      }
      if (!vm.searchReq.endTime) {
        //vm.$message.error("结束时间不能为空");
        return true;
      }
      if (vm.searchReq.startTime > vm.searchReq.endTime) {
        vm.$message.error("开始时间不能晚于结束时间");
        return false;
      }
      return true;
    },
    //状态选项请求
    // statusSilde: function() {
    //   this.$http
    //     .get(`${this.proxyUrl}/content/csText/getCsStatus`, { emulateJSON: true })
    //     .then(function(res) {
    //       if (res.data.resStatus == "0") {
    //         for(let i=0;i<res.data.datas.length;i++){
    //           if(res.data.datas[i].csStatusName=="待上架"){
    //             res.data.datas.splice(i,1);
    //           }
    //         }
    //         this.statusData = res.data.datas;
    //       } else if (res.data.resStatus == "1") {
    //         console.log("请求失败");
    //       }
    //     });
    // },
    //表格多选框
    handleSelectionChange (val) {
      this.multipleSelection = val;
      this.delRequest.csContentNos.length = 0;
      for (var i = 0; i < val.length; i++) {
        this.delRequest.csContentNos.push(val[i].csTextInfoNo);
      }
    },
    //请求----------------------------
    //查询请求
    search: function (searchReq) {
      if (!this.check(this)) {
        return false;
      }
      if (this.dateTime && this.dateTime.length > 0) {
        searchReq.startTime = formDate(new Date(this.dateTime[0]), 'yyyy-MM-dd hh:mm:ss');
        searchReq.endTime = formDate(new Date(this.dateTime[1]), 'yyyy-MM-dd hh:mm:ss');
      } else {
        searchReq.startTime = '';
        searchReq.endTime = '';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csText/getCsText`, searchReq, {
          emulateJSON: true
        })
        .then(function (res) {
          // if(res.data.datas.length==0){
          //   this.$message('查无数据');
          //   this.tableLoading=false;
          //   return false;
          // }
          this.tableLoading = false;
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
        });
    },
    //获取文件信息
    handleChange (file, fileList) {
      this.file = file.raw;
      this.fileName = file.name;
    },
    handleRemove (file, fileList) {
      this.file = '';
      this.fileName = '';
    },
    //导入彩印文件
    importTextCY () {
      this.importCYHiddent = true;
      this.fileName = '';
      if (this.$refs.upload) {
        this.$refs.upload.clearFiles();
      }
    },
    //提交批量用户信息
    submitForm () {
      if (!this.file) {
        this.$message({
          message: '请上传excel文件',
          type: 'warning'
        })
        return;
      }
      // imgList中的图片blob转base64,fileNames:fileBase64|fileNames:fileBase64拼接
      const list = this.imgList.map(async item => {
        // base64格式的图片url不为空
        if (item.url && item.url.indexOf("data:image") === 0) {
          return `${item.name}:${item.url}`;
        } else {
          const base64 = await this.blobToBase64(item.raw);
          return `${item.name}:${base64}`;
        }
      });
      Promise.all(list).then(result => {
        const qualificationsFiles = result.join("|");
        let formData = new FormData();
        formData.append('file', this.file);
        formData.append('qualificationsFiles', qualificationsFiles);
        post(`/content/csText/uploadText`, formData).then(res => {
          if (res.data.resStatus == "0") {
            this.$message({
              message: '提交成功',
              type: 'success'
            })
            this.importCYHiddent = false;
            this.file = '';
            this.imgList = [];
          } else if (res.data.resStatus == "1" && res.data.datas) {
            this.innerVisible = true;
            this.errorList = res.data.datas;
          } else {
            this.$message.error(res.data.resText);
          }
        })
      });

    },
    //下载模板文件
    getTemp () {
      window.open(`${this.proxyUrl}/content/csText/getTemplate`);
    },
    //导出excel
    exportText () {
      this.loading = true;
      if (!this.check(this)) {
        return false;
      }
      if (this.searchReq.startTime == null) {
        this.searchReq.startTime = '';
      }
      if (this.searchReq.endTime == null) {
        this.searchReq.endTime = '';
      }
      //this.tableLoading=true;
      this.exportReq.csContent = this.searchReq.csContent;
      this.exportReq.csContentNo = this.searchReq.csContentNo;
      this.exportReq.startTime = this.searchReq.startTime;
      this.exportReq.endTime = this.searchReq.endTime;
      this.exportReq.csStatus = this.searchReq.csStatus;
      this.exportReq.contentType = this.searchReq.contentType;
      postDownload(`/content/csText/exportCsText`, this.exportReq).then(res => {
        dowandFile(res.data, '文本彩印.xlsx');
        this.loading = false;
      })
    },

    labelChange: function (labelName) {
      let checkedBoolean = false;
      for (let i = 0; i < this.ckLabelNameArray.length; i++) {
        if (labelName === this.ckLabelNameArray[i]) {
          this.ckLabelNameArray.splice(i, 1);
          checkedBoolean = true;
        }
      }
      if (!checkedBoolean) {
        this.ckLabelNameArray.push(labelName);
      }
    },
    subCheckLabel: function () {
      this.updateRequest.csLabelId = this.ckLabelIdArray.join(',');
      this.ckLabelNames = this.ckLabelNameArray.join(',');
    },
    changeContentType (e) {
      this.operatorList[0].disabled = true;
      this.operatorList[1].disabled = (e == 1 || e == null);
      this.operatorList[2].disabled = (e == 1 || e == null);
    },
    //编辑
    async openModify (row) {
      this.imgList = [];
      this.updateRequest.csId = row.csTextInfoNo;
      this.updateRequest.csContentNo = row.csNumber;
      this.updateRequest.csContent = row.csTextContent;
      this.updateRequest.csGroupId = row.csGroupId;
      this.updateRequest.csLabelId = row.csLabelId;
      this.updateRequest.contentType = row.contentType;
      this.updateRequest.operator = row.operator ? row.operator.split(",") : [];
      console.log(this.updateRequest, "编辑文本彩印请求参数");
      this.request.serviceType = row.serviceType;
      this.changeContentType(row.contentType);
      this.imgList = row.qualificationsFiles ? row.qualificationsFiles.split("|").map(item => {
        let arr = item.split(":");
        return {
          name: arr[0],
          url: `${arr[1]}:${arr[2]}`
        };
      }) : [];
      console.log(this.imgList, "资质文件列表");
      this.updateRequest.serviceType = row.serviceType;
      this.ckLabelNames = row.csLabelName;

      if (row.csLabelName != null && row.csLabelName != '') {
        this.ckLabelNameArray = row.csLabelName.split(",");
      }

      if (row.csLabelId != null && row.csLabelId != '') {
        this.ckLabelIdArray = row.csLabelId.split(",");
      }

      for (let i = 0; i < this.ckLabelIdArray.length; i++) {
        this.ckLabelIdArray[i] = parseInt(this.ckLabelIdArray[i]);
      }
      await this.groupDatas();
      await this.labelDatas();
      this.updateVisible = true;
    },
    blobToBase64 (blob) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(blob);
        reader.onload = () => {
          const base64 = reader.result;
          resolve(base64);
        }
        reader.onerror = function () {
          reject(new Error("Failed to load file"));
        };
      })
    },
    updatetr: function () {
      if (!this.updateRequest.csGroupId) {
        this.$message("内容分类不能为空");
        return;
      }
      if (!this.updateRequest.csLabelId) {
        this.$message("内容标签不能为空");
        return;
      }
      if (!this.updateRequest.csContent || !this.updateRequest.csContent.trim()) {
        this.$message('内容不能为空');
        return;
      }
      // 有联通或电信运营商时，资质文件不能为空
      if (
        this.updateRequest.operator.includes(2) ||
        this.updateRequest.operator.includes(3)
      ) {
        if (this.imgList.length == 0) {
          this.$message('请上传资质文件');
          return;
        }
      }
      const operator = this.updateRequest.operator.join(",");
      // 资质文件 "fileNames:fileBase64|fileNames:fileBase64"
      // imgList中的图片blob转base64,fileNames:fileBase64|fileNames:fileBase64拼接
      const list = this.imgList.map(async item => {
        // base64格式的图片url不为空
        if (item.url && item.url.indexOf("data:image") === 0) {
          return `${item.name}:${item.url}`;
        } else {
          const base64 = await this.blobToBase64(item.raw);
          return `${item.name}:${base64}`;
        }
      });
      Promise.all(list).then(result => {
        this.updateRequest.qualificationsFiles = result.join("|");
        console.log(this.updateRequest.qualificationsFiles, "资质文件列表");
        console.log({ ...this.updateRequest, operator: operator }, "编辑文本彩印请求参数");
        this.updateRequest.csContent = this.updateRequest.csContent.trim();
        if (this.updateRequest.contentType == 1) {
          this.updateRequest.csType = 2;
        }
        if (this.updateRequest.contentType == 5) {
          this.updateRequest.csType = 8;
        }
        if (this.updateRequest.contentType == 6) {
          this.updateRequest.csType = 9;
        }
        this.updateVisible = false;
        this.$http
          .post(`${this.proxyUrl}/content/csText/updateCsText`, { ...this.updateRequest, operator: operator }, {
            emulateJSON: true
          })
          .then(function (res) {
            if (res.data.resStatus == "0") {
              this.search(this.searchReq);
              this.$message.success('修改成功');
            } else if (res.data.resStatus == "1") {
              this.$message.error("修改失败 " + res.data.resText);
            }
          });
      });

    },
    //单行删除
    deltr: function () {
      this.$http
        .post(`${this.proxyUrl}/content/csText/delCsText`, JSON.stringify(this.delRequest))
        .then(function (res) {
          const h = this.$createElement;
          this.delRequest.csContentNos.length = 0;
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.search(this.searchReq);
            this.delRequest.csContentNos.length = 0;
          } else if (res.data.resStatus == "1") {
            this.$message.error(res.data.resText);
            this.delRequest.csContentNos.length = 0;
          }
        });
    },
    //批量删除JSON.stringify(this.offSaleReq)
    delAny: function () {
      this.$http
        .post(`${this.proxyUrl}/content/csText/delCsText`, JSON.stringify(this.delRequest))
        .then(function (res) {
          const h = this.$createElement;
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.search(this.searchReq);
            this.delRequest.csContentNos.length = 0;
          } else if (res.data.resStatus == "1") {
            this.$message.error(res.data.resText);
            this.delRequest.csContentNos.length = 0;
          }

        });
    },
    handleSizeChange (val) {
      this.searchReq.pageNum = 1;
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if (this.dateTime && this.dateTime.length > 0) {
        this.searchReq.startTime = formDate(new Date(this.dateTime[0]), 'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime = formDate(new Date(this.dateTime[1]), 'yyyy-MM-dd hh:mm:ss');
      } else {
        this.searchReq.startTime = '';
        this.searchReq.endTime = '';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csText/getCsText`, this.searchReq, {
          emulateJSON: true
        })
        .then(function (res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
    handleCurrentChange (val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if (this.dateTime && this.dateTime.length > 0) {
        this.searchReq.startTime = formDate(new Date(this.dateTime[0]), 'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime = formDate(new Date(this.dateTime[1]), 'yyyy-MM-dd hh:mm:ss');
      } else {
        this.searchReq.startTime = '';
        this.searchReq.endTime = '';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csText/getCsText`, this.searchReq, {
          emulateJSON: true
        })
        .then(function (res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
    tableheaderClassName ({ row, rowIndex }) {
      return "table-head-th";
    },
    //路由跳转
    locationHref (href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
  margin-top: 3%;
  background-color: blue;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin: 0 auto;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th {
  background-color: #f5f5f5;
}
.file-box {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}
.temp-down {
  width: 200px;
}
.error-list-wrap {
  width: 100%;
  max-height: 400px;
  overflow-y:scroll;
  overflow-x: hidden;
}
.error-item+ .error-item {
  margin-top: 10px;
}
</style>
