<template>
    <div class="standard">
        <div class="user-titler">{{$route.name}}</div>
        <div style="margin: 20px;">
            <div class="button">
                <el-button type="primary" @click="addStandard()" size="small">新增标准类型</el-button>
            </div>
            <div class="tablebox">
                <el-table
                        :data="tableData"
                        border :header-cell-class-name="tableheaderClassNameZ" style="width:100%">
                    <el-table-column
                            prop="standardTypeName"
                            label="标准类型">
                    </el-table-column>
                    <el-table-column
                            label="来源信息">
                        <template slot-scope="scope">
                            <el-button type="text" @click="details(scope.row)" size="small">查看详情</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column
                            prop="numberCount"
                            label="号码个数">
                    </el-table-column>
                    <el-table-column
                            label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="editStandard(scope.row)">编辑</el-button>
                            <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                                <p style="margin: 10px;text-align:center">确定删除此项?</p>
                                <div style="margin: 10px;text-align:center">
                                    <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                    <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                                </div>
                                <div slot="reference">
                                    <el-button  type="text" size="small">删除</el-button>
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
        </div>
        <!--新增-->
        <div>
            <el-dialog title="新增标准类型" :visible.sync="addVisible"   :close-on-click-modal="false">
                <addstandard @addStandard="addStandard"></addstandard>
            </el-dialog>
        </div>
        <!--编辑-->
        <div>
            <el-dialog title="编辑标准类型" :visible.sync="editVisible"   :close-on-click-modal="false">
                <editstandard :standardTypeId="standardTypeId" @editStandard="editStandard"></editstandard>
            </el-dialog>
        </div>

        <!--查看详情-->
        <el-dialog
                title="详情"
                :visible.sync="dialogVisible"
                width="30%" :close-on-click-modal="false">
            <div>
                <p>来源<span class="mlet">标记类型</span></p>
                <p v-for="item in tableList">
                    {{item.sourceName}}
                    <span class="mlet">{{item.markType}}</span>
                </p>
            </div>
            <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">返回</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
    import addstandard from './addstandard'
    import editstandard from './editstandard'
    import {postHeader} from '@/servers/httpServer.js'
    export default {
        name: 'standard',
        data(){
            return{
                editVisible:false,
                addVisible:false,
                dialogVisible: false,
                tableData: [],
                currentPage: 1,
                total:0,
                searchForm:{
                    pageSize:10,
                    pageNo:1
                },
                tableList:[],
                standardTypeId:''//编辑id
            }
        },
        components: {
            addstandard,
            editstandard
        },
        created(){
//            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            search() {
                let vm = this;
                postHeader('queryStandardType',JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.standardTypeList;
                        vm.total = data.data.total
                    }
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
            //新增标准类型
            addStandard() {
                let vm = this;
                vm.addVisible = !vm.addVisible;
            },
            //编辑
            editStandard(row){
                let vm = this;
                vm.editVisible = !vm.editVisible;
                if(row){
                    vm.standardTypeId = row.standardTypeId;
                }
            },
            //删除
            deletebtn(row) {
                let vm = this;
                row.show = false;
                postHeader('deleteStandardType',JSON.stringify({standardTypeId:`${row.standardTypeId}`})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("删除成功");
                        this.search();
                    }else{
                        vm.$message.error("删除失败");
                    }
                })
            },
            //查看详情
            details(row){
                let vm = this;
                postHeader('querySourceDetail',JSON.stringify({standardTypeId:`${row.standardTypeId}`})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.dialogVisible = true;
                        let list = data.data.sourceDetailList;
                        let listtype = [
//                            {
//                                sourceName:'360',
//                                markType:'广告，营销'
//                            }
                        ]
                        list.forEach(item => {
                            let onoff = true;
                            listtype.forEach(val => {
                                if(item.sourceName == val.sourceName){
                                    onoff = false;
                                    val.markType += `,${item.markType}`;
                                }
                            })
                            if(onoff){
                                listtype.push({'sourceName':`${item.sourceName}`,'markType':`${item.markType}`});
                            }
                        })
                        vm.tableList = listtype;
                    }
                })
            }
        }
    }
</script>

<style scoped>
    .standard{

    }
    .button{
        margin-bottom: 20px;
    }
    .tablebox{
        max-width: 100%;
    }
    .mlet{
        margin-left: 20px;
    }
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
