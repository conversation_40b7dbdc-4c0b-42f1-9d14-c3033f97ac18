<template>
    <div class="addrule">
        <div class="content">
            <el-form :model="addForm.offlinePkgRule" ref="addForm.offlinePkgRule" :rules="rules" class="demo-form-inline app-form-item" label-width="35%" size="small" style="width: 80%">
                <el-form-item label="离线包ID：" prop="offlinePkgId">
                    <p>{{addForm.offlinePkgRule.offlinePkgId}}</p>
                </el-form-item>
                <el-form-item label="离线包名称：" prop="offlinePkgName">
                    <el-input v-model="addForm.offlinePkgRule.offlinePkgName"></el-input>
                </el-form-item>
                <el-form-item label="省份：" prop="provinceId">
                    <!--<el-select v-model="addForm.offlinePkgRule.provinceId" placeholder="请选择" @change="querySearchRegionList">-->
                        <!--<el-option-->
                                <!--v-for="item in provinceList"-->
                                <!--:key="item.provinceCode"-->
                                <!--:label="item.provinceName"-->
                                <!--:value="item.provinceCode">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                    <p>{{addForm.offlinePkgRule.provinceName}}</p>
                </el-form-item>
                <el-form-item label="地市：" prop="countyId">
                    <!--<el-select v-model="addForm.offlinePkgRule.countyId" placeholder="请选择">-->
                        <!--<el-option-->
                                <!--v-for="item in city"-->
                                <!--:key="item.regionCode"-->
                                <!--:label="item.regionName"-->
                                <!--:value="item.regionCode">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                    <p>{{addForm.offlinePkgRule.countyName}}</p>
                </el-form-item>
                <el-form-item label="分类：" prop="categoryid">
                    <el-checkbox-group v-model="addForm.offlinePkgRule.categoryid">
                        <el-checkbox :label="1" key="1">诈骗</el-checkbox>
                        <el-checkbox :label="2" key="2">黄页</el-checkbox>
                        <el-checkbox :label="3" key="3">标记</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="号码条数：" prop="numberCount">
                    <el-input v-model.number="addForm.offlinePkgRule.numberCount"></el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="cancle" size="small">取 消</el-button>
            <el-button type="primary" @click="submit" size="small">确 定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    export default {
        name: 'addrule',
        data(){
            return{
                name:'',
                addForm:{
                    type:2,//1新增，2编辑
                    offlinePkgRule:{
                        offlinePkgName:"",//离线包名称
                        provinceId:"",//省份id
                        countyId:'',//地市id
                        category:'',//分类
                        categoryid:[],//分类
                        numberCount:''//号码条数
                    },
                },
                rules: {
                    offlinePkgId:[
                        { required: true, message: '离线包id', trigger: 'blur' },
                    ],
                    offlinePkgName: [
                        { required: true, message: '请输入离线包名称', trigger: 'blur' },
                    ],
                    provinceId: [
                        { required: true, message: '请选择省份', trigger: 'change' }
                    ],
                    countyId: [
                        { required: true, message: '请选择地市', trigger: 'change' }
                    ],
                    categoryid: [
                        { type: 'array', required: true, message: '请至少选择一个活动性质', trigger: 'change' }
                    ],
                    numberCount: [
                        {required: true, message: '请输入号码条数', trigger: 'blur' },
                        { type: 'number', message: '必须为数字值',trigger: 'blur'}
                    ],
                },
                dialogVisible:false,
                filterText:'',
                listarr:[],
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                city:[],
            }
        },
        components: {},
        created(){
            this.listrow1();
        },
        props:['editVisible','listrow'],

        watch: {
            editVisible() {
                if(!this.editVisible){
                    this.addForm={
                        type:2,//1新增，2编辑
                        offlinePkgRule:{
                            offlinePkgName:"",//离线包名称
                            provinceId:"",//省份id
                            countyId:'',//地市id
                            category:'',//分类
                            categoryid:[],//分类
                            numberCount:''//号码条数
                        }
                    }
                    this.resetForm('addForm.offlinePkgRule');
                }
            },
            listrow(){
                if(this.listrow){
                    let categoryid = [];
                    let arr = this.listrow.category.split(',');
                    arr.forEach(item=>{
                        if(item=='诈骗'){
                            categoryid.push(1)
                        }else if(item=='黄页'){
                            categoryid.push(2)
                        }else if(item=='标记'){
                            categoryid.push(3)
                        }
                    })
                    this.addForm.offlinePkgRule.offlinePkgId = this.listrow.offlinePkgId;
                    this.addForm.offlinePkgRule.offlinePkgName = this.listrow.offlinePkgName;
                    this.addForm.offlinePkgRule.countyName = this.listrow.countyName;
                    this.addForm.offlinePkgRule.countyId = this.listrow.countyId;
                    this.addForm.offlinePkgRule.provinceId = this.listrow.provinceId;
                    this.addForm.offlinePkgRule.provinceName = this.listrow.provinceName;
                    this.addForm.offlinePkgRule.numberCount = this.listrow.numberCount;
                    this.addForm.offlinePkgRule.categoryid = categoryid;
                }
            }
        },
        methods:{
            cancle(){
                this.$emit('editbtn');
            },
            submit(){
                let vm = this;
                vm.addForm.offlinePkgRule.category = vm.addForm.offlinePkgRule.categoryid.join(',');
                //编辑离线包
                this.$refs['addForm.offlinePkgRule'].validate((valid) => {
                    if (valid) {
                        postHeader('addOrUpdateOfflinePkg', JSON.stringify(this.addForm)).then(res=>{
                            let data = res.data;
                            if(data.code==0){
                                vm.$message.success("编辑成功");
                            }else{
                                vm.$message.error("编辑失败");
                            }
                            this.$emit('editbtn');
                        })
                    }else{
                        return false;
                    }
                });
            },
            //查询地市
            querySearchRegionList(){
                var queryRegion={
                    provinceCode: this.addForm.offlinePkgRule.provinceId
                };
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.city=res.data;
                    })
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            },
            //解决第一次进入页面没触发watch
            listrow1(){
                if(this.listrow){
                    let categoryid = [];
                    let arr = this.listrow.category.split(',');
                    arr.forEach(item=>{
                        if(item=='诈骗'){
                        categoryid.push(1)
                    }else if(item=='黄页'){
                        categoryid.push(2)
                    }else if(item=='标记'){
                        categoryid.push(3)
                    }
                })
                    this.addForm.offlinePkgRule.offlinePkgId = this.listrow.offlinePkgId;
                    this.addForm.offlinePkgRule.offlinePkgName = this.listrow.offlinePkgName;
                    this.addForm.offlinePkgRule.countyName = this.listrow.countyName;
                    this.addForm.offlinePkgRule.countyId = this.listrow.countyId;
                    this.addForm.offlinePkgRule.provinceId = this.listrow.provinceId;
                    this.addForm.offlinePkgRule.provinceName = this.listrow.provinceName;
                    this.addForm.offlinePkgRule.numberCount = this.listrow.numberCount;
                    this.addForm.offlinePkgRule.categoryid = categoryid;
                }
            }
        }
    }
</script>

<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .content{
        width: 640px;
        margin: 20px auto;
    }
    .content1{
        text-align: center;
    }
    .treebox{
        height: 350px;
        overflow: hidden;
    }
    .treeleft{
        width: 49%;
        height: 350px;
        display: inline-block;
        border-right: 1px solid #ccc;
        float: left;
        overflow: auto;
    }
    .treeright{
        width: 47%;
        display: inline-block;
        float: left;
        margin-left: 2%;
    }
    .delecla{
        display: inline-block;
        cursor: pointer;
        width: 20px;
        height: 20px;
        float: right;
        text-align: center;
        line-height: 20px;
    }
    .protitle{
        display: inline-block;
        width: 100%;
        line-height: 20px;
    }
    .procont{
        margin-right: 5px;
    }
</style>
