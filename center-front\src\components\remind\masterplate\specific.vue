<template>
    <div class="specific">
        <div class="user-titler">{{$route.name}}</div>
        <div class="app-norbox">
            <!--查询条件-->
            <div>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline" size="small">
                    <el-form-item label="模版ID">
                        <el-input  v-model="searchForm.sysUserName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="模版名称">
                        <el-input  v-model="searchForm.sysUserCompany" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="号码">
                        <el-input  v-model="searchForm.sysUserCompany" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" >查询</el-button>
                    </el-form-item>
                </el-form>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-add app-bottom">
                    <el-form-item>
                        <el-button type="primary" @click="addList()" size="small">新增</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain size="small">导出excel</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                <el-table-column prop="sysUserName" label="模版ID" />
                <el-table-column prop="sysStaffName" label="模版名称" />
                <el-table-column prop="sysRoleName" label="号码／号码群" />
                <el-table-column prop="sysUserEmail" label="模版内容">
                    <template slot-scope="scope">
                        <el-button type="text" @click="details" size="small">查看详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="sysState" label="提交时间"/>
                <el-table-column prop="sysState" label="审核时间"/>
                <el-table-column label="操作">
                    <template slot-scope="scope">
                        <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                            <p style="margin: 10px;text-align:center">确定删除此项?</p>
                            <div style="margin: 10px;text-align:center">
                                <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                            </div>
                            <div slot="reference">
                                <el-button  type="text" size="small">删除</el-button>
                            </div>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
            <!--分页-->
            <div class="block app-pageganit">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
        <!--查看详情-->
        <el-dialog
                title="模版详情"
                :visible.sync="dialogVisible"
                width="40%">
            <ul class="detbox">
                <li>
                    <p>拨打号码短信提醒内容:</p>
                    <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
                </li>
                <li>
                    <p>拨打号码短信提醒内容:</p>
                    <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
                </li>
                <li>
                    <p>拨打号码短信提醒内容:</p>
                    <p class="fontcol">您现在拨打的号码被****标记为*****~~~</p>
                </li>
            </ul>
        </el-dialog>
        <!--新增-->
        <div>
            <el-dialog title="新增标准类型" :visible.sync="addVisible"   :close-on-click-modal="false">
                <addspecific :provinceList="provinceList" @addList="addList"></addspecific>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    import addspecific from './addspecific'
    export default {
        name: 'specific',
        data(){
            return{
                addVisible:false,
                dialogVisible:false,
                //查询form对象定义
                searchForm: {
                    sysState:'', //状态
                    sysRoleId:'',//角色id,
                    sysUserName:'', //模糊查询
                    sysUserCompany: '', //模糊查询
                    provinceCode: '',
                    startTime:'',//创建时间
                    pageSize:10,// 每页显示条数
                    pageNum:1 // 查询的页码
                },
                provinceList:[{ provinceCode: '', provinceName: '全部'},
                    { provinceCode: '01', provinceName: '北京'},
                    { provinceCode: '02', provinceName: '天津'},
                    { provinceCode: '03', provinceName: '河北'},
                    { provinceCode: '04', provinceName: '山西'},
                    { provinceCode: '05', provinceName: '内蒙古'},
                    { provinceCode: '06', provinceName: '辽宁'},
                    { provinceCode: '07', provinceName: '吉林'},
                    { provinceCode: '08', provinceName: '黑龙江'},
                    { provinceCode: '09', provinceName: '上海'},
                    { provinceCode: '10', provinceName: '江苏'},
                    { provinceCode: '11', provinceName: '浙江'},
                    { provinceCode: '12', provinceName: '安徽'},
                    { provinceCode: '13', provinceName: '福建'},
                    { provinceCode: '14', provinceName: '江西'},
                    { provinceCode: '15', provinceName: '山东'},
                    { provinceCode: '16', provinceName: '河南'},
                    { provinceCode: '17', provinceName: '湖北'},
                    { provinceCode: '18', provinceName: '湖南'},
                    { provinceCode: '19', provinceName: '广东'},
                    { provinceCode: '20', provinceName: '海南'},
                    { provinceCode: '21', provinceName: '广西'},
                    { provinceCode: '22', provinceName: '重庆'},
                    { provinceCode: '23', provinceName: '四川'},
                    { provinceCode: '24', provinceName: '贵州'},
                    { provinceCode: '25', provinceName: '云南'},
                    { provinceCode: '26', provinceName: '陕西'},
                    { provinceCode: '27', provinceName: '甘肃'},
                    { provinceCode: '28', provinceName: '青海'},
                    { provinceCode: '29', provinceName: '宁夏'},
                    { provinceCode: '30', provinceName: '新疆'},
                    { provinceCode: '31', provinceName: '西藏'}
                ],
                tableData:[],
                currentPage: 1,
                total:0
            }
        },
        components: {
            addspecific
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
            },
            //查看详情
            details(){
                let vm = this;
                vm.dialogVisible = true;
            },
            //新增按钮
            addList(){
                let vm = this;
                vm.addVisible = !vm.addVisible
            },
            //删除
            deletebtn(row) {
                row.show=false;
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .detbox{
        margin-left: 30px;
        margin-right: 20px;
    }
    .detbox li{
        border-bottom: 1px solid #cccccc;
    }
    .fontcol{
        color: #cccccc;
    }
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
