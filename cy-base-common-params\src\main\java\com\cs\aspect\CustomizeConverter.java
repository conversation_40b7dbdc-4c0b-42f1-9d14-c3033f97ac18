package com.cs.aspect;

import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

import com.cs.param.constants.LogConstant;


public class CustomizeConverter extends ClassicConverter
{
    @Override
    public String convert(ILoggingEvent event)
    {
        String linkID = ThreadContext.get("linkID");
        String traceID = ThreadContext.get("traceID");
        String businessName = ThreadContext.get("businessName");
        if (StringUtils.isEmpty(linkID))
        {
            linkID= "";
        }
        if (StringUtils.isEmpty(traceID))
        {
            traceID = UUID.randomUUID().toString();
        }
        
        if (StringUtils.isEmpty(businessName))
        {
            businessName= "";
        }
        
        String logExt =
            "serialID_" + UUID.randomUUID().toString() + "|sessionID_" + traceID + "|linkID_" + linkID + "|" + LogConstant.LOG_MICROSERVICE_NAME + "|"
                + businessName;
        return logExt;
    }
}
