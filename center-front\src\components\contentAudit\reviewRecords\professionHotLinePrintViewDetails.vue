<template>
  <div>
    <h1 class="user-title">行业名片号-热线彩印审核明细</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="彩印ID">
          <el-input
            v-model="searchReq.hcNumber"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input
            v-model="searchReq.content"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印类型">
          <el-select v-model="searchReq.deliveryType" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="热线彩印闪信" value="1"></el-option>
            <el-option label="热线彩印挂机短信" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
            v-model="createTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 355px"
            size="small"
          />
        </el-form-item>
        <el-form-item label="审核时间">
          <el-date-picker
            v-model="auditTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 355px"
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleCurrentChange(1)"
            >查询</el-button
          >
          <el-button type="primary" size="small" @click="propVisible = true"
            >导出CSV</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!-- 表格 -->
    <div class="user-table">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
      >
        <el-table-column
          prop="hcNumber"
          label="彩印ID"
          width="240"
        ></el-table-column>
        <el-table-column
          prop="content"
          label="彩印内容"
          :show-overflow-tooltip="true"
          width="240"
        >
          <template slot-scope="scope">
            <div v-html="highLight(scope.row)"></div>
          </template>
        </el-table-column>
        <el-table-column label="热线号码" >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showPhoneDetail(scope.row.hotlineNoList)"
              >详情</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="deliveryType" label="彩印类型" width="200">
          <template slot-scope="scope">
            {{
              scope.row.deliveryType == 1 ? "热线彩印闪信" : "热线彩印挂机短信"
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="企业ID"
          prop="enterpriseId"
          width="200"
        ></el-table-column>
        <el-table-column
          label="企业名称"
          prop="enterpriseName"
          width="200"
        ></el-table-column>
        <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="showCorpImage(scope.row.certificateUrl)"
              :style="
                !scope.row.certificateUrl ? 'color:#808080' : 'color: #409EFF'
              "
              >详情</el-button
            >
          </template>
        </el-table-column>
        <el-table-column label="提交时间" prop="createTime" width="200">
          <template slot-scope="scope">
            {{
              scope.row.createTime
                ? moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column label="移动审核时间" prop="auditTime" width="200">
          <template slot-scope="scope">
            {{
              scope.row.auditTime
                ? moment(scope.row.auditTime).format("YYYY-MM-DD HH:mm:ss")
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column label="移动审核结果" width="200">
          <template slot-scope="scope">
            <span> {{ getStatusText(scope.row.approveStatus) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="联通审核结果" width="200">
          <template slot-scope="scope">
            <span> {{ getStatusText(scope.row.unicomApproveStatus) }} </span>
          </template>
        </el-table-column>
        <el-table-column label="电信审核结果" width="200">
          <template slot-scope="scope">
            <span>{{ getStatusText(scope.row.telecomApproveStatus) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="移动审核意见"
          prop="approveIdea"
          width="200"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="联通审核意见"
          prop="unicomApproveIdea"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
        <el-table-column
          label="电信审核意见"
          prop="telecomApproveIdea"
          :show-overflow-tooltip="true"
          width="200"
        ></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 弹窗-热线号码详情 -->
    <el-dialog title="热线号码" width="30%" :visible.sync="phonesVisible">
      <el-table :data="phonesData" :max-height="450">
        <el-table-column
          type="index"
          label="序号"
          width="120"
        ></el-table-column>
        <el-table-column prop="phone" label="热线号码"></el-table-column>
      </el-table>
    </el-dialog>
    <!-- 弹窗-企业资质 -->
    <el-dialog
      title="企业资质"
      class="zzWrap"
      width="30%"
      :visible.sync="corpImageVisible"
    >
      <ul
        class="contentlist"
        v-for="(corpImage, index) in corpImageList"
        :key="`${index}_${{ corpImage }}`"
      >
        <li>
          <a :href="corpImage" target="_blank">资质{{ index + 1 }}</a>
        </li>
      </ul>
    </el-dialog>
    <!-- 弹窗-导出CSV -->
    <el-dialog
      @open="exportClick"
      title="导出"
      :visible.sync="propVisible"
      :close-on-click-modal="false"
      width="45%"
    >
      <el-form
        label-width="80px"
        justify="center"
        :model="addReq"
        :rules="rules"
        ref="addReqForm"
      >
        <el-form-item label="文件名" prop="fileName">
          <el-input
            v-model="addReq.fileName"
            type="input"
            size="small"
            placeholder='请输入文件名，不能包含特殊字符：\/:*?"<>|，最多64字'
            style="width: 90%"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="addReq.remark"
            type="input"
            size="small"
            placeholder="请输入备注，长度不能超过256"
            style="width: 90%"
          ></el-input>
        </el-form-item>
      </el-form>
      <div style="margin-left: 80px; color: red">
        导出后请到系统管理-导出文件下载对应文件
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button type="primary" @click="confirmExport">确定</el-button>
        <el-button @click="cancelExport">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import axios from "axios";
export default {
  data () {
    return {
      tableLoading: false,
      tableData: [], // 表格数据
      // 搜索条件
      searchReq: {
        hcNumber: "",
        content: "",
        deliveryType: "",
        createTimeStart: "",
        createTimeEnd: "",
        auditTimeStart: "",
        auditTimeEnd: ""
      },
      createTime: [], // 提交时间范围
      auditTime: [], // 审核时间范围
      pageTotal: 0, // 总页数
      phonesData: [], // 热线号码详情数据
      phonesVisible: false, // 热线号码详情弹窗
      corpImageVisible: false, // 企业资质弹窗
      corpImageList: [], // 企业资质图片
      propVisible: false,
      addReq: {
        fileName: '',
        remark: ''
      },
      rules: {
        fileName: [
          { required: true, message: '请输入文件名', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
          { max: 64, message: '文件名不能超过64个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
        ]
      },
    }
  },
  created () {
    this.search();
  },
  methods: {
    moment,
    highLight (row) {
      return row.sign ? `【${row.sign}】${row.content}` : row.content;
    },
    getStatusText (status) {
      switch (status) {
        case "1":
          return "待提交审核";
        case "2":
          return "审核中";
        case "3":
          return "审核通过";
        case "4":
          return "审核驳回";
        case "-1":
          return "未使用";
        default:
          return "";
      }
    },
    handleSizeChange (val) {
      this.searchReq.pageNum = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange (val) {
      //当前页
      this.searchReq.pageNum = val;
      this.search();
    },
    search () {
      this.tableLoading = true;
      if (this.createTime) {
        this.searchReq.createTimeStart = this.createTime[0]
          ? moment(new Date(this.createTime[0])).format("YYYYMMDD")
          : "";
        this.searchReq.createTimeEnd = this.createTime[1]
          ? moment(new Date(this.createTime[1])).format("YYYYMMDD")
          : "";
      } else {
        this.searchReq.createTimeStart = "";
        this.searchReq.createTimeEnd = "";
      }
      if (this.auditTime) {
        this.searchReq.auditTimeStart = this.auditTime[0]
          ? moment(new Date(this.auditTime[0])).format("YYYYMMDD")
          : "";

        this.searchReq.auditTimeEnd = this.auditTime[1]
          ? moment(new Date(this.auditTime[1])).format("YYYYMMDD")
          : "";
      } else {
        this.searchReq.auditTimeStart = "";
        this.searchReq.auditTimeEnd = "";
      }
      console.log(this.searchReq);
      this.$http
        .post(
          `${this.proxyUrl}/content/hotSend/content/queryHotContentReview`,
          JSON.stringify(this.searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.resStatus == 0) {
            this.tableData = res.datas || [];
            this.pageTotal = res.pageTotal;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    exportClick () {
      this.$nextTick(() => {
        this.$refs.addReqForm.resetFields();
      })
    },
    // 导出CSV
    confirmExport () {
      this.$refs.addReqForm.validate(valid => {
        if (valid) {
          this.propVisible = !this.propVisible;
          if (this.searchReq.createTime) {
            this.searchReq.submitStart = this.searchReq.createTime[0] ? moment(new Date(this.searchReq.createTime[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.submitEnd = this.searchReq.createTime[1] ? moment(new Date(this.searchReq.createTime[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.submitStart = "";
            this.searchReq.submitEnd = "";
          }
          //审核时间
          if (this.searchReq.auditTime) {
            this.searchReq.reviewerStart = this.searchReq.auditTime[0] ? moment(new Date(this.searchReq.auditTime[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.reviewerEnd = this.searchReq.auditTime[1] ? moment(new Date(this.searchReq.auditTime[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.reviewerStart = "";
            this.searchReq.reviewerEnd = "";
          }
          var req = {
            fileName: this.addReq.fileName,
            remark: this.addReq.remark,
            taskType: 15,
            params: JSON.stringify(this.searchReq)
          }
          axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then((res) => {
            let data = res.data;
            if (data.code == 0) {
              this.$message.success("系统将生成文件名为" + this.addReq.fileName + "的文件");
            } else {
              this.$message.error(data.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    cancelExport () {
      this.propVisible = !this.propVisible;
      this.$refs.addReqForm.resetFields();
    },
    // 热线号码-详情
    showPhoneDetail (phones) {
      this.phonesData = (phones || []).map(item => ({ phone: item }));
      this.phonesVisible = true;
    },
    // 企业资质-详情
    showCorpImage (corpImage) {
      this.corpImageList = corpImage ? corpImage.split(";") : [];
      this.corpImageVisible = true;
    },
  }
}
</script>

