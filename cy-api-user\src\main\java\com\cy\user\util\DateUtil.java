package com.cy.user.util;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期、时间类
 *
 * <AUTHOR>
 */
public class DateUtil {

	private static ThreadLocal<SimpleDateFormat> local = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyy");
		}
	};
	private static ThreadLocal<SimpleDateFormat> localss = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyyMMddHHmmss");
		}
	};
	private static ThreadLocal<SimpleDateFormat> localSSS = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyyMMddHHmmssSSS");
		}
	};

	private static ThreadLocal<SimpleDateFormat> localStandard = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		}
	};
	private static ThreadLocal<SimpleDateFormat> localStandardDate = new ThreadLocal<SimpleDateFormat>() {
		@Override
		protected SimpleDateFormat initialValue() {
			return new SimpleDateFormat("yyyy-MM-dd");
		}
	};

	/**
	 * 获取系统当前时间（秒）
	 *
	 * <AUTHOR>
	 * @return
	 */
	public static Timestamp getTime() {
		SimpleDateFormat myFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar calendar = Calendar.getInstance();
		String mystrdate = myFormat.format(calendar.getTime());
		return Timestamp.valueOf(mystrdate);
	}

	/**
	 * 两个日期相减 格式 yyyyMMdd
	 *
	 * @param oldDate
	 * @param newDate
	 * @return 相差的天数
	 */
	public static String getsubDate() {
		SimpleDateFormat sdf = local.get();
		int currentYear = 0;
		int periodNum = 0;
		currentYear = Integer.parseInt(sdf.format(new Date()));
		periodNum = 2036 - currentYear;
		return String.valueOf(periodNum);
	}

	public static String getCurrentTime() {
		SimpleDateFormat myFormat = localss.get();
		return myFormat.format(new Date());
	}

	public static String getDateyyyyMMddHHmmssSSS() {
		SimpleDateFormat myFormat = localSSS.get();

		return myFormat.format(new Date());
	}

	public static String getStandardCurrentDate() {
		SimpleDateFormat myFormat = localStandardDate.get();

		return myFormat.format(new Date());
	}

	public static String getStandardCurrentTime() {
		SimpleDateFormat myFormat = localStandard.get();

		return myFormat.format(new Date());
	}

	public static Date getDateyyyyMMddHHmmssSSSDate() throws ParseException {
		SimpleDateFormat myFormat = localSSS.get();
		Calendar calendar = Calendar.getInstance();
		return myFormat.parse(calendar.getTime().toString());
	}

	public static String getDateyyyyMMddHHmmss() {
		SimpleDateFormat myFormat = localss.get();
		return myFormat.format(new Date());
	}

	public static String getDateyyyyMMdd() {
		SimpleDateFormat myFormat = local.get();
		return myFormat.format(new Date());
	}

}
