package com.cs.param.common;

/**
 * 
 * 操作类结果对象
 *
 * <AUTHOR>
 * @version [SAS4 V401R001, 2018年3月30日]
 *
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public class ResultCommon {
	// 0成功，1失败
	private Integer resStatus;
	private String resText;

	private static ResultCommon res = new ResultCommon();

	public static ResultCommon getInstance(Integer resStatus, String resText) {
		res.setResStatus(resStatus);
		res.setResText(resText);
		return res;
	}

	public Integer getResStatus() {
		return resStatus;
	}

	public void setResStatus(Integer resStatus) {
		this.resStatus = resStatus;
	}

	public String getResText() {
		return resText;
	}

	public void setResText(String resText) {
		this.resText = resText;
	}

}
