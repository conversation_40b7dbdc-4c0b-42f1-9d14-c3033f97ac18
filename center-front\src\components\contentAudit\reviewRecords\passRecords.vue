<template scope="scope">
    <div>
        <h1 class="user-title">已通过审核记录</h1>
        <div class="user-line"></div>
          <!--已通过审核记录-->
            <div class="app-search">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="类型">
                  <el-select v-model="searchReq.svMold" placeholder="请选择" size="small">
                    <el-option
                        v-for="item in statusData"
                        :key="item.csTypeId"
                        :label="item.csTypeName"
                        :value="item.csTypeId">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="内容ID">
                  <el-input v-model="searchReq.svNumber" size="small" :maxlength="32" clearable></el-input>
                </el-form-item>
                <el-form-item label="内容">
                  <el-input v-model="searchReq.svCard" size="small" :maxlength="50" clearable></el-input>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="审核人">
                  <el-input v-model="searchReq.svAssessor" size="small" clearable></el-input>
                </el-form-item>
                <el-form-item label="提交人">
                  <el-input v-model="searchReq.svSubmitUser" size="small" clearable></el-input>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                 <!-- <el-form-item label="审核人">
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName=='admin'" clearable></el-input>
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName=='shadmin'" clearable></el-input>
                  <el-input v-model="searchReq.svAssessor" size="small" v-if="userInfo.sysUserName!='admin' && userInfo.sysUserName!='shadmin'" :disabled="true"></el-input>
                </el-form-item> -->
                
                <el-form-item label="提交时间">
                  <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px" size="small"
                    />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="searchReq.pageNum = 1;search(searchReq)" size="small">查询</el-button>
                </el-form-item>
              </el-form>

            </div>
            <div class="user-table">
              <el-table
                  v-loading="tableLoading"
                  :data="tableData"
                  border
                  class="app-tab"
                  >
                <el-table-column
                    prop="svMoldName"
                    label="类型"
                    width="100">
                </el-table-column>
                <el-table-column
                    prop="svNumber"
                    label="内容ID"
                    width="240">
                </el-table-column>
                <el-table-column
                    label="内容"
                    width="400"
                    :show-overflow-tooltip="true">
                    <template slot-scope="scope">
                      <el-button v-if="(scope.row.svMold==3)" type="text" size="small" @click="detailVisible=true;rowData=scope.row;">{{scope.row.svCard}}</el-button>
                      <span v-if="!(scope.row.svMold==3)">{{scope.row.svCard}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="svStatusName"
                    label="审核意见"
                    width="100">
                </el-table-column>
                <el-table-column label="资质" width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showOtherImage(scope.row.qualificationsUrlList)"
                               :style="hasOtherImage(scope.row.qualificationsUrlList)?'':'color: #808080'">详情
                    </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="submitUser"
                    label="提交人"
                    width="200" :formatter="formmatPhone">
                </el-table-column>
                <el-table-column
                    prop="submitTime"
                    label="提交时间"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="assessor"
                    label="审核人"
                    width="200">
                </el-table-column>
                <el-table-column
                    prop="svSssesstime"
                    label="审核时间"
                    width="200">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-button @click="revokeVisible=true;rowData=scope.row;" type="text" size="small">撤销</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-dialog
                  width="30%"
                  title="撤销"
                  :visible.sync="revokeVisible"
                  :close-on-click-modal="false"
                  append-to-body>
                <el-form >
                  <el-form-item label="撤销原因">
                    <el-input type="textarea" v-model="revokeRequest.svRevokeCause" :maxlength="150" style="width:250px;" size=3></el-input>
                  </el-form-item>
                  <el-form-item>
                     <span> <font color="red">注：内容撤销后，用户当前设置将失效</font></span>
                  </el-form-item>
                </el-form>
                <div slot="footer" style="text-align: right;">
                  <el-button @click="revokeVisible = false;revokeRequest.svRevokeCause='' ">取 消</el-button>
                  <el-button type="primary" @click="revokeVisible = false;revoke(rowData);">确 定</el-button>
                </div>
              </el-dialog>
              <el-dialog title="资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
                <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
                  <li>
                    <a :href="item" target="_blank">资质{{ index + 1 }}</a>
                  </li>
                </ul>
              </el-dialog>
              <el-dialog
                  width="40%"
                  title="内容详情"
                  :visible.sync="detailVisible"
                  :close-on-click-modal="false"
                  append-to-body>
                  <el-row>
                  <el-col :span="12">彩印ID</el-col>
                  <el-col :span="12">内容</el-col>
                </el-row>
                <div style="height:300px;overflow:auto;">
                  <el-row v-if="rowData.content1">
                    <el-col :span="12"><div v-html="rowData.svNumber+'1'"></div></el-col>
                    <el-col :span="12"><div v-html="rowData.content1"></div></el-col>
                  </el-row>

                   <el-row v-if="rowData.content2">
                      <el-col :span="12"><div v-html="rowData.svNumber+'2'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.content2"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content3">
                      <el-col :span="12"><div v-html="rowData.svNumber+'3'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.content3"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content4">
                      <el-col :span="12"><div v-html="rowData.svNumber+'4'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content4"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content5">
                      <el-col :span="12"><div v-html="rowData.svNumber+'5'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content5"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content6">
                      <el-col :span="12"><div v-html="rowData.svNumber+'6'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content6"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content7">
                      <el-col :span="12"><div v-html="rowData.svNumber+'7'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content7"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content8">
                      <el-col :span="12"><div v-html="rowData.svNumber+'8'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content8"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content9">
                      <el-col :span="12"><div v-html="rowData.svNumber+'9'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content9"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content10">
                      <el-col :span="12"><div v-html="rowData.svNumber+'10'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content10"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content11">
                      <el-col :span="12"><div v-html="rowData.svNumber+'11'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content11"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content12">
                      <el-col :span="12"><div v-html="rowData.svNumber+'12'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content12"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content13">
                      <el-col :span="12"><div v-html="rowData.svNumber+'13'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content13"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content14">
                      <el-col :span="12"><div v-html="rowData.svNumber+'14'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content14"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content15">
                      <el-col :span="12"><div v-html="rowData.svNumber+'15'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content15"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content16">
                      <el-col :span="12"><div v-html="rowData.svNumber+'16'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content16"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content17">
                      <el-col :span="12"><div v-html="rowData.svNumber+'17'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content17"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content18">
                      <el-col :span="12"><div v-html="rowData.svNumber+'18'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content18"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content19">
                      <el-col :span="12"><div v-html="rowData.svNumber+'19'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content19"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content20">
                      <el-col :span="12"><div v-html="rowData.svNumber+'20'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content20"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content21">
                      <el-col :span="12"><div v-html="rowData.svNumber+'21'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content21"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content22">
                      <el-col :span="12"><div v-html="rowData.svNumber+'22'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content22"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content23">
                      <el-col :span="12"><div v-html="rowData.svNumber+'23'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content23"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content24">
                      <el-col :span="12"><div v-html="rowData.svNumber+'24'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content24"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content25">
                      <el-col :span="12"><div v-html="rowData.svNumber+'25'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content25"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.content26">
                      <el-col :span="12"><div v-html="rowData.svNumber+'26'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content26"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content27">
                      <el-col :span="12"><div v-html="rowData.svNumber+'27'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content27"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content28">
                      <el-col :span="12"><div v-html="rowData.svNumber+'28'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content28"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content29">
                      <el-col :span="12"><div v-html="rowData.svNumber+'29'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content29"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.content30">
                       <el-col :span="12"><div v-html="rowData.svNumber+'30'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.content30"></div></el-col>
                    </el-row>
                  </div>
                <div slot="footer" style="text-align: right;">
                  <el-button type="primary" @click="detailVisible = false">确 定</el-button>
                </div>
              </el-dialog>
               <!-- 分页 -->
              <div class="block app-pageganit">
              <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="searchReq.pageNum"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="pageTotal"  style="text-align: right;">
              </el-pagination>
              </div>
            </div>
    </div>
</template>
<script>
import {formDate} from './../../../util/core.js';
import {replacePhone} from './../../../util/core.js';
export default {
  //        name: 'textCS',
  data() {
    return {
      userInfo:JSON.parse(sessionStorage.getItem('userInfo')),
      tableLoading: false,
      rowData:'',
      pageTotal:0,
      pageNum:1,
      disabled: false,
      statusData:[],//类型下拉栏变量
      activeName: "passRecords",
      checked: [],
      rowData: [],
      revokeVisible: false,
      detailVisible: false,
      tableData: [
        // {
        //   isDisabled: false,
        //   csTextInfoNo: "1231",
        //   csGroupName: "asd",
        //   csLabelName: "asdas",
        //   csTextContent: "sdfsd",
        //   csTextStatus: "1",
        //   csSubmitTime: "2323",
        //   auditor: "sdfsdf",
        //   passTime: "asdas",
        //   useNumber: "asdasd"
        // },
        // {
        //   isDisabled: false,
        //   csTextInfoNo: "1231",
        //   csGroupName: "asd",
        //   csLabelName: "asdas",
        //   csTextContent: "sdfsd",
        //   csTextStatus: "2",
        //   csSubmitTime: "2323",
        //   auditor: "sdfsdf",
        //   passTime: "asdas",
        //   useNumber: "asdasd"
        // },
        // {
        //   isDisabled: false,
        //   csTextInfoNo: "1231",
        //   csGroupName: "asd",
        //   csLabelName: "asdas",
        //   csTextContent: "sdfsd",
        //   csTextStatus: "4",
        //   csSubmitTime: "2323",
        //   auditor: "sdfsdf",
        //   passTime: "asdas",
        //   useNumber: "asdasd"
        // },
        // {
        //   isDisabled: false,
        //   csTextInfoNo: "1231",
        //   csGroupName: "asd",
        //   csLabelName: "asdas",
        //   csTextContent: "sdfsd",
        //   csTextStatus: "1",
        //   csSubmitTime: "2323",
        //   auditor: "sdfsdf",
        //   passTime: "asdas",
        //   useNumber: "asdasd"
        // }
      ],
      // tableData:[],
      multipleSelection: [],
      dateTime:[],
      otherImageVisible: false,
      otherImage: [], //其他资质
      //请求数据
      //查询
      searchReq: {
        svAssessor: "",
        svSubmitUser: "",
        svMold:1,
        svNumber: "",
        svCard : "",
        startTime: "",
        endTime: "",
        svAssessor:"",
        pageSize: 10,
        pageNum: 1
      },
      revokeRequest: {
        svId:"",
        svNumber: "",
        svMold:"",
        svRevokeCause:""
      }
    };
  },
    created(){
        this.search();
    },
  mounted() {
    this.mold();
    // if(this.userInfo.sysUserName != "admin" && this.userInfo.sysUserName != "shadmin"){
    //     this.searchReq.svAssessor = this.userInfo.sysUserName;
    //   }
  },
  methods: {
    showOtherImage(otherImage) {
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    hasOtherImage(otherImage) {
      return !(otherImage == null || otherImage.length == 0);
    },
      formmatPhone(row, column){
          return replacePhone(row.submitUser);
      },
    //撤销请求
    revoke: function(val) {
      this.revokeRequest.svId = val.svId;
      this.revokeRequest.svNumber = val.svNumber;
      this.revokeRequest.svMold = val.svMold;
      if(!this.revokeRequest.svRevokeCause){
        this.$message.error("请填写撤销原因");
        this.revokeVisible=true;
        return false;
      }
      this.$http
        .post(`${this.proxyUrl}/content/auditRecord/revokeAuditRecord`, this.revokeRequest, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success("撤销成功");
            this.revokeRequest.svRevokeCause="";
            this.search(this.searchReq);
          } else if (res.data.resStatus == "1") {
            this.$message.error("撤销失败 "+res.data.resText);
            this.revokeRequest.svRevokeCause="";
          }
        });
    },
    //----------------------已通过审核记录--------------------------
    //类型下拉栏选项请求
    mold() {
      this.$http
        .get(`${this.proxyUrl}/content/csOff/getCsType`, { emulateJSON: true })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.statusData = res.data.datas;
          } else if (res.data.resStatus == "1") {
            console.log("请求失败");
          }
        });
    },
    //多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.checked = true;
      console.log(val);
    },
    //查询请求
    search: function(request) {
      // console.log(request);
      if(this.dateTime && this.dateTime.length>0){
        request.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        request.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        request.startTime='';
        request.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditRecord/getPassAuditRecord`, request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
          this.tableLoading=false;
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageNum = 1;
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading=true;
      this.$http
          .post(`${this.proxyUrl}/content/auditRecord/getPassAuditRecord`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
          this.tableLoading=false;
      })
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading=true;
       this.$http
          .post(`${this.proxyUrl}/content/auditRecord/getPassAuditRecord`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
          this.tableLoading=false;
        })
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
  background-color: #F5F5F5;
}
</style>
