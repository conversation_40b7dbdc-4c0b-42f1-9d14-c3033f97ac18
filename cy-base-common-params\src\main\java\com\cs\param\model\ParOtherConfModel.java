
package com.cs.param.model;

public class ParOtherConfModel {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String provinceCode;
	private String regionCode;
	private String createTime;
	private String effectTime;
	private String sendType;
	private String domainSelection;
	private String isCronSend;
	private String isFirst;
	private String sendOptionCode;
	private String sendOptionName;
	private String defPerContantId;
	private String defPerContant;
	private String defPerContantSt;
	private String defPerContantEt;
	private String defPerBoxId;
	private String defPerBoxName;
	private String defPerBoxSt;
	private String defPerBoxEt;
	private String suffixVariable;
	private String isDelete;
	private String provinceName;
	private String regionName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getDefPerBoxName() {
		return defPerBoxName;
	}

	public void setDefPerBoxName(String defPerBoxName) {
		this.defPerBoxName = defPerBoxName;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public String getCreateTime() {
		return createTime;
	}

	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

	public String getSendType() {
		return sendType;
	}

	public void setSendType(String sendType) {
		this.sendType = sendType;
	}

	public String getDomainSelection() {
		return domainSelection;
	}

	public void setDomainSelection(String domainSelection) {
		this.domainSelection = domainSelection;
	}

	public String getIsCronSend() {
		return isCronSend;
	}

	public void setIsCronSend(String isCronSend) {
		this.isCronSend = isCronSend;
	}

	public String getIsFirst() {
		return isFirst;
	}

	public void setIsFirst(String isFirst) {
		this.isFirst = isFirst;
	}

	public String getSendOptionCode() {
		return sendOptionCode;
	}

	public void setSendOptionCode(String sendOptionCode) {
		this.sendOptionCode = sendOptionCode;
	}

	public String getSendOptionName() {
		return sendOptionName;
	}

	public void setSendOptionName(String sendOptionName) {
		this.sendOptionName = sendOptionName;
	}

	public String getDefPerContantSt() {
		return defPerContantSt;
	}

	public void setDefPerContantSt(String defPerContantSt) {
		this.defPerContantSt = defPerContantSt;
	}

	public String getDefPerContantEt() {
		return defPerContantEt;
	}

	public void setDefPerContantEt(String defPerContantEt) {
		this.defPerContantEt = defPerContantEt;
	}

	public String getDefPerContantId() {
		return defPerContantId;
	}

	public void setDefPerContantId(String defPerContantId) {
		this.defPerContantId = defPerContantId;
	}

	public String getDefPerBoxId() {
		return defPerBoxId;
	}

	public void setDefPerBoxId(String defPerBoxId) {
		this.defPerBoxId = defPerBoxId;
	}

	public String getDefPerBoxSt() {
		return defPerBoxSt;
	}

	public void setDefPerBoxSt(String defPerBoxSt) {
		this.defPerBoxSt = defPerBoxSt;
	}

	public String getDefPerBoxEt() {
		return defPerBoxEt;
	}

	public void setDefPerBoxEt(String defPerBoxEt) {
		this.defPerBoxEt = defPerBoxEt;
	}

	public String getSuffixVariable() {
		return suffixVariable;
	}

	public void setSuffixVariable(String suffixVariable) {
		this.suffixVariable = suffixVariable;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getEffectTime() {
		return effectTime;
	}

	public void setEffectTime(String effectTime) {
		this.effectTime = effectTime;
	}

	public String getDefPerContant() {
		return defPerContant;
	}

	public void setDefPerContant(String defPerContant) {
		this.defPerContant = defPerContant;
	}

}
