package com.cy.user.api;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.cy.user.model.AddressListModel;
import com.cy.user.model.HarassModel;
import com.cy.user.model.HarassTypeModel;
import com.cy.user.model.VersionModel;

public interface HarassAPI {

	@RequestMapping(value = "/atfSevice/setHarass")
	void setHarass(HarassModel model);

	@RequestMapping(value = "/atfSevice/unmarkHarass")
	void unmarkHarass(HarassModel model);

	@RequestMapping(value = "/base/uploadAddressBook")
	void uploadAddressBook(AddressListModel model);

	@RequestMapping(value = "/atfSevice/getRemindStatus")
	Integer getRemindStatus(String phone);

	@RequestMapping(value = "/atfSevice/getHarass")
	HarassModel getHarass(HarassModel model);

	@RequestMapping(value = "/getHarassType")
	List<HarassTypeModel> getHarassType();

	@RequestMapping(value = "/getMarkMobiles")
	List<HarassModel> getMarkMobiles(HarassModel model);

	@RequestMapping(value = "/base/getLatestVersion")
	VersionModel getLatestVersion(VersionModel versionModel);

}
