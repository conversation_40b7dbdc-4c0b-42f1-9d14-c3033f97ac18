<template>
  <div>
    <h1 class="user-title">行业名片号-行业名片彩印</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="类型">
          <el-select v-model="searchReq.svType" size="small">
            <el-option
              v-for="(type, index) in typeList"
              :label="type.label"
              :value="type.value"
              :key="`${index}_type`"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="彩印ID">
          <el-input
            v-model="searchReq.svNumber"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input
            v-model="searchReq.svName"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="审核结果">
          <el-select v-model="searchReq.auditStatus" size="small">
            <el-option label="驳回" value="0"></el-option>
            <el-option label="通过" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核人">
          <el-input
            v-model="searchReq.auditUserName"
            size="small"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="提交人">
          <el-input
            v-model="searchReq.submitUserName"
            size="small"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="提交时间">
          <el-date-picker
            v-model="dateTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
            style="width: 355px"
            size="small"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="small">查询</el-button>
          <el-button type="primary" size="small">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="user-table">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column label="彩印类型"></el-table-column>
        <el-table-column prop="svNumber" label="彩印ID"></el-table-column>
        <el-table-column prop="svContent" label="彩印内容"></el-table-column>
        <el-table-column prop="" label="敏感词"></el-table-column>
        <el-table-column prop="" label="资质"></el-table-column>
        <el-table-column prop="" label="审核结果"></el-table-column>
        <el-table-column prop="" label="审核意见"></el-table-column>
        <el-table-column prop="" label="提交人"></el-table-column>
        <el-table-column prop="" label="提交时间"></el-table-column>
        <el-table-column prop="" label="审核人"></el-table-column>
        <el-table-column prop="" label="审核时间"></el-table-column>
        <el-table-column fixed="right" label="操作" width="160">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handlePass(scope.$index)"
              >通过</el-button
            >
            <el-button
              @click="handleReject(scope.$index)"
              type="text"
              size="small"
              >驳回</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right"
        >
        </el-pagination>
      </div>
      <!-- 资质详情 -->
      <el-dialog title="资质" :visible.sync="otherImageVisible" width="30%">
        <ul
          class="contentlist"
          v-for="(item, index) in otherImageList"
          :key="`${index}_img`"
        >
          <li>
            <a :href="item" target="_blank">资质{{ index + 1 }}</a>
          </li>
        </ul>
      </el-dialog>
      <!-- 驳回弹窗 -->
      <el-dialog title="驳回" :visible.sync="rejectVisible" width="30%">
        <el-form
          ref="rejectForm"
          :model="rejectForm"
          :rules="rejectRules"
          label-width="80px"
          :close-on-click-modal="false"
        >
          <el-form-item label="驳回原因" prop="type" >
            <el-radio-group v-model="rejectForm.type" @input="rejectForm.rejectReason = ''">
              <el-radio label="1">手动输入</el-radio>
              <el-radio label="2">系统预设</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" prop="rejectReason" >
            <el-input
              type="textarea"
              v-show="rejectForm.type == 1"
              v-model="rejectForm.rejectReason"
              maxlength="1024"
              show-word-limit
              placeholder=""
            />
            <el-select
              v-show="rejectForm.type == 2"
              v-model="rejectForm.rejectReason"
              placeholder="请选择"
              clearable
              size="small"
            >
              <el-option
                v-for="item in rejectResonses"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="handleRejectCancel">取 消</el-button>
          <el-button type="primary" @click="handleRejectSubmit"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
export default {
  data () {
    return {
      userInfo: {}, // 用户信息
      tableLoading: false,
      tableData: [], // 表格数据
      // 搜索条件
      searchReq: {
        svNumber: '',
        svName: '',
        svCompanyName: '',
        svCompanyId: '',
        svType: '',
        submitTime: '',
        pageNum: 1,
        pageSize: 10
      },
      dateTime: [], // 时间范围
      pageTotal: 0,// 总页数
      typeList: [{
        label: "所有",
        value: "-1"
      },
      {
        label: "主叫文本彩印",
        value: "1"
      },
      {
        label: "被叫文本彩印",
        value: "2"
      },
      {
        label: "用户主叫DIY",
        value: "3"
      },
      {
        label: "用户被叫DIY",
        value: "4"
      },
      ],
      otherImageVisible: false, // 资质弹窗
      otherImageList: [], // 资质列表
      rejectVisible: false,// 驳回弹窗
      rejectForm: {
        type: "1",// 1表示手动输入，2表示系统预设
        rejectReason: "",
        reviewer: "",
      },
      rejectResonses: [
        {
          value: '出现敏感词',
        }, {
          value: '缺少必填项',
        }, {
          value: '已存在相同彩印',
        }, {
          value: '内容不友好',
        }, {
          value: '其他问题',
        }
      ], // 驳回原因
      rejectRules: {
        type: [
          {
            required: true,
            message: '',
            trigger: ''
          }
        ],
        rejectReason: [
          {
            required: true,
            message: '请输入驳回原因',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  created () {
    const userInfo = sessionStorage.getItem("userInfo") || "{}";
    this.userInfo = JSON.parse(userInfo);
  },
  methods: {
    handleSizeChange (val) {
    },
    handleCurrentChange (val) {

    },
    showOtherImage (list) {
      this.otherImageList = list;
      this.otherImageVisible = true;
    },
    hasOtherImage (list) {
      return !(list == null || list.length == 0);
    },
    handlePass () { },
    handleReject () { },
    handleRejectSubmit () {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          this.rejectVisible = false;
          this.rejectForm.reviewer = this.userInfo.userName || "";
        } else {
          this.$message.error('请检查输入项');
        }
      });
    },
    handleRejectCancel () {
      // this.rejectVisible = false;
      this.$refs.rejectForm.resetFields();
    },
  }
}
</script>
