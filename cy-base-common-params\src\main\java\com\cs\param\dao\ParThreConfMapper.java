package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParThreConfCommon;
import com.cs.param.model.ParThreConfModel;

@Repository
public interface ParThreConfMapper {

	int updateParThreConfByPK(ParThreConfCommon common) throws SQLException;

	int deleteParThreConfByPK(ParThreConfCommon common) throws SQLException;

	ParThreConfModel getParThreConfDetailById(ParThreConfCommon common) throws SQLException;

	List<ParThreConfModel> queryPageInfo(ParThreConfCommon common) throws SQLException;

	List<ParThreConfModel> queryAllThreConfig() throws SQLException;

	Integer queryPageCount(ParThreConfCommon common) throws SQLException;

	int addParThreConf(ParThreConfCommon common) throws SQLException;

}
