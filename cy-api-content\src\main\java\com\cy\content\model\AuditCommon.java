package com.cy.content.model;

import java.io.Serializable;
import java.util.List;

public class AuditCommon implements Serializable {

    /**
	 * 
	 */
    private static final long serialVersionUID = 1L;
    /**
     * 彩印id
     */
    private String svId;
    /**
     * 彩印盒id
     */
    private String pkgId;
    /**
     * 彩印盒id
     */
    private String pkgName;

    private List<Integer> ids;
    /**
     * 彩印类型 0：新增 1：删除 2：修改（对于未修改的字段不需要填写到该表）
     */
    private String svType;

    // 0用户diy彩印 1单条彩印 2彩印盒
    private String svMold;
    /**
     * 彩印内容
     */
    private String svCard;
    /**
     * 彩印名称
     */
    private String svName;
    /**
     * 彩印编号
     */
    private String svNumber;
    /**
     * 彩印提交人
     */
    private String svSubmitUser;
    /**
     * 彩印提交时间
     */
    private String svSubmitTime;
    /**
     * 彩印提交来源 0：短信 1：WEB 2：SMC 3：WAP 4：手机客户端 5：第三方
     */
    private String svSubmitType;
    /**
     * 审核状态 1：待审批 2：审批不通过 3：审核通过 4：失效（提交新纪录，或彩印被删除）
     */
    private String svStatus;

    /**
     * 查询此审核状态以外的记录
     */
    private String noSvStatus;

    private List<String> svStatusList;
    /**
     * 0：新增 1：删除 2：修改（对于未修改的字段不需要填写到该表）
     */
    private String auditStatus;
    /**
     * 审核人
     */
    private String svAssessor;
    /**
     * 审核时间
     */
    private String svAssesstime;
    /**
     * 审批不通过的原因
     */
    private String svCause;

    private int pageNum = 1;// 查询的页码

    private int pageSize = 20;// 每页显示条数

    private int startNum;// 起始查询行

    private String startTime;
    private String endTime;
    private String revoke; //撤销人

    private String auditStartTime;
    private String auditEndTime;

    private String groupId;
    private String labelId;

    private String csPkgContent1, csPkgContent2, csPkgContent3, csPkgContent4, csPkgContent5, csPkgContent6,
	    csPkgContent7, csPkgContent8, csPkgContent9, csPkgContent10, csPkgContent11, csPkgContent12,
	    csPkgContent13, csPkgContent14, csPkgContent15, csPkgContent16, csPkgContent17, csPkgContent18,
	    csPkgContent19, csPkgContent20, csPkgContent21, csPkgContent22, csPkgContent23, csPkgContent24,
	    csPkgContent25, csPkgContent26, csPkgContent27, csPkgContent28, csPkgContent29, csPkgContent30;

    private String operator;

    private String svRevokeCause;

    private String sensitiveWords;

    private int submitCount;

    private List<String> opreatorProvIds;

    private String svProId;

    private String svProName;
    
    private String isBatch;

    private String svSignId;

    private String templateID;

    private String qualificationsFiles;

    /**
     * 联通审核状态
     * 1：待审批
     * 2：审批不通过
     * 3：审核通过
     */
    private Integer unicomStatus;

    /**
     * 联通审核意见
     */
    private String unicomMessage;

    /**
     * 电信审核状态
     * 1：待审批
     * 2：审批不通过
     * 3：审核通过
     */
    private Integer telecomStatus;

    /**
     * 电信审核意见
     */
    private String telecomMessage;

    private String notifyurl;

    private String deliveryType;
    private Integer serviceType;

    public Integer getServiceType() {
        return serviceType;
    }

    public void setServiceType(Integer serviceType) {
        this.serviceType = serviceType;
    }

    public String getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(String deliveryType) {
        this.deliveryType = deliveryType;
    }

    public String getNotifyurl() {
        return notifyurl;
    }

    public void setNotifyurl(String notifyurl) {
        this.notifyurl = notifyurl;
    }

    public String getTelecomMessage() {
        return telecomMessage;
    }

    public void setTelecomMessage(String telecomMessage) {
        this.telecomMessage = telecomMessage;
    }

    public Integer getTelecomStatus() {
        return telecomStatus;
    }

    public void setTelecomStatus(Integer telecomStatus) {
        this.telecomStatus = telecomStatus;
    }

    public String getUnicomMessage() {
        return unicomMessage;
    }

    public void setUnicomMessage(String unicomMessage) {
        this.unicomMessage = unicomMessage;
    }

    public Integer getUnicomStatus() {
        return unicomStatus;
    }

    public void setUnicomStatus(Integer unicomStatus) {
        this.unicomStatus = unicomStatus;
    }

    public String getQualificationsFiles() {
        return qualificationsFiles;
    }

    public void setQualificationsFiles(String qualificationsFiles) {
        this.qualificationsFiles = qualificationsFiles;
    }

    public String getTemplateID() {
        return templateID;
    }

    public void setTemplateID(String templateID) {
        this.templateID = templateID;
    }

    public String getSvSignId(){
        return svSignId;
    }

    public void setSvSignId(String svSignId){
        this.svSignId = svSignId;
    }

    public String getRevoke() {
        return revoke;
    }

    public void setRevoke(String revoke) {
        this.revoke = revoke;
    }

    public String getAuditStartTime() {
        return auditStartTime;
    }

    public void setAuditStartTime(String auditStartTime) {
        this.auditStartTime = auditStartTime;
    }

    public String getAuditEndTime() {
        return auditEndTime;
    }

    public void setAuditEndTime(String auditEndTime) {
        this.auditEndTime = auditEndTime;
    }

    public String getIsBatch() {
        return isBatch;
    }

    public void setIsBatch(String isBatch) {
        this.isBatch = isBatch;
    }

    public String getSvProId() {
	return svProId;
    }

    public void setSvProId(String svProId) {
	this.svProId = svProId;
    }

    public String getSvProName() {
	return svProName;
    }

    public void setSvProName(String svProName) {
	this.svProName = svProName;
    }

    public List<String> getOpreatorProvIds() {
	return opreatorProvIds;
    }

    public void setOpreatorProvIds(List<String> opreatorProvIds) {
	this.opreatorProvIds = opreatorProvIds;
    }

    public int getSubmitCount() {
	return submitCount;
    }

    public void setSubmitCount(int submitCount) {
	this.submitCount = submitCount;
    }

    public List<String> getSvStatusList() {
	return svStatusList;
    }

    public void setSvStatusList(List<String> svStatusList) {
	this.svStatusList = svStatusList;
    }

    public String getSensitiveWords() {
	return sensitiveWords;
    }

    public void setSensitiveWords(String sensitiveWords) {
	this.sensitiveWords = sensitiveWords;
    }

    public String getSvRevokeCause() {
	return svRevokeCause;
    }

    public void setSvRevokeCause(String svRevokeCause) {
	this.svRevokeCause = svRevokeCause;
    }

    public String getPkgName() {
	return pkgName;
    }

    public void setPkgName(String pkgName) {
	this.pkgName = pkgName;
    }

    public String getNoSvStatus() {
	return noSvStatus;
    }

    public void setNoSvStatus(String noSvStatus) {
	this.noSvStatus = noSvStatus;
    }

    public String getAuditStatus() {
	return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
	this.auditStatus = auditStatus;
    }

    public String getOperator() {
	return operator;
    }

    public void setOperator(String operator) {
	this.operator = operator;
    }

    public String getSvName() {
	return svName;
    }

    public void setSvName(String svName) {
	this.svName = svName;
    }

    public String getStartTime() {
	return startTime;
    }

    public void setStartTime(String startTime) {
	this.startTime = startTime;
    }

    public String getEndTime() {
	return endTime;
    }

    public void setEndTime(String endTime) {
	this.endTime = endTime;
    }

    public void setStartPage() {
	this.startNum = (pageNum - 1) * pageSize;
    }

    public int getPageNum() {
	return pageNum;
    }

    public void setPageNum(int pageNum) {
	this.pageNum = pageNum;
    }

    public int getPageSize() {
	return pageSize;
    }

    public void setPageSize(int pageSize) {
	this.pageSize = pageSize;
    }

    public int getStartNum() {
	return startNum;
    }

    public void setStartNum(int startNum) {
	this.startNum = startNum;
    }

    public String getSvCard() {
	return svCard;
    }

    public void setSvCard(String svCard) {
	this.svCard = svCard;
    }

    public String getSvNumber() {
	return svNumber;
    }

    public void setSvNumber(String svNumber) {
	this.svNumber = svNumber;
    }

    public String getSvAssessor() {
	return svAssessor;
    }

    public void setSvAssessor(String svAssessor) {
	this.svAssessor = svAssessor;
    }

    public String getSvAssesstime() {
	return svAssesstime;
    }

    public void setSvAssesstime(String svAssesstime) {
	this.svAssesstime = svAssesstime;
    }

    public String getSvCause() {
	return svCause;
    }

    public void setSvCause(String svCause) {
	this.svCause = svCause;
    }

    public String getSvId() {
	return svId;
    }

    public void setSvId(String svId) {
	this.svId = svId;
    }

    public String getSvType() {
	return svType;
    }

    public void setSvType(String svType) {
	this.svType = svType;
    }

    public String getSvSubmitUser() {
	return svSubmitUser;
    }

    public void setSvSubmitUser(String svSubmitUser) {
	this.svSubmitUser = svSubmitUser;
    }

    public String getSvSubmitTime() {
	return svSubmitTime;
    }

    public void setSvSubmitTime(String svSubmitTime) {
	this.svSubmitTime = svSubmitTime;
    }

    public String getSvSubmitType() {
	return svSubmitType;
    }

    public void setSvSubmitType(String svSubmitType) {
	this.svSubmitType = svSubmitType;
    }

    public List<Integer> getIds() {
	return ids;
    }

    public void setIds(List<Integer> ids) {
	this.ids = ids;
    }

    public String getSvMold() {
	return svMold;
    }

    public void setSvMold(String svMold) {
	this.svMold = svMold;
    }

    public String getPkgId() {
	return pkgId;
    }

    public void setPkgId(String pkgId) {
	this.pkgId = pkgId;
    }

    public String getSvStatus() {
	return svStatus;
    }

    public void setSvStatus(String svStatus) {
	this.svStatus = svStatus;
    }

    public String getGroupId() {
	return groupId;
    }

    public void setGroupId(String groupId) {
	this.groupId = groupId;
    }

    public String getLabelId() {
	return labelId;
    }

    public void setLabelId(String labelId) {
	this.labelId = labelId;
    }

    public String getCsPkgContent1() {
	return csPkgContent1;
    }

    public void setCsPkgContent1(String csPkgContent1) {
	this.csPkgContent1 = csPkgContent1;
    }

    public String getCsPkgContent2() {
	return csPkgContent2;
    }

    public void setCsPkgContent2(String csPkgContent2) {
	this.csPkgContent2 = csPkgContent2;
    }

    public String getCsPkgContent3() {
	return csPkgContent3;
    }

    public void setCsPkgContent3(String csPkgContent3) {
	this.csPkgContent3 = csPkgContent3;
    }

    public String getCsPkgContent4() {
	return csPkgContent4;
    }

    public void setCsPkgContent4(String csPkgContent4) {
	this.csPkgContent4 = csPkgContent4;
    }

    public String getCsPkgContent5() {
	return csPkgContent5;
    }

    public void setCsPkgContent5(String csPkgContent5) {
	this.csPkgContent5 = csPkgContent5;
    }

    public String getCsPkgContent6() {
	return csPkgContent6;
    }

    public void setCsPkgContent6(String csPkgContent6) {
	this.csPkgContent6 = csPkgContent6;
    }

    public String getCsPkgContent7() {
	return csPkgContent7;
    }

    public void setCsPkgContent7(String csPkgContent7) {
	this.csPkgContent7 = csPkgContent7;
    }

    public String getCsPkgContent8() {
	return csPkgContent8;
    }

    public void setCsPkgContent8(String csPkgContent8) {
	this.csPkgContent8 = csPkgContent8;
    }

    public String getCsPkgContent9() {
	return csPkgContent9;
    }

    public void setCsPkgContent9(String csPkgContent9) {
	this.csPkgContent9 = csPkgContent9;
    }

    public String getCsPkgContent10() {
	return csPkgContent10;
    }

    public void setCsPkgContent10(String csPkgContent10) {
	this.csPkgContent10 = csPkgContent10;
    }

    public String getCsPkgContent11() {
	return csPkgContent11;
    }

    public void setCsPkgContent11(String csPkgContent11) {
	this.csPkgContent11 = csPkgContent11;
    }

    public String getCsPkgContent12() {
	return csPkgContent12;
    }

    public void setCsPkgContent12(String csPkgContent12) {
	this.csPkgContent12 = csPkgContent12;
    }

    public String getCsPkgContent13() {
	return csPkgContent13;
    }

    public void setCsPkgContent13(String csPkgContent13) {
	this.csPkgContent13 = csPkgContent13;
    }

    public String getCsPkgContent14() {
	return csPkgContent14;
    }

    public void setCsPkgContent14(String csPkgContent14) {
	this.csPkgContent14 = csPkgContent14;
    }

    public String getCsPkgContent15() {
	return csPkgContent15;
    }

    public void setCsPkgContent15(String csPkgContent15) {
	this.csPkgContent15 = csPkgContent15;
    }

    public String getCsPkgContent16() {
	return csPkgContent16;
    }

    public void setCsPkgContent16(String csPkgContent16) {
	this.csPkgContent16 = csPkgContent16;
    }

    public String getCsPkgContent17() {
	return csPkgContent17;
    }

    public void setCsPkgContent17(String csPkgContent17) {
	this.csPkgContent17 = csPkgContent17;
    }

    public String getCsPkgContent18() {
	return csPkgContent18;
    }

    public void setCsPkgContent18(String csPkgContent18) {
	this.csPkgContent18 = csPkgContent18;
    }

    public String getCsPkgContent19() {
	return csPkgContent19;
    }

    public void setCsPkgContent19(String csPkgContent19) {
	this.csPkgContent19 = csPkgContent19;
    }

    public String getCsPkgContent20() {
	return csPkgContent20;
    }

    public void setCsPkgContent20(String csPkgContent20) {
	this.csPkgContent20 = csPkgContent20;
    }

    public String getCsPkgContent21() {
	return csPkgContent21;
    }

    public void setCsPkgContent21(String csPkgContent21) {
	this.csPkgContent21 = csPkgContent21;
    }

    public String getCsPkgContent22() {
	return csPkgContent22;
    }

    public void setCsPkgContent22(String csPkgContent22) {
	this.csPkgContent22 = csPkgContent22;
    }

    public String getCsPkgContent23() {
	return csPkgContent23;
    }

    public void setCsPkgContent23(String csPkgContent23) {
	this.csPkgContent23 = csPkgContent23;
    }

    public String getCsPkgContent24() {
	return csPkgContent24;
    }

    public void setCsPkgContent24(String csPkgContent24) {
	this.csPkgContent24 = csPkgContent24;
    }

    public String getCsPkgContent25() {
	return csPkgContent25;
    }

    public void setCsPkgContent25(String csPkgContent25) {
	this.csPkgContent25 = csPkgContent25;
    }

    public String getCsPkgContent26() {
	return csPkgContent26;
    }

    public void setCsPkgContent26(String csPkgContent26) {
	this.csPkgContent26 = csPkgContent26;
    }

    public String getCsPkgContent27() {
	return csPkgContent27;
    }

    public void setCsPkgContent27(String csPkgContent27) {
	this.csPkgContent27 = csPkgContent27;
    }

    public String getCsPkgContent28() {
	return csPkgContent28;
    }

    public void setCsPkgContent28(String csPkgContent28) {
	this.csPkgContent28 = csPkgContent28;
    }

    public String getCsPkgContent29() {
	return csPkgContent29;
    }

    public void setCsPkgContent29(String csPkgContent29) {
	this.csPkgContent29 = csPkgContent29;
    }

    public String getCsPkgContent30() {
	return csPkgContent30;
    }

    public void setCsPkgContent30(String csPkgContent30) {
	this.csPkgContent30 = csPkgContent30;
    }

}
