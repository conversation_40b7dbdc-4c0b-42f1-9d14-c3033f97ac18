option java_outer_classname = "ThresholdMessage";//生成的数据访问类的类名  
message ThresholdEntity {
  required string provinceCode = 1;
  optional string perPushTime = 2;
  optional string perPushMaxNum = 3;
  optional string perAllPushMaxNum = 4;
  optional string perDelayTime = 5;
  optional string bussPushTime = 6;
  optional string bussPushMaxNum = 7;
  optional string bussAllPushMaxNum = 8;
  optional string bussDelayTime = 9;
  optional string mediaPushTime = 10;
  optional string mediaPushMaxNum = 11;
  optional string mediaAllPushMaxNum = 12;
  optional string mediaDelayTime = 13;
}  