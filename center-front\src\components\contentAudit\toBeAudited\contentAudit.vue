<template scope="scope">
    <div>
        <!--<h1 class="user-title">批量下架内容</h1>-->
        <!--<hr class="user-line"/>-->
        <el-tabs v-model="activeName" class="user-title">
          <!--个人彩印-->
          <el-tab-pane label="个人彩印" name="personalCS">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline" style="width:105%;">
                <el-form-item label="彩印ID">
                  <el-input v-model="searchReq.svId"></el-input>
                </el-form-item>
                <el-form-item label="彩印内容">
                  <el-input v-model="searchReq.svCard"></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <!--<div>{{request.startTime}}</div>-->
                    <el-date-picker
                        v-model="searchReq.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                    <el-date-picker
                        v-model="searchReq.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search(searchReq)">查询</el-button>
                </el-form-item>
              </el-form>
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <el-button type="primary">批量通过</el-button>
                  <el-button type="primary">批量驳回</el-button>
                </el-form-item>
              </el-form>

            </div>
            <div>
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="svMoldName"
                    label="类型">
                </el-table-column>
                <el-table-column
                    prop="svId"
                    label="彩印ID">
                </el-table-column>
                <el-table-column
                    label="彩印内容">
                    <template slot-scope="scope">
                      <el-button type="text" size="small">查看详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="submitUser"
                    label="用户手机号">
                </el-table-column>
                <el-table-column
                    prop="submitCount"
                    label="提交次数">
                </el-table-column>
                <el-table-column
                    prop="submitTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="pass(scope.row);">通过</el-button>
                    <el-button @click="edit1Visible=true;rowData=scope.row;" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
                <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="edit1Visible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input v-model="rejectReq.svCause" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="edit1Visible = false">取 消</el-button>
                        <el-button type="primary" @click="edit1Visible = false;reject(rowData)">确 定</el-button>
                      </div>
                </el-dialog>
              <!-- 分页 -->
              <div class="block app-pageganit">
              <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="tableData.pageNum"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="pageTotal"  style="text-align: right;">
              </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <!--企业彩印/彩印盒-->
          <el-tab-pane label="企业彩印/彩印盒" name="enterpriseCS">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="企业编号">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="企业名称">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <!--<div>{{request.startTime}}</div>-->
                    <el-date-picker
                        v-model="request.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                    <el-date-picker
                        v-model="request.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </div>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="省份">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="地区">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="彩印/彩印盒内容:">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search()">查询</el-button>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <el-button type="primary">批量通过</el-button>
                  <el-button type="primary">批量驳回</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="彩印ID">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="企业编号">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="企业名称">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="省份">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="地区">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="彩印类型">
                </el-table-column>
                <el-table-column
                    label="彩印/彩印盒内容">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">查看详情</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input type="textarea" v-model="rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;reject(scope.row)">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="block app-pageganit">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="10"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableData.pageTotal"  style="text-align: right;">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <!--新媒彩印-->
          <el-tab-pane label="新媒彩印" name="mediaCS">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline" style="width:105%;">
                <el-form-item label="企业编号">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="企业名称">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <!--<div>{{request.startTime}}</div>-->
                    <el-date-picker
                        v-model="request.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                    <el-date-picker
                        v-model="request.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button style="float:right;" type="primary" @click="search()">查询</el-button>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <el-button type="primary">批量通过</el-button>
                  <el-button type="primary">批量驳回</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="彩印ID">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="企业编号">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="企业名称">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="新媒屏显">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="新媒挂机">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input type="textarea" v-model="rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;reject(scope.row)">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="block app-pageganit">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="10"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableData.pageTotal"  style="text-align: right;">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <!--省市地区提醒模版-->
          <el-tab-pane label="省市地区提醒模版" name="provTemplate">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="模板ID">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="模板名称">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="分类">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline" style="width:105%;">
                <el-form-item label="省份">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="地区">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <el-date-picker
                        v-model="request.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                    <el-date-picker
                        v-model="request.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search()">查询</el-button>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <el-button type="primary">批量通过</el-button>
                  <el-button type="primary">批量驳回</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="模板ID">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="模板名称">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="分类">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="省份">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="地区">
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="描述">
                </el-table-column>
                <el-table-column
                    label="模板内容">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">查看</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input type="textarea" v-model="rejectReq.rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;reject(scope.row)">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="block app-pageganit">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="10"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableData.pageTotal"  style="text-align: right;">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
          <!--特定号码提醒模板-->
          <el-tab-pane label="特定号码提醒模板" name="numTemplate">
            <div class="user-search3">
              <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="模板ID">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="模板名称">
                  <el-input v-model="request.csContentNo"></el-input>
                </el-form-item>
                <el-form-item label="分类">
                  <el-select v-model="request.status" placeholder="请选择">
                    <el-option
                        v-for="(value,key) in sildeData"
                        :key="key"
                        :label="value"
                        :value="key">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <div class="block">
                    <span class="demonstration">提交时间</span>
                    <el-date-picker
                        v-model="request.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                    <el-date-picker
                        v-model="request.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                  </div>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="search()">查询</el-button>
                </el-form-item>
              </el-form>

              <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                  <el-button type="primary">批量通过</el-button>
                  <el-button type="primary">批量驳回</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="user-table">
              <el-table
                  :data="tableData"
                  border
                  style="width: 94%;margin-left:0;"
                  @selection-change="handleSelectionChange">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="模板编号">
                </el-table-column>
                <el-table-column
                    prop="csGroupName"
                    label="模板名称">
                </el-table-column>
                <el-table-column
                    prop="csTextStatus"
                    label="号码/号码群">
                </el-table-column>
                <el-table-column
                    prop="auditor"
                    label="描述">
                </el-table-column>
                <el-table-column
                    label="模板内容">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">查看</el-button>
                  </template>
                </el-table-column>
                <el-table-column
                    prop="passTime"
                    label="提交时间">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="100">
                  <template slot-scope="scope">
                    <el-dialog
                        width="30%"
                        title="驳回"
                        :visible.sync="editVisible"
                        append-to-body>
                      <el-form label-width="80px" justify="center">
                        <el-form-item label="驳回原因">
                          <el-input type="textarea" v-model="rejectReason" ></el-input>
                        </el-form-item>
                      </el-form>
                      <div slot="footer" style="text-align: right;">
                        <el-button @click="editVisible = false">取 消</el-button>
                        <el-button type="primary" @click="editVisible = false;reject(scope.row)">确 定</el-button>
                      </div>
                    </el-dialog>
                    <el-button type="text" size="small">通过</el-button>
                    <el-button @click="editVisible=true" type="text" size="small">驳回</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 分页 -->
              <div class="block app-pageganit">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="10"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="tableData.pageTotal"  style="text-align: right;">
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
export default {
  //        name: 'textCS',
  data() {
    return {
      pageTotal:1,
      rowData:'',
      sildeData: [],
      rejectReason: "", //驳回原因
      activeName: "personalCS",
      // csTextStatus: [{ "1": "上架", "2": "待上架", "4": "下架" }],
      checked: [],
      editVisible: false,
      edit1Visible: false,
      //  tableData: [
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      csLabelName: "asdas",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      csLabelName: "asdas",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "2",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      csLabelName: "asdas",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "4",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      csLabelName: "asdas",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    }
      //  ],
      tableData:[],
      multipleSelection: [],
      searchReq: {
        svId: "",
        svCard: "",
        startTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1
      },
      //通过请求参数
      passRequest:{
        svId:"",
        svMold:""
      },
      //驳回请求参数
      rejectReq:{
        svCause:"",
        svId:"",
        svMold:""
      },
      delRequest: {
        index: ""
      },
      request: {}
    };
  },
  //        created(){
  //          console.log(this.$route.params.index);
  //          this.activeName=this.$route.params.index;
  //        },
  methods: {
    check: function(index, row) {
      //              console.log(index,row);
      //              console.log(!(row.csTextStatus==2));
      console.log(this.checked);
    },
    //标签点击事件
    // handleClick(tab, event) {
    //   console.log(tab.name, tab.index);
    // },
    //多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.checked = true;
      console.log(val);
    },
    //------------------个人彩印--------------------
    //查询请求
    search: function(searchReq) {
      // console.log(request);
      if (searchReq.startTime == null) {
        searchReq.startTime = "";
      } else if (searchReq.endTime == null) {
        searchReq.endTIme = "";
      }
      this.$http
        .post(`${this.proxyUrl}/audit/getAuditCustomerCs`, searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
          // console.log(this.tableData);
          //替换对应的状态码为状态名称
          // for (var item in this.tableData) {
          //   //                         console.log(this.tableData[item].csTextStatus);
          //   //                          console.log(this.csTextStatus[0][this.tableData[item].csTextStatus]);
          //   this.tableData[item].csTextStatus = this.csTextStatus[0][
          //     this.tableData[item].csTextStatus
          //   ];
          // }
        });
    },
    //内容详情
    contentDetail:function(){
      pkgId:''
    },
    //删除请求
    deltr: function() {
      console.log("删除成功");
      console.log(!tnis.tableData.csTextStatus == "2");
      //               this.$http
      //                   .post(`${this.proxyUrl}/csText/delCsTextInfo`,delRequest,{emulateJSON:true})
      //                   .then(function(res){
      //                     console.log(res);
      //                   })
    },
    //通过请求---单
    pass:function(val){
      console.log(val);
      this.passRequest.svId=val.svId;
      this.passRequest.svMold=val.svMold;
      this.$http
          .post(`${this.proxyUrl}/audit/passCustomerCs`,this.passRequest,{emulateJSON:true})
          .then(function(res){
            console.log(res.data);
          })
    },
    //通过请求---多
     passlist:function(val){
      console.log(val);
      this.passRequest.svId=val.svId;
      this.passRequest.svMold=val.svMold;
      this.$http
          .post(`${this.proxyUrl}/audit/passCustomerCslist`,this.passRequest,{emulateJSON:true})
          .then(function(res){
            console.log(res.data);
          })
    },
    //驳回请求---单
    reject:function(val){
      this.rejectReq.svId=val.svId;
      this.rejectReq.svMold=val.svMold;
      console.log(this.rejectReq);
       this.$http
          .post(`${this.proxyUrl}/audit/rejectCustomerCs`,this.rejectReq,{emulateJSON:true})
          .then(function(res){
            console.log(res.data);
          })
    },
    //--------------------企业彩印/彩印盒------------------------
    //--------------------新媒彩印------------------------
    //--------------------省市地区提醒模板------------------------
    //--------------------特定号码提醒模板------------------------
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      this.$http
          .post(`${this.proxyUrl}/audit/getAuditCustomerCs`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
      })
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      this.$http
          .post(`${this.proxyUrl}/audit/getAuditCustomerCs`,this.searchReq,{emulateJSON:true})
          .then(function(res){
          this.tableData=res.data.datas;
        })
    },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-search3 {
  width: 94%;
  margin-top: 3%;
}
.el-table {
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
</style>
