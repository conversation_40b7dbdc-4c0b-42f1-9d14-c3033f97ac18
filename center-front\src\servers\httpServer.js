import axios from 'axios';
import {errorFour,sessionOut} from './../util/core.js';
axios.defaults.headers = { 'Content-Type': 'application/json' || 'multipart/form-data','X-Frame-Options':'SAMEORIGIN'};
// axios.defaults.baseURL = '/api/';
axios.defaults.baseURL = '';
///请求拦截器
axios.interceptors.request.use(
    (config)=>{
        let token=sessionStorage.getItem('TOKEN');
        if(token){
            config.headers.token=token
        }
        return config;
    },
    (err)=>{
        return Promise.reject(err);
    }
)
//请求响应拦截器
axios.interceptors.response.use(
    (response)=>{
        
        return response;
    },
    (error)=>{
        if (error.response.status === 404) {
            errorFour();
        }
        if(error.status==401){
            sessionOut();
        }
        return Promise.reject(error);
    }
);
//post请求
export function post(url,params){
    return axios.post(url,params);
}
// post form-data
export function postFromData(url, params) {
    return axios.post(url, params, {headers: {'Content-Type': 'multipart/form-data'}})
}
//get请求
export function get(url,params){
    return axios.get(url,{params:params});
}
//post下载文件
export function postDownload(url,params){
    return axios.post(url,params,{responseType:'blob'});
}
//post请求
export function postHeader(headers,params){
    return axios.post('/oper/sop',params,{headers:{'CY-operation':headers}});
}
//get请求
export function getHeader(headers,params){
    return axios.get('/oper/sop',{params:params,headers:{'CY-operation':headers}});
}
//post下载文件
export function postDownloadHeader(headers,params){
    return axios.post('/oper/sop',params,{responseType:'blob',headers:{'CY-operation':headers}});
}