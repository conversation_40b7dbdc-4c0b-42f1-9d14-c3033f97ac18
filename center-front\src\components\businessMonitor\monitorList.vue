<template>
  <div>
    <el-row style="border-bottom: 1px solid #d9d9d9;" type="flex" align="middle">
      <el-col :span="6" style="font-size: 20px;padding-left: 20px;border-bottom: none;" class="user-titler">{{$route.name}}</el-col>
      <el-col :span="18">
        <el-form :inline="true" class="demo-form-inline" size="small">
          <el-form-item label="时间:" style="float: right; margin-right: 20px;">
            <el-date-picker
              v-model="selectDate"
              @change="selectDateChange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyyMMddHHmmss"
              unlink-panels
              style="width: 250px;"
            ></el-date-picker>
          </el-form-item>
          <el-form-item style="float: right;">
            <el-radio-group v-model="checkDate" size="small" @change="checkDateChange">
              <el-radio-button label="1小时"></el-radio-button>
              <el-radio-button label="1天"></el-radio-button>
              <el-radio-button label="1周"></el-radio-button>
              <el-radio-button label="1个月"></el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div style="margin: 20px;">
      <el-tabs type="border-card" id="chart_wrap" @tab-click="tabHandleClick">
        <el-tab-pane
          v-for="item in tabList"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-tab-pane>
        <!-- 修改图表容器部分 -->
        <div v-show="isDeliveryTab" class="dual-chart-container">
          <div class="chart-container">
            <div class="chart-title">投递并发量</div>
            <div id="chart1" :style="{height:'350px'}"/>
          </div>
          <div class="chart-container">
            <div class="chart-title">投递成功率</div>
            <div id="chart2" :style="{height:'350px'}"/>
          </div>
        </div>
        <div id="chart" :style="{height:'350px' , display: isDeliveryTab ? 'none' : 'block'}"/>

      </el-tabs>
      <el-row class="app-tab02 table-wrap" v-show="['order/pkg','order/success','order/result','content','rule'].includes(tabUrl)">
        <el-col :span="12" class="table-b-r">
          <el-table :data="tableData" :header-cell-class-name="tableheaderClassNameZ">
            <el-table-column prop="statValue" label="上报值">
              <template slot-scope="scope">
                <span>{{tabUrl == "order/pkg" ? scope.row.statValue : scope.row.statValue + '%'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="isCordon" label="状态">
              <template slot-scope="scope">
                <i v-if="scope.row.isCordon == 0" class="el-icon-success el-alert--success"></i>
                <i v-else class="el-icon-warning el-alert--error"></i>
                &nbsp;
                <span v-if="scope.row.isCordon == 0">正常</span>
                <span v-if="scope.row.isCordon == 1" style="color: red">异常</span>
              </template>
            </el-table-column>
            <el-table-column prop="insertTime" label="上报时间" width="220">
              <template slot-scope="scope">
                <span>{{scope.row.insertTime | dateFormat}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="12">
          <el-table :data="tableData1" :header-cell-class-name="tableheaderClassNameZ">
            <el-table-column prop="statValue" label="上报值">
              <template slot-scope="scope">
                <span>{{tabUrl == "order/pkg" ? scope.row.statValue :scope.row.statValue + '%'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="isCordon" label="状态">
              <template slot-scope="scope">
                <i v-if="scope.row.isCordon == 0" class="el-icon-success el-alert--success"></i>
                <i v-else class="el-icon-warning el-alert--error"></i>
                &nbsp;
                <span v-if="scope.row.isCordon == 0">正常</span>
                <span v-if="scope.row.isCordon == 1" style="color: red">异常</span>
              </template>
            </el-table-column>
            <el-table-column prop="insertTime" label="上报时间" width="220">
              <template slot-scope="scope">
                <span>{{scope.row.insertTime | dateFormat}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row class="app-tab02 table-wrap" v-show="['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun', 'mo'].includes(tabUrl)">
        <el-col :span="12" class="table-b-r">
          <el-table :data="tableData" :header-cell-class-name="tableheaderClassNameZ">
            <el-table-column prop="statValue" label="上报值" v-if="tabUrl === 'mo'">
              <template slot-scope="scope">
                <span>{{scope.row.statValueCount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="statValue" label="并发量" v-if="['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun'].includes(tabUrl)">
              <template slot-scope="scope">
                <span>{{scope.row.statValueCount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="statValue" label="上报值" v-if="['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun'].includes(tabUrl)">
              <template slot-scope="scope">
                <span>{{scope.row.statValue + '%'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="isCordon" label="状态">
              <template slot-scope="scope">
                <i v-if="scope.row.isCordon == 0" class="el-icon-success el-alert--success"></i>
                <i v-else class="el-icon-warning el-alert--error"></i>
                &nbsp;
                <span v-if="scope.row.isCordon == 0">正常</span>
                <span v-if="scope.row.isCordon == 1" style="color: red">异常</span>
              </template>
            </el-table-column>
            <el-table-column prop="insertTime" label="上报时间" width="220">
              <template slot-scope="scope">
                <span>{{scope.row.insertTime | dateFormat}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
        <el-col :span="12">
          <el-table :data="tableData1" :header-cell-class-name="tableheaderClassNameZ">
            <el-table-column prop="statValue" label="上报值" v-if="tabUrl === 'mo'">
              <template slot-scope="scope">
                <span>{{scope.row.statValueCount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="statValue" label="并发量" v-if="['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun'].includes(tabUrl)">
              <template slot-scope="scope">
                <span>{{scope.row.statValueCount}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="statValue" label="上报值" v-if="['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun'].includes(tabUrl)">
              <template slot-scope="scope">
                <span>{{scope.row.statValue + '%'}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="isCordon" label="状态">
              <template slot-scope="scope">
                <i v-if="scope.row.isCordon == 0" class="el-icon-success el-alert--success"></i>
                <i v-else class="el-icon-warning el-alert--error"></i>
                &nbsp;
                <span v-if="scope.row.isCordon == 0">正常</span>
                <span v-if="scope.row.isCordon == 1" style="color: red">异常</span>
              </template>
            </el-table-column>
            <el-table-column prop="insertTime" label="上报时间" width="220">
              <template slot-scope="scope">
                <span>{{scope.row.insertTime | dateFormat}}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div class="app-pageganit">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.p"
          :page-sizes="[20, 30, 40, 50]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
import { debounce } from "@/util/tools";
import { get } from "@/servers/httpServer";

const dateFormatFn = function(date) {
      if (!date) return ''
      return date.replace(/^(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})$/, "$1-$2-$3 $4:$5:$6");
    }

export default {
  data() {
    return {
      tabUrl: 'order/success',
      tabList: [
        {
          label: "订购成功率",
          value: "order/success"
        },
        {
          label: "订购结果通知率",
          value: "order/result"
        },
        {
          label: "订购关系差异量",
          value: "order/pkg"
        },
        {
          label: "内容同步成功率",
          value: "content"
        },
        {
          label: "规则同步成功率",
          value: "rule"
        },
        {
          label: "下行通道监控-本网",
          value: "delivery-mobile"
        },
        {
          label: "下行通道监控-号百",
          value: "delivery-haobai"
        },
        {
          label: "下行通道监控-联通在线",
          value: "delivery-ltonlion"
        },
        {
          label: "下行通道监控-嘉讯",
          value: "delivery-jiaxun"
        },
        {
          label: "下行通道监控-彩讯",
          value: "delivery-caixun"
        },
        {
          label: "上行通道监控",
          value: "mo"
        }
      ],
      checkDate: "1小时",
      searchData: {
        t: 1,
        start: "",
        end: "",
      },
      selectDate: "",
      chart: null,
      tableData: [],
      tableData1: [],
      pagination: {
        total: 0,
        p: 1,
        pz: 20
      }
    };
  },
  filters: {
    dateFormat: dateFormatFn
  },
  computed: {
    isDeliveryTab() {
      return ['delivery-mobile','delivery-haobai','delivery-ltonlion','delivery-jiaxun','delivery-caixun'].includes(this.tabUrl)
    }
  },
  mounted() {
    const { t } = this.searchData;
    const { p, pz } = this.pagination;
    this.getInitData(this.tabUrl, {t}, p, pz);
    this.chart = echarts.init(document.getElementById("chart"));
    // 初始化图表
    this.initCharts();
    this.__resizeHandler = debounce(() => {
      if (this.chart || this.chart1 || this.chart2) {
        this.width = document.getElementById("chart_wrap").offsetWidth + "px";
        this.$nextTick(() => {
          this.chart && this.chart.resize();
          this.chart1 && this.chart1.resize();
          this.chart2 && this.chart2.resize();
        });
      }
    }, 100);
    window.addEventListener("resize", this.__resizeHandler);
    console.log('chart1 实例:', this.chart1);
  },
  methods: {
    initCharts() {
      this.$nextTick(() => {
        if (this.isDeliveryTab) {
          // 销毁单图表实例
          if (this.chart) {
            this.chart.dispose();
            this.chart = null;
          }
          // 初始化双图表
          if (!this.chart1 && document.getElementById("chart1")) {
            this.chart1 = echarts.init(document.getElementById("chart1"));
          }
          if (!this.chart2 && document.getElementById("chart2")) {
            this.chart2 = echarts.init(document.getElementById("chart2"));
          }
        } else {
          // 销毁双图表实例
          if (this.chart1) {
            this.chart1.dispose();
            this.chart1 = null;
          }
          if (this.chart2) {
            this.chart2.dispose();
            this.chart2 = null;
          }
          // 初始化单图表
          if (!this.chart && document.getElementById("chart")) {
            this.chart = echarts.init(document.getElementById("chart"));
          }
        }
      });

    },
    checkDateChange(value) {
      this.selectDate = ""
      let type;
      switch (value) {
        case '1小时':
          type = 1;
          break;
        case '1天':
          type = 2;
          break;
        case '1周':
          type = 3;
          break;
        case '1个月':
          type = 4;
          break;
        default:
          break;
      }
      this.searchData.t = type;
      const { t } = this.searchData;
      this.getInitData(this.tabUrl, {t}, 1, 20);
    },
    selectDateChange(value) {
      this.checkDate = "";
      this.searchData.t = 0;
      this.searchData.start = value[0];
      this.searchData.end = value[1];
      this.getInitData(this.tabUrl, {...this.searchData}, 1, 20)
    },
    tabHandleClick(tab, event) {
      this.tabUrl = tab.$attrs.value;

      this.$nextTick(() => {
        const { t } = this.searchData;
        const { p, pz } = this.pagination;
        this.initCharts(); // 先确保图表初始化
        if (t == 0) {
          this.getInitData(tab.$attrs.value, {...this.searchData}, p, pz);
        } else {
          this.getInitData(tab.$attrs.value, {t}, p, pz);
        }
      });
    },
    getInitData(type, time, p, pz) {
      // 先确保图表初始化
      this.initCharts();
      // 映射 tabUrl 到 dataType
      const typeMap = {
        'delivery-mobile': 1,
        'delivery-haobai': 5,
        'delivery-ltonlion': 6,
        'delivery-jiaxun': 7,
        'delivery-caixun': 8,
        'mo': 0
      };
      const dataType = typeMap[type];
      console.log(this.chart);

      console.log(this.chart1);
      console.log(this.chart2);
      if (dataType !== undefined) {
        // 如果是 delivery 类型，调用 /stat/delivery POST 接口
        const payload = {
          dataType: dataType,
          queryType: time.t || 0,
          startTime: time.start || '',
          endTime: time.end || '',
          page: p,
          pageSize: pz
        };

        this.$http
            .post(`${this.proxyUrl}/stat/delivery`, JSON.stringify(payload)).then(res => {
          let data = res.data;
          if (data.code === 0) {
            if(!data.data) {
              this.pagination.total = 0;
              this.tableData = [];
              this.tableData1 = [];
              const emptyData = [{"id":0,"insertTime":null,"statValueCount":0,"statValue":0,"isCordon":0}];
              if (this.isDeliveryTab) {
                this.initChart1(emptyData, this.legend, this.chart1);
                this.initChart(emptyData, this.legend, this.chart2);
              } else {
                console.log('mo 1');

                this.initChart([], this.legend, this.chart);
              }

            } else {
              this.pagination.total = data.total || 0;
              const tableData = Object.assign([], data.data || []);
              const len = Math.ceil(tableData.length / 2);
              this.tableData = tableData.splice(0, len);
              this.tableData1 = tableData;
              console.log('接收到 delivery 数据:', data.data);
              if (this.isDeliveryTab) {
                this.initChart1(data.data, this.legend, this.chart1);
                this.initChart(data.data, this.legend, this.chart2);
              } else {
                console.log('mo 2');
                this.initChart(data.data, this.legend, this.chart);
              }


            }

          } else {
            this.pagination.total = 0;
            this.tableData = [];
            this.tableData1 = [];
            this.$message.warning('目前无监控数据');
          }
        }).catch(err => {
          console.error("请求 /stat/delivery 出错：", err);
          this.$message.error("接口请求失败");
        });
      }
      else
      {
        get("/stat/" + type, { ...time, p, pz }).then(res => {
          let data = res.data;
          if (data.code == 0) {
            if(!data.data) {
              this.pagination.total = 0;
              this.tableData = [];
              this.tableData1 = [];
              this.initChart([{"id":0,"insertTime":null,"statValue":100.0,"isCordon":0},{"id":0,"insertTime":null,"statValue":100.0,"isCordon":0}], this.legend, this.chart);
            } else {
              this.pagination.total = data.total;
              const tableData = Object.assign([], data.data);
              const len = Math.ceil(tableData.length / 2);
              this.tableData = tableData.splice(0, len);
              this.tableData1 = tableData;
              this.initChart(data.data, this.legend, this.chart);
            }
          } else {
            this.pagination.total = 0;
            this.tableData = [];
            this.tableData1 = [];
            this.initChart([], this.legend, this.chart);
            this.$message({
              type: 'warning',
              message: `目前无监控数据！`
            });
          }
        });
      }

    },
    initChart(data, legend, chart) {
      chart.setOption({
        tooltip: {
          trigger: "axis"
        },
        grid: {
          left: "40",
          right: "60",
          top: "15%",
          containLabel: true
        },
        calculable: true,
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: data.map(item => {
              return dateFormatFn(item.insertTime)
            }).reverse()
          }
        ],
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false
          },
        },
        color: "rgba(64,158,255)",
        series: [
          {
            type: "line",
            itemStyle: {
              color: "rgba(64,158,255)"
            },
            lineStyle: {
              color: "rgba(64,158,255)"
            },
            areaStyle: {
              normal: {
                type: "default",
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: "rgba(64,158,255,0.2)"
                    },
                    {
                      offset: 1,
                      color: "rgba(64,158,255,0.2)"
                    }
                  ],
                  false
                )
              }
            },
            smooth: true,
            itemStyle: {
              normal: { areaStyle: { type: "default" } }
            },
            data: data.map(function(item) {
              return item.statValue;
            }).reverse()
          }
        ]
      });
    },
    initChart1(data, legend, chart) {
      chart.setOption({
        tooltip: {
          trigger: "axis"
        },
        grid: {
          left: "40",
          right: "60",
          top: "15%",
          containLabel: true
        },
        calculable: true,
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: data.map(item => {
              return dateFormatFn(item.insertTime)
            }).reverse()
          }
        ],
        yAxis: {
          type: "value",
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false
          },
        },
        color: "rgba(64,158,255)",
        series: [
          {
            type: "line",
            itemStyle: {
              color: "rgba(64,158,255)"
            },
            lineStyle: {
              color: "rgba(64,158,255)"
            },
            areaStyle: {
              normal: {
                type: "default",
                color: new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      {
                        offset: 0,
                        color: "rgba(64,158,255,0.2)"
                      },
                      {
                        offset: 1,
                        color: "rgba(64,158,255,0.2)"
                      }
                    ],
                    false
                )
              }
            },
            smooth: true,
            itemStyle: {
              normal: { areaStyle: { type: "default" } }
            },
            data: data.map(function(item) {
              return item.statValueCount;
            }).reverse()
          }
        ]
      });
    },
    tableheaderClassNameZ({ row, rowIndex }) {
      return "table-head-thz";
    },
    handleSizeChange(val) {
      this.pagination.pz = val;
      const { t } = this.searchData;
      const { p, pz } = this.pagination;
      if(t == 0) {
        this.getInitData(this.tabUrl, {...this.searchData}, p, pz);
      } else {
        this.getInitData(this.tabUrl, {t}, p, pz);
      }
    },
    handleCurrentChange(val) {
      this.pagination.p = val
      const { t } = this.searchData;
      const { p, pz } = this.pagination;
      if(t == 0) {
        this.getInitData(this.tabUrl, {...this.searchData}, p, pz);
      } else {
        this.getInitData(this.tabUrl, {t}, p, pz);
      }
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    if (this.chart1) {
      this.chart1.dispose();
      this.chart1 = null;
    }
    if (this.chart2) {
      this.chart2.dispose();
      this.chart2 = null;
    }
    window.removeEventListener("resize", this.__resizeHandler);
  }
};
</script>

<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.header-fanhui {
  padding: 10px 0 1%;
}

.user-line {
  margin-top: 3%;
  border-color: #dddfe6;

  border-width: 0px 0px 1px 0px;
  width: 100%;
  margin: 0 auto;
}

.el-tabs--border-card {
  box-shadow: none;
  border-bottom: 1px solid #cccccc;
}

.el-tabs--border-card >>> .el-tabs__header {
  background-color: #ffffff!important;
}

.el-tabs--border-card >>> .el-tabs__header .el-tabs__item.is-active {
  background-color: #409EFF;
  color: #ffffff;
}

.table-wrap {
  border: 1px solid #eaeaea;
}

.el-table {
  border: none !important;
}

.table-b-r {
  border-right: 1px solid #eaeaea;
}

.el-table::before {
  height: 0;
}

.el-table >>> td,
.el-table >>> th {
  border: none !important;
  content: "hello world";
}

.el-icon-warning, .el-icon-success {
  vertical-align: middle;
  font-size: 20px;
  width: 20px;
}
.el-alert--success {
  color: #4caf50;
}
.el-alert--error {
  color: #f44336;
}

.dual-chart-container {
  display: flex;
  justify-content: space-between;
}

.chart-container {
  width: 49%;
  position: relative;
}

.chart-title {
  text-align: center;
  font-weight: bold;
  padding: 10px 0;
}
</style>
