package com.cy.user.api;

import com.cy.user.model.ContentSiteModel;
import com.cy.user.model.CsEnterpriseInfoModel;
import com.cy.user.model.CsEnterpriseServControlModel;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface CsEnterpriseAPI {




	@RequestMapping(value = "/enterprise/queryServControl")
	List<CsEnterpriseServControlModel> findEnterpriseServControlById(@RequestParam("id") Integer id);

	@RequestMapping(value = "/enterprise/queryEnterpriseInfo")
	List<CsEnterpriseInfoModel> findEnterpriseById(@RequestParam("id") Integer id);


}
