<template>
    <div class="innerbox">
        <div class="effect">
            <div>
                <!--查询条件-->
                <div>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" label-width="60px">
                        <el-form-item label="号码">
                            <el-input  v-model="searchForm.phoneNumber" placeholder="" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="search(1)">查询</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <!--表格-->
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                    <el-table-column prop="phoneNumber" label="号码" />
                    <el-table-column prop="classBtype" label="号码描述" />
                    <el-table-column prop="provinceName" label="省份" />
                    <el-table-column prop="countyName" label="地市"/>
                    <el-table-column prop="status" label="状态"/>
                    <el-table-column prop="categoryName" label="分类"/>
                    <el-table-column prop="standardTypeName" label="标准类型"/>
                    <el-table-column prop="markType" label="标记类型"/>
                    <el-table-column prop="sourceName" label="号码来源"/>
                    <el-table-column prop="markTimes" label="标记次数"/>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total>20">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="50"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '../../../servers/httpServer.js';
    export default {
        name: 'effect',
        data(){
            return{
                //查询form对象定义
                searchForm: {
                    numType:4, //号码库
                    phoneNumber:'',//号码
                    pageSize:50,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                tableData:[],//表数据
                currentPage: 1,
                total:0
            }
        },
        components: {

        },
        created(){
//            this.search();
        },
        methods:{
            //每页条数
            handleSizeChange(val) {
                this.searchForm.pageSize = val;
                this.search();
            },
            //当前页面
            handleCurrentChange(val) {
                this.searchForm.pageNo = val;
                this.search();
            },
            //查询请求
            search: function(pg) {
                let vm = this;
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                if(!this.searchForm.phoneNumber){
                    vm.$message.error("请输入号码查询");
                    return;
                }
                postHeader('queryNumInfo', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.tableData = data.data.queryNumInfoList;
                    vm.total = data.data.total;
                }
            })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .innerbox{
        margin-bottom: 20px;
    }
    .el-table{
        margin: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
