package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParAlarmSmsCommon;
import com.cs.param.model.ParAlarmSmsModel;

@Repository
public interface ParAlarmSmsMapper {

	int insertParAlarmSms(ParAlarmSmsCommon common) throws SQLException;

	int deleteParAlarmSmsByPK(ParAlarmSmsCommon common) throws SQLException;

	int openOrCloseStatusById(ParAlarmSmsCommon common) throws SQLException;

	ParAlarmSmsModel getParAlarmSmsByPK(ParAlarmSmsCommon common) throws SQLException;

	List<ParAlarmSmsModel> queryPageInfo(ParAlarmSmsCommon common) throws SQLException;

	Integer queryPageCount(ParAlarmSmsCommon common) throws SQLException;

}
