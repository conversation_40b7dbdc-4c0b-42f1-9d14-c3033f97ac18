<template>
  <div class="labelWrap">
    <h1 class="user-title">{{subjectPrintHandleType ? "编辑" : "新增"}}专题内容</h1>
    <div class="user-line"></div>
    <el-form label-width="120px" :model="form" :rules="rules" ref="ruleForm" class="demo-ruleForm" style="margin-top: 22px;">
      <el-form-item label="专题标签" prop="checkListStr">
        <el-input v-show="false" v-model="form.checkListStr"></el-input>
        <el-button type="primary" @click="searchContentLabel(),addLabelVisibel = true">添加标签</el-button>
        <el-tag
          v-for="(tag, index) in tags"
          :key="tag.id"
          @close="handleClose(index)"
          closable
        >{{tag.labelName}}</el-tag>
      </el-form-item>
      <el-form-item label="选择省份" prop="flag">
        <el-select
          v-model="form.flag"
          clearable
          placeholder="请选择"
          size="small"
          class="app-input"
        >
          <el-option
            v-for="item in provinceList"
            :key="item.provinceName"
            :label="item.provinceName"
            :value="item.provinceCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类别" prop="subjectStatus">
        <el-select
          v-model="form.subjectStatus"
          clearable
          placeholder="请选择"
          size="small"
          class="app-input"
        >
          <el-option label="默认" :value="null"></el-option>
          <el-option label="NEW" value="1"></el-option>
          <el-option label="HOT" value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="彩印专题内容" prop="subjectContent">
        <el-input type="textarea" v-model="form.subjectContent" style="width: 250px;"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>
    <!-- 添加标签 -->
    <el-dialog title="添加标签" :visible.sync="addLabelVisibel">
      <el-checkbox-group v-model="form.checkList">
        <el-checkbox
          v-for="(item, index) in labelList"
          :key="index"
          :label="item.id"
        >{{item.labelName}}</el-checkbox>
      </el-checkbox-group>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addLabelVisibel = false">取 消</el-button>
        <el-button type="primary" @click="show(),addLabelVisibel = false">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script type="text/ecmascript-6">
export default {
  name: "userPush",
  data() {
    const validatePass = (rule, value, callback) => {
        if (value !== null && !value) {
          callback(new Error('请选择类别'));
        } else {
          callback();
        }
      };
    return {
       //省份
      provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
      form: {
        flag: "",
        subjectStatus: null,
        subjectContent: "",
        checkList: [],
        checkListStr: 'dfd'
      },
      commitBy:
        sessionStorage.getItem("userInfo") &&
        JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
      addLabelVisibel: false,
      labelList: [],
      tags: [],
      rules: {
        checkListStr: [
          { required: true, message: '请添加专题标签' },
        ],
        flag: [
          { required: true, message: '请选择省份' }
        ],
        subjectStatus: [
          { validator: validatePass }
        ],
        subjectContent: [
          { required: true, message: '请填写彩印内容' }
        ],
      },
      subjectPrintHandleType: "",
    };
  },
  watch: {
    'tags': {
      handler(newVal) {
        if(newVal.map(i => i.id).toString() != '') {
          this.form.checkListStr = '1'
        } else {
          this.form.checkListStr = ''
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.form.checkListStr = '1222'
    this.subjectPrintHandleType = sessionStorage.getItem(
      "subjectPrintHandleType"
    );
    if (this.subjectPrintHandleType == "edit") {
      this.imgEdit = false;
      const { flag, subjectStatus, subjectContent, subjectLabelId, id } = JSON.parse(
        sessionStorage.getItem("subjectPrintContent")
      );
      this.form = {
        id,
        flag,
        subjectStatus,
        subjectContent,
        checkListStr: this.tags.map(i => i.id).toString() != '' ? '1' : '',
        checkList: subjectLabelId.split(',')
      };
      this.searchContentLabel().then(() => {
        this.tags = this.form.checkList.map(item => {
          return this.LabelLiIdMap[item];
        });
      })
    }
  },
  methods: {
    handleClose(index) {
      this.tags.splice(index, 1);
      this.form.checkList = this.tags.map(item => {
        return '' + item.id;
      });
    },
    show() {
      this.tags = this.form.checkList.map(item => {
        return this.LabelLiIdMap[item];
      });
    },
    searchContentLabel() {
      return new Promise((resove, reject) => {
        this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectLabelList`,
          JSON.stringify({
          labelName: ""
          }),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(function(res) {
          this.labelList = res.data.data;
          this.LabelLiIdMap = res.data.data.reduce((prev, item) => {
            return {
              ...prev,
              [item.id]: item
            };
          }, {});
          resove()
        });
      })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
          console.log(this.form.subjectContent, this.form.checkList);
        if (valid) {
          const { form: { subjectContent, flag, subjectStatus, id , checkList }, tags, commitBy } = this;
          if (this.subjectPrintHandleType == "edit") {
            console.log('编辑提交 ')
            this.$http
            .post(
              `${this.proxyUrl}/cySubject/updateSubjectContent`,
              JSON.stringify({
                id,
                labelIdArray: tags.map(i => i.id),
                subjectStatus,
                flag,
                subjectContent,
                commitBy
              })
            )
            .then(res => {
              if (res.data.code == 0) {
                this.$message.success("更新成功!");
                this.$router.push("/subjectPrint");
              } else {
                this.$message.error(res.data.message);
              }
            });
          } else {
            this.$http
              .post(
                `${this.proxyUrl}/cySubject/addSubjectContent`,
                JSON.stringify({
                  labelIdArray: checkList,
                  subjectStatus,
                  flag,
                  subjectContent,
                  commitBy
                })
              )
              .then(res => {
                if (res.data.code == 0) {
                  this.$message.success("新建成功!");
                  this.$router.push("/subjectPrint");
                } else {
                  this.$message.error(res.data.message);
                }
              });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
};
</script>

<style scoped>
.user-title {
  padding: 10px 0px 0px 0px;
}
.user-line {
  width: 100%;
  margin: 0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #dddfe6;
}
.el-form-item {
  margin-bottom: 22px !important;
}
.labelWrap >>> .el-checkbox {
  margin-left: 20px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
