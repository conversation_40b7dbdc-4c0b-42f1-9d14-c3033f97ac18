<template>
<div>
	<div v-if="!closeLogin" class="login-margin">
		<el-row>
        <el-col :span="13" style="margin-top: 3px"></el-col>
        <el-col :span="6" style="margin-top:11%">
            <div class="login-warp-box">
                <div class="user-login">
                    <h1>和彩印中央管理平台</h1>
                    <ul>
                        <li>
                            <el-input v-model="loginForm.name" placeholder="请输入用户名" size="small" :maxlength="15"></el-input>
                        </li>
                        <li>
                            <el-input type="password" placeholder="请输入密码" v-model="loginForm.password" size="small" :maxlength="16"  @keyup.native.enter="login"></el-input>
                        </li>
                        <li>
                            <span class="jiaoyan"><el-input placeholder="请输入验证码"  v-model="loginForm.captcha" size="small" :maxlength="4" @keyup.native.enter="login"></el-input></span>
                            <span class="yan-img"><img id="imgCaptcha" :src="imgSrc" alt="更换验证码" width="100%" @click="getCaptcha"></span>
                            <!-- <el-row>
                                <el-col :span="16" class="jiaoyan">
                                    
                                </el-col>
                                <el-col :span="9" class="yan-img">
                                    
                                </el-col>
                            </el-row> -->
                        </li>
                    </ul>
                    <div class="login-tip">
                        <a href="javaScript:;" style="color:white" @click="forgetPassWord()">忘记密码</a>
                    </div>
                    <div class="button-box"><el-button type="success" @click="login()" :disabled="!loginForm.name || !loginForm.password||!loginForm.captcha" style="width: 100%;">登录</el-button></div>
                </div>
            </div>
        </el-col>
    </el-row>
    <!-- 弹窗 -->
    <div>
        <el-dialog
                width="30%"
                title="重置密码"
                :visible.sync="this.modPasswordVisible"
                :close-on-click-modal="false" :before-close="cleanForm">
            <el-form label-width="100px" justify="center">
                <el-form-item label="用户名">
                    <el-input v-model="modForm.sysUserName" size="small"></el-input>
                </el-form-item>
                <el-form-item label="邮箱">
                    <el-input v-model="modForm.sysUserEmail" size="small"></el-input>
                </el-form-item>
                <el-form-item label="新密码">
                    <el-input v-model="modForm.newPassword" type="password" size="small"></el-input>
                </el-form-item>
                <el-form-item label="确认新密码">
                    <el-input v-model="modForm.newPassword2" type="password" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" style="text-align: center;">
                <el-button @click="cleanForm()" size="small">取 消</el-button>
                <el-button type="primary" @click="mod()" size="small">确 定</el-button>
            </div>
        </el-dialog>
        <el-dialog
                width="30%"
                title="修改密码"
                :visible.sync="this.overdueVisible"
                :close-on-click-modal="false" :before-close="cleanForm">
            <p style="color: red;size: 11px;padding-top: -100px">您的密码已过期，请重新修改后登录</p>
            <el-form label-width="100px" justify="center">
                <el-form-item label="用户名">
                    <el-input v-model="overdueForm.sysUserName" size="small"></el-input>
                </el-form-item>
                <el-form-item label="旧密码">
                    <el-input v-model="overdueForm.oldPassword" type="password" size="small"></el-input>
                </el-form-item>
                <el-form-item label="新密码">
                    <el-input v-model="overdueForm.newPassword" type="password" size="small"></el-input>
                </el-form-item>
                <el-form-item label="确认新密码">
                    <el-input v-model="overdueForm.newPassword2" type="password" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" style="text-align: center;">
                <el-button @click="cleanForm()" size="small">取 消</el-button>
                <el-button type="primary" @click="overdueMod()" size="small">确 定</el-button>
            </div>
        </el-dialog>
    </div>
	</div>
</div>
</template>
<script src="./login.js"></script>

<style>
    /*.lo_reg_s{ margin-bottom:100px; clear:both; overflow:hidden}*/
    /*.lo_reg_s img.fl{ margin-top:60px;}*/
    .user-search{


    }
    .login-line{
        height: 1px;
        background: #e5e5e5;
        box-shadow: 0 3px 3px #e5e5e5;
    }
    .yt{
      margin-top: 0 !important;
    }
    p{color:red;}
</style>
