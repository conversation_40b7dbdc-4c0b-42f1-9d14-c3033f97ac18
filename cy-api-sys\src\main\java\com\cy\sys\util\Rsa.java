package com.cy.sys.util;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;

import javax.crypto.Cipher;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;

/**
 * RSA公私钥加解密算法
 *
 * <AUTHOR> @version
 * @see
 */
public final class Rsa
{
    /**
     * 登录密码RSA解密私钥
     */
//    public static final String PWD_RSA_DECRYPT_KEY = new StringBuffer()
//            .append("MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKL3b8Bjvdf1LP6NO6ObI4DdzIsB\n"+
//                    "y5/RAqQCu1y/xyDo8G4rdIDbRre7m6h2Vr6AAvuuulS5auMiN7mx+x5/4KQxaNQg9fJx8asfngFZ\n"+
//                    "WbE/qrZyddDb372rr6/ARjWyZyGmIJtGol+sv+ENnHw/AKnBa43u8Lp8kAuGdhxJK0KDAgMBAAEC\n"+
//                    "gYA+NKT7kxcMMBIUuGRjdvx+XCuPhAft4SJY6JIMPUgNT902sG+wIANGbt5j14gU/1gkKfYGESCz\n"+
//                    "woPy5fUtFzLv3Z/MtA9EmQRdAZmLjCmS6e+iGKMLXI43S6dp3hYJUjswtRje0/gXKjqMMsX+zGGp\n"+
//                    "i345LLbFSbmTGF23O6n+wQJBANfwi3FCux+GZbKinKWqLBaFIb6uHoSIMBQm/u3f+cU8gmp5uZMG\n"+
//                    "82YLVPrYvrlU9cFGzGhoH7mWXUDED4ehHi0CQQDBMxT9BjkewDApNeBDeX8698aOjz+7CmT3agQG\n"+
//                    "YuIyYVyCCrhPFbN6LrWwgEPsp8txnUyWqX6tC+NNdvINlQFvAkEAr/gHyg5VKsV8zmuRN7dLIjbv\n"+
//                    "XtHJcLsCYwm8KOCiS8aZiGcVgOjjJD8LyzchhSnk8tHc9SAU6knMSGD9PstfKQJBAK6GOJWBQ6Q1\n"+
//                    "tMvlcVtCq95W6bTqToXmE+M0j8I9HVypeum2SVyXm/PpshNpKvLjePJ/SCppnpcvv/vAUVRXoF0C\n"+
//                    "QQC3Ud6+Zh+KhFdqUUDbn4qTQdioBRunv2UaXI/FhKvgoCaw6MDyGcsqm0/gBZ9q5S4HYR+F9fML\n"+
//                    "QVEzpyM+/TfG")
//          .toString();


    /**
     * RSA解密方法
     * @param data 要解密的数据
     * @param key  解密key
     *
     */
    public static String rsaDecrypt(String data, String key) throws Exception {
        try
        {
            // 64位解码加密后的字符串
            byte[] encrypted = Base64.decodeBase64(data.getBytes("UTF-8"));

            byte[] keyBytes = Base64.decodeBase64(key);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            RSAPrivateKey priKey = (RSAPrivateKey)keyFactory.generatePrivate(keySpec);

            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, priKey);

            byte[] result = cipher.doFinal(encrypted);
            return new String(result);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 登录密码解密
     *
     */
//    public static String rsaDecryptLoginPwd(String pwd)  {
//        String desPwd = null;
//        try {
//            desPwd = rsaDecrypt(pwd, PWD_RSA_DECRYPT_KEY);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return desPwd;
//    }
}