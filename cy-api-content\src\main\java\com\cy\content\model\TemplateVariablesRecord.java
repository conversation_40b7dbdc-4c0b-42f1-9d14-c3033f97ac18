package com.cy.content.model;

import java.util.Date;

public class TemplateVariablesRecord {
    private Long id;
    private String templateId;
    private String templateContent;
    private String variables;
    private String dealContent;
    private Date createTime;
    private Date updateTime;

    // Constructor
    public TemplateVariablesRecord() {
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateContent() {
        return templateContent;
    }

    public void setTemplateContent(String templateContent) {
        this.templateContent = templateContent;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getDealContent() {
        return dealContent;
    }

    public void setDealContent(String dealContent) {
        this.dealContent = dealContent;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "TemplateVariablesRecord{" +
                "id=" + id +
                ", templateId='" + templateId + '\'' +
                ", templateContent='" + templateContent + '\'' +
                ", variables='" + variables + '\'' +
                ", dealContent='" + dealContent + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}

