<template scope="scope">
  <div>
    <div class="user-titler">彩印专题内容</div>
    <!--广告彩印-->
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="专题ID">
              <el-input v-model="searchReq.id" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专题内容">
              <el-input v-model="searchReq.subjectContent" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态">
              <el-select
                v-model="searchReq.status"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option label="上架" value="0"></el-option>
                <el-option label="下架" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="省份">
              <el-select
                v-model="searchReq.flag"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.provinceName"
                  :label="item.provinceName"
                  :value="item.provinceCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" size="small" @click="add">新增专题内容</el-button>
          <!-- <el-button type="primary" size="small" @click="$router.push('/uploadSubjectPrint')">批量新增</el-button> -->
          <el-button type="primary" size="small" @click="download()">导出excel</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        :header-cell-class-name="tableheaderClassName"
      >
        <!-- <el-table-column type="selection" width="55"></el-table-column> -->
        <el-table-column prop="id" label="专题ID" width="120"></el-table-column>
        <el-table-column prop="subjectContent" label="专题内容" width="200"></el-table-column>
        <el-table-column prop="labelName" label="专题标签" width="160"></el-table-column>
        <el-table-column label="状态" width="80">
          <template slot-scope="scope">{{scope.row.status == 0 ? '上架' : '下架'}}</template>
        </el-table-column>
        <el-table-column prop="flag" label="省份" width="100"></el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="200"></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="200"></el-table-column>
        <el-table-column prop="userNum" label="使用人数" width="100"></el-table-column>
        <el-table-column prop="subjectStatus" label="类别" width="100"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button @click="editItem(scope.row)" type="text" size="small">编辑</el-button>
            <el-button slot="reference" @click="del(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import {dowandFile} from '@/util/core.js';
import {postDownload} from '@/servers/httpServer.js';
export default {
  data() {
    return {
      pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
      },
      //省份
      provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
      tableLoading: false,
      //查询条件
      searchReq: {
        id: "",
        subjectContent: "",
        status: "",
        flag: "",
        timearr: [],
        beginTime: "",
        endTime: "",
        pageIndex: 1, //页码
        pageSize: 10, //一页的数量
      },
      //数据表
      tableData: [],
      //操作列表
      pageTotal: 0, //总条数
    };
  },
  created() {
    this.search();
  },
  methods: {
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr) {
        this.searchReq.beginTime = this.searchReq.timearr[0]
          ? moment(new Date(this.searchReq.timearr[0])).format("YYYY-MM-DD")
          : "";
        this.searchReq.endTime = this.searchReq.timearr[1]
          ? moment(new Date(this.searchReq.timearr[1])).format("YYYY-MM-DD")
          : "";
      } else {
        this.searchReq.beginTime = "";
        this.searchReq.endTime = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectContentList`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data.map((item) => {
              const province = this.provinceList.find(i => i.provinceCode == item.flag)
              item.flag = province && province.provinceName || ''
              return item
            }) || [];
            this.pageTotal = res.totalCount;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    handleSizeChange(val) {
      this.searchReq.pageIndex = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.pageIndex = val;
      this.search();
    },
    download() {
      if (this.searchReq.timearr) {
        this.searchReq.beginTime = this.searchReq.timearr[0]
          ? moment(new Date(this.searchReq.timearr[0])).format("YYYY-MM-DD")
          : "";
        this.searchReq.endTime = this.searchReq.timearr[1]
          ? moment(new Date(this.searchReq.timearr[1])).format("YYYY-MM-DD")
          : "";
      } else {
        this.searchReq.beginTime = "";
        this.searchReq.endTime = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      postDownload(`${this.proxyUrl}/cySubject/exportSubjectContent`, JSON.stringify(searchReq))
        .then(function(res) {
          dowandFile(res.data, '彩印专题内容.xlsx');
        });
    },
    add() {
      sessionStorage.removeItem('subjectPrintHandleType')
      this.$router.push('/newSubjectPrint')
    },
    editItem(row) {
      sessionStorage.setItem('subjectPrintHandleType', 'edit')
      sessionStorage.setItem('subjectPrintContent', JSON.stringify(row))
      this.$router.push('/newSubjectPrint')
    },
    del(row) {
      this.$confirm("确定删除此项?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消"
      })
        .then(() => {
          this.$http
            .post(
              `${this.proxyUrl}/cySubject/delSubjectContent`,
              JSON.stringify({ id: row.id })
            )
            .then(res => {
              const data = res.data;
              if (data.code == 0) {
                this.$message({
                  type: "success",
                  message: "删除成功!"
                });
                this.search();
              }
            });
        })
        .catch(() => {});
    }
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}

.zzWrap >>> .el-dialog__body {
  text-align: center;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
