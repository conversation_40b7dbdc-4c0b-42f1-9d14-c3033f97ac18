<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.caiyin</groupId>
	<artifactId>cy-api-common-params</artifactId>
	<version>0.0.2</version>
	<packaging>jar</packaging>

	<name>cy-api-common-params</name>
	<description>Demo project for Spring Boot</description>

	<parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.1</version>
    </parent>

	<dependencies>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>3.0.2</version>  <!-- 选择适合的版本 -->
		</dependency>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>3.0.0</version> <!-- 使用适当的版本 -->
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<version>4.0.4</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
  
  <build>
		<resources>
		   <resource>
		      <directory>src/main/resources</directory>
		      <includes>
		         <include>**/*.properties</include>
		         <include>**/*.yml</include>
		         <include>**/*.xml</include>
		      </includes>
		      <filtering>true</filtering>
		   </resource>
		</resources>
		<pluginManagement>
		   <plugins>
		      <plugin>
		         <artifactId>maven-resources-plugin</artifactId>
		         <configuration>
		            <encoding>utf-8</encoding>
		            <useDefaultDelimiters>true</useDefaultDelimiters>
		         </configuration>
		      </plugin>
		   </plugins>
		</pluginManagement>
	</build>
	<dependencyManagement>
		<dependencies>
									            			<!--标记位-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.4.0</version>
            </dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.18.0</version>
			</dependency>

		</dependencies>
	</dependencyManagement>
	<distributionManagement>
	    <repository>
	        <id>caiyin-nexus</id>
	        <name>caiyn-nexus</name>
	        <url>${ReleaseRepository}</url>
	    </repository>
	    <snapshotRepository>
	        <id>caiyin-nexus</id>
	        <name>caiyin-nexus</name>
	        <url>${SnapshotRepository}</url>
	    </snapshotRepository>
	</distributionManagement>
</project>