import Vue from 'vue';
import Vuex from 'vuex';
import modules from './../modules/modules.js'
import leftMenu from './modules/leftMenu'

Vue.use(Vuex);
const state={
  menuList:new Array(),//菜单项数据存储
};
const mutations={
  MENU_LIST(state,value){
    state.menuList=value;
  }
}
const actions={
  setMenuList: ({commit, state}, list) => {
     commit('MENU_LIST', list)
  }
}
const store = new Vuex.Store({
  state:state,
  actions:actions,
  mutations:mutations,
  modules: modules
})
export default store


