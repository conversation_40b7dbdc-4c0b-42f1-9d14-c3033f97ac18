import {procMoniQuery,exportExcel} from './procMoniServer.js';
import {dowandFile,formDate,fmoney} from './../../../util/core.js';
import echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/map';
import 'echarts/map/js/china.js'
import 'echarts/lib/component/tooltip';
import 'echarts/lib/component/title';
export default {
    data(){
        return{
            procMoni:{days:'yesterday',dateTime:[],selectCount:1},
            myChart:'',
            pickerOptions:{
                disabledDate:function (today) {
                    return today.getTime()>Date.now();
                }
            },
            dataMessage:new Array(),
            //指标类型
            procMoniTypeList:[
                {label:1,value:'有效用户数',name:'effectUser',type:'effectUser'},
                {label:2,value:'有效付费用户数',name:'payUser',type:'effectPayUser'},
                {label:3,value:'有效免费用户数',name:'freeUser',type:'effectFreeUser'},
                {label:4,value:'新增用户数',name:'addUser',type:'addUser'},
                {label:5,value:'新增付费用户数',name:'newPayUser',type:'newPayUser'},
                {label:6,value:'新增免费用户数',name:'newFreeUser',type:'newFreeUser'},
                {label:7,value:'退订用户数',name:'unsubscribeUser',type:'unsubscribeUser'},
                {label:8,value:'退订付费用户数',name:'unsubPayUser',type:'unsubPayUser'},
                {label:9,value:'退订免费用户数',name:'unsubFreeUser',type:'unsubFreeUser'},
                {label:10,value:'彩印投递数',name:'cyDeliveryCount',type:'cyDeliveryCount'},
                {label:11,value:'修改彩印数',name:'cyModifyCount',type:'cyModifyCount'}
            ],
            fmoney:fmoney,
            procMoniList:new Array(),
            procMoniZhanbiList:new Array(),
            loading:false
        }
    },
    beforeMount(){
           
    },
    mounted(){
        this.procMoniSearch();
        this.myChart=echarts.init(document.getElementById('myChart'));
        this.drow([]);
    },
     watch: {
        'procMoni.days':function (n, o) {
            if(n!=''){
                this.procMoni.dateTime='';
            }
        },
        'procMoni.dateTime':function (n, o) {
            if(n!=''){
                this.procMoni.days='';
            }
        },
    },
    methods:{
        getDay(day){    
                var today = new Date();    
                   
                var targetday_milliseconds=today.getTime() + 1000*60*60*24*day;            
                today.setTime(targetday_milliseconds); //注意，这行是关键代码  
                   
                var tYear = today.getFullYear();    
                var tMonth = today.getMonth();    
                var tDate = today.getDate();    
                tMonth = this.doHandleMonth(tMonth + 1);    
                tDate = this.doHandleMonth(tDate);    
                return tYear+"-"+tMonth+"-"+tDate;    
            },
            doHandleMonth(month){    
                   var m = month;    
                   if(month.toString().length == 1){    
                      m = "0" + month;    
                   }    
                   return m;    
            },
            getDateStr(today){
                var tYear = today.getFullYear();    
                var tMonth = today.getMonth();    
                var tDate = today.getDate();    
                tMonth = this.doHandleMonth(tMonth + 1);    
                tDate = this.doHandleMonth(tDate);    
                return tYear+"-"+tMonth+"-"+tDate; 
            },
        //查询
        procMoniSearch(){
            /*if(this.procMoni.days==7){
                params={
                    startTime:formDate(new Date(),'yyyy-MM-dd'),
                    endTime:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),'yyyy-MM-dd'),
                }
            }else if(this.procMoni.days=='yesterday'){
                params={
                    startTime:formDate((new Date().getTime() - 3600 * 1000 * 24),'yyyy-MM-dd'),
                    endTime:formDate((new Date().getTime() - 3600 * 1000 * 24),'yyyy-MM-dd'),
                }
            }else if(this.procMoni.days==30){
                params={
                    startTime:formDate(new Date(),'yyyy-MM-dd'),
                    endTime:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-30),'yyyy-MM-dd'),
                }
            }else{
                params={
                    startTime:formDate(new Date(this.procMoni.dateTime[0]),'yyyy-MM-dd'),
                    endTime:formDate(new Date(this.procMoni.dateTime[1]),'yyyy-MM-dd'),
                }
            }*/
            let params={
                startDate:formDate(new Date(this.procMoni.dateTime[0]),'yyyy-MM-dd'),
                endDate:formDate(new Date(this.procMoni.dateTime[1]),'yyyy-MM-dd'),
            }
            if(this.procMoni.days=='yesterday'){
                params.startDate=this.getDay(-1);
                params.endDate=this.getDay(0);
            }else if(this.procMoni.days=='7'){
                params.startDate=this.getDay(-7);
                params.endDate=this.getDay(0);
            }else if(this.procMoni.days=='30'){
                params.startDate=this.getDay(-30);
                params.endDate=this.getDay(0);
            }
            procMoniQuery('getPerformanceMonitor',params).then(res=>{
                if(res.code==0){
                    this.dataMessage=new Array();
                    this.procMoniList=res.data;
                    for(let i=0;i<res.data.monitorDetail.length;i++){
                        let proportion='0.00%';
                        if(this.procMoniList[this.procMoniTypeList[this.procMoni.selectCount-1].type]!=0){
                            proportion=((res.data.monitorDetail[i].effectUser/this.procMoniList[this.procMoniTypeList[this.procMoni.selectCount-1].type])*100).toFixed(2)+'%';
                        }
                         this.dataMessage.push({
                            name:res.data.monitorDetail[i].provinceName,
                            value:res.data.monitorDetail[i].effectUser,
                            proportion:proportion
                        })
                    }
                    this.drow();
                }
            })
        },
        //根据指标显示不同数据
        selectCount(){
            let selectCountValue='';
            this.dataMessage=new Array();
            for(let i=0;i<this.procMoniTypeList.length;i++){
                if(this.procMoniTypeList[i].label==this.procMoni.selectCount){
                    selectCountValue=this.procMoniTypeList[i].name;
                }
            }
            for(let i=0;i<this.procMoniList.monitorDetail.length;i++){
                let proportion='0.00%';
                console.info(this.procMoniList[this.procMoniTypeList[this.procMoni.selectCount-1].type]!=0);
                if(this.procMoniList[this.procMoniTypeList[this.procMoni.selectCount-1].type]!=0){
                    proportion=(this.procMoniList.monitorDetail[i][selectCountValue]/this.procMoniList[this.procMoniTypeList[this.procMoni.selectCount-1].type]).toFixed(2)+'%'
                }
                this.dataMessage.push({
                    name:this.procMoniList.monitorDetail[i].provinceName,
                    value:this.procMoniList.monitorDetail[i][selectCountValue]*100,
                    proportion:proportion
                })
            }
            this.drow();
        },
        //导出
        procMoniDownload(){
            let params={
                startDate:formDate(new Date(this.procMoni.dateTime[0]),'yyyy-MM-dd'),
                endDate:formDate(new Date(this.procMoni.dateTime[1]),'yyyy-MM-dd'),
            }
            if(this.procMoni.days=='yesterday'){
                params.startDate=this.getDay(-1);
                params.endDate=this.getDay(0);
            }else if(this.procMoni.days=='7'){
                params.startDate=this.getDay(-7);
                params.endDate=this.getDay(0);
            }else if(this.procMoni.days=='30'){
                params.startDate=this.getDay(-30);
                params.endDate=this.getDay(0);
            }

            exportExcel('performanceMonitorExportExcel',params).then(res=>{
                dowandFile(res,'业务监控数据.xlsx');
            })
        },
        //画图
        drow(){
            let option = {
                tooltip: {
                    trigger: 'item',
                    formatter:function(a){
                        if(a && a.data){
                            return a.data.name+':'+a.data.value+'</br>'+'占比:'+a.data.proportion;
                        }
                        
                    }
                },
                dataRange: {
                    orient: 'horizontal',
                    min: 10,
                    max: 10000000,
                    text:['高','低'],           // 文本，默认为数值文本
                    splitNumber:0,
                    inRange: {
                        color: ['white','lightskyblue','yellow', 'orangered']
                    }
                },
                series: [
                    {
                        name: '中国',
                        type: 'map',
                        mapType: 'china', // 自定义扩展图表类型
                        itemStyle:{
                            normal:{label:{show:true}},
                            emphasis:{label:{show:true}}
                        },
                        data:this.dataMessage,
                    }
                ]
            };
            this.myChart.setOption(option,true);
        }
    }
}