require('../static/css/reset.css');
require('../static/css/main.css');
import './commonStyles/common.css'

import Vue from 'vue';
import vuex from 'vuex';
import ElementUI from 'element-ui';
import App from './App.vue';
import router from './router';
import 'element-ui/lib/theme-chalk/index.css';
import VueResource from 'vue-resource';
import store from './store/store';
import {sessionOut,errorFour} from './util/core.js';
import $ from 'jquery'

window.$ = $;

Vue.use(VueResource);
Vue.use(ElementUI);
Vue.use(vuex);

//请求地址
// Vue.prototype.proxyUrl = '/api'
Vue.prototype.proxyUrl = '';
//提醒彩印请求地址
Vue.prototype.startUrl = 'http://47.98.106.192:19099/sop';
Vue.http.interceptors.push((request, next)  =>{
    let token=sessionStorage.getItem('TOKEN');
    if(token){
        request.headers.set('token',token);
    }
    next((response) => {
        if(response.status==404){
            errorFour();
        }
        if(response.status==401){
            sessionOut();
        }
        return response;
    });
});
new Vue({
  store:store,
  router: router,
  render: h => h(App)
}).$mount("#app")
