<template>
  <div>
    <h1 class="user-title">彩印盒</h1>
   <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="彩印盒ID" >
        <el-input v-model="print.svNumber"  placeholder="彩印盒ID" size="small" style="width:170px;" :maxlength="32" clearable></el-input>
      </el-form-item>
      <el-form-item label="彩印盒名称" >
        <el-input v-model="print.svName" placeholder="彩印盒名称" size="small" style="width:170px;" :maxlength="50" clearable></el-input>
      </el-form-item>

      <el-form-item label="审批状态">
          <el-select v-model="print.svStatus" placeholder="请选择" size="small" clearable>
              <el-option
                      v-for="item in sildeData"
                      :key="item.auditStatusNo"
                      :label="item.auditStatusName"
                      :value="item.auditStatusNo">
              </el-option>
          </el-select>
      </el-form-item>

        <el-form-item label="提交时间">
           <el-date-picker v-model="dateTime"
                type="datetimerange"
                range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              style="width:355px"
              size="small"
              />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(print)" size="small">查询</el-button>
        </el-form-item>
      </el-form>
  </div>
 
 <div class="user-table">
    <el-table
    v-loading="tableLoading"
    :data="tableData"
    border
     class="app-tab"
    :header-cell-class-name="tableheaderClassName">
    <el-table-column
      prop="svNumber"
      label="彩印盒ID"
      width="240">
    </el-table-column>
    <el-table-column
      prop="svName"
      label="彩印盒名称"
      width="200">
    </el-table-column>
    <el-table-column
      label="彩印盒内容"
      width="200">
      <template slot-scope="scope">
        <el-button type="text" @click="detailVisible=true;rowData=scope.row;">内容详情</el-button>
      </template>
    </el-table-column>
    <el-table-column
      prop="groupName"
      label="内容分类"
      width="200">
    </el-table-column>
    <el-table-column
      prop="labelName"
      label="内容标签"
      width="200"
      :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column
      prop="submitTime"
      label=" 提交时间"
       width="200">
    </el-table-column>
    <el-table-column
      label="审核状态"
       width="120">
        <template slot-scope="scope">
            <span v-show="(scope.row.svStatus==1)">待审批</span>
            <span v-show="(scope.row.svStatus==2)">审批不通过</span>
            <span v-show="(scope.row.svStatus==3)">审批通过</span>
            <span v-show="(scope.row.svStatus==4)">审批不通过</span>
            <span v-show="(scope.row.svStatus==5)">已撤销</span>
         </template>
    </el-table-column>

    <el-table-column
      prop="assessor"
      label="审核人"
       width="200">
    </el-table-column>
    <el-table-column
      prop="svSssesstime"
      label="审核时间"
       width="200">
    </el-table-column>
  </el-table>



<div>
      
      <el-dialog
          width="40%"
          title="内容详情"
          :visible.sync="detailVisible"
          :close-on-click-modal="false"
          append-to-body>
          <el-row>
          <el-col :span="12">彩印ID</el-col>
          <el-col :span="12">内容</el-col>
        </el-row>
        <div style="height:300px;overflow:auto;">
          <el-row v-if="rowData.content1">
            <el-col :span="12"><div v-html="rowData.svNumber+'1'"></div></el-col>
            <el-col :span="12"><div v-html="rowData.content1"></div></el-col>
          </el-row>

         <el-row v-if="rowData.content2">
            <el-col :span="12"><div v-html="rowData.svNumber+'2'"></div></el-col>
            <el-col :span="12"><div  v-html="rowData.content2"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content3">
            <el-col :span="12"><div v-html="rowData.svNumber+'3'"></div></el-col>
            <el-col :span="12"><div  v-html="rowData.content3"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content4">
            <el-col :span="12"><div v-html="rowData.svNumber+'4'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content4"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content5">
            <el-col :span="12"><div v-html="rowData.svNumber+'5'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content5"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content6">
            <el-col :span="12"><div v-html="rowData.svNumber+'6'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content6"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content7">
            <el-col :span="12"><div v-html="rowData.svNumber+'7'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content7"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content8">
            <el-col :span="12"><div v-html="rowData.svNumber+'8'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content8"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content9">
            <el-col :span="12"><div v-html="rowData.svNumber+'9'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content9"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content10">
            <el-col :span="12"><div v-html="rowData.svNumber+'10'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content10"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content11">
            <el-col :span="12"><div v-html="rowData.svNumber+'11'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content11"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content12">
            <el-col :span="12"><div v-html="rowData.svNumber+'12'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content12"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content13">
            <el-col :span="12"><div v-html="rowData.svNumber+'13'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content13"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content14">
            <el-col :span="12"><div v-html="rowData.svNumber+'14'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content14"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content15">
            <el-col :span="12"><div v-html="rowData.svNumber+'15'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content15"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content16">
            <el-col :span="12"><div v-html="rowData.svNumber+'16'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content16"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content17">
            <el-col :span="12"><div v-html="rowData.svNumber+'17'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content17"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content18">
            <el-col :span="12"><div v-html="rowData.svNumber+'18'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content18"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content19">
            <el-col :span="12"><div v-html="rowData.svNumber+'19'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content19"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content20">
            <el-col :span="12"><div v-html="rowData.svNumber+'20'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content20"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content21">
            <el-col :span="12"><div v-html="rowData.svNumber+'21'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content21"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content22">
            <el-col :span="12"><div v-html="rowData.svNumber+'22'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content22"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content23">
            <el-col :span="12"><div v-html="rowData.svNumber+'23'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content23"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content24">
            <el-col :span="12"><div v-html="rowData.svNumber+'24'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content24"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content25">
            <el-col :span="12"><div v-html="rowData.svNumber+'25'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content25"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content26">
            <el-col :span="12"><div v-html="rowData.svNumber+'26'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content26"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content27">
            <el-col :span="12"><div v-html="rowData.svNumber+'27'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content27"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content28">
            <el-col :span="12"><div v-html="rowData.svNumber+'28'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content28"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content29">
            <el-col :span="12"><div v-html="rowData.svNumber+'29'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content29"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content30">
             <el-col :span="12"><div v-html="rowData.svNumber+'30'"></div></el-col>
             <el-col :span="12"><div  v-html="rowData.content30"></div></el-col>
          </el-row>
        </div>
          
          
        <div slot="footer" style="text-align: right;">
          <el-button @click="detailVisible = false">确 定</el-button>
        </div>
      </el-dialog>
    </div>


 <div class="block app-pageganit">
  <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableData.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"  style="text-align: right;">
  </el-pagination>
  </div>
  </div>
</div>
</template>
<script>
import {formDate} from './../../../util/core.js';
  export default {
    name: 'UserList',
    data() {
      return {
        tableLoading: false,
        pageTotal:0,
        detailVisible:false,
        rowData: [],
        sildeData:[
                  {auditStatusNo:'1',auditStatusName:'待审批'},
                  {auditStatusNo:'2',auditStatusName:'审批不通过'},
                  {auditStatusNo:'3',auditStatusName:'审批通过'},
                  // {auditStatusNo:'4',auditStatusName:'失效'},
                  {auditStatusNo:'5',auditStatusName:'已撤销'}
        ],
        // tableData: [{
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }],
        tableData:[],
        value6: '',
        dateTime:[],
        print:{
            svNumber:"",
            svName:"",
            svStatus:"",
            startTime:"",
            endTime:"",
            pageNum:1,
            pageSize:10
        }

      }
    },
    methods: {
      handleSizeChange(val) {
      this.print.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyCreatePkg`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
    handleCurrentChange(val) {
      this.print.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyCreatePkg`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
      onSubmit(val) {
        if(this.dateTime && this.dateTime.length>0){
          this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          this.print.startTime='';
          this.print.endTime='';
        }
        this.tableLoading=true;
        this.$http.post(`${this.proxyUrl}/content/auditBox/getMyCreatePkg`, this.print).then((response) => {
              // console.log(response.data);
              this.tableData=response.data.datas;
              this.pageTotal=response.data.pageTotal;
              this.tableLoading=false;
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '查询异常'
              });
               this.tableLoading=false;
          });
      },
        tableheaderClassName({ row, rowIndex }) {
            return "table-head-th";
        }
    },
    created() {
    },
    components: {}
  }


</script>
<style>
.user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }
  .el-pagination{
    margin-left:270px;
  }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
