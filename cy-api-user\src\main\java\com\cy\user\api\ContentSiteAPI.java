package com.cy.user.api;

import com.cy.user.api.request.ContentSiteQueryReq;
import com.cy.user.model.ContentSiteModel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface ContentSiteAPI {

	@RequestMapping(value = "/cy/user/save/ContentSite")
	int insertContent(@RequestBody ContentSiteModel contentSiteModel);

	@RequestMapping(value = "/cy/user/save/ContentSites")
	int insertContents(@RequestBody List<ContentSiteModel> contentSiteModels);

	@RequestMapping(value = "/cy/user/find/BatchNoContentSite/size")
	List<ContentSiteModel> getContentSiteIdsByBatchNoBySize(@RequestBody ContentSiteQueryReq req);

	@RequestMapping(value = "/cy/user/update/ContentSite")
	void modifyContent(@RequestParam("id") Integer id, @RequestParam("verifyState") Integer verifyState,
			@RequestParam("number") String number);

	@RequestMapping(value = "/cy/user/find/ContentSite")
	ContentSiteModel findContentById(@RequestParam("id") Integer id);

	@RequestMapping(value = "/cy/user/find/BatchNoContentSite")
	List<ContentSiteModel> getContentSiteIdsByBatchNo(@RequestParam("batchNo") String batchNo);

	@RequestMapping(value = "/cy/user/find/BatchNoContentSite/batch")
	List<ContentSiteModel> getContentSiteIdsByBatchNo(@RequestParam("batchNo") String batchNo, @RequestParam("startId") int startId, @RequestParam("size") int size);

	@RequestMapping(value = "/cy/user/find/BatchNoErrorContentSite")
	List<ContentSiteModel> getErrorContentSiteIdsByBatchNo(@RequestParam("batchNo") String batchNo);

    @RequestMapping(value = "/cy/user/find/BatchNoErrorContentSite/total")
    long getTotalErrorContentSiteIdsByBatchNo(@RequestParam("batchNo") String batchNo);

    @RequestMapping(value = "/cy/user/find/BatchNoErrorContentSite/success")
    long getSuccessErrorContentSiteIdsByBatchNo(@RequestParam("batchNo") String batchNo);

    @RequestMapping(value = "/cy/user/find/BatchNoErrorContentSite/batch")
    List<ContentSiteModel> getErrorContentSiteIdsByBatchNoPaged(@RequestParam("batchNo") String batchNo, @RequestParam("startId") int startId, @RequestParam("size") int size);
}
