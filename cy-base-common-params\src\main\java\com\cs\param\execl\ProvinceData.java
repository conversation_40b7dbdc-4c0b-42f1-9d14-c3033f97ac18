package com.cs.param.execl;

import com.github.crab2died.annotation.ExcelField;

public class ProvinceData {

	@ExcelField(title = "国家编码", order = 1)
	private String countryCode;

	@ExcelField(title = "国家名称", order = 2)
	private String countryName;

	@ExcelField(title = "分区编码", order = 3)
	private String partitionCode;

	@ExcelField(title = "分区名称", order = 4)
	private String partitionName;

	@ExcelField(title = "省份编码", order = 5)
	private String provinceCode;

	@ExcelField(title = "省份名称", order = 6)
	private String provinceName;

	@ExcelField(title = "地市编码", order = 7)
	private String regionCode;

	@ExcelField(title = "地市名称", order = 8)
	private String regionName;

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getPartitionCode() {
		return partitionCode;
	}

	public void setPartitionCode(String partitionCode) {
		this.partitionCode = partitionCode;
	}

	public String getPartitionName() {
		return partitionName;
	}

	public void setPartitionName(String partitionName) {
		this.partitionName = partitionName;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

}
