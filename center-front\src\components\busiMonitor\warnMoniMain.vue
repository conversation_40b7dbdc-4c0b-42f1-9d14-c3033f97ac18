<template>
    <div class="fun_page">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="开销户异常监控" name="first">
                <warn-moni-new-user/>
            </el-tab-pane>
            <el-tab-pane label="推送量异常监控" name="second">
                <warnMoniPush/>
            </el-tab-pane>
            <el-tab-pane label="告警人员管理" name="third">
                <warnMoniWarnUser></warnMoniWarnUser>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
    //1.先使用import导入你要在该组件中使用的子组件
    import warnMoniNewUser from './warnMoniNewUser/warnMoniNewUser.vue'
    import warnMoniPush from './warnMoniPush/warnMoniPush.vue'
    import warnMoniWarnUser from './warnMoniWarnUser/warnMoniWarnUser.vue'

    export default {
        name: 'warnMoni',
        data() {
            return {
                activeName:'first'
            }
        },
        methods: {
            handleClick(){

            }
        },
        components: {warnMoniNewUser,warnMoniPush,warnMoniWarnUser},
    }
</script>
<style>
    .el-tabs__item{
        font-weight:bold;
        font-size: 16px;
    }
    .el-tabs__nav-wrap::after{
        background-color: #3898FE;
        height: 1px;
    }
</style>
