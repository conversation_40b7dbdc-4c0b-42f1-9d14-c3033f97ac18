import {postHeader,postDownloadHeader,post} from './../../../servers/httpServer.js';
//查询开户预警数据列表
export async function warnMoniQuery(url,params) {
    let result=await postHeader(url,params);
    return result.data;
}
//导出开户预警数据列表
export async function warnMoniExport(url,params) {
    let result=await postDownloadHeader(url,params);
    return result.data;
}
//下发短信
export async function warnMoniSendSms(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//下发邮件
export async function warnMoniSendEmail(url,params){
    let result=await postHeader(url,params);
    return result.data;
}
//根据省份查询城市列表
export async function queryCityList(url,params){
    let result=await post(url,params);
    return result.data;
}