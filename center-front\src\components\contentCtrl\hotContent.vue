<template>
  <div>
    <h1 class="user-title">热线彩印</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="彩印ID">
          <el-input
              v-model="searchReq.hcNumber"
              size="small"
              clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="彩印内容">
          <el-input
              v-model="searchReq.content"
              size="small"
              clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-select v-model="searchReq.deliveryType" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="热线彩印闪信" value="1"></el-option>
            <el-option label="热线彩印短信" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电信投递通道">
          <el-select v-model="searchReq.telecomDeliveryWay" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="东盟" value="2"></el-option>
            <el-option label="号百" value="5"></el-option>
            <el-option label="彩讯" value="8"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联通投递通道">
          <el-select v-model="searchReq.unicomDeliveryWay" size="small">
            <el-option label="全部" value=""></el-option>
            <el-option label="东盟" value="2"></el-option>
            <el-option label="联通在线" value="6"></el-option>
            <el-option label="彩讯" value="8"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间">
          <el-date-picker
              v-model="dateTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              style="width: 355px"
              size="small"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" size="small" @click="handleCurrentChange(1)"
          >查询</el-button
          >
          <el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
          <el-button
              type="danger"
              size="small"
              @click="handleBatchDelete"
              :disabled="selectedRows.length === 0"
          >批量删除</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="user-table">
      <el-table
          v-loading="tableLoading"
          :data="tableData"
          border
          class="app-tab"
          @selection-change="handleSelectionChange"
      >
        <el-table-column
            type="selection"
            width="55"
        ></el-table-column>
        <el-table-column
            prop="contentType"
            label="内容类型"
            width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.deliveryType === '1' ? "热线彩印闪信" : "热线彩印短信" }}
          </template>
        </el-table-column>
        <el-table-column
            prop="hcNumber"
            label="彩印ID"
            width="200"
        ></el-table-column>
        <el-table-column
            prop="content"
            label="彩印内容"
            width="240"
            :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div v-html="highLight(scope.row)"></div>
          </template>
        </el-table-column>
        <el-table-column
            label="提交时间"
            prop="createTime"
            width="200"
        >
          <template slot-scope="scope">
            {{
              scope.row.createTime
                  ? moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                  : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
            label="通过审核时间"
            prop="auditTime"
            width="200"
        >
          <template slot-scope="scope">
            {{
              scope.row.auditTime
                  ? moment(scope.row.auditTime).format("YYYY-MM-DD HH:mm:ss")
                  : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
            label="电信投递通道"
            prop="telecomDeliveryWay"
            width="150"
        ><template slot-scope="scope">
          {{ getDeliveryWayName(scope.row.telecomDeliveryWay) }}
        </template></el-table-column>
        <el-table-column
            label="联通投递通道"
            prop="unicomDeliveryWay"
            width="150"
        ><template slot-scope="scope">
          {{ getDeliveryWayName(scope.row.unicomDeliveryWay) }}
        </template></el-table-column>
        <el-table-column label="操作" width="160">
          <template slot-scope="scope">
            <el-button
                type="text"
                size="small"
                @click="handleDelete(scope.row)"
            >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
            v-show="pageTotal"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="searchReq.pageNum"
            :page-sizes="[10, 20, 30, 50]"
            :page-size="searchReq.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageTotal"
            style="text-align: right"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
        @open="exportClick"
        title="导出"
        :visible.sync="propVisible"
        :close-on-click-modal="false"
        width="45%">
      <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
        <el-form-item label="文件名" prop="fileName">
          <el-input v-model="addReq.fileName" type="input" size="small"
                    placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字"
                    style="width: 90%;"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256"
                    style="width: 90%;"></el-input>
        </el-form-item>
      </el-form>
      <div style=" margin-left: 80px; color: red;">
        导出后请到系统管理-导出文件下载对应文件
      </div>

      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="confirmExport">确定</el-button>
        <el-button @click="cancelExport">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from "moment";
import axios from '../../../node_modules/axios/dist/axios';

export default {
  data() {
    return {
      propVisible: false,
      addReq:{
        fileName: '',
        remark: ''
      },
      rules:{
        fileName: [
          { required: true, message: '请输入文件名', trigger: 'blur' },
          { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
          { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
        ],
        remark: [
          { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
        ]
      },
      tableLoading: false,
      tableData: [], // 表格数据
      selectedRows: [], // 选中的行
      // 搜索条件
      searchReq: {
        hcNumber: '',
        content: '',
        approveStatus: "3",
        deliveryType: '',
        telecomDeliveryWay: '', // 电信投递通道
        unicomDeliveryWay: '', // 联通投递通道
        createTimeStart: '',
        createTimeEnd: '',
        pageNum: 1,
        pageSize: 10
      },
      dateTime: [], // 时间范围
      pageTotal: 0, // 总页数
    };
  },
  created() {
    this.search();
  },
  methods: {
    moment,
    exportClick(){
      this.$refs.addReqForm.resetFields();
    },
    confirmExport() {
      this.$refs.addReqForm.validate(valid => {
        if (valid) {
          this.propVisible = !this.propVisible;

          if (this.searchReq.timearr) {
            this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.submitStart = "";
            this.searchReq.submitEnd = "";
          }
          //审核时间
          if (this.searchReq.timearr1) {
            this.searchReq.reviewStart = this.searchReq.timearr1[0] ? moment(new Date(this.searchReq.timearr1[0])).format('YYYYMMDDHHmmss') : '';
            this.searchReq.reviewEnd = this.searchReq.timearr1[1] ? moment(new Date(this.searchReq.timearr1[1])).format('YYYYMMDDHHmmss') : '';
          } else {
            this.searchReq.reviewStart = "";
            this.searchReq.reviewEnd = "";
          }
          const {timearr, timearr1, ...searchReq} = this.searchReq;

          const vm = this;
          var req = {
            fileName: this.addReq.fileName,
            remark: this.addReq.remark,
            taskType: 16,
            params: JSON.stringify(this.searchReq)
          }
          axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then(function (res) {

            let data = res.data;
            if (data.code == 0) {
              vm.$message.success("系统将生成文件名为" + vm.addReq.fileName + "的文件");
            } else {
              vm.$message.error(data.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    cancelExport() {
      this.propVisible = !this.propVisible;
      this.$refs.addReqForm.resetFields();
    },
    highLight(row) {
      return row.sign ? `【${row.sign}】${row.content}` : row.content;
    },
    handleSizeChange(val) {
      this.searchReq.pageNum = 1;
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      this.searchReq.pageNum = val;
      this.search();
    },
    search() {
      this.tableLoading = true;
      if (this.dateTime) {
        this.searchReq.createTimeStart = this.dateTime[0]
            ? moment(new Date(this.dateTime[0])).format("YYYYMMDD")
            : "";
        this.searchReq.createTimeEnd = this.dateTime[1]
            ? moment(new Date(this.dateTime[1])).format("YYYYMMDD")
            : "";
      } else {
        this.searchReq.createTimeStart = "";
        this.searchReq.createTimeEnd = "";
      }
      this.$http
          .post(
              `${this.proxyUrl}/content/hotSend/content/queryHotContent`,
              JSON.stringify(this.searchReq),
              {
                headers: {
                  "X-Requested-With": "XMLHttpRequest",
                  "Content-Type": "application/json",
                  charset: "utf-8"
                },
                emulateJSON: true,
                timeout: 5000
              }
          )
          .then(res => {
            this.tableLoading = false;
            var res = res.data;
            if (res.resStatus == 0) {
              this.tableData = res.datas || [];
              this.pageTotal = res.pageTotal;
            } else {
              this.tableData = [];
              this.pageTotal = 0;
            }
          });
    },
    getDeliveryWayName: function (deliveryWay) {
      if (deliveryWay == null) {
        return "-";
      } else if (deliveryWay == 2) {
        return "东盟";
      } else if (deliveryWay == 5) {
        return "号百";
      } else if (deliveryWay == 6) {
        return "联通在线";
      } else if (deliveryWay == 8) {
        return "彩讯";
      }
    },
    // 删除操作
    handleDelete(row) {
      this.$confirm('确定删除该彩印内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const deleteReq = {
          enterpriseID: row.enterpriseId,
          cyID: row.hcNumber,
          operType: "1" // 1表示删除
        };
        console.log(row.enterpriseID);
        this.$http
            .post(
                `${this.proxyUrl}/content/hotSend/content/syncContent`,
                JSON.stringify(deleteReq),
                {
                  headers: {
                    "X-Requested-With": "XMLHttpRequest",
                    "Content-Type": "application/json",
                    charset: "utf-8"
                  },
                  emulateJSON: true,
                  timeout: 5000
                }
            )
            .then(res => {
              console.log(res.data.resultCode);
              if (res.data.resultCode === "0") {
                this.$message.success('删除成功');
                this.search();
              } else {
                this.$message.error('删除失败');
              }
            });
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    },
    // 批量删除操作
    handleBatchDelete() {
      this.$confirm('确定删除选中的彩印内容吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const batchDeleteReq = {
          synContentReqList: this.selectedRows.map(row => ({
            enterpriseID: row.enterpriseId,
            cyID: row.hcNumber,
            operType: "1" // 1表示删除
          }))
        };
        this.$http
            .post(
                `${this.proxyUrl}/content/hotSend/content/batchDeleteContent`,
                JSON.stringify(batchDeleteReq),
                {
                  headers: {
                    "X-Requested-With": "XMLHttpRequest",
                    "Content-Type": "application/json",
                    charset: "utf-8"
                  },
                  emulateJSON: true,
                  timeout: 5000
                }
            )
            .then(res => {
              if (res.data.resultCode === "0") {
                this.$message.success('批量删除成功');
                this.search();
              } else {
                this.$message.error('批量删除失败');
              }
            });
      }).catch(() => {
        this.$message.info('已取消批量删除');
      });
    },
    // 表格选中行变化
    handleSelectionChange(rows) {
      this.selectedRows = rows;
    }
  }
};
</script>

<style scoped>
.el-table .table-head-th {
  background-color: #f5f5f5;
}

.batch-delete {
  margin-top: 20px;
  text-align: right;
}
</style>
