package com.cy.audit.model;

import java.io.Serializable;

public class PersonalNotifyRsp implements Serializable{
	String code;
	String info;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public static PersonalNotifyRsp success(){
		PersonalNotifyRsp personalNotifyRsp = new PersonalNotifyRsp();
		personalNotifyRsp.setCode("000000");
		personalNotifyRsp.setInfo("success");
		return personalNotifyRsp;
	}


}
