
package com.cs.param.model;

public class ParAlarmSmsModel {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String status;
	private String acceptNo;
	private Integer monId;
	private String content;
	private String sendNo;
	private String exceptionType;
	private String updateTime;
	private String isDelete;
	private String monUserMobile;
	private String exceptionName;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAcceptNo() {
		return acceptNo;
	}

	public void setAcceptNo(String acceptNo) {
		this.acceptNo = acceptNo;
	}

	public Integer getMonId() {
		return monId;
	}

	public void setMonId(Integer monId) {
		this.monId = monId;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getSendNo() {
		return sendNo;
	}

	public void setSendNo(String sendNo) {
		this.sendNo = sendNo;
	}

	public String getExceptionType() {
		return exceptionType;
	}

	public void setExceptionType(String exceptionType) {
		this.exceptionType = exceptionType;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getMonUserMobile() {
		return monUserMobile;
	}

	public void setMonUserMobile(String monUserMobile) {
		this.monUserMobile = monUserMobile;
	}

	public String getExceptionName() {
		return exceptionName;
	}

	public void setExceptionName(String exceptionName) {
		this.exceptionName = exceptionName;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

}
