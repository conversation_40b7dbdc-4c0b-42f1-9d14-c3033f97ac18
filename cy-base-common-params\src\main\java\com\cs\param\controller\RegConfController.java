package com.cs.param.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.common.ParOtherConfCommon;
import com.cs.param.common.ParThreConfCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.dao.ParOtherConfMapper;
import com.cs.param.dao.ParThreConfMapper;
import com.cs.param.model.PackageModel;
import com.cs.param.model.ParDefperBoxModel;
import com.cs.param.model.ParDefperContantModel;
import com.cs.param.model.ParOtherConfModel;
import com.cs.param.model.ParThreConfModel;
import com.cs.param.model.SignModel;
import com.cs.param.model.ThreConfModel;
import com.cs.param.services.RedisService;
import com.cs.param.services.SignBoxService;
import com.cs.param.services.TextSignService;
import com.cs.param.utils.DateUtil;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;
import com.cy.content.model.CsPackageModel;
import com.cy.content.model.CsTextModel;

/**
 * 
 * 地区配置管理Controller
 *
 */
@RequestMapping("/regConf")
@RestController
public class RegConfController {

	private static final Logger log = LoggerFactory.getLogger(RegConfController.class);

	@Autowired
	private ParThreConfMapper parThreConfMapper;

	@Autowired
	private ParOtherConfMapper parOtherConfMapper;

	@Autowired
	private SignBoxService signBoxService;

	@Autowired
	private TextSignService textSignService;

	@Autowired
	private RedisService redisService;

	/**
	 * 
	 * 获取默认个人彩印内容
	 *
	 */
	@RequestMapping(value = "getAllParDefperContant")
	public List<ParDefperContantModel> getAllParDefperContant(@RequestBody CsTextModel common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getAllParDefperContant", "获取默认个人彩印内容");
		try {
			CsTextModel textModel = new CsTextModel();
			textModel.setCsTextContent(common.getCsTextContent());
			List<CsTextModel> list = textSignService.getTextContent(textModel);
			List<ParDefperContantModel> modelList = new ArrayList<ParDefperContantModel>();
			for (CsTextModel csTextModel : list) {
				ParDefperContantModel model = new ParDefperContantModel();
				model.setId(csTextModel.getCsNumber());
				model.setDefPerContant(csTextModel.getCsTextContent());
				modelList.add(model);
			}
			return modelList;
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getAllParDefperContant", "获取默认个人彩印内容出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 获取默认个人彩印盒列表
	 *
	 */
	@RequestMapping(value = "getAllParDefperBox")
	public List<ParDefperBoxModel> getAllParDefperBox(@RequestBody CsPackageModel common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getAllParDefperBox", "获取默认个人彩印盒列表");
		try {
			CsPackageModel pkgModel = new CsPackageModel();
			pkgModel.setCsPkgName(common.getCsPkgName());
			List<CsPackageModel> list = signBoxService.getPkgContent(pkgModel);
			List<ParDefperBoxModel> modelList = new ArrayList<ParDefperBoxModel>();
			for (CsPackageModel csPackageModel : list) {
				ParDefperBoxModel model = new ParDefperBoxModel();
				model.setId(csPackageModel.getCsPkgNumber());
				model.setDefPerBox(csPackageModel.getCsPkgName());
				modelList.add(model);
			}
			return modelList;
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getAllParDefperBox", "获取默认个人彩印盒列表出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 根据ID获取默认个人彩印盒
	 *
	 */
	@RequestMapping(value = "getDefperBoxById")
	public List<PackageModel> getDefperBoxById(@RequestBody CsPackageModel common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getDefperBoxById", "根据ID获取默认个人彩印盒", common);
		try {
			List<PackageModel> packageList = new ArrayList<>();
			List<SignModel> signList = new ArrayList<>();
			CsPackageModel packageModel = signBoxService.getContent(common);
			PackageModel model = new PackageModel();
			model.setId(packageModel.getCsPkgNumber());
			model.setName(packageModel.getCsPkgName());
			setInfo(signList, packageModel.getContentId1(), packageModel.getCsPkgContent1());
			setInfo(signList, packageModel.getContentId2(), packageModel.getCsPkgContent2());
			setInfo(signList, packageModel.getContentId3(), packageModel.getCsPkgContent3());
			setInfo(signList, packageModel.getContentId4(), packageModel.getCsPkgContent4());
			setInfo(signList, packageModel.getContentId5(), packageModel.getCsPkgContent5());
			setInfo(signList, packageModel.getContentId6(), packageModel.getCsPkgContent6());
			setInfo(signList, packageModel.getContentId7(), packageModel.getCsPkgContent7());
			setInfo(signList, packageModel.getContentId8(), packageModel.getCsPkgContent8());
			setInfo(signList, packageModel.getContentId9(), packageModel.getCsPkgContent9());
			setInfo(signList, packageModel.getContentId10(), packageModel.getCsPkgContent10());
			setInfo(signList, packageModel.getContentId11(), packageModel.getCsPkgContent11());
			setInfo(signList, packageModel.getContentId12(), packageModel.getCsPkgContent12());
			setInfo(signList, packageModel.getContentId13(), packageModel.getCsPkgContent13());
			setInfo(signList, packageModel.getContentId14(), packageModel.getCsPkgContent14());
			setInfo(signList, packageModel.getContentId15(), packageModel.getCsPkgContent15());
			setInfo(signList, packageModel.getContentId16(), packageModel.getCsPkgContent16());
			setInfo(signList, packageModel.getContentId17(), packageModel.getCsPkgContent17());
			setInfo(signList, packageModel.getContentId18(), packageModel.getCsPkgContent18());
			setInfo(signList, packageModel.getContentId19(), packageModel.getCsPkgContent19());
			setInfo(signList, packageModel.getContentId20(), packageModel.getCsPkgContent20());
			setInfo(signList, packageModel.getContentId21(), packageModel.getCsPkgContent21());
			setInfo(signList, packageModel.getContentId22(), packageModel.getCsPkgContent22());
			setInfo(signList, packageModel.getContentId23(), packageModel.getCsPkgContent23());
			setInfo(signList, packageModel.getContentId24(), packageModel.getCsPkgContent24());
			setInfo(signList, packageModel.getContentId25(), packageModel.getCsPkgContent25());
			setInfo(signList, packageModel.getContentId26(), packageModel.getCsPkgContent26());
			setInfo(signList, packageModel.getContentId27(), packageModel.getCsPkgContent27());
			setInfo(signList, packageModel.getContentId28(), packageModel.getCsPkgContent28());
			setInfo(signList, packageModel.getContentId29(), packageModel.getCsPkgContent29());
			setInfo(signList, packageModel.getContentId30(), packageModel.getCsPkgContent30());
			model.setList(signList);
			packageList.add(model);
			return packageList;
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getDefperBoxById", "根据ID获取默认个人彩印盒出错！", e);
		}
		return null;
	}

	private void setInfo(List<SignModel> modelList, String id, String content) {
		if (id != null) {
			SignModel model = new SignModel();
			model.setId(id);
			model.setContent(content);
			modelList.add(model);
		}
	}

	/**
	 * 
	 * 获取阈值配置列表
	 *
	 */
	@RequestMapping(value = "getThreConfPage")
	@CySysLog(methodName = "获取阈值配置列表", modularName = "公参模块", optContent = "获取阈值配置列表")
	public ResultListCommon getThreConfPage(@ModelAttribute("common") ParThreConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getThreConfPage", "获取阈值配置列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parThreConfMapper.queryPageCount(common));
				// 数据分页数据
				List<ParThreConfModel> list = parThreConfMapper.queryPageInfo(common);
				List<ThreConfModel> newList = new ArrayList<ThreConfModel>();
				for (ParThreConfModel parThreConfModel : list) {
					ThreConfModel perModel = new ThreConfModel();
					perModel.setId(parThreConfModel.getId());
					perModel.setSignType("个人彩印");
					perModel.setCreateTime(parThreConfModel.getCreateTime());
					perModel.setProvinceCode(parThreConfModel.getProvinceCode());
					perModel.setProvinceName(parThreConfModel.getProvinceName());
					perModel.setDelayTime(parThreConfModel.getPerDelayTime());
					perModel.setPushTime(parThreConfModel.getPerPushTime());
					perModel.setPushMaxNum(parThreConfModel.getPerPushTime());
					perModel.setAllPushMaxNum(parThreConfModel.getPerAllPushMaxNum());
					newList.add(perModel);
					ThreConfModel bussModel = new ThreConfModel();
					bussModel.setId(parThreConfModel.getId());
					bussModel.setSignType("企业彩印");
					bussModel.setCreateTime(parThreConfModel.getCreateTime());
					bussModel.setProvinceCode(parThreConfModel.getProvinceCode());
					bussModel.setProvinceName(parThreConfModel.getProvinceName());
					bussModel.setDelayTime(parThreConfModel.getBussDelayTime());
					bussModel.setPushTime(parThreConfModel.getBussPushTime());
					bussModel.setPushMaxNum(parThreConfModel.getBussPushTime());
					bussModel.setAllPushMaxNum(parThreConfModel.getBussAllPushMaxNum());
					newList.add(bussModel);
					ThreConfModel mediaModel = new ThreConfModel();
					mediaModel.setId(parThreConfModel.getId());
					mediaModel.setSignType("新媒彩印");
					mediaModel.setCreateTime(parThreConfModel.getCreateTime());
					mediaModel.setProvinceCode(parThreConfModel.getProvinceCode());
					mediaModel.setProvinceName(parThreConfModel.getProvinceName());
					mediaModel.setDelayTime(parThreConfModel.getMediaDelayTime());
					mediaModel.setPushTime(parThreConfModel.getMediaPushTime());
					mediaModel.setPushMaxNum(parThreConfModel.getMediaPushTime());
					mediaModel.setAllPushMaxNum(parThreConfModel.getMediaAllPushMaxNum());
					newList.add(mediaModel);
				}
				result.setDatas(newList);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getThreConfPage", "获取阈值配置列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取阈值配置详情
	 *
	 */
	@RequestMapping(value = "getParThreConfDetail")
	public ParThreConfModel getParThreConfDetail(@ModelAttribute("common") ParThreConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getParThreConfDetail", "获取阈值配置详情", common);
		try {
			return parThreConfMapper.getParThreConfDetailById(common);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getParThreConfDetail", "获取阈值配置详情出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 修改阈值配置
	 *
	 */
	@RequestMapping(value = "updateParThreConf")
	@CySysLog(methodName = "修改阈值配置", modularName = "公参模块", optContent = "修改阈值配置")
	public ResultCommon updateParThreConf(@ModelAttribute("common") ParThreConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateParThreConf", "修改阈值配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parThreConfMapper.updateParThreConfByPK(common) > 0) {
				result.setResStatus(0);
				ParThreConfModel model = new ParThreConfModel();
				BeanUtils.copyProperties(common, model);
				redisService.threConfToRedis(model);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "updateParThreConf", "修改阈值配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 添加阈值配置
	 *
	 */
	@RequestMapping(value = "addParThreConf")
	@CySysLog(methodName = "添加阈值配置", modularName = "公参模块", optContent = "添加阈值配置")
	public ResultCommon addParThreConf(@ModelAttribute("common") ParThreConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "addParThreConf", "添加阈值配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			// 设置创建时间
			common.setCreateTime(DateUtil.parseDate(new Date(), DateUtil.DEFAULT_TIME));
			if (parThreConfMapper.addParThreConf(common) > 0) {
				result.setResStatus(0);
				ParThreConfModel model = new ParThreConfModel();
				BeanUtils.copyProperties(common, model);
				redisService.threConfToRedis(model);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParThreConf", "添加阈值配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除阈值配置
	 *
	 */
	@RequestMapping(value = "deleteParThreConf")
	@CySysLog(methodName = "删除阈值配置", modularName = "公参模块", optContent = "删除阈值配置")
	public ResultCommon deleteParThreConf(@ModelAttribute("common") ParThreConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParThreConf", "删除阈值配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parThreConfMapper.deleteParThreConfByPK(common) > 0) {
				result.setResStatus(0);
				redisService.deleteThreConfFromRedis(common.getProvinceCode());
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "deleteParThreConf", "删除阈值配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取其他配置列表
	 *
	 */
	@RequestMapping(value = "getOtherConfPage")
	@CySysLog(methodName = "获取其他配置列表", modularName = "公参模块", optContent = "获取其他配置列表")
	public ResultListCommon getOtherConfPage(@ModelAttribute("common") ParOtherConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getOtherConfPage", "获取其他配置列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				result.setPageNum(common.getPageNum());
				// 计算分页初始位置数
				common.setPageNum((common.getPageNum() - 1) * common.getPageSize());
				// 获取总条数
				result.setPageTotal(parOtherConfMapper.queryPageCount(common));
				// 数据分页数据
				result.setDatas(parOtherConfMapper.queryPageInfo(common));
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getOtherConfPage", "获取其他配置列表出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取其他配置详情
	 *
	 */
	@RequestMapping(value = "getParOtherConfDetail")
	public ParOtherConfModel getParOtherConfDetail(@ModelAttribute("common") ParOtherConfCommon common)
			throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getParOtherConfDetail", "获取其他配置详情", common);
		try {
			return parOtherConfMapper.getParOtherConfDetailById(common);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getParOtherConfDetail", "获取其他配置详情出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 修改其他配置
	 *
	 */
	@RequestMapping(value = "updateParOtherConf")
	@CySysLog(methodName = "修改其他配置", modularName = "公参模块", optContent = "修改其他配置")
	public ResultCommon updateParOtherConf(@ModelAttribute("common") ParOtherConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "updateParOtherConf", "修改其他配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parOtherConfMapper.updateParOtherConfByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "updateParOtherConf", "修改其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除其他配置
	 *
	 */
	@RequestMapping(value = "deleteParOtherConf")
	@CySysLog(methodName = "删除其他配置", modularName = "公参模块", optContent = "删除其他配置")
	public ResultCommon deleteParOtherConf(@ModelAttribute("common") ParOtherConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteParOtherConf", "删除其他配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parOtherConfMapper.deleteParOtherConfByPK(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "deleteParOtherConf", "删除其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 添加其他配置
	 *
	 */
	@RequestMapping(value = "addParOtherConf")
	@CySysLog(methodName = "添加其他配置", modularName = "公参模块", optContent = "添加其他配置")
	public ResultCommon addParOtherConf(@ModelAttribute("common") ParOtherConfCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		if (Util.isEmpty(common.getProvinceCode())) {
			result.setResText("请选择省份！");
			return result;
		}
		try {
			if (Util.isEmpty(common.getRegionCode())) {
				int count = parOtherConfMapper.isExistWithoutCity(common);
				if (count > 0) {
					result.setResText("该省份地市数据已存在！");
					return result;
				}
			} else {
				int count = parOtherConfMapper.queryPageCount(common);
				if (count > 0) {
					result.setResText("该省份地市数据已存在！");
					return result;
				}
			}

			// 设置创建时间
			common.setCreateTime(DateUtil.parseDate(new Date(), DateUtil.DEFAULT_TIME));
			if (parOtherConfMapper.addParOtherConf(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}
}
