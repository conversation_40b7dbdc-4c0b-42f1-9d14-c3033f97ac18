package com.cy.content.model;

import lombok.Data;

@Data
public class HotContentReq
{
    /**
     * 企彩印唯一编号，不为空的编号不能重复
     */
    private String hcNumber;


    /**
     * 彩印内容或模板内容
     */
    private String content;

    /**
     * 内容类型1:普通内容
     */
    private String contentType;

    /**
     *投递类型 1、闪信，2、短信
     */
    private String deliveryType;

    /**
     * 提交开始时间
     */
    private String createTimeStart;
    /**
     * 提交结束时间
     */
    private String createTimeEnd;
    /**
     * 审核开始时间
     */
    private String auditTimeStart;
    /**
     * 审核结束时间
     */
    private String auditTimeEnd;
    /**
     * 热线号码
     */
    private String hotlineNo;

    private String approveStatus;
    private String unicomApproveStatus;
    private String telecomApproveStatus;
    /**
     * 电信投递通道，5：号百；8：彩讯
     */
    private String telecomDeliveryWay;

    /**
     * 联通投递通道，6：联通在线；8：彩讯
     */
    private String unicomDeliveryWay;
    /**
     *  查询的页码
     */
    private int pageNum = 1;

    /**
     * 每页显示条数
     */
    private int pageSize = 10;

}
