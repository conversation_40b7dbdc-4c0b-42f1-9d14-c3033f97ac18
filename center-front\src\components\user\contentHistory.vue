<template>
    <div>
        <div>
            <el-row style="width: 94%;margin: 10px auto">
              <el-form :inline="true">
                <el-form-item label="设置类型">
                    <el-select v-model="searchReq.csSetType" placeholder="请选择" clearable size="small" style="width:222px;">
                        <el-option
                            v-for="item in slideData.csSetTypes"
                            :key="item.setTypelId"
                            :label="item.setTypeName"
                            :value="item.setTypelId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="设置内容">
                    <el-input v-model="searchReq.csContent"size="small" :maxlength="50" style="width:222px;"></el-input>
                </el-form-item>
                <el-form-item label="接收方类型">
                    <el-select v-model="searchReq.csRecType" placeholder="请选择" clearable size="small" style="width:222px;">
                        <el-option
                            v-for="item in slideData.cRecTypes"
                            :key="item.recTypeId"
                            :label="item.recTypeName"
                            :value="item.recTypeId">
                        </el-option>
                    </el-select>
                </el-form-item>
              </el-form>
              <el-form :inline="true">
                <el-form-item label="设置渠道">
                    <el-select v-model="searchReq.csChannelType" placeholder="请选择" clearable size="small" style="width:222px;">
                        <el-option
                            v-for="item in slideData.csChannelTypes"
                            :key="item.channelId"
                            :label="item.channelName ? item.channelName : item.channelId"
                            :value="item.channelId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="彩印状态">
                    <el-select v-model="searchReq.csStatusType" placeholder="请选择" clearable size="small" style="width:222px;">
                        <el-option
                            v-for="item in slideData.csStatusTypes"
                            :key="item.statuslId"
                            :label="item.statusName"
                            :value="item.statuslId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item style="width:312px;">
                    <div class="block">
                    <span class="demonstration" style="text-align:right;width:70px;display:inline-block;padding-right:12px;">设置时间</span>
                    <el-date-picker
                        v-model="searchReq.startTime"
                        type="date"
                        placeholder="开始日期"
                        value-format="yyyy-MM-dd"
                        style="width:110px;margin-left:-5px;"size="small">
                    </el-date-picker>
                    <el-date-picker
                        v-model="searchReq.endTime"
                        type="date"
                        placeholder="结束日期"
                        value-format="yyyy-MM-dd"
                        style="width:110px;"size="small">
                    </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item>
                   <el-button type="primary" @click="search(searchReq)"size="small" class="lue-btn">查询</el-button>
                </el-form-item>
              </el-form>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <el-table :data="tableData.datas" border style="margin:0 auto;width: 100%;">
                    <el-table-column prop="typeName" width="180" label="设置类型">
                        <!-- <template slot-scope="scope">
                            <span>{{labels.setCsType[scope.row.setCsType]}}</span>
                        </template> -->
                    </el-table-column>
                    <el-table-column prop="csContent" width="160" label="设置内容" :show-overflow-tooltip="true">
                    </el-table-column>
                     <el-table-column prop="setCsContentType" width="160" label="内容类型" :show-overflow-tooltip="true">
                    </el-table-column>
                    <el-table-column prop="recTypeName" width="120" label="接收方类型">
                        <!-- <template slot-scope="scope">
                            <span>{{labels.setRecType[scope.row.setRecType]}}</span>
                        </template> -->
                    </el-table-column>
                    <el-table-column prop="csRecPhone" width="160" label="接收号码" :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <span style="margin-left: 10px" v-if="scope.row.csRecType==1">
                                <el-button @click="getRecPhoneDetail(scope.row);RecPhoneDetail=true" type="text" size="small">号码详情</el-button>
                            </span>
                            <span style="margin-left: 10px" v-else-if="scope.row.csRecType=='0'">
                            全部
                            </span>
                            <span style="margin-left: 10px" v-else-if="scope.row.csRecType==2">
                                {{scope.row.csRecPhone}}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="channelName" width="120" label="设置渠道">
                    </el-table-column>
                    <el-table-column prop="statusName" width="100" label="彩印状态">
                    </el-table-column>
                    <el-table-column width="80" label="详情">
                        <template slot-scope="scope">
                            <el-button @click="openPersonCsDetail(scope.row);csDetail=true;" type="text" size="small">查看详情</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column prop="csSetTime" width="160" label="设置时间">
                    </el-table-column>
                     <el-table-column prop="crEndDate" width="160" label="结束时间">
                    </el-table-column>
                    <el-table-column label="操作" width="100" fixed="right">
                        <template slot-scope="scope" v-if="scope.row.csSetType!=2 && scope.row.csRecType==0 && scope.row.serviceCode == '03002'">
                            <el-button @click="openPerson(scope.row)" type="text" size="small">内容重置</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </el-row>
            <el-dialog title="选择内容重置" :visible.sync='openPersonHiddent' :close-on-click-modal="false">
                <el-row style="margin-left:10%">
                    <el-col >
                        <label>选择通过的历史审核内容</label>
                    </el-col>
                </el-row>
                <el-row v-for="list of csHisList" :key="list"  style="margin-left:10%">
                    <el-col >
                        <el-radio v-model="csHisRadio" :label="list" ></el-radio>
                    </el-col>
                </el-row>
                <el-row style="text-align: right;">
                        <el-col :span="12" :push='10'>
                            <el-button size="small" @click="openPersonHiddent=false">取消</el-button>
                            <el-button type="primary" size="small" @click="savePersonHis()">保存</el-button>
                        </el-col>
                    </el-row>
            </el-dialog>
            <!-- 分页 -->
            <div class="block app-pageganit">
                <el-pagination v-if="tableData.pageTotal>15" class="user-page" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="tableData.pageNum" :page-sizes="[10,20,30, 50]" :page-size=10 layout="total, sizes, prev, pager, next, jumper" :total="tableData.pageTotal">
                </el-pagination>
            </div>
        </div>
        <div>
            <!-- 号码详情弹窗 -->
            <el-dialog width="400px" title="号码详情" :visible.sync='RecPhoneDetail' :close-on-click-modal="false">
                <div style="height:150px;overflow-y:auto;overflow-x:hidden;">
                    <el-row :gutter="15">
                        <el-col :span="8" v-for="(phone, key, index) in RecPhoneDetailData.csRecPhones" :key="index" style="margin-bottom:10px;text-align:center">
                            {{phone}}
                        </el-col>
                    </el-row>
                </div>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button size="small" @click="RecPhoneDetail=false">关闭</el-button>
                </div>
            </el-dialog>
            <!-- 详情弹窗 -->
            <el-dialog title="详情" :visible.sync='csDetail' :close-on-click-modal="false">
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置类型:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.typeName}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">{{csDetailData.csSetType!=2 ? '彩印ID':'彩印盒ID'}}：</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.csId}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15" v-if="csDetailData.csSetType=='2'">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">彩印盒名称：</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{cyHisList.name}}</div>
                    </el-col>
                </el-row>
                <!--详情 文本彩印 -->
                <el-row :gutter="15" v-if="csDetailData.csSetType==0||csDetailData.csSetType==1">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">个人彩印内容:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.csContent}}</div>
                    </el-col>
                </el-row>
                <!--详情 彩印盒 -->
                <el-row :gutter="15" v-else-if="csDetailData.csSetType==2">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">彩印盒内容:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">
                            <el-table :data="cyHisList.list" border style="width: 400px;">
                                <el-table-column property="id" label="彩印ID"style="width: 100px;"></el-table-column>
                                <el-table-column property="content"  label="内容" style="width: 300px;"></el-table-column>
                            </el-table>
                        </div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">接收方类型:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.recTypeName}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置时间:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.csSetTime}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">设置渠道:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.channelName}}</div>
                    </el-col>
                </el-row>
                <el-row :gutter="15">
                    <el-col :span="6">
                        <div class="grid-content bg-purple user-info-label">彩印状态:</div>
                    </el-col>
                    <el-col :span="18">
                        <div class="grid-content bg-purple">{{csDetailData.statusName}}</div>
                    </el-col>
                </el-row>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button size="small" @click="csDetail=false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
    export default {
        name: "UserList",
        data() {
            return {
                csDetail:false,
                csDetailData:{},
                RecPhoneDetail:false,
                openPersonHiddent:false,//是否显示重置
                csHisInfo:{},//保存当前需要重置人员信息
                csHisRadio:'',//获取选中的审核内容信息
                csHisList:new Array(),//保存审核内容
                cyHisList:new Array(),//查看详情彩印盒内容
                RecPhoneDetailData:{},
                slideData:{
                    csSetTypes:[{setTypelId:'03000',setTypeName:'个人彩印-彩印盒'},{setTypelId:'03002',setTypeName:'个人彩印-文本彩印'},
                                {setTypelId:'03004',setTypeName:'个人彩印-情景彩印'},{setTypelId:'03006',setTypeName:'个人彩印-挂机彩漫'},
                                {setTypelId:'02100',setTypeName: '新媒彩印-屏显彩印'},
                                {setTypelId:'02102',setTypeName:'新媒彩印-挂机彩漫'},{setTypelId:'02103',setTypeName:'新媒彩印-挂机短信'},
                                {setTypelId:'01100',setTypeName:'企业彩印-主叫名片'},{setTypelId:'01101',setTypeName:'企业彩印-成员彩印'},
                                {setTypelId:'01102',setTypeName:'企业彩印-主被叫彩印'},{setTypelId:'01103',setTypeName:'企业彩印-热线彩印'},
                                {setTypelId:'01104',setTypeName:'企业彩印-挂机彩漫'},{setTypelId:'01105',setTypeName:'企业彩印-挂机短信'},
                                {setTypelId:'01108',setTypeName:'省内版企业彩印-主叫名片'},{setTypelId:'01109',setTypeName:'省内版企业彩印-成员名片'},
                                {setTypelId:'01110',setTypeName:'省内版企业彩印-主被叫彩印'},{setTypelId:'01111',setTypeName:'省内版企业彩印-热线彩印'},
                                {setTypelId:'01112',setTypeName:'省内版企业彩印-挂机彩漫'},{setTypelId:'01113',setTypeName:'省内版企业彩印-挂机短信'},
                                {setTypelId:'03007',setTypeName:'企业视彩号被叫挂机短信'},{setTypelId:'03008',setTypeName:'个人彩印-主叫彩印'},
                                {setTypelId:'03009',setTypeName:'个人彩印-被叫彩印'},{setTypelId:'03010',setTypeName:'个人彩印-主叫挂机短信'},
                                {setTypelId:'03011',setTypeName:'个人彩印-被叫挂机短信'},{setTypelId:'03012',setTypeName:'企业视彩号主叫彩印'},
                                {setTypelId:'03013',setTypeName:'企业视彩号被叫彩印'},{setTypelId:'03001',setTypeName:'个人彩印-主叫默认彩印'}],//设置类型
                    cRecTypes:[{recTypeId:'0',recTypeName:'所有'},{recTypeId:'1',recTypeName:'号码分组'},{recTypeId:'2',recTypeName:'指定号码'}],//接受方类型
                    csStatusTypes:[{statuslId:'0',statusName:'正常'},{statuslId:'1',statusName:'暂停'}],//彩印状态
                    csChannelTypes:[{channelId:'0',channelName:'短信'},{channelId:'1',channelName:'门户'},{channelId:'16',channelName:'客户端'},{channelId:'22',channelName:'中央平台'}]//设置渠道
                },
                totalCount: 0,
                searchReq:{
                    phone:'',
                    csSetType:'',//设置类型
                    csChannelType:'',//渠道类型
                    csRecType:'',//接收方类型
                    csStatusType:'',//彩印状态
                    csContent:'',//内容
                    startTime:'',
                    endTime:'',
                    pageNum:1,
                    pageSize:10
                },
                page: {
                    phone: "",
                    pageNum: 1,
                    pageSize: 20
                },
                tableData: {},
                modals: {}
            };
        },
        methods: {
            //查询请求
            search(request){
                request.phone = sessionStorage.getItem('pkCurUserid');
                this.$http
                    .post(`${this.proxyUrl}/user/customerHis/getHisCustomerCs?v=`+Math.random(),request,{ emulateJSON: true})
                    .then((res)=>{
                        this.tableData=res.data;
                    })
            },
            //分页
            handleSizeChange(val) {
                // console.log(`每页 ${val} 条`);
                this.searchReq.pageSize=val;
                this.search(this.searchReq);
            },
            handleCurrentChange(val) {
                // console.log(`当前页: ${val}`);
                this.searchReq.pageNum=val;
                this.search(this.searchReq);
            },
            //查看详情
            openPersonCsDetail: function(row) {
                this.csDetailData=row;
                if(row.csSetType==2){
                    post('/user/customerHis/getHisCustomerCsBoxContent',{pkgId:row.csId}).then(res=>{
                        console.log(res);
                        if(res.status===200){
                            this.cyHisList=res.data;
                        }
                    })
                }
            },
            getRecPhoneDetail(row){
                this.RecPhoneDetailData=row;
            },
            //显示重置内容
            openPerson(row){
               this.csHisInfo.csHisId=row.csHisId;
               this.csHisInfo.phone = sessionStorage.getItem('pkCurUserid');
                post('/user/customerHis/queryContents',{phone:this.csHisInfo.phone,csHisId:row.csHisId}).then(res=>{
                    console.info(res.status);
                    if(res.status===200){
                        this.openPersonHiddent=true;
                        this.csHisList=res.data;
                        this.csHisRadio=res.data[0];
                    }else{
                        this.$message({
                            message:'不能重置',
                            type:'warning'
                        });
                    }
                })
            },
            //确认重置内容
            savePersonHis(){
                let params={
                    csHisId:this.csHisInfo.csHisId,
                    csContent:this.csHisRadio,
                    phone:sessionStorage.getItem('pkCurUserid')
                }
                post('/user/customerHis/contentReset',params).then(res=>{
                    console.info(res);
                    if(res.data.status==0){
                        this.$message({
                            message:'保存成功',
                            type:'success'
                        });
                        this.openPersonHiddent=false;
                        this.search(this.searchReq);
                    }else{
                        this.$message({
                            message:'重置失败',
                            type:'error'
                        });
                    }
                })
            },
            //内容重置
            openPersonCsReset: function(row) {
                this.$http
                    .post(
                        `${this.proxyUrl}/user/customerHis/reSetHisCustomer`,
                        {
                            // phone: this.page.phone,
                            csHisId: row.csHisId,
                            csId: row.csId
                        },
                        {
                            emulateJSON: true
                        }
                    )
                    .then(function(res) {
                        console.info(res.data.status+'aaaaaaaaaaaa');
                        // this.$set(this.modals, "setCsType", 2);
                        // this.$set(this.modals, "visible", "csModify");
                        // this.$set(this.modals, "info", res.data);
                        // this.$set(this.modals, "setCsContent", res.data.setCsContent);
                        if(res.data.status==0){
                            console.log('重置成功');
                        }
                        else if(res.data.status==1) {
                            console.log('重置失败');
                        }
                    });
            },
            //编辑提交
            updateCsContent: function() {
                let param = {
                    phone: this.page.phone,
                    csNumber: this.modals.info.csNumber,
                    csPkgNumber: this.csPkgNumber || null,
                    setCsType: this.modals.setCsType,
                    csContent: this.modals.setCsContent,
                    csContent1: this.modals.info.csContent1,
                    csContent2: this.modals.info.csContent2,
                    csContent3: this.modals.info.csContent3,
                    csContent4: this.modals.info.csContent4,
                    csContent5: this.modals.info.csContent5
                };
                this.$http
                    .post(`${this.proxyUrl}/user/personInfo/modPersonCs`, param, {
                        emulateJSON: true
                    })
                    .then(function(res) {});
                this.$set(this.modals, "visible", "");
            }
        },
        mounted() {
            this.$set(this.searchReq, "phone", sessionStorage.getItem("userId"));
            // this.pageQuery(this.page);
            this.search(this.searchReq);
        },
        computed: {},
        components: {}
    };
</script>
<style scoped>
    .el-radio__label{
        display: inline-block;
        width: 400px;
        word-wrap: break-word
    }
    .el-radio {
        white-space: pre-wrap;
    }

</style>