
package com.cy.model;

import java.util.List;

/**
 * @date 2018年4月20日 - 上午11:33:00
 * @Description 角色model
 */
public class SysRoleModel {
	private static final long serialVersionUID = 1L;
	/**
	 * 角色ID
	 */
	private Integer sysRoleId;
	/**
	 * 角色名称
	 */
	private String sysRoleName;
	/**
	 * 创建时间
	 */
	private String sysGrleDate;
	/**
	 * 角色描述
	 */
	private String sysDescs;
	/**
	 * 是否删除
	 */
	private String isDelete;

	private List<SysResourcesModel> resources;

	private String sysResourcesName;

	private Integer[] sysResourcesIds;

	/**
	 * @return the resources
	 */
	public List<SysResourcesModel> getResources() {
		return resources;
	}

	/**
	 * @param resources
	 *            the resources to set
	 */
	public void setResources(List<SysResourcesModel> resources) {
		this.resources = resources;
	}

	public Integer getSysRoleId() {
		return sysRoleId;
	}

	public void setSysRoleId(Integer sysRoleId) {
		this.sysRoleId = sysRoleId;
	}

	public String getSysRoleName() {
		return sysRoleName;
	}

	public void setSysRoleName(String sysRoleName) {
		this.sysRoleName = sysRoleName;
	}

	public String getSysGrleDate() {
		return sysGrleDate;
	}

	public void setSysGrleDate(String sysGrleDate) {
		this.sysGrleDate = sysGrleDate;
	}

	public String getSysDescs() {
		return sysDescs;
	}

	public void setSysDescs(String sysDescs) {
		this.sysDescs = sysDescs;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getSysResourcesName() {
		return sysResourcesName;
	}

	public void setSysResourcesName(String sysResourcesName) {
		this.sysResourcesName = sysResourcesName;
	}

	public Integer[] getSysResourcesIds() {
		return sysResourcesIds;
	}

	public void setSysResourcesIds(Integer[] sysResourcesIds) {
		this.sysResourcesIds = sysResourcesIds;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "SysRoleModel [sysRoleId=" + sysRoleId + ", sysRoleName=" + sysRoleName + ", sysGrleDate=" + sysGrleDate
				+ ", sysDescs=" + sysDescs + ", isDelete=" + isDelete + "]";
	}

}
