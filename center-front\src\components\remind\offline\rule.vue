<template>
    <div class="rule">
        <div>
            <div class="user-titler">{{$route.name}}</div>
            <div class="contentbox">
                <!--查询条件-->
                <div class="app-norable">
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline" size="small">
                        <el-form-item label="离线包名称">
                            <el-input  v-model="searchForm.offlinePkgName" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="离线包ID">
                            <el-input  v-model="searchForm.offlinePkgId" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="时间">
                            <el-date-picker
                                    v-model="searchForm.startTime"
                                    type="daterange"
                                    value-format="yyyy-MM-dd"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <br>
                    <el-form  :inline="true" class="demo-form-inline" size="small">
                        <el-form-item>
                            <el-button type="primary" @click="addList">新增离线包</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <!--表格-->
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ" class="app-tab02">
                    <el-table-column prop="offlinePkgId" label="离线包ID" />
                    <el-table-column prop="offlinePkgName" label="离线包名称" />
                    <el-table-column prop="createTime" label="创建时间" />
                    <el-table-column prop="provinceName" label="省份" />
                    <el-table-column prop="countyName" label="地市" />
                    <el-table-column prop="category" label="分类" />
                    <el-table-column prop="sysRoleName" label="操作">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="editbtn(scope.row)">编辑</el-button>
                            <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                                <p style="margin: 10px;text-align:center">确定删除此项?</p>
                                <div style="margin: 10px;text-align:center">
                                    <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                    <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                                </div>
                                <div slot="reference">
                                    <el-button  type="text" size="small" >删除</el-button>
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
        </div>
        <!--新增-->
        <el-dialog title="新增离线包规则" :visible.sync="addVisible"   :close-on-click-modal="false">
            <addrule :addVisible="addVisible" @addList="addList"></addrule>
        </el-dialog>
        <!--编辑-->
        <el-dialog title="编辑离线包规则" :visible.sync="editVisible"   :close-on-click-modal="false">
            <editrule :editVisible="editVisible" :listrow="listrow" @editbtn="editbtn"></editrule>
        </el-dialog>
    </div>
</template>

<script>
    import {postHeader} from '@/servers/httpServer.js';
    import addrule from './addrule';
    import editrule from './editrule';
    export default {
        name: 'rule',
        data(){
            return{
                addVisible:false,
                editVisible:false,
                //查询form对象定义
                searchForm: {
                    offlinePkgId:'', //离线包id
                    offlinePkgName:'',//离线包名称
                    startDate:'', //开始时间
                    endDate: '', //
                    startTime:[],//时间
                    pageNo: 1, //页数
                    pageSize: 10,//每页条数
                },
                tableData:[],
                currentPage: 1,
                total:0,
                listrow:'',
            }
        },
        components: {
            addrule,
            editrule
        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            //查询请求
            search() {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                postHeader('queryOfflinePkgRule', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.offlinePkgRuleList;
                        vm.total = data.data.total;
                    }
                })
            },
            //删除
            deletebtn(row) {
                let vm = this;
                let parms = {
                    offlinePkgId:row.offlinePkgId,
                }
                postHeader('deleteOfflinePkgRule', JSON.stringify(parms)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("删除成功");
                        this.search();
                    }else{
                        vm.$message.error("删除失败");
                    }
                })
                row.show = false;
            },
            //新增
            addList(){
                let vm = this;
                vm.addVisible = !vm.addVisible;
                if(!vm.addVisible){
                    this.search();
                }
            },
            //编辑
            editbtn(row){
                let vm = this;
                vm.editVisible = !vm.editVisible;
                if(row){
                    vm.listrow = row;
                }
                if(!vm.editVisible){
                    this.search();
                }
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>
<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .contentbox{
        margin:0 15px;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>