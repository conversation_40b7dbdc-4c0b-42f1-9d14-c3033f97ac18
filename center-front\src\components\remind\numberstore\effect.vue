<template>
    <div id="effect">
        <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="查询" name="first">
                <!--查询-->
                <div class="boxcontent">
                    <el-form v-model="searchaleck" :inline="true" class="demo-form-inline app-form-item" label-width="60px">
                        <el-form-item label="号码库" size="small">
                            <el-select v-model="searchaleck" placeholder="请选择" class="app-input">
                                <el-option
                                        v-for="item in itemize"
                                        :key="item.item"
                                        :label="item.item"
                                        :value="item.numType">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <!--显示内容切换-->
                <div v-show="searchaleck==1" class="boxcontent">
                    <effect1></effect1>
                </div>
                <div v-show="searchaleck==2" class="boxcontent">
                    <deleter></deleter>
                </div>
                <div v-show="searchaleck==3" class="boxcontent">
                    <whitelisting></whitelisting>
                </div>
                <div v-show="searchaleck==4" class="boxcontent">
                    <history></history>
                </div>
            </el-tab-pane>
            <el-tab-pane label="导出" name="second">
                <!--导出-->
                <div class="content" style="margin-left: 125px;">
                    <el-form v-model="searchaleck" :inline="true" class="demo-form-inline app-form-item" label-width="100px">
                        <el-form-item label="号码库" size="small">
                            <el-select v-model="searchaleckd" placeholder="请选择">
                                <el-option
                                        v-for="item in itemize"
                                        :key="item.item"
                                        :label="item.item"
                                        :value="item.numType">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>
                </div>
                <!--显示内容切换-->
                <div v-show="searchaleckd==1" class="content">
                    <exporteffect></exporteffect>
                </div>
                <div v-show="searchaleckd==2" class="content">
                    <exportdeleter></exportdeleter>
                </div>
                <div v-show="searchaleckd==3" class="content">
                    <exportwhite></exportwhite>
                </div>
                <div v-show="searchaleckd==4" class="content">
                    <exporthistory></exporthistory>
                </div>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
    //生效号码
    import effect1 from './effect1';
    //已删除号码库
    import deleter from './deleter';
    //白名单号码库
    import whitelisting from './whitelisting';
    //号码历史记录
    import history from './history';
    //导出生效
    import exporteffect from './exporteffect';
    //导出删除
    import exportdeleter from './exportdeleter';
    //导出白名单
    import exportwhite from './exportwhite';
    //导出历史
    import exporthistory from './exporthistory';
    export default {
        name: 'effect',
        data(){
            return{
                activeName: 'first',
                searchaleck:1,
                searchaleckd:1,
                //号码库条件
                itemize:[
                    {
                        item:'生效号码库',
                        numType:1,
                    },
                    {
                        item:'已删除号码库',
                        numType:2,
                    },
                    {
                        item:'白名单号码库',
                        numType:3,
                    },
                    {
                        item:'号码历史记录',
                        numType:4,
                    }
                ],

            }
        },
        components: {
            effect1,
            deleter,
            whitelisting,
            history,
            exporteffect,
            exportdeleter,
            exportwhite,
            exporthistory
        },
        methods:{
            handleClick(tab, event) {
//                console.log(tab, event);
            },
        }
    }
</script>

<style scoped>
    .boxcontent{
        margin:0 15px;
    }
    .content{
        width: 640px;
    }
</style>
<style>
    #effect .el-tabs__item{
        font-size: 20px;
        height: 56px;
        line-height: 56px;
        text-align: center;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
        box-shadow: none;
    }
    #effect .el-tabs__nav{
        margin-left: 24px;
    }
</style>
