package com.cy.user.api;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.cy.user.model.ContentSiteModel;

/**
 * 
 * @date  2018年5月3日 - 上午11:42:30
 * @Description 用户内容审核完成回掉接口
 */
public interface AuditComplatedAPI {

    
    
    @RequestMapping(value = "/cy/user/content/auditComplated")
    void auditComplate(@RequestBody ContentSiteModel contentSiteModel);
    
}
