package com.cy.user.model;

/**
 * 
 * @date  2018年4月17日 - 下午8:00:01
 * @Description
 */

public class UserModel {

    /**
     * 手机号
     */

    private String msisdn;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 彩印状态
     */
    private Integer registerFlag;

    /**
     * 省编码
     */

    private String provinceno;

    /**
     * 省名称
     */
    private String provinceName;

    /**
     * 地区编码
     */
    private String areaNo;

    /**
     * 地区名称
     */
    private String areaName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 生日
     * 
     * 格式YYYYMMDD
     */
    private String birthday;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 
     * 个人彩印状态 0 未开通 1 开通
     */
    private String personStatus;

    /**
     * 
     * 新媒彩印状态 0 未开通 1 开通
     */
    private String mediaStatus;

    /**
     * 
     * 提醒彩印状态 0 未开通 1 开通
     */
    private String remindStatus;

    /**
     * 
     * 企业彩印状态 0 未开通 1 开通
     */
    private String cdpStatus;

    /**
     * 默认彩印内容
     */

    private String defaultSign;

    /**
     * 默认彩印Id
     */

    private String defaultSignNum;

    /**
     * 默认彩印盒ID
     */

    private String defaultSignGid;

    /**
     * 年龄
     */

    private Integer age;
    /**
     * 头像
     */

    private String head;
    /**
     * 个性签名
     */

    private String signature;

    /**
     * @return the defaultSign
     */
    public String getDefaultSign() {
        return defaultSign;
    }

    /**
     * @param defaultSign the defaultSign to set
     */
    public void setDefaultSign(String defaultSign) {
        this.defaultSign = defaultSign;
    }

    /**
     * @return the defaultSignNum
     */
    public String getDefaultSignNum() {
        return defaultSignNum;
    }

    /**
     * @param defaultSignNum the defaultSignNum to set
     */
    public void setDefaultSignNum(String defaultSignNum) {
        this.defaultSignNum = defaultSignNum;
    }

    /**
     * @return the defaultSignGid
     */
    public String getDefaultSignGid() {
        return defaultSignGid;
    }

    /**
     * @param defaultSignGid the defaultSignGid to set
     */
    public void setDefaultSignGid(String defaultSignGid) {
        this.defaultSignGid = defaultSignGid;
    }

    /**
     * @return the msisdn
     */
    public String getMsisdn() {
        return msisdn;
    }

    /**
     * @param msisdn the msisdn to set
     */
    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    /**
     * @return the nick
     */
    public String getNick() {
        return nick;
    }

    /**
     * @param nick the nick to set
     */
    public void setNick(String nick) {
        this.nick = nick;
    }

    /**
     * @return the registerFlag
     */
    public Integer getRegisterFlag() {
        return registerFlag;
    }

    /**
     * @param registerFlag the registerFlag to set
     */
    public void setRegisterFlag(Integer registerFlag) {
        this.registerFlag = registerFlag;
    }

    /**
     * @return the provinceno
     */
    public String getProvinceno() {
        return provinceno;
    }

    /**
     * @param provinceno the provinceno to set
     */
    public void setProvinceno(String provinceno) {
        this.provinceno = provinceno;
    }

    /**
     * @return the provinceName
     */
    public String getProvinceName() {
        return provinceName;
    }

    /**
     * @param provinceName the provinceName to set
     */
    public void setProvinceName(String provinceName) {
        this.provinceName = provinceName;
    }

    /**
     * @return the areaNo
     */
    public String getAreaNo() {
        return areaNo;
    }

    /**
     * @param areaNo the areaNo to set
     */
    public void setAreaNo(String areaNo) {
        this.areaNo = areaNo;
    }

    /**
     * @return the areaName
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * @param areaName the areaName to set
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the birthday
     */
    public String getBirthday() {
        return birthday;
    }

    /**
     * @param birthday the birthday to set
     */
    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    /**
     * @return the gender
     */
    public Integer getGender() {
        return gender;
    }

    /**
     * @param gender the gender to set
     */
    public void setGender(Integer gender) {
        this.gender = gender;
    }

    /**
     * @return the personStatus
     */
    public String getPersonStatus() {
        return personStatus;
    }

    /**
     * @param personStatus the personStatus to set
     */
    public void setPersonStatus(String personStatus) {
        this.personStatus = personStatus;
    }

    /**
     * @return the mediaStatus
     */
    public String getMediaStatus() {
        return mediaStatus;
    }

    /**
     * @param mediaStatus the mediaStatus to set
     */
    public void setMediaStatus(String mediaStatus) {
        this.mediaStatus = mediaStatus;
    }

    /**
     * @return the remindStatus
     */
    public String getRemindStatus() {
        return remindStatus;
    }

    /**
     * @param remindStatus the remindStatus to set
     */
    public void setRemindStatus(String remindStatus) {
        this.remindStatus = remindStatus;
    }

    /**
     * @return the cdpStatus
     */
    public String getCdpStatus() {
        return cdpStatus;
    }

    /**
     * @param cdpStatus the cdpStatus to set
     */
    public void setCdpStatus(String cdpStatus) {
        this.cdpStatus = cdpStatus;
    }

    /**
     * @return the age
     */
    public Integer getAge() {
        return age;
    }

    /**
     * @param age the age to set
     */
    public void setAge(Integer age) {
        this.age = age;
    }

    /**
     * @return the head
     */
    public String getHead() {
        return head;
    }

    /**
     * @param head the head to set
     */
    public void setHead(String head) {
        this.head = head;
    }

    /**
     * @return the signature
     */
    public String getSignature() {
        return signature;
    }

    /**
     * @param signature the signature to set
     */
    public void setSignature(String signature) {
        this.signature = signature;
    }

    /**
     * @Title: toString
     * @Description: TODO
     * @return
     */
    @Override
    public String toString() {
        return "UserModel [msisdn=" + msisdn + ", nick=" + nick + ", registerFlag=" + registerFlag + ", provinceno=" + provinceno + ", provinceName="
                + provinceName + ", areaNo=" + areaNo + ", areaName=" + areaName + ", email=" + email + ", birthday=" + birthday + ", gender="
                + gender + ", personStatus=" + personStatus + ", mediaStatus=" + mediaStatus + ", remindStatus=" + remindStatus + ", cdpStatus="
                + cdpStatus + ", defaultSign=" + defaultSign + ", defaultSignNum=" + defaultSignNum + ", defaultSignGid=" + defaultSignGid + ", age="
                + age + ", head=" + head + ", signature=" + signature + "]";
    }

}
