{"name": "v<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "vue-cli", "author": "<PERSON><PERSON><PERSON>", "private": true, "scripts": {"dev": "node build/dev-server.js", "start": "npm run dev", "build": "node build/build.js"}, "dependencies": {"axios": "^0.18.0", "blueimp-md5": "^2.10.0", "echarts": "^4.0.4", "element-ui": "^2.0.1", "jquery": "^3.6.0", "moment": "^2.22.0", "v-charts": "^1.16.8", "vue": "^2.5.2", "vue-resource": "^1.5.0", "vue-router": "^3.0.1", "vuex": "^2.5.0"}, "devDependencies": {"ajv": "^6.4.0", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-loader": "^7.1.1", "babel-plugin-transform-runtime": "^6.22.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "connect-history-api-fallback": "^1.3.0", "copy-webpack-plugin": "^4.0.1", "css-loader": "^0.28.0", "eventsource-polyfill": "^0.9.6", "express": "^4.14.1", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "http-proxy-middleware": "^0.17.3", "jsencrypt": "^3.2.1", "opn": "^5.1.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-middleware": "^1.12.0", "webpack-hot-middleware": "^2.18.2", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}