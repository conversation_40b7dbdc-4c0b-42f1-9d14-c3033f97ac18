<template>
  <div>
    <h1 class="user-title">我的代办-编辑企业彩印</h1>
    <hr class="user-line"/>
    <div id="printingUpTab">
  <el-form ref="form" :model="form" label-width="80px" >
  <el-form-item label="彩印ID：" style="margin-bottom: 0px;!important">
    {{this.$route.params.name}}
  </el-form-item>
  <el-form-item label="彩印ID：" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="企业编号" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="企业名称" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="省份：" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="地市：" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="彩印类型" style="margin-bottom: 0px;!important">
    
  </el-form-item>
  <el-form-item label="内容">
    <el-input type="textarea" v-model="form.desc"></el-input>
  </el-form-item>
  <el-form-item>
    <el-button type="primary" @click="onSubmit">提交</el-button>
  </el-form-item>
</el-form>
</div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        }
      }
    },
    methods: {
      onSubmit() {
        console.log('submit!');
      }
    }
  }
</script>
<style>


  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    height: 100px;
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingUpTab{
    margin-top: 3%;
    width:400px;
    margin-left: auto;
    margin-right: auto;
  }

</style>
