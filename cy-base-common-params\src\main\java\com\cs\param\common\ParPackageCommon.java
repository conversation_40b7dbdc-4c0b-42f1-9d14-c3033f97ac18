
package com.cs.param.common;

public class ParPackageCommon {
	private Integer id; // 套餐ID
	private String companyCode; // 企业代码
	private String businessCode; // 业务代码
	private String chargeCode;// 计费代码
	private String setMealName;// 套餐名称
	private String setMealType;// 套餐类型
	private String productMark;// 产品标识
	private String setMealMarkCode;// 套餐标识code
	private String sendMark;// 发送标志
	private String setMealFunction;// 套餐功能
	private String setMealDesc;// 套餐描述
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getCompanyCode() {
		return companyCode;
	}

	public void setCompanyCode(String companyCode) {
		this.companyCode = companyCode;
	}

	public String getBusinessCode() {
		return businessCode;
	}

	public void setBusinessCode(String businessCode) {
		this.businessCode = businessCode;
	}

	public String getChargeCode() {
		return chargeCode;
	}

	public void setChargeCode(String chargeCode) {
		this.chargeCode = chargeCode;
	}

	public String getSetMealName() {
		return setMealName;
	}

	public void setSetMealName(String setMealName) {
		this.setMealName = setMealName;
	}

	public String getSetMealType() {
		return setMealType;
	}

	public void setSetMealType(String setMealType) {
		this.setMealType = setMealType;
	}

	public String getProductMark() {
		return productMark;
	}

	public void setProductMark(String productMark) {
		this.productMark = productMark;
	}

	public String getSetMealMarkCode() {
		return setMealMarkCode;
	}

	public void setSetMealMarkCode(String setMealMarkCode) {
		this.setMealMarkCode = setMealMarkCode;
	}

	public String getSendMark() {
		return sendMark;
	}

	public void setSendMark(String sendMark) {
		this.sendMark = sendMark;
	}

	public String getSetMealFunction() {
		return setMealFunction;
	}

	public void setSetMealFunction(String setMealFunction) {
		this.setMealFunction = setMealFunction;
	}

	public String getSetMealDesc() {
		return setMealDesc;
	}

	public void setSetMealDesc(String setMealDesc) {
		this.setMealDesc = setMealDesc;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

}
