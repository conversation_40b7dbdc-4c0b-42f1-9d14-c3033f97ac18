package com.cy.user.cs.request;

import com.cy.user.util.DateUtil;

/**
 * 
 * <AUTHOR> @date
 * @Description 彩印平台请求订购彩印信息
 */
public class ContractSubscribeReq extends ContractReq {

	/**
	 * 订购类型
	 * 
	 */
	private String subscribeType = "2";

	/**
	 * 是否自动续订
	 */
	private String isAutoExtend = "0";

	/**
	 * 订购周期默认到2036年
	 */

	private String periodNum = DateUtil.getsubDate();

	/**
	 * 周期的单位 默认为年
	 */

	private String periodUnit = "3";

	/**
	 * 
	 */
	private String saleType = "0";

	public ContractSubscribeReq() {
	}

	public ContractSubscribeReq(Header header) {
		this.requestHeader = header;
	}

	/**
	 * @return the subscribeType
	 */
	public String getSubscribeType() {
		return subscribeType;
	}

	/**
	 * @param subscribeType
	 *            the subscribeType to set
	 */
	public void setSubscribeType(String subscribeType) {
		this.subscribeType = subscribeType;
	}

	/**
	 * @return the isAutoExtend
	 */
	public String getIsAutoExtend() {
		return isAutoExtend;
	}

	/**
	 * @param isAutoExtend
	 *            the isAutoExtend to set
	 */
	public void setIsAutoExtend(String isAutoExtend) {
		this.isAutoExtend = isAutoExtend;
	}

	/**
	 * @return the periodNum
	 */
	public String getPeriodNum() {
		return periodNum;
	}

	/**
	 * @param periodNum
	 *            the periodNum to set
	 */
	public void setPeriodNum(String periodNum) {
		this.periodNum = periodNum;
	}

	/**
	 * @return the periodUnit
	 */
	public String getPeriodUnit() {
		return periodUnit;
	}

	/**
	 * @param periodUnit
	 *            the periodUnit to set
	 */
	public void setPeriodUnit(String periodUnit) {
		this.periodUnit = periodUnit;
	}

	/**
	 * @return the saleType
	 */
	public String getSaleType() {
		return saleType;
	}

	/**
	 * @param saleType
	 *            the saleType to set
	 */
	public void setSaleType(String saleType) {
		this.saleType = saleType;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {

		return "ContractSubscribeReq [subscribeType=" + subscribeType + ", isAutoExtend=" + isAutoExtend
				+ ", periodNum=" + periodNum + ", periodUnit=" + periodUnit + ", saleType=" + saleType
				+ ","+super.toString() + "]";
	}

}
