import {warnMoniTestQuery,warnMoniTestSendEmail,warnMoniTestSendSms} from './warnMoniTestServer.js';
import {formDate} from './../../../util/core.js';
import {warnMoniSendEmail, warnMoniSendSms} from "../warnMoniNewUser/warnMoniNewUserServer";
export default {
    data(){
        return{
            form: {
                pageNo: 1,
                pageSize: 100,
                total: 0,
                startDate: new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),
                endDate: new Date(),
                datetype: 'date',
                provinceId:'',
                selectedOptions: [],
                locationId:''
            },
            //省份列表
            provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
            tableData: new Array(),
            pickerOptions: {
                disabledDate: function (today) {
                    return today.getTime() > Date.now();
                }
            },
            tableLoading: false
        }
    },
    mounted(){
        //this.search(1);
    },
    methods:{
        //查询
        search(pageNo){
            this.form.pageNo=pageNo;
            let startDate='';
            let endDate='';
            if(this.form.type=='month'){
                startDate=formDate(this.form.startDate,'yyyy-MM');
                endDate=formDate(this.form.endDate,'yyyy-MM');
            }else{
                startDate=formDate(this.form.startDate,'yyyy-MM-dd');
                endDate=formDate(this.form.endDate,'yyyy-MM-dd');
            }
            if(this.form.provinceId===''){
                this.$message({
                    message:'省份不能为空',
                    type:'warning'
                })
                return;
            }
            let params={
                beginTime:startDate,
                endTime:endDate,
                pageNo:this.form.pageNo,
                pageSize:this.form.pageSize,
                provinceId:this.form.provinceId
            }
            warnMoniTestQuery('pushDelayList',params).then(res=>{
                if(res.code===0){
                    this.tableData=res.data.pushDelayList;
                    this.form.total=res.data.total;
                }
            })
        },
        //根据不同pageSize查询数据
        handleSizeChange(pageSize){
            this.form.pageSize=pageSize;
            this.search(1)
        },
        //下发短信
        sendSms(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:3};
            warnMoniSendSms('sendSMS',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        },
        //下发邮件
        sendEmail(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:3};
            warnMoniSendEmail('sendEmail',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        },
        getProvinceName(row, col, val) {
            return this.provsMap[val.substr(0, 2)].name;
        },
        getCityName(row, col, val) {
            return this.provsMap[val.substr(0, 2)].citys[val.substr(2, 2)];
        }
    }
}