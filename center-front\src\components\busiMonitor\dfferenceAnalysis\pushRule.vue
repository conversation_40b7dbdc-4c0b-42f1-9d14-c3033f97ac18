<template>
    <div class="fun_page">
        <h1 class="user-title">推送规则</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <!--图表信息-->
        <el-radio-group v-model="searchForm.dataType" size="small" class="tab_radio">
            <el-radio-button label="differenceNum">推送关系差异数量</el-radio-button>
        </el-radio-group>
        <div class="chartArea">
            <el-form :inline="true" :model="searchForm" size="small">
                <el-row>
                    <el-col :span="24">
                        <el-form-item>
                            <el-radio-group v-model="searchForm.date1" size="small" @change="searchForm.startDate='';earchForm.endDate='';">
                                <el-radio-button label="yesterday">昨天</el-radio-button>
                                <el-radio-button label="7">7天</el-radio-button>
                                <el-radio-button label="30">30天</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="时间：">
                            <el-select v-model="dateType" style="width: 60px" @change="searchForm.date1=''">
                                <el-option label="天" value="date"></el-option>
                                <el-option label="月" value="month"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker placeholder="开始日期" v-model="searchForm.startDate" @change="searchForm.date1=''"
                                            style="width: 140px" :type="dateType"/>
                            至
                            <el-date-picker placeholder="结束日期" v-model="searchForm.endDate" @change="searchForm.date1=''"
                                            style="width: 140px"  :type="dateType"/>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" class="app-bnt" @click="search(1)">查询</el-button>
                    </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div id="myChart" style="width:100%;height:300px;"></div>
        </div>

        <!--数据信息-->
        <div style="margin-top: 30px;">
            <el-table :data="tableData" border style="width: 100%" max-height=500 v-loading="tableLoading">
                <!-- <el-table-column prop="statsDate" label="时间"></el-table-column> -->
                <el-table-column prop="contrastPlatform" label="对比平台"></el-table-column>
                <el-table-column prop="totalContrastData" label="对比数据总量"></el-table-column>
                <el-table-column prop="sameDataCount" label="相同数据量"></el-table-column>
                <el-table-column prop="contrastCount" label="差异数量"></el-table-column>
                <el-table-column prop="contrastPercent" label="差异比例"></el-table-column>
                <!-- <el-table-column prop="csTextInfoNo" label="详情">
                  <template slot-scope="scope">
                    <el-button type="text" size="small">差异清单</el-button>
                  </template>
              </el-table-column> -->
            </el-table>
            <div class="block app-pageganit">
                <el-pagination  @size-change="handleSizeChange" @current-change="search" :current-page="page.pageNo"
                                    :page-sizes="[10, 20, 30, 100]" :page-size="page.pageSize"
                                    layout="total, sizes, prev, pager, next, jumper" :total="page.total">
                    </el-pagination>
            </div>
        </div>
    </div></div>
</template>

<script>
    import {pushRuleQuery} from './dfferenceAnalysisServer.js';
    import {formDate} from './../../../util/core.js';
    // 引入 ECharts 主模块
    var echarts = require('echarts/lib/echarts');
    // 引入柱状图
    require('echarts/lib/chart/bar');
    // 引入提示框和标题组件
    require('echarts/lib/component/tooltip');
    require('echarts/lib/component/title');
    export default {
        name: 'pushRule',
        data() {
            return {
              data:new Array(),
              dataCity:new Array(),
              dateType:'date',
              searchForm:{
                dataType:'differenceNum',
                date1:'7',
                dates:[],
                dateTimes:[new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),new Date()],
                startDate:'',
                endDate:''
              },
              page:{
                  pageNo:1,
                  pageSize:10,
                  total:0
              },
              pickerOptions:{
                disabledDate:function (today) {
                  return today.getTime()>Date.now();
                }
              },
              tableLoading:false,
              tableData:[]
            }
        },
        computed:{},
        beforeMount(){
          this.search(1);
        },
        methods: {
            draw(){
                var myChart = echarts.init(document.getElementById('myChart'));
                var option = {
                    color: ['#3398DB'],
                    tooltip : {
                        trigger: 'axis',
                        axisPointer : {            
                            type : 'shadow' 
                        }
                    },
                    xAxis: {
                        data: this.dataCity
                    },
                    yAxis: {},
                    series: [
                        {
                        name: '销量',
                        type: 'bar',
                        data: this.data,
                        itemStyle: {
                            normal: {
                            color: function (params) {
                                if (params.data >20) {
                                return '#e4393c';
                                } else {
                                return '#fff';
                                }
                            }
                            }
                        }
                        }
                    ]
                };
                myChart.setOption(option);
            },
            search(pageNo) {
                this.tableLoading=true
                this.page.pageNo=pageNo;
                let params={};
                if(this.searchForm.date1==7){
                    params={
                        type:1,
                        pageNo:this.page.pageNo,
                        pageSize:this.page.pageSize,
                        startDate:formDate(new Date(),'yyyy-MM-dd'),
                        endDate:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),'yyyy-MM-dd'),
                    }
                }else if(this.searchForm.date1=='yesterday'){
                    params={
                        type:1,
                        pageNo:this.page.pageNo,
                        pageSize:this.page.pageSize,
                        startDate:formDate(new Date(new Date().getTime() - 3600 * 1000 *24),'yyyy-MM-dd'),
                        endDate:formDate(new Date(new Date().getTime() - 3600 * 1000 * 24),'yyyy-MM-dd'),
                    }
                }else if(this.searchForm.date1==30){
                    params={
                        type:1,
                        pageNo:this.page.pageNo,
                        pageSize:this.page.pageSize,
                        startDate:formDate(new Date(),'yyyy-MM-dd'),
                        endDate:formDate(new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-30),'yyyy-MM-dd'),
                    }
                }else{
                     if(this.searchForm.startDate===''){
                        this.$message('请选择开始时间');
                        return;
                    }else if(this.searchForm.endDate===''){
                        this.$message('请选择结束时间');
                        return;
                    }else if(new Date(this.searchForm.startDate).getTime()>new Date(this.searchForm.endDate).getTime()){
                        this.$message('开始时间不得小于结束时间');
                        return;
                    }
                    let type=1;
                    if(this.dateType==='date'){
                        type=1;
                    }else{
                        type=2;
                    }
                    params={
                        type:type,
                        pageNo:this.page.pageNo,
                        pageSize:this.page.pageSize,
                        startDate:formDate(this.searchForm.startDate,'yyyy-MM-dd'),
                        endDate:formDate(this.searchForm.endDate,'yyyy-MM-dd'),
                    }
                }
                pushRuleQuery('pushRuleConsistency',params).then(res=>{
                    if(res.code===0){
                        this.tableLoading=false;
                        this.tableData=res.data.pushRuleConsistencyInfoList;
                        this.page.total=res.data.total;
                        this.data=new Array();
                        this.dataCity=new Array();
                        for(let i=0;i<res.data.pushRuleConsistencyInfoList.length;i++){
                            this.dataCity.push(res.data.pushRuleConsistencyInfoList[i].contrastPlatform);
                            this.data.push(res.data.pushRuleConsistencyInfoList[i].totalContrastData);
                        }
                        this.draw();
                    }
                })
            },
            handleSizeChange(pageSize){
                this.page.pageSize=pageSize;
                this.search(1);
            }
        }
    }
</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
    .el-radio-button:first-child .el-radio-button__inner,.el-radio-button:last-child .el-radio-button__inner{
        border-radius:0;
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #434343;
        border-color:#DDDFE6;
        box-shadow:-1px 0 0 0 #DDDFE6;
        color:#fff;
    }
    .chartArea{
        border:1px solid #DDDFE6;
        margin-top:-1px;
    }
    .chartArea form{
        margin:20px 0 0 20px;
    }
    .chartArea .el-radio-button__inner{
        background:transparent;
        border: 1px solid transparent;
    }
    .chartArea .el-radio-button__orig-radio:checked+.el-radio-button__inner{
        background-color:#3B9ED8;
    }
</style>
