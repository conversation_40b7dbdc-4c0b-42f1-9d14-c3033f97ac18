package com.cy.content.model;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
public class Content {
    @JacksonXmlProperty(localName = "CYID")
    private String cYID;

    @JacksonXmlProperty(localName = "CYContent")
    private String cYContent;

    @JacksonXmlProperty(localName = "ContentGroupName")
    private String contentGroupName;

    @JacksonXmlProperty(localName = "ContentLabelName")
    private String contentLabelName;

    @JacksonXmlProperty(localName = "ContentGroupID")
    private String contentGroupID;

    @JacksonXmlProperty(localName = "ContentLabelID")
    private String contentLabelID;

    @JacksonXmlProperty(localName = "Status")
    private String status;

    @JacksonXmlProperty(localName = "Message")
    private String message;

    @JacksonXmlProperty(localName = "UnicomStatus")
    private String unicomStatus;

    @JacksonXmlProperty(localName = "UnicomMessage")
    private String unicomMessage;

    @JacksonXmlProperty(localName = "TelecomStatus")
    private String telecomStatus;

    @JacksonXmlProperty(localName = "TelecomMessage")
    private String telecomMessage;

    public String getcYID() {
        return cYID;
    }

    public void setcYID(String cYID) {
        this.cYID = cYID;
    }

    public String getcYContent() {
        return cYContent;
    }

    public void setcYContent(String cYContent) {
        this.cYContent = cYContent;
    }

    public String getContentGroupName() {
        return contentGroupName;
    }

    public void setContentGroupName(String contentGroupName) {
        this.contentGroupName = contentGroupName;
    }

    public String getContentLabelName() {
        return contentLabelName;
    }

    public void setContentLabelName(String contentLabelName) {
        this.contentLabelName = contentLabelName;
    }

    public String getContentGroupID() {
        return contentGroupID;
    }

    public void setContentGroupID(String contentGroupID) {
        this.contentGroupID = contentGroupID;
    }

    public String getContentLabelID() {
        return contentLabelID;
    }

    public void setContentLabelID(String contentLabelID) {
        this.contentLabelID = contentLabelID;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getUnicomStatus() {
        return unicomStatus;
    }

    public void setUnicomStatus(String unicomStatus) {
        this.unicomStatus = unicomStatus;
    }

    public String getUnicomMessage() {
        return unicomMessage;
    }

    public void setUnicomMessage(String unicomMessage) {
        this.unicomMessage = unicomMessage;
    }

    public String getTelecomStatus() {
        return telecomStatus;
    }

    public void setTelecomStatus(String telecomStatus) {
        this.telecomStatus = telecomStatus;
    }

    public String getTelecomMessage() {
        return telecomMessage;
    }

    public void setTelecomMessage(String telecomMessage) {
        this.telecomMessage = telecomMessage;
    }

    @Override
    public String toString() {
        return "Content{" +
                "cYID='" + cYID + '\'' +
                ", cYContent='" + cYContent + '\'' +
                ", contentGroupName='" + contentGroupName + '\'' +
                ", contentLabelName='" + contentLabelName + '\'' +
                ", contentGroupID='" + contentGroupID + '\'' +
                ", contentLabelID='" + contentLabelID + '\'' +
                ", status='" + status + '\'' +
                ", message='" + message + '\'' +
                ", unicomStatus='" + unicomStatus + '\'' +
                ", unicomMessage='" + unicomMessage + '\'' +
                ", telecomStatus='" + telecomStatus + '\'' +
                ", telecomMessage='" + telecomMessage + '\'' +
                '}';
    }
}

