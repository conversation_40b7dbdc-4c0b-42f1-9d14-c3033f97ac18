package com.cs.param.constants;

public interface LogConstant
{
    /**
     * 微服务名称
     */
    public static final String LOG_MICROSERVICE_NAME = "cy-base-common-params";
    
    /**
     * 请求标识
     */
    public static final String LOG_TYPE_REQ = "REQ";
    
    /**
     * 响应标识
     */
    public static final String LOG_TYPE_RSP = "RSP";
    
    /**
     * 部件名称
     */
    public static final String LOG_SYSTEM_NAME = "cy-base-common-params";
    
    /**
     * 接口类型
     */
    public static final String INTERFACE_TYPE_JSON = "HTTP+JSON";
    
    /**
     * 接口类型
     */
    public static final String INTERFACE_TYPE_XML = "HTTP+XML";
    
    /**
     * 接口类型
     */
    public static final String INTERFACE_TYPE_DSF = "DSF";
    
    /**
     * 接口类型
     */
    public static final String INTERFACE_TYPE_REST = "HTTP+REST";
    
    /**
     * 接口类型
     */
    public static final String INTERFACE_TYPE_FEIGN = "FEIGN";
    
    /**
     * ip的请求
     */
    public static final String HTTP_HEAD = "http://";
    
    /**
     * ipde的请求
     */
    public static final String HTTPS_HEAD = "https://";
    
    /**
     * 东盟
     */
    public static final String EAST_LOG = "east_log";
    
    /**
     * 号百
     */
    public static final String HAOBAI_LOG = "haobai_log";
}
