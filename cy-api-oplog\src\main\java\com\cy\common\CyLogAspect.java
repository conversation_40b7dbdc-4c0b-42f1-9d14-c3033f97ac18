package com.cy.common;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.cy.jwt.JwtUtil.JWTHelper;
import com.cy.model.SysLogModel;
import com.cy.service.SysLogService;

/**
 * 
 * Description: 切面类记录接口调用失败日志信息
 * 
 * @date 2018-4-21
 * 
 */
@Aspect
@Component
public class CyLogAspect {

	private Logger logger = LoggerFactory.getLogger(CyLogAspect.class);
	
	private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");

	public static final int CODE_SUCCESS = 0;
	
	@Autowired
	private SysLogService sysLogService;

	@Pointcut("@annotation(com.cy.common.CySysLog)")
	public void controllerAspect() {
	}

	@AfterReturning(pointcut = "controllerAspect()", returning = "res")
	public void doAfter(JoinPoint joinPoint, Object res) throws Exception {

//		// 获取反射参数
//		String methodName = joinPoint.getSignature().getName();
//		logger.debug("---------------AfterReturning开始--------------");
//		if (null == res&&!"JKServiceGetTicket".equals(methodName)) {
//			return;
//		}
//		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//		//获取真实ip
//		String ip = getIpAdrress(request);
//
//		String message = "";
////		if(res!=null){
//////			Map<String, Object> map = Obj2Map(res);
////			message = (String) map.get("message");
////		}
//
//		// 类名
//		String targetName = joinPoint.getTarget().getClass().getSimpleName();
//
//		MethodSignature ms = (MethodSignature) joinPoint.getSignature();
//		// 入参key
//		String[] parameterNames = ms.getParameterNames();
//		// 入参value
//		Object[] arguments = joinPoint.getArgs();
//		Method method = ms.getMethod();
//		// 方法的注解对象
//		CySysLog logParam = method.getAnnotation(CySysLog.class);
//		logger.debug("modularName：" + logParam.modularName());
//		logger.debug("methodName：" + logParam.methodName());
//		logger.debug("optContent" + logParam.optContent());
//		logger.debug("targetName:" + targetName);
//		logger.debug("methodName:" + methodName);
//		logger.debug("ms:" + ms);
//		try {
//			logger.debug("arguments:" + JSONObject.toJSONString(arguments));
//			logger.debug("parameterNames:" + JSONObject.toJSONString(parameterNames));
//		}catch (Exception e){
//			logger.debug("arguments:" + "");
//			logger.debug("parameterNames:" + "");
//		}
//
//		logger.debug("method:" + JSONObject.toJSONString(method));
//
//		// 拼参数
//		// PartnerSystemLog sysLog = new PartnerSystemLog();
//		// 获取用户
//
//		// sysLog.setSend(logParam.modularName());
//		// sysLog.setUrl(logParam.methodName());
//		// sysLog.setType(logParam.optContent());
//		// 入参字符串
//		StringBuffer jsonParamSb = new StringBuffer();
//		try{
//
//			for (int i = 0; i < parameterNames.length; i++) {
//				jsonParamSb.append(parameterNames[i]).append("=").append(JSONObject.toJSONString(arguments[i]));
//				if (i != (parameterNames.length - 1)) {
//					jsonParamSb.append("&");
//				}
//			}
//		}catch (Exception ex){
//			logger.error("jsonParamSb error",ex);
//		}
//		// 截取返回json
//		logger.debug(jsonParamSb.toString());
//		// 出参
//		logger.debug(JSONObject.toJSONString(res));
//		StringBuffer remarkSb = new StringBuffer();
//		remarkSb.append(targetName).append(".").append(methodName).append("报错信息：").append(message);
//		// 截取remark
//		logger.debug(JSONObject.toJSONString(remarkSb.toString()));
//		SysLogModel sysLogModel = new SysLogModel();
//
//		try {
//			logger.debug(getUserName());
//			sysLogModel.setSysUseName(getUserName());
//			sysLogModel.setSysUseResult("成功");
//
//		}catch (Exception exception){
//			sysLogModel.setSysUseName("loginError");
//			logger.error("getUserName error",exception);
//			sysLogModel.setSysUseResult("失败");
//
//		}
//		// sysLog.setCreateTime(new Date());
//		// sysLog.setUpdateTime(new Date());
//		// handleLog(sysLog);
//		logger.debug("---------------AfterReturning结束--------------");
//		sysLogModel.setSysUseModule(logParam.modularName());
//		sysLogModel.setSysUseTime(sdf.format(new Date()));
//		sysLogModel.setSysUseObject(logParam.methodName());
//		sysLogModel.setSysUseContent(logParam.optContent());
//		sysLogModel.setSysUseIp(ip);
//		sysLogModel.setSysUseType("");
//		handleLog(sysLogModel);
		
	}

	@AfterThrowing(value = "controllerAspect()", throwing = "e")
	public void doAfter(JoinPoint joinPoint, Exception e) {
//		// 获取反射参数
//		logger.debug("---------------AfterThrowing开始--------------");
//		if (null == e) {
//			return;
//		}
//		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//		//获取真实ip
//		String ip = getIpAdrress(request);
//		// 类名
//		String targetName = joinPoint.getTarget().getClass().getSimpleName();
//		// 得到方法名
//		String methodName = joinPoint.getSignature().getName();
//		MethodSignature ms = (MethodSignature) joinPoint.getSignature();
//		// 入参key
//		String[] parameterNames = ms.getParameterNames();
//		// 入参value
//		Object[] arguments = joinPoint.getArgs();
//		Method method = ms.getMethod();
//		// 方法的注解对象
//		CySysLog logParam = method.getAnnotation(CySysLog.class);
//		logger.debug("modularName：" + logParam.modularName());
//		logger.debug("methodName：" + logParam.methodName());
//		logger.debug("optContent" + logParam.optContent());
//		logger.debug("targetName:" + targetName);
//		logger.debug("methodName:" + methodName);
//		logger.debug("ms:" + ms);
//		try {
//			logger.debug("arguments:" + JSONObject.toJSONString(arguments));
//			logger.debug("parameterNames:" + JSONObject.toJSONString(parameterNames));
//		}catch (Exception ex){
//			logger.debug("arguments:" + "");
//			logger.debug("parameterNames:" + "");
//		}
//		logger.debug("method:" + JSONObject.toJSONString(method));
//
//		// 拼参数
//		// PartnerSystemLog sysLog = new PartnerSystemLog();
//		// 获取用户
//
//		// sysLog.setSend(logParam.modularName());
//		// sysLog.setUrl(logParam.methodName());
//		// sysLog.setType(logParam.optContent());
//		// 入参字符串
//		StringBuffer jsonParamSb = new StringBuffer();
//		try {
//			for (int i = 0; i < parameterNames.length; i++) {
//				jsonParamSb.append(parameterNames[i]).append("=").append(JSONObject.toJSONString(arguments[i]));
//				if (i != (parameterNames.length - 1)) {
//					jsonParamSb.append("&");
//				}
//			}
//		}catch (Exception ex){
//			logger.error("jsonParamSb error",e);
//		}
//		// 截取返回json
//		logger.debug(jsonParamSb.toString());
//		// 出参
//		StringBuffer remarkSb = new StringBuffer();
//		remarkSb.append(targetName).append(".").append(methodName).append("报错信息：").append(e);
//		// 截取remark
//		logger.debug(JSONObject.toJSONString(remarkSb.toString()));
//		SysLogModel sysLogModel = new SysLogModel();
//		try {
//			logger.debug(getUserName());
//			sysLogModel.setSysUseName(getUserName());
//
//		}catch (Exception exception){
//			logger.error("getUserName error",exception);
//		}
//		// sysLog.setCreateTime(new Date());
//		// sysLog.setUpdateTime(new Date());
//		// handleLog(sysLog);
//		logger.debug("---------------AfterThrowing结束--------------");
//		sysLogModel.setSysUseModule(logParam.modularName());
//		sysLogModel.setSysUseTime(sdf.format(new Date()));
//		sysLogModel.setSysUseObject(logParam.methodName());
//		sysLogModel.setSysUseContent(logParam.optContent());
//		sysLogModel.setSysUseResult("失败");
//		sysLogModel.setSysUseIp(ip);
//		sysLogModel.setSysUseType("");
//		handleLog(sysLogModel);
	}

	public void handleLog(SysLogModel sysLogModel) {
		try {
			sysLogService.insertSysLogInfo(sysLogModel);
		} catch (Exception e) {
			LogUtil.error(logger, LogUtil.BIZ, "CyLogAspect.handleLog", e.getMessage());
		}
		
	}

	/**
	 * 
	 * Description: 对象转map
	 * 
	 * @param obj
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2017-2-8
	 */
	public Map<String, Object> Obj2Map(Object obj) throws Exception {
		Map<String, Object> map = new HashMap<String, Object>();
		Field[] fields = obj.getClass().getDeclaredFields();
		for (Field field : fields) {
			field.setAccessible(true);
			map.put(field.getName(), field.get(obj));
		}
		return map;
	}

	/**
	 * 
	 * Description: 获取APP用户ID
	 * 
	 * @return
	 * <AUTHOR>
	 * @date 2017-2-8
	 */
	protected String getUserName() {
		return JWTHelper.getSysUser().getSysUserName();
	}

	/**
	 * 获取Ip地址
	 *
	 * @param request
	 * @return
	 */
	public String getIpAdrress(HttpServletRequest request)
	{
		String ip = request.getRemoteAddr();
		String XFor = request.getHeader("X-Forwarded-For");
		String xfor = request.getHeader("x-forwarded-for");
		logger.debug("getIpAdrress ip "+ip);
		logger.debug("getIpAdrress XFor "+XFor);
		logger.debug("getIpAdrress xfor "+xfor);
		//大写取不到，用小写取一次
		if(StringUtils.isEmpty(XFor)){
			XFor=xfor;
		}
		if (null!=XFor && XFor!="" && !"unKnown".equalsIgnoreCase(XFor))
		{
			// 多次反向代理后会有多个ip值，第一个ip才是真实ip
			int index = XFor.indexOf(",");
			if (index != -1)
			{
				return XFor.substring(0, index);
			}
			else
			{
				return XFor;
			}
		}
		XFor=ip;
		return XFor;
	}
}
