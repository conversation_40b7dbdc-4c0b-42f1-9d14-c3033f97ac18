<template>
    <div class="provinces">
        <div class="user-titler">{{$route.name}}</div>
        <div class="app-norbox">
            <!--查询条件-->
            <div>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" label-width="70px">
                    <el-form-item label="模版ID">
                        <el-input  v-model="searchForm.templateId" placeholder="" class="app-input"></el-input>
                    </el-form-item>
                    <el-form-item label="内容">
                        <el-input  v-model="searchForm.remindContent" placeholder="" class="app-input"></el-input>
                    </el-form-item>
                    <el-form-item label="审核人">
                        <el-input  v-model="auditUserAccount" placeholder="" class="app-input"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" label-width="70px">
                    <el-form-item label="审核意见">
                        <el-select v-model="searchForm.auditStatus" placeholder="请选择" class="app-input">
                            <el-option
                                    v-for="item in statusList"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search(1)">查询</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border
                      :header-cell-class-name="tableheaderClassNameZ"
                      class="app-tab02">
                <el-table-column prop="templateId" label="模版ID" />
                <!--<el-table-column prop="templateName" label="模版名称" />-->
                <el-table-column prop="callingRemindContent" label="主叫提醒内容" />
                <el-table-column prop="calledRemindContent" label="被叫提醒内容" />
                <!--<el-table-column prop="sysUserEmail" label="模版内容">-->
                    <!--<template slot-scope="scope">-->
                        <!--<el-button type="text" @click="details(scope.row)" size="small">查看详情</el-button>-->
                    <!--</template>-->
                <!--</el-table-column>-->
                <el-table-column prop="submitUserAccount" label="提交人"/>
                <!--<el-table-column prop="submitCount" label="提交次数"/>-->
                <el-table-column prop="submitTime" label="提交时间"/>
                <el-table-column prop="auditUserAccount" label="审核人"/>
                <el-table-column prop="auditTime" label="审核时间"/>
                <el-table-column prop="auditStatus" label="审核意见"/>
                <el-table-column prop="auditReason" label="原因"/>
            </el-table>

            <!--分页-->
            <div class="block app-pageganit" v-show="total">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
        <!--查看详情-->
        <el-dialog
                title="模版详情"
                :visible.sync="dialogVisible"
                width="40%" :close-on-click-modal="false">
            <ul class="detbox">
                <li>
                    <p>拨打号码提醒内容:</p>
                    <p class="fontcol">{{callingRemindContent}}</p>
                </li>
                <li>
                    <p>接听号码提醒内容:</p>
                    <p class="fontcol">{{calledRemindContent}}</p>
                </li>
                <!--<li>-->
                    <!--<p>描述:</p>-->
                    <!--<p class="fontcol">{{description}}</p>-->
                <!--</li>-->
            </ul>
        </el-dialog>
    </div>
</template>

<script>
    //    import editprovinces from './editprovinces'
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'provinces',
        data(){
            return{
                dialogVisible:false,
                //查询form对象定义
                searchForm: {
                    templateId:'', //模版id
                    remindContent:'',//内容,
                    auditStatus:'',//审核意见
                    pageSize:10,// 每页显示条数
                    pageNo:1, // 查询的页码
                },
                auditUserAccount:'',//审核人
                tableData:[],
                currentPage: 1,
                total:0,
                callingRemindContent:'',//主叫提醒
                calledRemindContent:'',//被叫提醒
                description:'',//描述
                statusList:[
                    {
                        id:1,
                        name:'已通过'
                    },
                    {
                        id:2,
                        name:'驳回'
                    },
                    {
                        id:3,
                        name:'已撤销'
                    },
                ]
            }
        },
        components: {
//            editprovinces
        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search()
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search()
            },
            //查看详情
            details(row){
                let vm = this;
                vm.dialogVisible = true;
                vm.callingRemindContent = row.callingRemindContent;
                vm.calledRemindContent = row.calledRemindContent;
                vm.description = row.description;
            },

            //查询请求
            search(pg) {
                let vm = this;
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                if(this.auditUserAccount){
                    this.searchForm.auditUserAccount = this.auditUserAccount;
                }else{
                    delete this.searchForm.auditUserAccount;
                }
                postHeader('queryNumTemplateRecordList', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.tableData = data.data.list;
                    if(vm.tableData&&vm.tableData.length){
                        vm.tableData.forEach(item=>{
                            switch(Number(item.auditStatus)){
                        case 0:
                            item.auditStatus = '未审核';
                            break;
                        case 1:
                            item.auditStatus = '已通过';
                            break;
                        case 2:
                            item.auditStatus = '驳回';
                            break;
                        case 3:
                            item.auditStatus = '已撤销';
                            break;
                        default:
                            break;
                        }
                    })
                    }
                    vm.total = data.data.total;
                }
            })
            },

            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .detbox{
        margin-left: 30px;
        margin-right: 20px;
    }
    .detbox li{
        border-bottom: 1px solid #cccccc;
    }
    .fontcol{
        color: #cccccc;
    }
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
