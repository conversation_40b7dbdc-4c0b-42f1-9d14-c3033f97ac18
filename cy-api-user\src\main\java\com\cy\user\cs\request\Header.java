package com.cy.user.cs.request;

/**
 * 
 * <AUTHOR> @date
 * @Description 请求参数头
 */
public class Header {

	/**
	 * 请求版本默认1.0
	 */
	private String version = "1.0";

	/**
	 * 接入账号,统一由能力平台配置，一个账号对应一种门户类型或者平台
	 * 
	 */

	private String sourceDeviceCode;
	/**
	 * 认证码，使用SHA256算法对（sourceDeviceCode + sharedSecret + timeStamp）加密。
	 * 其中sharedSecret由能力统一分配
	 */
	private String authenticatorSource;

	/**
	 * 时间戳，格式为yyyyMMddHHmmss，取本地时间
	 */
	private String timeStamp;

	/**
	 * 交易流水号
	 */

	private String transactionId;

	/**
	 * 参考附录产品线编码
	 */
	private String productLine;

	/**
	 * 参考附录接入类型编码
	 */
	private String portalType;

	/**
	 * 平台标识
	 */
	private String platform;

	/**
	 * 厂商标识
	 */
	private String companyId;

	/**
	 * imei
	 */
	private String imei;

	/**
	 * imsi
	 */
	private String imsi;

	/**
	 * 客户端版本号
	 */

	private String clientVer;

	/**
	 * 应用ID
	 */
	private String appid;

	/**
	 * 应用名称
	 */

	private String appName;

	/**
	 * 用户访问线索，依据产品侧格式填写
	 */
	private String accessInfo;

	/**
	 * 灰度使用
	 */
	private String extention;

	public Header() {
	}

	public Header(String sourceDeviceCode, String authenticatorSource, String transactionId, String productLine,
			String portalType, String platform, String companyId,String timeStamp) {
		super();
		this.sourceDeviceCode = sourceDeviceCode;
		this.authenticatorSource = authenticatorSource;
		this.transactionId = transactionId;
		this.productLine = productLine;
		this.portalType = portalType;
		this.platform = platform;
		this.companyId = companyId;
		this.timeStamp = timeStamp;
	}

	/**
	 * @return the version
	 */
	public String getVersion() {
		return version;
	}

	/**
	 * @param version
	 *            the version to set
	 */
	public void setVersion(String version) {
		this.version = version;
	}

	/**
	 * @return the sourceDeviceCode
	 */
	public String getSourceDeviceCode() {
		return sourceDeviceCode;
	}

	/**
	 * @param sourceDeviceCode
	 *            the sourceDeviceCode to set
	 */
	public void setSourceDeviceCode(String sourceDeviceCode) {
		this.sourceDeviceCode = sourceDeviceCode;
	}

	/**
	 * @return the authenticatorSource
	 */
	public String getAuthenticatorSource() {
		return authenticatorSource;
	}

	/**
	 * @param authenticatorSource
	 *            the authenticatorSource to set
	 */
	public void setAuthenticatorSource(String authenticatorSource) {
		this.authenticatorSource = authenticatorSource;
	}

	/**
	 * @return the timeStamp
	 */
	public String getTimeStamp() {
		return timeStamp;
	}

	/**
	 * @param timeStamp
	 *            the timeStamp to set
	 */
	public void setTimeStamp(String timeStamp) {
		this.timeStamp = timeStamp;
	}

	/**
	 * @return the transactionId
	 */
	public String getTransactionId() {
		return transactionId;
	}

	/**
	 * @param transactionId
	 *            the transactionId to set
	 */
	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	/**
	 * @return the productLine
	 */
	public String getProductLine() {
		return productLine;
	}

	/**
	 * @param productLine
	 *            the productLine to set
	 */
	public void setProductLine(String productLine) {
		this.productLine = productLine;
	}

	/**
	 * @return the portalType
	 */
	public String getPortalType() {
		return portalType;
	}

	/**
	 * @param portalType
	 *            the portalType to set
	 */
	public void setPortalType(String portalType) {
		this.portalType = portalType;
	}

	/**
	 * @return the platform
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * @param platform
	 *            the platform to set
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * @return the companyId
	 */
	public String getCompanyId() {
		return companyId;
	}

	/**
	 * @param companyId
	 *            the companyId to set
	 */
	public void setCompanyId(String companyId) {
		this.companyId = companyId;
	}

	/**
	 * @return the imei
	 */
	public String getImei() {
		return imei;
	}

	/**
	 * @param imei
	 *            the imei to set
	 */
	public void setImei(String imei) {
		this.imei = imei;
	}

	/**
	 * @return the imsi
	 */
	public String getImsi() {
		return imsi;
	}

	/**
	 * @param imsi
	 *            the imsi to set
	 */
	public void setImsi(String imsi) {
		this.imsi = imsi;
	}

	/**
	 * @return the clientVer
	 */
	public String getClientVer() {
		return clientVer;
	}

	/**
	 * @param clientVer
	 *            the clientVer to set
	 */
	public void setClientVer(String clientVer) {
		this.clientVer = clientVer;
	}

	/**
	 * @return the appid
	 */
	public String getAppid() {
		return appid;
	}

	/**
	 * @param appid
	 *            the appid to set
	 */
	public void setAppid(String appid) {
		this.appid = appid;
	}

	/**
	 * @return the appName
	 */
	public String getAppName() {
		return appName;
	}

	/**
	 * @param appName
	 *            the appName to set
	 */
	public void setAppName(String appName) {
		this.appName = appName;
	}

	/**
	 * @return the accessInfo
	 */
	public String getAccessInfo() {
		return accessInfo;
	}

	/**
	 * @param accessInfo
	 *            the accessInfo to set
	 */
	public void setAccessInfo(String accessInfo) {
		this.accessInfo = accessInfo;
	}

	/**
	 * @return the extention
	 */
	public String getExtention() {
		return extention;
	}

	/**
	 * @param extention
	 *            the extention to set
	 */
	public void setExtention(String extention) {
		this.extention = extention;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "Header [version=" + version + ", sourceDeviceCode=" + sourceDeviceCode + ", authenticatorSource="
				+ authenticatorSource + ", timeStamp=" + timeStamp + ", transactionId=" + transactionId
				+ ", productLine=" + productLine + ", portalType=" + portalType + ", platform=" + platform
				+ ", companyId=" + companyId + ", imei=" + imei + ", imsi=" + imsi + ", clientVer=" + clientVer
				+ ", appid=" + appid + ", appName=" + appName + ", accessInfo=" + accessInfo + ", extention="
				+ extention + "]";
	}

}
