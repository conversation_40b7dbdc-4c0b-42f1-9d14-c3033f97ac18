<template>
    <div>
        <div class="user-titler">{{$route.name}}</div>
        <div  class="classify">
            <el-table
                    :data="tableData"
                    border :header-cell-class-name="tableheaderClassNameZ">
                <el-table-column
                        prop="categoryName"
                        label="分类"
                        width="240">
                </el-table-column>
                <el-table-column
                        prop="numberCount"
                        label="号码个数"
                        width="240">
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import {postHeader} from '@/servers/httpServer.js';
export default {
    name: 'classify',
    data(){
        return{
            tableData: []
        }
    },
    created(){
        this.search();
    },
    methods:{
        search() {
            let vm = this;
            postHeader('queryCategory').then(res=>{
                let data = res.data;
                if(data.code==0){
                    vm.tableData = data.data.categoryList;
                }
            })
        },
        tableheaderClassNameZ({ row, rowIndex }) {
            return "table-head-thz";
        },
    },
    components: {}
}
</script>

<style scoped>
    .classify{
        width: 482px;
        margin: 20px;
    }
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>