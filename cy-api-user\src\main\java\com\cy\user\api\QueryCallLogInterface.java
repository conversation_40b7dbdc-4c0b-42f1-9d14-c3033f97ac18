package com.cy.user.api;


import com.cy.user.api.request.QueryCallLogRequest;
import com.cy.user.api.request.QueryCallLogRsponse;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.ResponseBody;

import jakarta.validation.Valid;

public interface QueryCallLogInterface {
    @PostMapping("/ai/user/queryCallLog")
    @ResponseBody
    QueryCallLogRsponse queryCallLog(@RequestHeader HttpHeaders headers, @Valid @RequestBody QueryCallLogRequest queryCallLogRequest);


}
