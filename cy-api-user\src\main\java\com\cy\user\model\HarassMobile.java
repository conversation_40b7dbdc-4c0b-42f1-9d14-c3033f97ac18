package com.cy.user.model;

public class HarassMobile {
	/**
	 * 电话号码
	 */
	private String mobile;
	/**
	 * 电话号码描述
	 */
	private String mobileDes;
	/**
	 * 标识类别类型
	 */
	private String typeId;
	/**
	 * 标识类别名称
	 */
	private String typeName;
	/**
	 * 标识类别缩略图地址
	 */
	private String typeThumbnailUrl;
	/**
	 * 标识类别缩略图地址
	 */
	private String typeBackgroundUrl;

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getMobileDes() {
		return mobileDes;
	}

	public void setMobileDes(String mobileDes) {
		this.mobileDes = mobileDes;
	}

	public String getTypeId() {
		return typeId;
	}

	public void setTypeId(String typeId) {
		this.typeId = typeId;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getTypeThumbnailUrl() {
		return typeThumbnailUrl;
	}

	public void setTypeThumbnailUrl(String typeThumbnailUrl) {
		this.typeThumbnailUrl = typeThumbnailUrl;
	}

	public String getTypeBackgroundUrl() {
		return typeBackgroundUrl;
	}

	public void setTypeBackgroundUrl(String typeBackgroundUrl) {
		this.typeBackgroundUrl = typeBackgroundUrl;
	}

}
