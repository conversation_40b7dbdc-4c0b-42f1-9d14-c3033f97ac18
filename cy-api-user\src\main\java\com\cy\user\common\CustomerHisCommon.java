package com.cy.user.common;

public class CustomerHisCommon {

	private String phone;
	private String csSetType;
	private String csChannelType;
	private String csRecType;
	private String csStatusType;
	private String csContent;
	private String startTime;
	private String endTime;
	private String ruleId;

	private String csId;// 正在使用的id
	private String csHisId;// 历史彩印id

	private int pageNum = 1;// 查询的页码

	private int pageSize = 20;// 每页显示条数

	private int startNum;// 起始查询行

	/**
	 * //1文本彩印，2彩印盒，3图片彩印，4视频彩印
	 */
	private String setCsType;

	private String pkgName;

	private String pkgStatus;
	private String csStatus;

	private String pkgId;

	public String getCsStatus() {
		return csStatus;
	}

	public void setCsStatus(String csStatus) {
		this.csStatus = csStatus;
	}

	public String getPkgId() {
		return pkgId;
	}

	public void setPkgId(String pkgId) {
		this.pkgId = pkgId;
	}

	public String getPkgStatus() {
		return pkgStatus;
	}

	public void setPkgStatus(String pkgStatus) {
		this.pkgStatus = pkgStatus;
	}

	public String getPkgName() {
		return pkgName;
	}

	public void setPkgName(String pkgName) {
		this.pkgName = pkgName;
	}

	public void setStartPage() {
		this.startNum = (pageNum - 1) * pageSize;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getCsSetType() {
		return csSetType;
	}

	public void setCsSetType(String csSetType) {
		this.csSetType = csSetType;
	}

	public String getCsChannelType() {
		return csChannelType;
	}

	public void setCsChannelType(String csChannelType) {
		this.csChannelType = csChannelType;
	}

	public String getCsRecType() {
		return csRecType;
	}

	public void setCsRecType(String csRecType) {
		this.csRecType = csRecType;
	}

	public String getCsStatusType() {
		return csStatusType;
	}

	public void setCsStatusType(String csStatusType) {
		this.csStatusType = csStatusType;
	}

	public String getCsContent() {
		return csContent;
	}

	public void setCsContent(String csContent) {
		this.csContent = csContent;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getCsId() {
		return csId;
	}

	public void setCsId(String csId) {
		this.csId = csId;
	}

	public String getCsHisId() {
		return csHisId;
	}

	public void setCsHisId(String csHisId) {
		this.csHisId = csHisId;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getStartNum() {
		return startNum;
	}

	public void setStartNum(int startNum) {
		this.startNum = startNum;
	}

	public String getSetCsType() {
		return setCsType;
	}

	public void setSetCsType(String setCsType) {
		this.setCsType = setCsType;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

}
