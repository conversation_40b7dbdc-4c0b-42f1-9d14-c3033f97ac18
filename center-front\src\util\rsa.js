import JSEncrypt from 'jsencrypt'

/**
 * RSA加密
 */
export function encrypt(text) {

  // 新建JSEncrypt对象
  const encryptor = new JSEncrypt()
  // 设置公钥
  encryptor.setPublicKey(
    'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCi92/AY73X9Sz+jTujmyOA3cyLAcuf0QKkArtc\n' +
    'v8cg6PBuK3SA20a3u5uodla+gAL7rrpUuWrjIje5sfsef+CkMWjUIPXycfGrH54BWVmxP6q2cnXQ\n' +
    '29+9q6+vwEY1smchpiCbRqJfrL/hDZx8PwCpwWuN7vC6fJALhnYcSStCgwIDAQAB'
  )
  // 加密
  const rsaPassWord = encryptor.encrypt(text)

  return rsaPassWord
}
