<template scope="scope">
  <div class="cardDetail">
    <div class="user-titler">彩印专题审核明细</div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-row>
          <!-- <el-col :span="8">
            <el-form-item label="专题标签">
              <el-select
                v-model="searchReq.subjectLabelId"
                clearable
                placeholder="请选择"
                size="small"
              >
                <el-option
                  v-for="item in labelList"
                  :key="item.id"
                  :label="item.labelName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="专题ID">
              <el-input v-model="searchReq.id" class="app-input" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="专题内容">
              <el-input v-model="searchReq.subjectContent" class="app-input" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="cancelVisible=true;cancelType=2;"
          >批量撤销</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="专题ID" width="200"></el-table-column>
        <el-table-column prop="subjectContent" label="专题内容" width="240"></el-table-column>
        <el-table-column prop="auditOpinion" label="审核意见" width="200"></el-table-column>
        <el-table-column prop="auditCause" label="驳回原因" width="200"></el-table-column>
        <el-table-column prop="commitBy" label="提交人" width="100"></el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="200"></el-table-column>
        <el-table-column prop="auditBy" label="审核人" width="100"></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="200"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              :disabled="scope.row.auditStatus == 3"
              @click="cancelVisible=true;cancelType = 1;rowData=scope.row"
            >撤销</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
      <el-dialog title="撤销原因" width="30%" :visible.sync="cancelVisible">
        <el-form>
          <el-form-item>
            <el-input
              type="textarea"
              :rows="5"
              placeholder="200字"
              maxlength="200"
              v-model="cancelReq.auditCause"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelVisible = false;cancelReq.auditCause = ''">取 消</el-button>
          <el-button type="primary" @click="cancelVisible = false; cancelCheck()">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from "moment";

export default {
  data() {
    return {
      pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
      },
      rowData: "",
      cancelVisible: false,
      tableLoading: false,
      //查询条件
      searchReq: {
        subjectLabelId: "",
        id: "",
        subjectContent: "",
        timearr: [], //提交时间
        beginTime: "",
        endTime: "",
        pageIndex: 1,
        pageSize: 10,
        auditStatusArray: [1, 2, 3]
      },
      cancelReq: {
        contentIdArray: [],
        auditStatus: 3,
        auditCause: "",
        auditBy:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        auditOpinion: ""
      },
      pageTotal: 0, //总条数
      typeoff: "info",
      clickoff: true,
      //数据表
      tableData: [
        {
          pageIndex: null,
          pageSize: null,
          id: 5,
          subjectContent: "新增内容7.23",
          subjectLabelId: 2,
          status: 0,
          auditTime: null,
          auditStatus: 0,
          auditCause: null,
          userNum: null,
          commitNum: null,
          isDelete: 0,
          createBy: 0,
          createTime: "2019-07-23 12:00:49.0",
          updateBy: 0,
          updateTime: "2019-07-23 12:00:49.0",
          commitBy: null,
          auditBy: null,
          auditOpinion: null
        }
      ],
      labelList: []
    };
  },
  watch: {
    "cancelReq.contentIdArray"() {
      if (this.cancelReq.contentIdArray.length) {
        this.clickoff = false;
        this.typeoff = "primary";
      } else {
        this.clickoff = true;
        this.typeoff = "info";
      }
    }
  },
  created() {
    this.initLabelList();
    this.search();
  },
  methods: {
    initLabelList() {
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectLabelList`,
          JSON.stringify({
          labelName: ""
          }),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(function(res) {
          this.labelList = res.data.data;
        });
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr) {
        this.searchReq.beginTime = this.searchReq.timearr[0]
          ? moment(new Date(this.searchReq.timearr[0])).format("YYYY-MM-DD")
          : "";
        this.searchReq.endTime = this.searchReq.timearr[1]
          ? moment(new Date(this.searchReq.timearr[1])).format("YYYY-MM-DD")
          : "";
      } else {
        this.searchReq.beginTime = "";
        this.searchReq.endTime = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/cySubject/getSubjectAuditList`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data;
            this.pageTotal = res.totalCount;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    cancelCheck() {
      if (!this.cancelReq.auditCause) {
        this.$message.error("请填写撤销原因");
        this.cancelVisible = true;
        return false;
      }
      if (this.cancelType == 1) {
        this.cancel();
      } else if (this.cancelType == 2) {
        this.cancelList();
      }
    },
    cancel() {
      this.cancelReq.contentIdArray = [this.rowData.id];
      this.$http
        .post(`${this.proxyUrl}/cySubject/doSubjectAudit`, JSON.stringify(this.cancelReq))
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("撤销成功");
            this.search();
          } else {
            this.$message("撤销失败");
          }
          this.cancelReq.contentIdArray = [];
          this.cancelReq.auditCause = "";
        });
    },
    cancelList() {
      this.$http
        .post(`${this.proxyUrl}/cySubject/doSubjectAudit`, JSON.stringify(this.cancelReq))
        .then(function(res) {
          if (res.data.code == "0") {
            this.$message.success("撤销成功");
            // let reslist = res.data.data;
            // let count = 0;
            // reslist.forEach(list => {
            //   if (list.success) {
            //     count++;
            //   }
            // });
            // this.$message.success(
            //   `撤销成功记录${count}条，失败记录${reslist.length -
            //     count}条，详细情况请查询企业明细`
            // );
            this.search();
          } else {
            this.$message("撤销失败");
          }
          this.cancelReq.contentIdArray = [];
          this.cancelReq.auditCause = "";
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageIndex = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.pageIndex = val;
      this.search();
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    //多选框
    handleSelectionChange(val) {
      this.cancelReq.contentIdArray = [];
      for (var i = 0; i < val.length; i++) {
        this.cancelReq.contentIdArray.push(val[i].id);
      }
    }
  }
};
</script>
<style scoped>
.cardDetail >>> .el-tabs__header {
  margin-left: 24px;
}
.inputWidth {
  width: 160px !important;
}
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}

.zzItemWrap {
  margin: 8px 0;
}

.zzItemLeft {
  float: left;
  width: 70px;
}

.zzItemRight >>> .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.carouselIndex {
  margin-left: 70px;
  text-align: center;
}

.carouselImg {
  display: inline-block;
  height: auto;
  width: 100%;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
