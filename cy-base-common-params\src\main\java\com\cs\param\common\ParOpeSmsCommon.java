
package com.cs.param.common;

public class ParOpeSmsCommon {
	private Integer id;// ID
	private String status;// 状态：0：关闭，1开启
	private String acceptNo;// 接收号码：0：所有用户 ，1：彩印用户，2：指定号码
	private String acceptPhone;// 接收电话号
	private String content;// 发送内容
	private String sendNo;// 发送源号码:1065,8086
	private String triggerSceneId;// 触发场景Id

	private String isDelete;// 标识是否为删除数据：0否，1是,默认为0
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAcceptNo() {
		return acceptNo;
	}

	public void setAcceptNo(String acceptNo) {
		this.acceptNo = acceptNo;
	}

	public String getAcceptPhone() {
		return acceptPhone;
	}

	public void setAcceptPhone(String acceptPhone) {
		this.acceptPhone = acceptPhone;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getSendNo() {
		return sendNo;
	}

	public void setSendNo(String sendNo) {
		this.sendNo = sendNo;
	}

	public String getTriggerSceneId() {
		return triggerSceneId;
	}

	public void setTriggerSceneId(String triggerSceneId) {
		this.triggerSceneId = triggerSceneId;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

}
