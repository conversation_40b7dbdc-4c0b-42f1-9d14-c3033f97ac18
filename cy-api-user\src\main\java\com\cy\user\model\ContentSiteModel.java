package com.cy.user.model;

/**
 * @Description 用户内容设置model
 */
public class ContentSiteModel {

    /**
     * 编号
     */
    private Integer id;

    /**
     * 用户手机号
     */

    private String iphone;

    /**
     * 彩印内容
     */
    private String content;

    /**
     * 彩印编号
     */

    private String number;

    /**
     * 新门户1 客户端16   短信 0   中央平台 22
     */
    private Integer channel;

    /**
     * 修改时间YYYYMMDDHHmmss
     */
    private String modifyTime;

    /**
     * 接收方号码
     */
    private String receiveNumber;

    /**
     * 接收类型 1：常用联系人 3：所有人
     */
    private int receiveType;

    /**
     * 审核状态
     * <p>
     * //1：待审批 2：审批不通过 3：审核通过 4：失效（提交新纪录，或彩印被删除）
     */
    private String verifyState;

    /**
     * 审核时间
     */

    private String verifyTime;

    /**
     * 彩印盒编号
     */
    private String boxId;

    private String batchNo;

    private String ruleId;

    private String operateUser;

    private String addOrDelete;

    private String csCreateTime;

    private String remark;

    private String type;//0默认 1批量相同内容 2 批量不通内容

    private String reciver;//专属号码

    private int svRuleStatus;//1：不生成规则；0生成规则

    private boolean isOne;// false批量下架 true单个下架

    private  String serviceId;//专属号码
    private String weekFlag;

    private String ruleStartTime;

    private String ruleEndTime;

    private String contactGroupId;

    public String getWeekFlag() {
        return weekFlag;
    }

    public void setWeekFlag(String weekFlag) {
        this.weekFlag = weekFlag;
    }

    public String getRuleStartTime() {
        return ruleStartTime;
    }

    public void setRuleStartTime(String ruleStartTime) {
        this.ruleStartTime = ruleStartTime;
    }

    public String getRuleEndTime() {
        return ruleEndTime;
    }

    public void setRuleEndTime(String ruleEndTime) {
        this.ruleEndTime = ruleEndTime;
    }

    public String getContactGroupId() {
        return contactGroupId;
    }

    public void setContactGroupId(String contactGroupId) {
        this.contactGroupId = contactGroupId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public int getSvRuleStatus() {
        return svRuleStatus;
    }

    public void setSvRuleStatus(int svRuleStatus) {
        this.svRuleStatus = svRuleStatus;
    }

    public String getReciver() {
        return reciver;
    }

    public void setReciver(String reciver) {
        this.reciver = reciver;
    }

    /**
     * @return the boxId
     */
    public String getBoxId() {
        return boxId;
    }

    /**
     * @param boxId the boxId to set
     */
    public void setBoxId(String boxId) {
        this.boxId = boxId;
    }

    /**
     * @return the id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return the content
     */
    public String getContent() {
        return content;
    }

    /**
     * @param content the content to set
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * @return the number
     */
    public String getNumber() {
        return number;
    }

    /**
     * @param number the number to set
     */
    public void setNumber(String number) {
        this.number = number;
    }

    /**
     * @return the channel
     */
    public Integer getChannel() {
        return channel;
    }

    /**
     * @param channel the channel to set
     */
    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    /**
     * @return the modifyTime
     */
    public String getModifyTime() {
        return modifyTime;
    }

    /**
     * @param modifyTime the modifyTime to set
     */
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * @return the receiveNumber
     */
    public String getReceiveNumber() {
        return receiveNumber;
    }

    /**
     * @param receiveNumber the receiveNumber to set
     */
    public void setReceiveNumber(String receiveNumber) {
        this.receiveNumber = receiveNumber;
    }

    /**
     * @return the verifyState
     */
    public String getVerifyState() {
        return verifyState;
    }

    /**
     * @param verifyState the verifyState to set
     */
    public void setVerifyState(String verifyState) {
        this.verifyState = verifyState;
    }

    /**
     * @return the verifyTime
     */
    public String getVerifyTime() {
        return verifyTime;
    }

    /**
     * @param verifyTime the verifyTime to set
     */
    public void setVerifyTime(String verifyTime) {
        this.verifyTime = verifyTime;
    }

    /**
     * @return the receiveType
     */
    public int getReceiveType() {
        return receiveType;
    }

    /**
     * @param receiveType the receiveType to set
     */
    public void setReceiveType(int receiveType) {
        this.receiveType = receiveType;
    }

    /**
     * @return the iphone
     */
    public String getIphone() {
        return iphone;
    }

    /**
     * @param iphone the iphone to set
     */
    public void setIphone(String iphone) {
        this.iphone = iphone;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    /**
     * @return the addOrDelete
     */
    public String getAddOrDelete() {
        return addOrDelete;
    }

    /**
     * @param addOrDelete the addOrDelete to set
     */
    public void setAddOrDelete(String addOrDelete) {
        this.addOrDelete = addOrDelete;
    }

    @Override
    public String toString() {
        return "ContentSiteModel{" +
                "id=" + id +
                ", iphone='" + iphone + '\'' +
                ", content='" + content + '\'' +
                ", number='" + number + '\'' +
                ", channel=" + channel +
                ", modifyTime='" + modifyTime + '\'' +
                ", receiveNumber='" + receiveNumber + '\'' +
                ", receiveType=" + receiveType +
                ", verifyState='" + verifyState + '\'' +
                ", verifyTime='" + verifyTime + '\'' +
                ", boxId='" + boxId + '\'' +
                ", batchNo='" + batchNo + '\'' +
                ", ruleId='" + ruleId + '\'' +
                ", operateUser='" + operateUser + '\'' +
                ", addOrDelete='" + addOrDelete + '\'' +
                ", csCreateTime='" + csCreateTime + '\'' +
                ", remark='" + remark + '\'' +
                ", type='" + type + '\'' +
                ", reciver='" + reciver + '\'' +
                ", svRuleStatus=" + svRuleStatus +
                ", isOne=" + isOne +
                ", serviceId='" + serviceId + '\'' +
                ", weekFlag='" + weekFlag + '\'' +
                ", ruleStartTime='" + ruleStartTime + '\'' +
                ", ruleEndTime='" + ruleEndTime + '\'' +
                ", contactGroupId='" + contactGroupId + '\'' +
                '}';
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getOperateUser() {
        return operateUser;
    }

    public void setOperateUser(String operateUser) {
        this.operateUser = operateUser;
    }

    public String getCsCreateTime() {
        return csCreateTime;
    }

    public void setCsCreateTime(String csCreateTime) {
        this.csCreateTime = csCreateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public boolean isOne() {
        return isOne;
    }

    public void setOne(boolean one) {
        isOne = one;
    }
}
