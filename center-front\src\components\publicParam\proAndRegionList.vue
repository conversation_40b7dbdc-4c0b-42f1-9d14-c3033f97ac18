<template>
    <div>
        <h1 class="user-title">省市管理</h1>
        <div class="user-line"></div>
        
        <div class="app-search">
            <el-button type="primary" @click="exportExcel" size="small">导出</el-button>
        </div>


        
        <el-popover
                ref="countryPop"
                placement="bottom"
                title="添加国家吗"
                width="200"
                trigger="click" v-model="countryPopShow">
            <el-form :model="addCountry" :inline="true" class="demo-form-inline">
                <el-form-item label="国家码">
                    <el-input v-model="addCountry.countryCode" placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="国家名称">
                    <el-input v-model="addCountry.countryName" placeholder="" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right;margin-top: 10px">
                <el-button size="small" type="info" @click="countryPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="addCountryShow(addCountry)">确定</el-button>
            </div>
        </el-popover>

        <div>
            <el-dialog title="添加分区" :visible.sync="addPartionVisible" :before-close="handleClose" width="56%">
                <el-form :model="addPartition" :rules="rules" ref="addPartitionForm"  class="demo-ruleForm" label-width="15%"  style="width: 100%">
                    <el-form-item label="国家">
                        {{addPartition.countryName}}
                    </el-form-item>
                    <el-form-item label="分区号:" prop="partitionCode">
                        <el-input v-model="addPartition.partitionCode"  placeholder="" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="分区名称"  prop="partitionName">
                        <el-input v-model="addPartition.partitionName"  placeholder="" size="small" style="margin-top: 15px"></el-input>
                    </el-form-item>
                    <el-form-item label="运营省份"  prop="provinceCodes" size="small"  style="margin-top: 10px">
                        <el-transfer
                                filterable
                                :titles="['未选省份','已选省份']"
                                filter-placeholder="请输入城市拼音"
                                v-model="addPartition.provinceCodesArr"
                                :data="provinceData">
                        </el-transfer>
                    </el-form-item>

                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addPartionVisible = false" size="small">取 消</el-button>
                    <el-button type="primary"  @click="addPartitionSubmit(addPartition)" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>


        <el-popover
                ref="provincePop"
                placement="bottom"
                title="添加省号"
                width="200"
                trigger="click" v-model="provincePopShow">
            <el-form :model="addProvince" :inline="true" class="demo-form-inline">
                <el-form-item label="省号">
                    <el-input v-model="addProvince.provinceCode" placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="省份名称">
                    <el-input v-model="addProvince.provinceName" placeholder="" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="provincePopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="addProvinceShow(addProvince)">确定</el-button>
            </div>

        </el-popover>
        <el-popover
                ref="regionPop"
                placement="bottom"
                title="添加地区号"
                width="200"
                trigger="click" v-model="regionPopShow">
            <el-form :model="addRegion" :inline="true" class="demo-form-inline">
                <el-form-item label="地区号">
                    <el-input v-model="addRegion.regionCode" placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="地区名称">
                    <el-input v-model="addRegion.regionName" placeholder="" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="regionPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="addRegionShow(addRegion)">确定</el-button>
            </div>

        </el-popover>

        <el-popover
                ref="editCountryPop"
                placement="bottom"
                title="修改国家码"
                width="200"
                trigger="click" v-model="editCountryPopShow"
                v-bind:style="{left:editCountryPopShowX+'px',top:editCountryPopShowY+'px'}" style="position:absolute">
            <el-form :model="editCountry" :inline="true" class="demo-form-inline">
                <el-form-item label="国家名称 : ">
                    <el-input v-model="editCountry.countryName" size="small" placeholder="">{{editCountry.countryName}}</el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="editCountryPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="editCountrySubmit(editCountry)">确定</el-button>
            </div>
        </el-popover>

        <div>
            <el-dialog title="修改分区" :visible.sync="editPartionVisible" :before-close="handleClose">
                <el-form :model="editPartition" :rules="rules" ref="editPartitionForm"  class="demo-ruleForm" label-width="25%"  style="width: 100%">
                    <el-form-item label="国家">
                        {{editPartition.countryName}}
                    </el-form-item>
                    <el-form-item label="分区号:" size="small" prop="partitionCode">
                        {{editPartition.partitionCode}}
                    </el-form-item>
                    <el-form-item label="分区名称"   size="small" prop="partitionName">
                        <el-input v-model="editPartition.partitionName"  placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="运营省份"  size="small" prop="provinceCodes">
                        <el-transfer
                                filterable
                                :right-default-checked="editPartition.provinceCodesArr"
                                :titles="['未选省份','已选省份']"
                                filter-placeholder="请输入省份"
                                v-model="editPartition.provinceCodesArr"
                                :data="provinceData">
                        </el-transfer>
                    </el-form-item>

                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="editPartionVisible = false" size="small">取 消</el-button>
                    <el-button type="primary"  @click="editPartitionSubmit(editPartition)" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>


        <el-popover
                ref="editProvincePop"
                placement="bottom"
                title="修改省号"
                width="200"
                trigger="click" v-model="editProvincePopShow" v-bind:style="{left:editProvincePopShowX+'px',top:editProvincePopShowY+'px'}" style="position:absolute">
            <el-form :model="editProvince" :inline="true" class="demo-form-inline">
                <el-form-item label="省份名称 : ">
                    <el-input v-model="editProvince.provinceName" placeholder="" size="small"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="editProvincePopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="editProvinceSubmit(editProvince)">确定</el-button>
            </div>
        </el-popover>

        <el-popover
                ref="editRegionPop"
                placement="bottom"
                title="修改地区"
                width="200"
                trigger="click" v-model="editRegionPopShow" v-bind:style="{left:editRegionPopShowX+'px',top:editRegionPopShowY+'px'}" style="position:absolute">
            <el-form :model="editRegion" :inline="true" class="demo-form-inline">
                <el-form-item label="地区名称 : ">
                    <el-input v-model="editRegion.regionName"  size="small" placeholder="">{{editRegion.regionName}}</el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="editRegionPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="editRegionSubmit(editRegion)">确定</el-button>
            </div>

        </el-popover>

        <el-popover
                ref="delCountryPop"
                placement="bottom"
                title="删除国家码"
                width="200"
                trigger="click" v-model="delCountryPopShow" v-bind:style="{left:delCountryPopShowX+'px',top:delCountryPopShowY+'px'}" style="position:absolute">
            <p>确定删除吗？</p>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="delCountryPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="delCountrySubmit">确定</el-button>
            </div>

        </el-popover>

        <el-popover
                ref="delPartitionPop"
                placement="bottom"
                title="删除分区"
                width="200"
                trigger="click" v-model="delPartitionPopShow" v-bind:style="{left:delPartitionPopShowX+'px',top:delPartitionPopShowY+'px'}" style="position:absolute">
            <p>确定删除吗？</p>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="delPartitionPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="delPartitionSubmit">确定</el-button>
            </div>

        </el-popover>

        <el-popover
                ref="delProvincePop"
                placement="bottom"
                title="删除省号"
                width="200"
                trigger="click" v-model="delProvincePopShow" v-bind:style="{left:delProvincePopShowX+'px',top:delProvincePopShowY+'px'}" style="position:absolute">
            <p>确定删除此项？</p>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="delProvincePopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="delProvinceSubmit">确定</el-button>
            </div>
        </el-popover>

        <el-popover
                ref="delRegionPop"
                placement="bottom"
                title="删除地区"
                width="200"
                trigger="click" v-model="delRegionPopShow" v-bind:style="{left:delRegionPopShowX+'px',top:delRegionPopShowY+'px'}" style="position:absolute">
            <p>确定删除此项？</p>
            <div style="text-align: right; margin-top: 10px">
                <el-button size="mini" type="info" @click="delRegionPopShow = false">取消</el-button>
                <el-button type="primary" size="mini" @click="delRegionSubmit">确定</el-button>
            </div>

        </el-popover>


        <div style="margin-left: 3%;">
            <el-row :gutter="0">
                <el-col :span="5">
                    <el-button type="small" class="el-icon-plus" v-popover:countryPop>添加国家码</el-button>
                </el-col>
                <el-col :span="5">
                    <el-button type="small" class="el-icon-plus" @click="addPartitionShow()">添加分区</el-button>
                </el-col>
                <el-col :span="5">
                    <el-button type="small" class="el-icon-plus" v-popover:provincePop>添加省号</el-button>
                </el-col>
                <el-col :span="5">
                    <el-button type="small" class="el-icon-plus" v-popover:regionPop>添加地区号</el-button>
                </el-col>
            </el-row>
            <el-row :gutter="0">
                <el-col :span="5">
                    <def-select  v-model="countryValue" iconShow filterable placeholder="请选择"
                                 @on-edit-method="editCountryShow" @on-close-method="delCountryShow" size="150" @change="queryPartitionList()">
                        <def-option
                                v-for="item in countryList"
                                :key="item.id"
                                :label="item.countryName"
                                :value="item.countryCode" >{{item.countryName}}    {{item.countryCode}}
                        </def-option>

                    </def-select>
                </el-col>

                <el-col :span="5">
                    <def-select v-model="partitionValue" iconShow filterable clearable placeholder="请选择"
                                @on-edit-method="editPartitionShow" @on-close-method="delPartitionShow" size="150" @change="queryProvinceList">
                        <def-option
                                v-for="item in partitionList"
                                :key="item.partitionCode"
                                :label="item.partitionName"
                                :value="item.partitionCode" >{{item.partitionName}}    {{item.partitionCode}}
                        </def-option>
                    </def-select>
                </el-col>

                <el-col :span="5">
                    <def-select v-model="provinceValue" iconShow filterable clearable placeholder="请选择"
                                @on-edit-method="editProvinceShow" @on-close-method="delProvinceShow" size="150" @change="queryRegionList">
                        <def-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode" >{{item.provinceName}}    {{item.provinceCode}}
                        </def-option>
                    </def-select>
                </el-col>

                <el-col :span="5">
                    <def-select v-model="regionValue" iconShow filterable clearable placeholder="请选择"
                                @on-edit-method="editRegionShow" @on-close-method="delRegionShow" size="150" >
                        <def-option
                                v-for="item in regionList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode" >{{item.regionName}}    {{item.regionCode}}
                        </def-option>
                    </def-select>
                </el-col>
            </el-row>

        </div>


        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false" size="small">确 定</el-button>
  </span>
        </el-dialog>


    </div>

</template>
<script>
    import {postDownload} from './../../servers/httpServer.js';
    import {dowandFile} from './../../util/core.js';
    //    import Vue from 'vue'
    import DefSelect from '../select/src/select.vue'
    import DefOption from '../select/src/option.vue'
    //    Vue.use(DefOption)
    //    Vue.use(DefSelect)
    export default {
        data() {
            return {
                propMsg:'',
                propVisible:false,
                addPartionVisible:false,
                editPartionVisible:false,
                provinceData:[],
                queryCountry:{
                    countryCode: '',
                    countryName: ''
                },
                queryPartition:{
                    countryCode:''
                },
                queryProvince:{
                    countryCode: '',
                    partitionCode: ''
                },
                queryRegion:{
                    countryCode:'',
                    partitionCode: '',
                    provinceCode: ''
                },
                addCountry:{
                    isAdd:1,
                    countryCode: '',
                    countryName: ''
                },
                addPartition:{
                    isAdd:1,
                    countryCode: '',
                    countryName:'',
                    partitionCode: '',
                    partitionName: '',
                    provinceCodes:'',
                    provinceCodesArr:[],
                },

                addProvince:{
                    isAdd:1,
                    countryCode: '',
                    partitionCode: '',
                    provinceCode: '',
                    provinceName: ''
                },

                addRegion:{
                    isAdd:1,
                    countryCode: '',
                    partitionCode: '',
                    provinceCode: '',
                    regionCode: '',
                    regionName: ''
                },
                editCountry:{
                    isAdd:0,
                    countryCode: '',
                    countryName: ''
                },
                editPartition:{
                    isAdd:0,
                    countryCode: '',
                    partitionCode: '',
                    partitionName: '',
                    provinceCodes:'',
                    provinceCodesArr:[]
                },
                editProvince:{
                    isAdd:0,
                    provinceCode: '',
                    provinceName: ''
                },
                editRegion:{
                    isAdd:0,
                    regionCode: '',
                    regionName: ''
                },

                delCountry:{
                    countryCode: '',
                },
                delPartition:{
                    partitionCode: '',
                },
                delProvince:{
                    provinceCode: '',
                },
                delRegion:{
                    regionCode: '',
                },

                countryPopShow:false,
                partitionPopShow:false,
                provincePopShow:false,
                regionPopShow:false,
                editCountryPopShow:false,
                editPartitionPopShow:false,
                editProvincePopShow:false,
                editRegionPopShow:false,
                delCountryPopShow:false,
                delPartitionPopShow:false,
                delProvincePopShow:false,
                delRegionPopShow:false,
                countryList: [],
                partitionList: [],
                provinceList: [],
                regionList: [],
                countryValue:'',
                partitionValue:'',
                provinceValue:'',
                regionValue:'',
                editCountryPopShowX:'',
                editCountryPopShowY:'',
                editPartitionPopShowX:'',
                editPartitionPopShowY:'',
                editProvincePopShowX:'',
                editProvincePopShowY:'',
                editRegionPopShowX:'',
                editRegionPopShowY:'',



                delCountryPopShowX:'',
                delCountryPopShowY:'',
                delPartitionPopShowX:'',
                delPartitionPopShowY:'',
                delProvincePopShowX:'',
                delProvincePopShowY:'',
                delRegionPopShowX:'',
                delRegionPopShowY:'',

                rules: {
                    partitionCode: [
                        { required: true, message: '请输入分区号', trigger: 'blur' },
                    ],
                    partitionName: [
                        { required: true, message: '请输入分区名称', trigger: 'blur' }
                    ]

                },

            }
        },
        mounted(){
//            this.slideData(this.proxyUrl);
            this.queryCountryList();

//            //获取分区list
//            this.queryPartitionList();
//
//            //获取省份list
//            this.queryProvinceList();
//
//            //获取地区list
//            this.queryRegionList();
        },
        methods: {
            exportExcel(){
                //导出
                this.queryRegion.countryCode = this.countryValue;
                this.queryRegion.partitionCode = this.partitionValue;
                this.queryRegion.provinceCode = this.provinceValue;
                postDownload('/param/regionMgt/exportExecl',this.queryRegion).then(res=>{
                    dowandFile(res.data,'省市数据.xlsx');
                })
            },

            generateData(){
                this.queryProvince.countryCode = this.countryValue;
                this.queryProvince.partitionCode = this.partitionCode;
                this.provinceData = [];
                console.log(this.queryProvince);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getProvince`,this.queryProvince,{emulateJSON:true})
                        .then(function(res){
                            const data=res.data;
                            data.forEach((pro, index) => {
                                this.provinceData.push({
                                label: pro.provinceName,
                                key: pro.provinceCode,
                            });
                        });
                        });
            },
            queryCountryList(){
                this.queryCountry.countryCode = this.countryValue;
                console.log(this.queryCountry);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getCountry`,this.queryCountry,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            this.countryList=res.data;
//                            this.queryPartitionList();

                        });
            },
            queryPartitionList(){
                this.queryPartition.countryCode = this.countryValue;
                this.partitionCode='';
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getPartition`,this.queryPartition,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            this.partitionList=res.data;
                            this.queryProvinceList();
                        });
            },
            queryProvinceList(){
                this.queryProvince.countryCode = this.countryValue;
                this.queryProvince.partitionCode = this.partitionValue;
                console.log(this.queryProvince);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getProvince`,this.queryProvince,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            this.provinceList=res.data;
                            this.queryRegionList();
                        })
            },
            queryRegionList(){
                this.queryRegion.countryCode = this.countryValue;
                this.queryRegion.partitionCode = this.partitionValue;
                this.queryRegion.provinceCode = this.provinceValue;
                console.log(this.queryRegion);

                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            this.regionList=res.data;
                        })
            },
            addCountryShow(addCountry){
                this.addCountry = addCountry;
                if (this.addCountry.countryCode.trim() == "" || this.addCountry.countryName.trim() == "") {
                    this.$message({
                        message: '请输入正确的国家码或国家名称！',
                        type: 'warning'
                    });
                } else {
                    this.$http.post(`${this.proxyUrl}/param/regionMgt/editorCountry`, this.addCountry, {emulateJSON: true})
                        .then(function (res) {
                            if (res.data.resStatus == 0) {
                                this.propMsg = '新增成功！';
                                this.propVisible = true;
                                this.countryPopShow = false;
                                this.queryCountryList();
                            } else {
                                this.propMsg = '新增失败!' + res.data.resText;
                                this.propVisible = true;
                            }
                            this.addCountry.countryCode="";
                            this.addCountry.countryName="";
                        })
                }
            },
            addPartitionShow(){
                let obj = {};
                if(this.countryValue == undefined || this.countryValue == ''){
                    this.propMsg = '请先选择国家!';
                    this.propVisible=true;
                    return;
                }
                obj = this.countryList.find((item)=>{
                            return item.countryCode == this.countryValue;
            });
                this.addPartition.countryName=obj.countryName;
                this.generateData();
                this.addPartionVisible=true;
            },

            addPartitionSubmit(addPartition){
                this.addPartition = addPartition;
                this.addPartition.countryCode=this.countryValue;
                this.$refs['addPartitionForm'].validate((valid) => {
                    if(valid){
                        this.addPartition.provinceCodes= this.addPartition.provinceCodesArr.toString();
                        this.$http.post(`${this.proxyUrl}/param/regionMgt/editorPartition`,this.addPartition,{emulateJSON:true})
                                .then(function(res){
                                    console.log(res);
                                    if(res.data.resStatus == 0){
                                        this.propMsg = '新增成功！';
                                        this.propVisible=true;
                                        this.addPartionVisible=false;
                                        this.queryPartitionList();
                                    }else{
                                        this.propMsg = '新增失败!'+ res.data.resText;;
                                        this.propVisible=true;
                                    }
                                });
                    }

                });
            },

            addProvinceShow(addProvince){
                console.log(addProvince);
                this.addProvince = addProvince;
                if(this.countryValue == undefined || this.countryValue == ''){
                    this.propMsg = '请先选择国家或者分区!';
                    this.propVisible=true;
                    return;
                }
                this.addProvince.countryCode=this.countryValue;
                this.addProvince.partitionCode=this.partitionValue;
                if (this.addProvince.provinceCode.trim() == "" || this.addProvince.provinceCode.trim() == "") {
                    this.$message({
                        message: '请输入正确的省号或省名称！',
                        type: 'warning'
                    });
                } else {
                    this.$http.post(`${this.proxyUrl}/param/regionMgt/editorProvince`, this.addProvince, {emulateJSON: true})
                        .then(function (res) {
                            if (res.data.resStatus == 0) {
                                this.propMsg = '新增成功！';
                                this.propVisible = true;
                                this.provincePopShow = false;
                                this.queryProvinceList();
                            } else {
                                this.propMsg = '新增失败!' + res.data.resText;
                                ;
                                this.propVisible = true;
                            }
                            this.addProvince.provinceCode="";
                            this.addProvince.provinceName="";
                        })
                }
            },
            addRegionShow(addRegion) {
                this.addRegion = addRegion;
                if (this.countryValue == undefined || this.countryValue == '') {
                    this.propMsg = '请先选择国家或、分区和省份!';
                    this.propVisible = true;
                    return;
                }
                if (this.provinceValue == undefined || this.provinceValue == '') {
                    this.propMsg = '请先选择国家或、分区和省份!';
                    this.propVisible = true;
                    return;
                }
                this.addRegion.provinceCode = this.provinceValue;
                this.addRegion.countryCode = this.countryValue;
                this.addRegion.partitionCode = this.partitionValue;
                if (this.addRegion.regionCode.trim() == "" || this.addRegion.regionName.trim() == "") {
                    this.$message({
                        message: '请输入正确的地区号或地区名称！',
                        type: 'warning'
                    });
                } else {
                    this.$http.post(`${this.proxyUrl}/param/regionMgt/editorRegion`, this.addRegion, {emulateJSON: true})
                        .then(function (res) {
                            if (res.data.resStatus == 0) {
                                this.propMsg = '新增成功！';
                                this.propVisible = true;
                                this.regionPopShow = false;
                                this.queryRegionList();
                            } else {
                                this.propMsg = '新增失败!' + res.data.resText;
                                ;
                                this.propVisible = true;
                            }
                            this.addRegion.regionCode = "";
                            this.addRegion.regionName = "";
                        })
                  }
            },

            editCountryShow(option){
                this.editCountryPopShowX=option.event.pageX;
                this.editCountryPopShowY=option.event.pageY;
                this.editCountryPopShow=true;
                this.editCountry.countryName=option.label;
                this.editCountry.countryCode=option.value;
            },
            editCountrySubmit(editCountry){
                this.editCountry=editCountry;
                console.log(this.editCountry);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/editorCountry`,this.editCountry,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            if(res.data.resStatus == 0){
                                this.propMsg = '修改成功！';
                                this.propVisible=true;
                                this.editCountryPopShow=false;
                                this.queryCountryList();
                            }else{
                                this.propMsg = '修改失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryCountryList();
                            }
                        })
            },
            editPartitionShow(option){
                this.editPartition.partitionName=option.label;
                this.editPartition.partitionCode=option.value;
                let obj = {};
                obj = this.partitionList.find((item)=>{
                            return item.partitionCode == option.value;
            });
                this.editPartition.provinceCodesArr=[];
                if(obj.provinceCodes != undefined && obj.provinceCodes != null){
                    this.editPartition.provinceCodesArr=obj.provinceCodes.split(',');
                }
                let country = {};
                country = this.countryList.find((item)=>{
                            return item.countryCode == this.countryValue;
            });
                this.editPartition.countryCode=this.countryValue;
                this.editPartition.countryName=country.countryName;
                this.generateData();
                this.editPartionVisible=true;
            },

            editPartitionSubmit(editPartition){
                this.editPartition=editPartition;
                this.$refs['editPartitionForm'].validate((valid) => {
                    if(valid){
                        this.editPartition.provinceCodes= this.editPartition.provinceCodesArr.toString();
                        this.$http.post(`${this.proxyUrl}/param/regionMgt/editorPartition`,this.editPartition,{emulateJSON:true})
                                .then(function(res){
                                    if(res.data.resStatus == 0){
                                        this.propMsg = '修改成功！';
                                        this.propVisible=true;
                                        this.editPartionVisible=false;
                                        this.queryPartitionList();
                                    }else{
                                        this.propMsg = '修改失败!'+ res.data.resText;;
                                        this.propVisible=true;
                                        this.queryPartitionList();
                                    }
                                });

                    }
                });
            },

            editProvinceShow(option){
                this.editProvincePopShowX=option.event.pageX;
                this.editProvincePopShowY=option.event.pageY;
                this.editProvincePopShow=true;
                this.editProvince.provinceName=option.label;
                this.editProvince.provinceCode=option.value;
            },
            editProvinceSubmit(editProvince){
                this.editProvince=editProvince;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/editorProvince`,this.editProvince,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            if(res.data.resStatus == 0){
                                this.propMsg = '修改成功！';
                                this.propVisible=true;
                                this.editProvincePopShow=false;
                                this.queryProvinceList();
                            }else{
                                this.propMsg = '修改失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryProvinceList();
                            }
                        })
            },
            editRegionShow(option){
                this.editRegionPopShowX = option.event.pageX;
                this.editRegionPopShowY = option.event.pageY;
                this.editRegionPopShow = true;
                this.editRegion.regionName = option.label;
                this.editRegion.regionCode = option.value;
            },
            editRegionSubmit(editRegion){
                this.editRegion=editRegion;
                console.log(this.editRegion);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/editorRegion`,this.editRegion,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            if(res.data.resStatus == 0){
                                this.propMsg = '修改成功！';
                                this.propVisible=true;
                                this.editRegionPopShow = false;
                                this.queryRegionList();
                            }else{
                                this.propMsg = '修改失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryRegionList();
                            }
                        })
            },


            delCountryShow(option){
                this.delCountryPopShowX=option.event.pageX;
                this.delCountryPopShowY=option.event.pageY;
                this.delCountryPopShow=true;
                this.delCountry.countryName=option.label;
                this.delCountry.countryCode=option.value;
            },
            delPartitionShow(option){
                this.delPartitionPopShowX=option.event.pageX;
                this.delPartitionPopShowY=option.event.pageY;
                this.delPartitionPopShow=true;
                this.delPartition.partitionName=option.label;
                this.delPartition.partitionCode=option.value;
            },
            delProvinceShow(option){
                this.delProvincePopShowX=option.event.pageX;
                this.delProvincePopShowY=option.event.pageY;
                this.delProvincePopShow=true;
                this.delProvince.provinceName=option.label;
                this.delProvince.provinceCode=option.value;
            },
            delRegionShow(option){
                this.delRegionPopShowX=option.event.pageX;
                this.delRegionPopShowY=option.event.pageY;
                this.delRegionPopShow=true;
                this.delRegion.regionName=option.label;
                this.delRegion.regionCode=option.value;
            },
            delCountrySubmit(){
                this.delCountryPopShow=false;
                console.log(this.delCountry);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/deleteCountry`,this.delCountry,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.queryCountryList();
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryCountryList();
                            }
                        })
            },
            delPartitionSubmit(){
                this.delPartitionPopShow=false;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/deletePartition`,this.delPartition,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.queryPartitionList();
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryPartitionList();
                            }
                        })
            },

            delProvinceSubmit(){
                this.delProvincePopShow=false;
                console.log(this.delProvince);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/deleteProvince`,this.delProvince,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.queryProvinceList();
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryProvinceList();
                            }
                        })
            },
            delRegionSubmit(){
                this.delRegionPopShow=false;
                console.log(this.delRegion);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/deleteRegion`,this.delRegion,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.queryRegionList();
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                                this.queryRegionList();
                            }
                            console.log(res);
                        })
            },
            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            }
        },
        created() {
        },
        components: {DefSelect,DefOption}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 80%;
        margin-top: 1%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
    /*.el-transfer__buttons{*/
    /*display:none;*/
    /*}*/

</style>
