<template>
    <div v-loading="loading">
        <h1 class="user-title">新建彩印盒</h1>
        <div class="user-line"></div>
        <div class="app-search">
          <el-form>
            <!-- 彩印类型 -->
            <el-form-item label="彩印盒名称">
              <el-input v-model="addReq.csPackageName" style="width:42%" size="small" :maxlength="50"></el-input>
            </el-form-item>
            <!-- 类别 -->
            <el-form-item label="内容分类">
                <el-select v-model="addReq.csPkgGroupId" placeholder="请选择" style="margin-left:15px;" size="small">
                <el-option
                    v-for="item in contentData"
                    :key="item.groupId"
                    :label="item.groupName"
                    :value="item.groupId">
                </el-option>
                </el-select>
            </el-form-item>
            
            <el-form-item label="内容标签">
              <el-button type="info" @click="visible = true" size="small" style="margin-left:15px;">添加标签</el-button>
              &nbsp;{{checkedLabelNames}}
              <el-dialog title="添加标签" :visible.sync="visible" :close-on-click-modal="false">
                 <div style="height:300px;overflow:auto;">
                  <el-form class="demo-form-inline" label-width="160px" justify="center">
                    <el-form-item>
                      <el-checkbox-group v-model="checkedLabelId">
                        <el-checkbox  @change="labelChange(item.liName)" v-for="item in labelData" :label="item.liId" :key="item.liName" style="display:inline-block;margin-left:30px;">{{item.liName}}</el-checkbox>
                      </el-checkbox-group>
                    </el-form-item>
                  </el-form>
                </div>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                  <span>{{"已选"+checkedLabelId.length+"个标签"}}</span>
                  <el-button @click="visible=false;" size="small">取消</el-button>
                  <el-button type="primary" @click="checkLabel();" size="small">确认</el-button>
                </div>
              </el-dialog>
            </el-form-item>
              <el-form-item v-for="(item,index) in CScontents" :key="item.label" :label='item.label'>
                <span v-if="index<9">&nbsp;</span>
                <el-input type="textarea" style="width:47%;margin-left:24px;margin-bottom:5px;" v-model='item.csPkgContent' :maxlength="50"></el-input>
              </el-form-item>

               <el-form-item style="padding: 0px 0px 0px 50%">
                  <el-button @click="delteContents()" type="text" size="small" v-if="CScontentsindex>3">删除</el-button>
               </el-form-item>

            <el-form-item>
              <el-button type="info" @click="addContents()" size="small" v-if="CScontentsindex<=30">+添加彩印</el-button>
            </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item>
                  <el-button type="primary" @click="submit" size="small">提交</el-button>
              </el-form-item>
            </el-form>
        </div>

    </div>
</template>
<script>
export default {
  name: "creatMaterial",
  data() {
    return {
      loading:false,
      delVisible: false,
      item: "彩印",
      CScontents:[
        {csPkgContent:'',label:'彩印1',name:'csPkgContent1'},
        {csPkgContent:'',label:'彩印2',name:'csPkgContent2'}
      ],
      CScontentsKey: {},
      CScontentsindex: 3,
      category: [],
      CStype: [],
      checkedLabelNames:'',
      checkedLabelId:[],
      checkedLabelName: [],
      contentData: [], //内容分类变量
      labelData: [], //内容标签变量
      visible: false,
      //添加标签
      //添加彩印盒
      addReq: {
        csPackageName: "",
        csPkgGroupId: "",
        csPkgLabelId: "",
      },
      request: {}
    };
  },
  mounted() {
    this.contentDatas();
    this.labelDatas();
  },
  methods: {
    checkLabel:function(){
      this.visible = false;
      this.checkedLabelNames = this.checkedLabelName.join(',');
      this.addReq.csPkgLabelId = this.checkedLabelId.join(',');
    },
    // 显示选中的标签名
    labelChange: function(labelName){
      let checkedBoolean=false;
      for(let i=0;i<this.checkedLabelName.length;i++){
        if(labelName===this.checkedLabelName[i]){
          this.checkedLabelName.splice(i,1);
          checkedBoolean=true;
        }
      }
      if(!checkedBoolean){
        this.checkedLabelName.push(labelName);
      }
      
    },
    //内容分类选项请求
    contentDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.contentData = res.data;
        });
    },
    //内容标签选项请求
    labelDatas: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
          emulateJSON: true
        })
        .then(function(res) {
          this.labelData = res.data;
        });
    },
    //动态添加彩印输入框
    addContents: function() {
      this.CScontents.push({
        label: '彩印'+(this.CScontents.length+1),
        csPkgContent: "",
        name:'csPkgContent'+(this.CScontents.length+1)
      });
      this.CScontentsindex++;
    },
    //删除添加的彩印项内容弄
    // delteContents(index){
    //   this.CScontents.splice(index,1);
    //   this.CScontentsindex--;
    // },
    delteContents(){
      this.CScontents.length--;
      this.CScontentsindex--;
    },
    submit: function() {
      // var s = '';
      // for(let i=0;i<this.addReq.csPkgLabelId.length;i++){
      //   s+=this.addReq.csPkgLabelId[i];
      //   if(i!=this.addReq.csPkgLabelId.length-1){
      //     s+=',';
      //   }
      // }

      if(!this.addReq.csPackageName || !this.addReq.csPackageName.trim()){
            this.$message('彩印盒名称不能为空');
            return;
      }

      if(!this.addReq.csPkgGroupId){
            this.$message('内容分类不能为空');
            return;
      }
      // this.addReq.csPkgLabelId=s;
      if(!this.addReq.csPkgLabelId || !this.addReq.csPkgLabelId.trim()){
            this.$message('内容标签不能为空');
            return;
      }

      for(let i=0;i<this.CScontents.length;i++){
        if(!this.CScontents[i].csPkgContent || !this.CScontents[i].csPkgContent.trim()){
            this.$message('内容不能为空');
            return;
        }
        this.addReq[this.CScontents[i].name]=this.CScontents[i].csPkgContent.trim();
      }
      this.addReq.csPackageName = this.addReq.csPackageName.trim();
      this.loading=true;
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/addCsPackage`, this.addReq, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
              this.$message({
                message:'添加成功',
                type:'success'
              });
              this.loading=false;
              this.$router.push({url:'/CSbox',name: 'CSbox'});
          } else if (res.data.resStatus == "1") {
              this.$message.error(res.data.resText);
              this.loading=false;
          }
        });
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search2 {
  width: 40%;
  margin: 0 auto;
  margin-top: 3%;
}
/* 弹窗checkbox样式 */
.el-form-item__content {
  margin: 0 !important;
}
</style>
