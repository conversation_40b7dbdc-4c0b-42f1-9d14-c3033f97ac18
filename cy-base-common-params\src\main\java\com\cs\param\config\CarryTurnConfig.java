package com.cs.param.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class CarryTurnConfig {

    @Value("${carryturn.version}")
    private String version;
    @Value("${carryturn.productLine}")
    private String productLine;
    @Value("${carryturn.portalType}")
    private String portalType;
    @Value("${carryturn.sourceDeviceCode}")
    private String sourceDeviceCode;
    @Value("${carryturn.platform}")
    private String platform;
    @Value("${carryturn.companyId}")
    private String companyId;
    @Value("${carryturn.sharedSecret}")
    private String sharedSecret;
    @Value("${carryturn.shopCode}")
    private String shopCode;
}
