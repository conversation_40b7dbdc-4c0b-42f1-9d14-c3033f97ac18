package com.cs.aspect;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.cs.param.constants.Constants;
import com.cs.param.constants.LogConstant;
import com.cs.param.utils.ManualLogUtils;
import com.cs.param.utils.Md5Utils;
import com.cs.param.utils.MqLogUtil;
import com.cs.param.utils.WriteThreadContextUtil;

/**
 * 
 * 接收MQ日志切面
 * 
 * <AUTHOR>
 * @version [版本号, 2022年5月11日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Aspect
@Component
public class ReceiveMqLogAspect
{
	
	private static final String MQ_TYPE = "subscribe";
	
    /**
     * 检查点配置 resdcm接口
     * 
     * <AUTHOR> 2018年5月16日
     * @see [类、类#方法、类#成员]
     */
	@Pointcut("@annotation(org.springframework.amqp.rabbit.annotation.RabbitListener)")
    public void webLog()
    {
    }
    
	private String getTopicName(JoinPoint joinPoint) {
        try {
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Annotation annotation = methodSignature.getMethod().getAnnotation(RabbitListener.class);
            Method[] methods = Class.forName(annotation.getClass().getName()).getDeclaredMethods();
            for (Method method : methods) {
                Annotation p = annotation;
                Method m = p.getClass()
                        .getDeclaredMethod("queues", null);
                String [] str = (String []) m.invoke(p, null);
                return str[0];
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
	
    /**
     * 拦截前操作
     * 
     * <AUTHOR> 2018年5月11日
     * @param joinPoint 切入点
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint)
    {
    	// 记录接收MQ的内容
        StringBuffer logMsgBuffer = new StringBuffer();
        logMsgBuffer.append(MQ_TYPE + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        //消息主题
        String topicName = getTopicName(joinPoint);
        logMsgBuffer.append(topicName + Constants.Split.EUROPE_THREE);
        //唯一流水号
        String serialID = UUID.randomUUID().toString();
        logMsgBuffer.append("serialID_"+serialID + Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = UUID.randomUUID().toString();
        WriteThreadContextUtil.writeThreadContext(topicName, linkID);
        logMsgBuffer.append("linkID_"+linkID + Constants.Split.EUROPE_THREE);
        Object arg = joinPoint.getArgs()[0];
        String body = joinPoint.getArgs()[0].toString();
        if(arg instanceof String)
    	{
        	body = arg.toString();
    	}
    	else if(arg instanceof Object)
    	{
    		body = JSON.toJSONString(arg);
    	}
        String messageId = "";
        if(StringUtils.isNotEmpty(body)) {
        	messageId = Md5Utils.MD5Encode(body);
        }
        // 消息ID
        logMsgBuffer.append(messageId + Constants.Split.EUROPE_THREE);
        // 消息报文
        logMsgBuffer.append(body);
        MqLogUtil.getInstance().printSendMqLog(logMsgBuffer.toString(), Boolean.FALSE);
    }
    
    
    /**
     * 拦截后的操作
     * 
     * <AUTHOR> 2018年5月16日
     * @param ret 返回值
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object ret)
    {
        
    }
    
    /**
     * 报错后的操作
     * 
     * @auth cWX319470 2018年5月11日
     * @see [类、类#方法、类#成员]
     */
    @AfterThrowing(throwing = "e", pointcut = "webLog()")
    public void doAfterError(JoinPoint joinPoint, Throwable e)
    {
    	// 记录MQ发送的内容
        StringBuffer logMsgBuffer = new StringBuffer();
        logMsgBuffer.append(MQ_TYPE + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        //消息主题
        String topicName = getTopicName(joinPoint);
        logMsgBuffer.append(topicName + Constants.Split.EUROPE_THREE);
        //唯一流水号
        String serialID = UUID.randomUUID().toString();
        logMsgBuffer.append("serialID_"+serialID + Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID = ThreadContext.get("linkID");
    	if(StringUtils.isEmpty(linkID)){
            linkID = UUID.randomUUID().toString();
        }
        logMsgBuffer.append("linkID_"+linkID + Constants.Split.EUROPE_THREE);
        Object arg = joinPoint.getArgs()[0];
        String body = joinPoint.getArgs()[0].toString();
        if(arg instanceof String)
    	{
        	body = arg.toString();
    	}
    	else if(arg instanceof Object)
    	{
    		body = JSON.toJSONString(arg);
    	}
        String messageId = "";
        if(StringUtils.isNotEmpty(body)) {
        	messageId = Md5Utils.MD5Encode(body);
        }
        // 消息ID
        logMsgBuffer.append(messageId + Constants.Split.EUROPE_THREE);
        // 消息报文
        //String resultMessage = e.getMessage();
        logMsgBuffer.append(body);
        ManualLogUtils.errorLog("system error!", e);
        MqLogUtil.getInstance().printSendMqLog(logMsgBuffer.toString(), Boolean.FALSE);
    }
}