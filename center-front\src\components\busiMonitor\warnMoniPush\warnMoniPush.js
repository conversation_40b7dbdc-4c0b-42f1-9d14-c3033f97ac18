import {wanMoniPushQuery,wanMoniPushExport} from './warnMoniPushServer.js';
import {warnMoniSendSms,warnMoniSendEmail} from './../warnMoniNewUser/warnMoniNewUserServer.js';
import {dowandFile,formDate} from './../../../util/core.js';
export default {
    data(){
        return{
            form: {
                page: {
                    pageNo: 1,
                    pageSize: 100,
                    total: 0
                },
                startDate: new Date(new Date().getFullYear(),new Date().getMonth(),new Date().getDate()-7),
                endDate: new Date(),
                datetype: 'date',
                provinceId:'',
                cityId:'',
                selectedOptions: [],
                locationId:''
            },
            tableData: new Array(),
            //省份列表
            provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
            regionList:new Array(),//城市列表
            pickerOptions: {
                disabledDate: function (today) {
                    return today.getTime() > Date.now();
                }
            },
            tableLoading: false
        }
    },
    mounted(){
        //this.search(1);
    },
    methods:{
        //判断省份城市等是否填写
        checkedParams(){
            if(this.form.provinceId===''){
                this.$message({
                    message:'省份不能为空',
                    type:'warning'
                })
                return false;
            }
            return true;
        },
        //查询
        search(pageNo){
            this.form.page.pageNo=pageNo;
            let startDate='';
            let endDate='';
            if(this.form.type=='month'){
                startDate=formDate(this.form.startDate,'yyyy-MM');
                endDate=formDate(this.form.endDate,'yyyy-MM');
            }else{
                startDate=formDate(this.form.startDate,'yyyy-MM-dd');
                endDate=formDate(this.form.endDate,'yyyy-MM-dd');
            }
            let params={
                startTime:startDate,
                endTime:endDate,
                pageNo:this.form.page.pageNo,
                pageSize:this.form.page.pageSize,
                provinceId:this.form.provinceId,
                countyId:this.form.cityId
            }
            if(this.checkedParams()){
                wanMoniPushQuery('pushPrewarning',params).then(res=>{
                    if(res.code===0){
                        this.tableData=res.data.pushPrewarningInfoList;
                        this.form.page.total=res.data.total;
                    }
                })
            }
        },
        handleSizeChange(pageSize){
            this.form.page.pageSize=pageSize;
            this.search(1);
        },
        //导出
        download(){
			let startDate='';
            let endDate='';
            if(this.form.type=='month'){
                startDate=formDate(this.form.startDate,'yyyy-MM');
                endDate=formDate(this.form.endDate,'yyyy-MM');
            }else{
                startDate=formDate(this.form.startDate,'yyyy-MM-dd');
                endDate=formDate(this.form.endDate,'yyyy-MM-dd');
            }
            let params={
                startTime:startDate,
                endTime:endDate,
                pageNo:this.form.page.pageNo,
                pageSize:this.form.page.pageSize,
                provinceId:this.form.provinceId,
                countyId:this.form.cityId
            }
            if(this.checkedParams()){
                wanMoniPushExport('pushExportExcel',params).then(res=>{
                    dowandFile(res,'推送量预警.xlsx');
                })
            }
        },
        //根据省份查询城市列表
        selectProvince(){
            this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.form.provinceId},{emulateJSON:true})
                        .then((res)=>{
                            this.form.cityId='';
                            this.regionList=res.data;
            })
        },

        //下发短信
        sendSms(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:2};
            warnMoniSendSms('sendSMS',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        },
        //下发邮件
        sendEmail(list){
            let params={provinceId:list.provinceId,cityId:list.cityId,type:2};
            warnMoniSendEmail('sendEmail',params).then(res=>{
                if(res.code==0){
                    this.$message.success("操作成功！");
                }else{
                    this.$message.error(res.msg);
                }
            })
        },
        formatPercent(row, col, val) {
            return Math.round(val * 10000) / 100 + '%';
        },
        formatRate(row, column){
            if(row.pushSuccessRate==undefined){
                return "";
            }else{
                return row.pushSuccessRate+"%";
            }
        },
        formatRateW(row, column){
            if(row.pushSuccessRateW==undefined){
                return "";
            }else{
                return row.pushSuccessRateW+"%";
            }
        },
        getProvinceName(row, col, val) {
            return this.provsMap[val.substr(0, 2)].name;
        },
        getCityName(row, col, val) {
            return this.provsMap[val.substr(0, 2)].citys[val.substr(2, 2)];
        }
    },
    filters:{
        status(value){
            if(value==0){
                return '正常';
            }else{
                return '偏低';
            }
        }
    }
}