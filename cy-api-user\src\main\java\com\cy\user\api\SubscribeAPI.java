package com.cy.user.api;

import com.cy.user.model.ParPackage;
import com.cy.user.model.SubConfigRequest;
import com.cy.user.model.SubscriptionResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.cy.user.cs.request.ContractSubscribeReq;
import com.cy.user.cs.request.ContractUnsubscribeReq;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 彩印管理后台系统申请订购/退订
 * 
 * @date 2018年4月17日 - 上午9:31:32
 * @Description
 */
public interface SubscribeAPI {

	/**
	 * @Title: createSubscribe
	 * @Description: 订购
	 * @return: void
	 */
	@RequestMapping(value = "/cy/internal/user/subscribe", method = RequestMethod.POST)
	public String createSubscribe(@RequestBody ContractSubscribeReq contractSub);

	/**
	 * 
	 * @Title: createUnSubscribe
	 * @Description: 退订
	 * @param unsubscribeReq
	 * @return
	 * @return: String
	 */

	@RequestMapping(value = "/cy/internal/user/unsubscribe", method = RequestMethod.POST)
	public String createUnSubscribe(@RequestBody ContractUnsubscribeReq unsubscribeReq);

	/**
	 * 体验周期订购接口
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/experienceSubConfig")
	SubscriptionResult experienceSubConfig(@RequestBody SubConfigRequest request);

	/**
	 * 通过局数据获取业务包信息
	 * @param businessCode
	 * @return
	 */
	@RequestMapping(value = "/getPackageInfoByBusinessCode")
	ParPackage getParPackageInfoByBusinessCode(@RequestParam("businessCode") String businessCode);
}
