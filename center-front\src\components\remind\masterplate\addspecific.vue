<template>
    <div class="addspecific">
        <div class="content">
            <el-form :model="addForm" ref="addForm" class="demo-form-inline" label-width="35%" size="small" style="width: 80%">
                <el-form-item label="模板名称：" prop="sysUserName">
                    <el-input v-model="addForm.sysUserName"></el-input>
                </el-form-item>
                <el-form-item label="号码/号码群：">
                    <el-input
                            type="textarea"
                            :rows="4"
                            resize="none"
                            placeholder="输入号码时使用逗号隔开"
                            v-model="addForm.textarea">
                    </el-input>
                    <el-upload
                            class="upload-demo"
                            action="https://jsonplaceholder.typicode.com/posts/"
                            :on-preview="handlePreview"
                            :on-remove="handleRemove"
                            multiple
                            :limit="3"
                            :on-exceed="handleExceed"
                            :file-list="fileList">
                        <el-button size="small" type="primary">上传excel表</el-button>
                        <el-button type="text" size="small">下载excel模版</el-button>
                    </el-upload>
                </el-form-item>
                <el-form-item label="拨打号码短信提醒内容：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.textarea">
                    </el-input>
                </el-form-item>
                <el-form-item label="拨打号码USSD提醒内容：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.textarea">
                    </el-input>
                </el-form-item>
                <el-form-item label="接听号码短信提醒内容：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.textarea">
                    </el-input>
                </el-form-item>
                <el-form-item label="接听号码USSD提醒内容：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.textarea">
                    </el-input>
                </el-form-item>
                <el-form-item label="描述：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.textarea">
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
        <div class="content1">
            <el-button type="primary" @click="submit" size="small">提交</el-button>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'addspecific',
        data(){
            return{
                name:'',
                addForm:{},
                fileList: [{name: 'food.jpeg', url: 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100'}],

            }
        },
        components: {},
        props:['provinceList'],
        methods:{
            submit(){
                this.$emit('addList');
            },
            //上传文件
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            handleExceed(files, fileList) {
                this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
            },
        }
    }
</script>

<style scoped>
    .addtitle{
        font-size: 18px;
        margin-left: 20px;
        margin-top: -20px;
    }
    .content{
        width: 640px;
        margin: 50px auto;
    }
    .content1{
        text-align: center;
    }
</style>
