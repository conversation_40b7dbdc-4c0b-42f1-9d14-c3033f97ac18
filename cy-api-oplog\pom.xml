<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>cn.caiyin</groupId>
	<artifactId>cy-api-oplog</artifactId>
	<version>0.0.1</version>
	<name>cy-api-oplog</name>
	<description>Demo project for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath /> <!-- lookup parent from repository -->
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<project.compiler.source>21</project.compiler.source>
		<project.compiler.target>21</project.compiler.target>
		<project.compiler.compilerVersion>21</project.compiler.compilerVersion>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<spring-cloud.version>2024.0.0</spring-cloud.version>
		<druid.version>1.1.9</druid.version>

		<mysql.driver.version>6.0.6</mysql.driver.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>3.0.2</version>  <!-- 选择适合的版本 -->
		</dependency>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>3.0.0</version> <!-- 使用适当的版本 -->
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<version>4.0.4</version>
			<scope>compile</scope>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>-->
<!--		</dependency>-->
				<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>
		<dependency>
			<groupId>cn.caiyin</groupId>
			<artifactId>cy-api-sys</artifactId>
			<version>0.0.1</version>
		</dependency>
	</dependencies>
	<dependencyManagement>
		<dependencies>
									            			<!--标记位-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.4.0</version>
            </dependency>

			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.18.0</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.yml</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-resources-plugin</artifactId>
					<configuration>
						<encoding>utf-8</encoding>
						<useDefaultDelimiters>true</useDefaultDelimiters>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<!-- Nexus Setting -->
	<distributionManagement>
		<repository>
			<id>caiyin-nexus</id>
			<name>caiyn-nexus</name>
			<url>${ReleaseRepository}</url>
		</repository>
		<snapshotRepository>
			<id>caiyin-nexus</id>
			<name>caiyin-nexus</name>
			<url>${SnapshotRepository}</url>
		</snapshotRepository>
	</distributionManagement>
</project>
