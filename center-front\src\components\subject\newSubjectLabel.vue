<template>
  <div>
    <h1 class="user-title">{{subjectLabelHandleType ? "编辑" : "新建"}}专题标签</h1>
    <div class="user-line"></div>
    <el-form
      :model="ruleForm"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
      style="margin-top: 22px;"
      :rules="rules"
    >
      <el-form-item label="专题标签" prop="labelName">
        <el-input v-model="ruleForm.labelName" clearable style="width: 250px;"></el-input>
      </el-form-item>
      <div v-if="subjectLabelHandleType != 'edit'">
        <el-form-item label="专题图片" prop="labelPhotoUrl">
          <div>
            <label class="uploadBtn" for="upload" v-if="imgEdit">上传图片</label>
            <input @change="showPic" ref="uploadFile" id="upload" type="file" accept="image/gif,image/jpeg,image/jpg,image/png" />
            <el-input v-if="false" v-model="ruleForm.labelPhotoUrl"></el-input>
          </div>
        </el-form-item>
        <el-form-item>
          <img class="uploadImg" :src="image" alt="">
        </el-form-item>
      </div>
      <el-form-item>
        <el-button type="primary" @click="back()">返回上级</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script type="text/ecmascript-6">
import { postFromData } from "@/servers/httpServer.js";

export default {
  name: "userPush",
  data() {
    return {
      image: "",
      ruleForm: {
        id: "",
        labelName: "",
        labelPhotoUrl: "",
        file: ""
      },
      rules: {
        labelName: [
          { required: true, message: "请输入专题标签", trigger: "blur" },
        ],
        labelPhotoUrl: [
          { required: true, message: "请上传专题图片", trigger: "change" }
        ]
      },
      subjectLabelHandleType: "",
      imgEdit: true
    };
  },
  mounted() {
    this.subjectLabelHandleType = sessionStorage.getItem(
      "subjectLabelHandleType"
    );
    if (this.subjectLabelHandleType == "edit") {
      this.imgEdit = false;
      const { id, labelName, labelPhotoUrl } = JSON.parse(
        sessionStorage.getItem("subjectLabelContent")
      );
      this.ruleForm = {
        id,
        labelName,
        labelPhotoUrl,
        file: ""
      };
      this.image = labelPhotoUrl
    }
  },
  methods: {
    showPic() {
      var file = this.$refs.uploadFile.files[0];
      this.ruleForm.file = file
      if (window.FileReader) {
        var fr = new FileReader();
        fr.readAsDataURL(file)
        fr.onloadend = e => {
          this.image = e.target.result;
          this.ruleForm.labelPhotoUrl = e.target.result;
        };  
        // fr.readAsDataURL(file);
      }
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if(!valid) {
          return false
        }
        let formData = new FormData();
        if (this.subjectLabelHandleType == "edit") {
          Object.keys(this.ruleForm).forEach(key => {
            if(this.ruleForm[key] && key != 'labelPhotoUrl') {
              formData.append(key, this.ruleForm[key])
            }
          })
          this.$http
            .post(
              `${this.proxyUrl}/cySubject/updateSubjectLabel`,
              formData
            )
            .then(res => {
              if (res.data.code == 0) {
                this.$message.success("更新成功!");
                this.$router.push("/subjectLabel");
              } else {
                this.$message.error(res.data.message);
              }
            });
        } else {
          const { id, labelName, file } = this.ruleForm;
          formData.append('labelName', labelName);
          formData.append('file', file);
          postFromData(
              `${this.proxyUrl}/cySubject/addSubjectLabel`,
              formData
            )
            .then(res => {
              if (res.data.code == 0) {
                this.$message.success("新建成功!");
                this.$router.push("/subjectLabel");
              } else {
                this.$message.error(res.data.message);
              }
            });
        }
      })
      

      // this.$refs[formName].validate(valid => {
      //   if (valid) {
      //     console.log(formName);
      //   } else {
      //     console.log("error submit!!");
      //     return false;
      //   }
      // });
    },
    back() {
      this.$router.push("/subjectLabel");
    }
  }
};
</script>

<style scoped>
.user-title {
  padding: 10px 0px 0px 0px;
}
.user-line {
  width: 100%;
  margin: 0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #dddfe6;
}
.el-form-item {
  margin-bottom: 22px !important;
}
#upload {
  display: none;
}
.uploadBtn {
  color: #169bd5;
  cursor: pointer;
}
.uploadImg {
  width: 200px;
}
</style>
