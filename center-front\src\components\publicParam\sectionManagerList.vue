<template>
    <div>
        <h1 class="user-title">号段管理</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="号段">
                    <el-input  v-model="searchForm.phoneSection" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in statusList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="终端类型" prop="terminalCode">
                    <el-select v-model="searchForm.terminalCode" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in terminalList"
                                :key="item.terminalCode"
                                :label="item.terminalType"
                                :value="item.terminalCode">
                        </el-option>
                    </el-select>
                </el-form-item>

            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="省份" >
                    <el-select v-model="searchForm.provinceCode" placeholder="请选择" size="small"  @change="querySearchRegionList" class="app-input">
                        <el-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="地区" prop="regionCode">
                    <el-select v-model="searchForm.regionCode" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in searchRegionList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
                    <el-button type="primary" @click="addVisible = true" size="small">新增号段</el-button>
                    <el-button type="primary" plain @click="addListVisible = true" size="small">批量导入</el-button>
                    <el-button type="primary" plain @click="exportExcel()" size="small">导出excel</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark"
            v-loading="dataHiddent"
            element-loading-text="数据量比较大!正拼命加载中……"
                  class="app-tab" :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="phoneSection" label="号段" width="120"/>
            <el-table-column prop="status" label="状态" :formatter="formatterStatus"  width="120"/>
            <el-table-column prop="optCode" label="运营商编码"  width="120"/>
            <el-table-column prop="netName" label="所在网络" width="120"/>
            <el-table-column prop="provinceName" label="省份" width="120"/>
            <el-table-column prop="regionName" label="地市" width="120"/>
            <el-table-column prop="hlrAddr" label="所属hlr地址" width="120"/>
            <el-table-column prop="terminalType" label="终端类型" width="120"/>
            <el-table-column prop="oper" label="操作" fixed="right" width="120">
                <template slot-scope="scope">
                    <el-button v-if="scope.row.status==0" type="text" size="small" @click="showEdit(scope.row)" >编辑</el-button>
                    <el-button v-if="scope.row.status==0" type="text" size="small" @click="delPhone(scope.row)" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>
        <div>
        <el-dialog title="新增号段" :visible.sync="addVisible" :close-on-click-modal="false">
            <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-ruleForm" label-width="25%"  style="width: 80%">
                <el-form-item label="号段"  prop="phoneSection">
                    <el-input v-model="addForm.phoneSection" placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="运营商编号" prop="optCode">
                    <el-input v-model="addForm.optCode"  placeholder="" size="small"></el-input>
                </el-form-item>
                <el-form-item label="所在网络" prop="netCode">
                    <el-select v-model="addForm.netCode" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in netList"
                                :key="item.netCode"
                                :label="item.netName"
                                :value="item.netCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-row style="margin-left: 14.5%">
                    <el-col :span="12">
                        <el-form-item label="省份" prop="provinceCode">
                        <el-select v-model="addForm.provinceCode" placeholder="请选择" size="small" @change="queryAddRegionList">
                            <el-option
                                    v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                            </el-option>
                        </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                    <el-form-item label="地区" prop="regionCode">
                        <el-select v-model="addForm.regionCode" placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in addRegionList"
                                    :key="item.regionCode"
                                    :label="item.regionName"
                                    :value="item.regionCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    </el-col>
                    </el-row>
                <el-form-item label="所属HLR地址"  prop="hlrAddr">
                    <el-input v-model="addForm.hlrAddr" placeholder="请输入所属hlr地址" size="small"></el-input>
                </el-form-item>
                <el-form-item label="终端类型" prop="terminalCode">
                    <el-select v-model="addForm.terminalCode" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in terminalList"
                                :key="item.terminalCode"
                                :label="item.terminalType"
                                :value="item.terminalCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!---->
                <!--<el-form-item label="适用对象"  prop="hlrAddr">-->
                    <!--<el-input v-model="addForm.hlrAddr" placeholder="请输入适用对象"></el-input>-->
                <!--</el-form-item>-->
                <!--<el-form-item label="状态" prop="terminalCode">-->
                    <!--<el-select v-model="addForm.terminalCode" placeholder="请选择">-->
                        <!--<el-option-->
                                <!--v-for="item in terminalList"-->
                                <!--:key="item.terminalCode"-->
                                <!--:label="item.terminalType"-->
                                <!--:value="item.terminalCode">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                <!--</el-form-item>-->
            </el-form>
            <div slot="footer" class="dialog-footer" style="text-align: right;">
                <el-button @click="addVisible = false" size="small">取 消</el-button>
                <el-button type="primary"  @click="addPhone('addForm')" size="small">确 定</el-button>
            </div>
        </el-dialog>
        </div>

        <div>
            <el-dialog title="批量添加" :visible.sync="addListVisible" :close-on-click-modal="false">
                <el-form :model="addListForm" :inline="true"  class="demo-form-inline" label-width="160px" justify="center">
                    <el-form-item label="号段文件">
                        <el-input v-model="addListForm.fileName" disabled="" size="small"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-upload
                                class="upload-demo"
                                ref="upload"
                                action=""
                                :auto-upload='false'
                                :on-change="handleChange"
                                :show-file-list="false"
                                accept=".xls, .xlsx"
                            >
                            <el-button size="small" type="primary">上传excel表</el-button>
                            <div slot="tip" class="el-upload__tip"></div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item>
                        <a :href="`${this.proxyUrl}/param/numberMat/downloadTemplate`">下载模板</a>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addListVisible = false"  size="small">取 消</el-button>
                    <el-button type="primary" @click="addListVisible = false;addSaveManager()"  size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <div>
            <el-dialog title="修改号段" :visible.sync="editVisible" :close-on-click-modal="false">
                <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-ruleForm" label-width="25%"  style="width: 80%">
                    <el-form-item label="号段">
                        {{editForm.phoneSection}}
                    </el-form-item>
                    <el-form-item label="运营商编号" prop="optCode">
                        <el-input v-model="editForm.optCode"  placeholder="" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="所在网络" prop="netCode">
                        <el-select v-model="editForm.netCode" placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in netList"
                                    :key="item.netCode"
                                    :label="item.netName"
                                    :value="item.netCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-row style="margin-left: 14.5%">
                        <el-col :span="12">
                            <el-form-item label="省份" prop="provinceCode">
                                <el-select v-model="editForm.provinceCode" placeholder="请选择" size="small" @change="queryEditRegionList">
                                    <el-option
                                            v-for="item in provinceList"
                                            :key="item.provinceCode"
                                            :label="item.provinceName"
                                            :value="item.provinceCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="地区" prop="regionCode">
                                <el-select v-model="editForm.regionCode" placeholder="请选择" size="small">
                                    <el-option
                                            v-for="item in editRegionList"
                                            :key="item.regionCode"
                                            :label="item.regionName"
                                            :value="item.regionCode">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item label="所属HLR地址"  prop="hlrAddr">
                        <el-input v-model="editForm.hlrAddr" placeholder="请输入所属hlr地址" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="终端类型" prop="terminalCode">
                        <el-select v-model="editForm.terminalCode" placeholder="请选择" size="small">
                            <el-option
                                    v-for="item in terminalList"
                                    :key="item.terminalCode"
                                    :label="item.terminalType"
                                    :value="item.terminalCode">
                            </el-option>
                        </el-select>
                    </el-form-item>

                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="editVisible = false">取 消</el-button>
                    <el-button type="primary"  @click="editPhone('editForm')">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
        </el-dialog>

    </div>


</template>
<script>
import {post,postDownload} from './../../servers/httpServer.js';
import {dowandFile} from './../../util/core.js';
    export default {
        data() {
            return {
                activeName:'sectionManager',
                propMsg:'',
                dataHiddent:false,
                propVisible:false,
                editVisible: false,
                addVisible:false,
                addListVisible:false,
                //查询form对象定义
                searchForm: {
                    provinceCode: '',
                    regionCode:'',
                    terminalCode: '',
                    brandCode:'',
                    pageSize:10,
                    pageNum :1 // 查询的页码
                },
                currentPage: 1,
                total:0,
                queryProvince:{
                    provinceCode: '',
                    provinceName: ''
                },
                queryRegion:{
                    provinceCode: ''
                },
                //新增form
                addForm:{
                    phoneSection:'',
                    optCode:'',
                    countryCode:'',
                    startSuffix:'',
                    endSuffix:'',
                    brandCode:'',
                    brandName:'',
                    netCode:'',
                    netName:'',
                    provinceCode:'',
                    regionCode:'',
                    hlrAddr:'',
                    terminalType:''
                },
                //新增form
                addListForm:{
                    fileName:'',
                },
                fileList:[],
                //修改form
                editForm:{
                    id:1,
                    phoneSection:'',
                    optCode:'',
                    countryCode:'',
                    startSuffix:'',
                    endSuffix:'',
                    brandCode:'',
                    brandName:'',
                    netCode:'',
                    netName:'',
                    provinceCode:'',
                    regionCode:'',
                    hlrAddr:'',
                    terminalType:''
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'', //
                },
                rules: {
                    phoneSection: [
                        { required: true, message: '请输入号段', trigger: 'blur' },
                    ],
                    netCode: [
                        { required: true, message: '请选择所在网络', trigger: 'blur' }
                    ],
                    provinceCode: [
                        { required: true, message: '请选择省份', trigger: 'blur' }
                    ],
                    regionCode: [
                        { required: true, message: '请选择地区', trigger: 'blur' }
                    ],
                    terminalCode: [
                        { required: true, message: '请选择终端类型', trigger: 'blur' }
                    ]
//                    sysResourcesIds: [
//                        { type: 'array', required: true, message: '请至少设置一个角色权限', trigger: 'change' }
//                    ]
                },
                tableData:[],

                countryList:[],
                terminalList:[],
                regionList:[],
                searchRegionList:[],
                editRegionList:[],
                addRegionList:[],
                provinceList:[],
                netList:[],
                brandList:[],
                statusList:[
                    {
                        key:'',
                        value:'全部'
                    },
                    {
                        key:'0',
                        value:'正常'
                    },{
                        key:'1',
                        value:'删除'
                    }
                ],
                provinceValue:''

            }
        },

        mounted(){
//            this.slideData(this.proxyUrl);
            //this.search();
            this.querCountryList();
            this.queryProvinceList();


            //this.querBrandList();
            //终端
            this.queryTerminalList();
            //网络
            this.queryNetList();
            this.queryAddRegionList();
            this.queryEditRegionList();
        },
        methods: {
            formatterStatus(row, column) {
                switch(row.status){
                    case '0':
                        return '正常';
                        break;
                    case '1':
                        return '删除';
                        break;
                }
            },
            querCountryList(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getCountry`).then(function(res){
                    console.log(res);
                    this.countryList=res.data;
                })
            },
             //品牌
            querBrandList(){
                this.$http.get(`${this.proxyUrl}/param/numberMat/getAllBrand`).then(function(res){
                    console.log(res);
                    this.brandList=res.data;
                })
            },
            //终端
            queryTerminalList(){
                this.$http.get(`${this.proxyUrl}/param/numberMat/getAllTerminal`).then(function(res){
                    this.terminalList=res.data;
                })
            },
            //网络
            queryNetList(){
                this.$http.get(`${this.proxyUrl}/param/numberMat/getAllNetwork`).then(function(res){
                    this.netList=res.data;
                })
            },
            queryProvinceList(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getProvince`).then(function(res){
                    this.provinceList=res.data;
                    //this.searchForm.provinceCode=this.provinceList[0].provinceCode;
                })
            },

            querySearchRegionList(){
                this.queryRegion.provinceCode = this.searchForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                        .then(function(res){
                            this.searchRegionList=res.data;
                        })
            },
            queryAddRegionList(){
                this.queryRegion.provinceCode = this.addForm.provinceCode;
                this.addForm.regionCode='';
                console.log(this.queryRegion);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                        .then(function(res){
                            console.log(res);
                            this.addRegionList=res.data;
                        })
            },
            queryEditRegionList(){
                this.queryRegion.provinceCode = this.editForm.provinceCode;
                this.editForm.regionCode='';
                console.log(this.queryRegion);
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                        .then(function(res){
                            this.editRegionList=res.data;
                        })
            },

            //查询列表请求
            search:function(searchForm){
                this.dataHiddent=true;
                this.$http.post(`${this.proxyUrl}/param/numberMat/getNumberPage`,searchForm,{emulateJSON:true})
                        .then(function(res){
                            this.currentPage=res.data.pageNum;
                            this.total=res.data.pageTotal;
                            this.tableData=res.data.datas;
                            this.dataHiddent=false;
                        })
            },
            // 弹出修改框
            showEdit:function(editForm){
                this.editForm = Object.assign({},editForm);

                this.editForm.provinceCode==this.provinceValue;
                console.log(this.editForm);
                this.editVisible = true;
//                this.$nextTick(() => {
//                    console.log(this.$refs.tree1);
//                    this.$refs.tree1.setCheckedKeys([1]);
//                });
            },
            //修改请求
            editPhone:function(formName){
                console.log(this.editForm);
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/numberMat/updateNumber`,this.editForm,{emulateJSON:true})
                                .then(function(res){
                                    console.log(res);
                                    if(res.data.resStatus == 0){
                                        this.$message({
                                            message: '修改成功！',
                                            type: 'success'
                                        });
                                        this.editVisible = false;
                                        this.search(this.searchForm);
                                    }else{
                                        this.$message.error('修改失败!'+ res.data.resText);
                                    }
                                })
                    } else {
                        return false;
                    }
            });
            },
            delPhone:function(formName){
                this.$confirm('确认要删除吗？')
                    .then(_ => {
                this.queryOrDelForm.id = formName.id;
                console.log(this.queryOrDelForm);
                this.$http.post(`${this.proxyUrl}/param/numberMat/deleteNumber`,this.queryOrDelForm,{emulateJSON:true})
                    .then(function(res){
                        console.log(res)
                        if(res.data.resStatus == 0){
                            this.$message({
                                message: '删除成功！',
                                type: 'success'
                            });
                            this.search(this.searchForm);
                        }else{
                            this.$message.error('删除失败!'+ res.data.resText);
                        }
                    })
                 })
            },
            // 新增
            addPhone(formName) {
                console.log(this.addForm);
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/numberMat/addNumber`,this.addForm,{emulateJSON:true})
                                .then(function(res){
                                    if(res.data.resStatus == 0){
                                        this.$message({
                                            message: '新增成功！',
                                            type: 'success'
                                        });
                                        this.addVisible = false;
                                        this.search(this.searchForm);
                                    }else{
                                        this.$message.error('新增失败!'+ res.data.resText);
                                    }
                                })
                    } else {
                        return false;
                    }
                });
            },

            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },

            // 上传移除
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            handleExceed(files, fileList) {
                this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
            },
            beforeRemove(file, fileList) {
                return this.$confirm(`确定移除 ${ file.name }？`);
            },
            // 关闭弹出框
            handleClose(done) {
                this.$confirm('确认关闭？')
                        .then(_ => {
                    done();
            })
            .catch(_ => {});
            },
            handleClick(activeName){
                this.$router.push(activeName);
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            //上传文件
            handleChange(file,fileName){
                    this.addListForm.fileName=file.name;
                    this.addListForm.file=file.raw;
            },
            //提交新增
            addSaveManager(){
                let formData=new FormData();
                formData.append('fileName',this.addListForm.fileName);
                formData.append('file',this.addListForm.file);
                post('/param/numberMat/batchInsert',formData).then(res=>{
                    if(res.data.resStatus===0){
                        this.$message.success("上传成功");
                        this.$refs.upload.clearFiles();
                    }else{
                        this.$message({
                            message:"上传错误，请确认文件或数据格式是否正确",
                            type:'error'
                        })
                    }
                    this.addListForm.fileName = "";
                })
            },
            //导出excel表格
            exportExcel(){
                this.dataHiddent=true;
                postDownload('/param/numberMat/exportExecl',this.searchForm).then(res=>{
                    dowandFile(res.data,'号码段数据列表.xlsx');
                    this.dataHiddent=false;
                })
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 0%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
