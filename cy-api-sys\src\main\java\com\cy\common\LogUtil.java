package com.cy.common;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;

public class LogUtil {
    public final static String BIZ="BIZ";
    public final static String SVC="SVC";

    public static void error(Logger logger, String type, String sid,String message) {
        logger.error("[{}] [SID={}] {}",type,sid,message);
    }

    public static void error(Logger logger, String type, String sid,String message, Object obj1) {
        logger.error("[{}] [SID={}] {}: {}", type, sid, message, JSON.toJSONString(obj1).toString());
    }

    public static  void error(Logger logger, String type, String sid,String message, Object obj1,Object obj2) {
        logger.error("[{}] [SID={}] {}: {} {}", type, sid, message, JSON.toJSONString(obj1).toString(),
                JSON.toJSONString(obj2).toString());
    }

    public static void error(Logger logger, String type, String sid,String message, Object... var2) {
        String str=message;
        if(var2!=null && message!=null){
            for(int i=0;i<var2.length;i++){
                str=str.replaceFirst("\\{\\}",var2[i].toString());
            }
        }

        String format="[{}] [SID={}] "+str;
        logger.error(format,type,sid);
    }

    public static void error(Logger logger, String type, String sid,String message, Throwable var2) {
        String str=String.format("[{}] [SID={}] {}: ",type,sid,message);
        logger.error(str, var2);
    }

    public static void warn(Logger logger, String type, String sid,String message) {
        logger.warn("[{}] [SID={}] {}",type,sid,message);
    }

    public static void warn(Logger logger, String type, String sid,String message, Object obj1) {
        logger.warn("[{}] [SID={}] {}: {}", type, sid, message, JSON.toJSONString(obj1).toString());
    }

    public static  void warn(Logger logger, String type, String sid,String message, Object obj1,Object obj2) {
        logger.warn("[{}] [SID={}] {}: {} {}", type, sid, message, JSON.toJSONString(obj1).toString(),
                JSON.toJSONString(obj2).toString());
    }

    public static void warn2(Logger logger, String type, String sid,String message, Object... var2) {
        String str=message;
        if(var2!=null && message!=null){
            for(int i=0;i<var2.length;i++){
                str=str.replaceFirst("\\{\\}",var2[i].toString());
            }
        }

        String format="[{}] [SID={}] "+str;
        logger.warn(format,type,sid);
    }

    public static void warn(Logger logger, String type, String sid,String message, Throwable var2) {
        String str=String.format("[{}] [SID={}] {}: ",type,sid,message);
        logger.warn(str, var2);
    }

    public static void info(Logger logger, String type, String sid,String message) {
        logger.info("[{}] [SID={}] {}",type,sid,message);
    }

    public static void info(Logger logger, String type, String sid,String message, Object obj1) {
        logger.info("[{}] [SID={}] {}: {}", type, sid, message, JSON.toJSONString(obj1).toString());
    }

    public static  void info(Logger logger, String type, String sid,String message, Object obj1,Object obj2) {
        logger.info("[{}] [SID={}] {}: {} {}", type, sid, message, JSON.toJSONString(obj1).toString(),
                JSON.toJSONString(obj2).toString());
    }

    public static void info2(Logger logger, String type, String sid,String message, Object... var2) {
        String str=message;
        if(var2!=null && message!=null){
            for(int i=0;i<var2.length;i++){
                str=str.replaceFirst("\\{\\}",var2[i].toString());
            }
        }

        String format="[{}] [SID={}] "+str;
        logger.info(format,type,sid);
    }

    public static void info(Logger logger, String type, String sid,String message, Throwable var2) {
        String str=String.format("[{}] [SID={}] {}: ",type,sid,message);
        logger.info(str, var2);
    }

    public static void debug(Logger logger, String type, String sid,String message) {
        logger.info("[{}] [SID={}] {}",type,sid,message);
    }

    public static void debug(Logger logger, String type, String sid,String message, Object obj1) {
        logger.info("[{}] [SID={}] {}: {}", type, sid, message, JSON.toJSONString(obj1).toString());
    }

    public static  void debug(Logger logger, String type, String sid,String message, Object obj1,Object obj2) {
        logger.info("[{}] [SID={}] {}: {} {}", type, sid, message, JSON.toJSONString(obj1).toString(),
                JSON.toJSONString(obj2).toString());
    }

    public static void debug2(Logger logger, String type, String sid,String message, Object... var2) {
        String str=message;
        if(var2!=null && message!=null){
            for(int i=0;i<var2.length;i++){
                str=str.replaceFirst("\\{\\}",var2[i].toString());
            }
        }

        String format="[{}] [SID={}] "+str;
        logger.info(format,type,sid);
    }

    public static void debug(Logger logger, String type, String sid,String message, Throwable var2) {
        String str=String.format("[{}] [SID={}] {}: ",type,sid,message);
        logger.info(str, var2);
    }

    public static void trace(Logger logger, String type, String sid,String message) {
        logger.info("[{}] [SID={}] {}",type,sid,message);
    }

    public static void trace(Logger logger, String type, String sid,String message, Object obj1) {
        logger.info("[{}] [SID={}] {}: {}", type, sid, message, JSON.toJSONString(obj1).toString());
    }

    public static  void trace(Logger logger, String type, String sid,String message, Object obj1,Object obj2) {
        logger.info("[{}] [SID={}] {}: {} {}", type, sid, message, JSON.toJSONString(obj1).toString(),
                JSON.toJSONString(obj2).toString());
    }

    public static void trace2(Logger logger, String type, String sid,String message, Object... var2) {
        String str=message;
        if(var2!=null && message!=null){
            for(int i=0;i<var2.length;i++){
                str=str.replaceFirst("\\{\\}",var2[i].toString());
            }
        }

        String format="[{}] [SID={}] "+str;
        logger.info(format,type,sid);
    }

    public static void trace(Logger logger, String type, String sid,String message, Throwable var2) {
        String str=String.format("[{}] [SID={}] {}: ",type,sid,message);
        logger.info(str, var2);
    }
}
