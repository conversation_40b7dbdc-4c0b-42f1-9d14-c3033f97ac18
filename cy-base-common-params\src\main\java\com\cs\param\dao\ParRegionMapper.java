package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParRegionCommon;
import com.cs.param.execl.ProvinceData;
import com.cs.param.model.ParRegionModel;

@Repository
public interface ParRegionMapper {

	int insertParRegion(ParRegionCommon common) throws SQLException;

	int updateParRegionByPK(ParRegionCommon common) throws SQLException;

	int deleteParRegionByCode(ParRegionCommon common) throws SQLException;

	List<ParRegionModel> getParRegionListByCond(ParRegionCommon common) throws SQLException;

	Integer queryCountByCode(ParRegionCommon common) throws SQLException;

	List<ProvinceData> queryExeclData() throws SQLException;

}
