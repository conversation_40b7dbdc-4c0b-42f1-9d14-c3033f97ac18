package com.cs.aspect;

import java.lang.reflect.Method;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletRequest;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.caiyin.rule.api.base.BaseResponse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cs.param.constants.CommonResultCode;
import com.cs.param.constants.Constants;
import com.cs.param.constants.LogConstant;
import com.cs.param.utils.ResultObject;
import com.cs.param.utils.ThreadLocalUtil;
import com.cs.param.utils.ToolUtil;

import feign.FeignException;

/**
 * 
 * Feign日志切面
 * 
 * <AUTHOR>
 * @version [版本号, 2022年5月11日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
@Aspect
@Component
public class FeignLogAspect
{
    private static String localIp;
    
    public static final String cySopUserOrder = "cy-sop-user-order";
    public static final String cySopUser = "cy-sop-user";
    public static final String cyBaseCommonParams = "cy-base-common-params";
    public static final String cyBaseSms = "cy-base-sms";
    public static final String cyCoreContentEnterprise = "cy-core-content-enterprise";
    public static final String cyCorePushrule = "cy-core-pushrule";
    public static final String cyCoreUser = "cy-core-user";
    
    static
    {
        try
        {
            localIp = ToolUtil.getLocalHostAddress().getHostAddress();
        }
        catch (Exception e)
        {
            localIp = "unknown";
        }
    }
    
    /**
     * 存放初始时间
     */
    private ThreadLocal<Long> startTime = new ThreadLocal<Long>();
    
    /**
     * 检查点配置 cmi接口
     * 
     * <AUTHOR> 2018年5月16日
     * @see [类、类#方法、类#成员]
     */
    @Pointcut("execution(public * com.cs.param.services.CarryTurnService.*(..))"
    	+ "|| execution(public * com.cs.param.services.RulesService.*(..))"
    	+ "|| execution(public * com.cs.param.services.SignBoxService.*(..))"
    	+ "|| execution(public * com.cs.param.services.SysTaskService.*(..))"
    	+ "|| execution(public * com.cs.param.services.TextSignService.*(..))"
    		)
    public void webLog()
    {
    }
    
    /**
     * 拦截前操作
     * 
     * <AUTHOR> 2018年5月11日
     * @param joinPoint 切入点
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint)
    {
        // 记录下请求内容
        StringBuffer logMsgBuffer = new StringBuffer();
        // 唯一流水号
        String serialID = UUID.randomUUID().toString();
        ThreadContext.put("Feign_serialID", serialID);
        logMsgBuffer.append("serialID_").append(serialID).append(Constants.Split.EUROPE_THREE);
        //事务ID
        String tranceID = ThreadContext.get("tranceID");
        if (StringUtils.isEmpty(tranceID))
        {
            tranceID = UUID.randomUUID().toString();
            ThreadContext.put("tranceID", tranceID);
        }
        logMsgBuffer.append("sessionID_").append(tranceID).append(Constants.Split.EUROPE_THREE);
        //链路ID
        String linkID= ThreadContext.get("linkID");
        if (StringUtils.isEmpty(linkID))
        {
            linkID = UUID.randomUUID().toString();
        }
        ThreadContext.put("linkID", linkID);
        ThreadLocalUtil.set("linkID", linkID);
        //链路ID
        logMsgBuffer.append("linkID_").append(linkID).append(Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_REQ + Constants.Split.EUROPE_THREE);
        // 部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名(映射)
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 接口名称
        logMsgBuffer.append(getUrl(joinPoint) + Constants.Split.EUROPE_THREE);
        // 源产品
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        // 目标部件
        String targetName = joinPoint.getTarget().toString();
        if(StringUtils.isNotEmpty(targetName) && targetName.indexOf("name=") >= 0) {
        	targetName = targetName.substring(targetName.indexOf("name="), targetName.length());
            targetName = targetName.substring(targetName.indexOf("=")+1, targetName.indexOf(","));
        }
        logMsgBuffer.append(targetName + Constants.Split.EUROPE_THREE);
        // 错误描述
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        //响应编码
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        // 耗时 无 | 返回码 无|
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        // 请求报文
        Object[] args = joinPoint.getArgs();
        String argStr = "";
        for (int i = 0; i < args.length; i++)
        {
            Object arg = args[i];
            if (arg instanceof BeanPropertyBindingResult)
            {
                continue;
            }
            else if (arg instanceof HttpServletRequest)
            {
                continue;
            }
            if (null != arg)
            {
                argStr = JSON.toJSONString(arg);
            }
            logMsgBuffer.append(argStr.replace('\n', ' ').replace('\r', ' '));
        }
        startTime.set(System.currentTimeMillis());
        log.info(logMsgBuffer.toString());
    }
    
    /**
     * 拦截后的操作
     * 
     * <AUTHOR> 2018年5月16日
     * @param ret 返回值
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object ret)
    {
        Signature signature = joinPoint.getSignature();
        // 记录下请求内容
        StringBuffer logMsgBuffer = new StringBuffer();
        // 唯一流水号
        logMsgBuffer.append("serialID_").append(ThreadContext.get("Feign_serialID")).append(Constants.Split.EUROPE_THREE);
        // 事物ID
        logMsgBuffer.append("sessionID_").append(ThreadContext.get("tranceID")).append(Constants.Split.EUROPE_THREE);
        // 链路ID
        logMsgBuffer.append("linkID_").append(ThreadContext.get("linkID")).append(Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_RSP + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 接口名称
        String methodsName = signature.getName();
        logMsgBuffer.append(getUrl(joinPoint) + Constants.Split.EUROPE_THREE);
        // 源产品
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        // 目标部件
        String targetName = joinPoint.getTarget().toString();
        if(StringUtils.isNotEmpty(targetName) && targetName.indexOf("name=") >= 0) {
        	targetName = targetName.substring(targetName.indexOf("name="), targetName.length());
            targetName = targetName.substring(targetName.indexOf("=")+1, targetName.indexOf(","));
        }
        logMsgBuffer.append(targetName + Constants.Split.EUROPE_THREE);
        String resultMsg = "";
        String resultCode = null;
        if (null != ret)
        {
        	MethodSignature methodSignature = (MethodSignature)joinPoint.getSignature();
        	Class clazz = methodSignature.getReturnType();
        	if(clazz.getName().equals("java.lang.Boolean")) {
        		resultCode = CommonResultCode.FINAL_SUCCESS;
        		resultMsg = ret.toString();
        	} else if (clazz.getName().equals("java.lang.Integer") || "int".equals(clazz.getName())) {
        		resultCode = CommonResultCode.FINAL_SUCCESS;
        		resultMsg = ret.toString();
        	} else {
        		if(clazz.getName().equals("java.lang.String")) {
        			resultMsg = ret.toString();
        		} else {
        			resultMsg = JSON.toJSONString(ret);
        		}
        		resultCode = getResultCode(resultMsg, targetName, methodsName);
                if(StringUtils.isEmpty(resultCode))
                {
                	if(StringUtils.isNotEmpty(resultMsg))
                	{
                		resultCode = CommonResultCode.FINAL_SUCCESS;
                	} else {
                		resultCode = CommonResultCode.FINAL_SERVICE_ERROR; 
                	}
                }
        	}
        }
        else
        {
            resultCode = CommonResultCode.FINAL_SUCCESS;  
        }
        String errorMsg = "";
        if(!CommonResultCode.FINAL_SUCCESS.equals(resultCode))
        {
        	errorMsg = getErrorMsg(resultMsg);
        }
        // 错误描述
        logMsgBuffer.append(errorMsg + Constants.Split.EUROPE_THREE);
        // 返回码
        logMsgBuffer.append(resultCode + Constants.Split.EUROPE_THREE);
        // 耗时 无
        Long cost = System.currentTimeMillis() - startTime.get();
        logMsgBuffer.append(cost + Constants.Split.EUROPE_THREE);
        // 响应报文
        logMsgBuffer.append(StringUtils.isEmpty(resultMsg) ? "" : resultMsg.replace('\n', ' ').replace('\r', ' '));
        
        log.info(logMsgBuffer.toString());
        //清除缓存
        ThreadContext.remove("Feign_serialID");
    }
    
    private String getErrorMsg(String jsonResp)
    {
    	String errorMsg = "";
    	jsonResp = jsonResp.replace("\\", "");
    	ResultObject resultObject = null;
    	try {
			resultObject = JSONObject.parseObject(jsonResp, ResultObject.class);
		} catch (Exception e) {
		}
    	if(resultObject != null)
    	{
    		if(StringUtils.isNotEmpty(resultObject.getErrorMsg()))
    		{
    			return resultObject.getErrorMsg();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getErrDesc()))
    		{
    			return resultObject.getErrDesc();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getText()))
    		{
    			return resultObject.getText();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getMessage()))
    		{
    			return resultObject.getMessage();
    		}
    		if(StringUtils.isNotEmpty(resultObject.getMsg()))
    		{
    			return resultObject.getMsg();
    		}
    	}
    	return errorMsg;
    }
    
    private String getResultCode(String resultMsg, String targetName, String businessName)
    {
    	String jsonResp = resultMsg.replace("\\", "");
        String resultCode = null;
        if(targetName.equalsIgnoreCase(cyBaseSms) && "check".equals(businessName))
        {
        	Pattern pattern1 = Pattern.compile("\"checkPassed\":true");
    		Matcher matcher1 = pattern1.matcher(jsonResp);
    		resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
    		while(matcher1.find())
            {
    			resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cyBaseSms) && "dxCloudCb".equals(businessName))
        {
        	Pattern pattern1 = Pattern.compile("\"code\":\"00000000\"");
    		Matcher matcher1 = pattern1.matcher(jsonResp);
    		resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
    		while(matcher1.find())
            {
    			resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cySopUserOrder) && "contractSync".equals(businessName))
        {
        	Pattern pattern = Pattern.compile("\"resultCode\":\"1\"|\"resultCode\":1");
            Matcher matcher = pattern.matcher(jsonResp);
            resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            while(matcher.find())
            {
            	resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cySopUserOrder) && "createUnSubscribe".equals(businessName))
        {
        	Pattern pattern = Pattern.compile("\"code\":\"1\"|\"code\":\"2\"|\"code\":1|\"code\":2");
            Matcher matcher = pattern.matcher(jsonResp);
            resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            while(matcher.find())
            {
            	resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cySopUserOrder) && "virtualProdSubscribe".equals(businessName))
        {
        	Pattern pattern = Pattern.compile("\"resultCode\":\"00000000\"");
            Matcher matcher = pattern.matcher(jsonResp);
            resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            while(matcher.find())
            {
            	resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cyCoreContentEnterprise) && "createTemplate".equals(businessName))
        {
        	Pattern pattern = Pattern.compile("\"code\":\"0000\"");
            Matcher matcher = pattern.matcher(jsonResp);
            resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            while(matcher.find())
            {
            	resultCode = CommonResultCode.FINAL_SUCCESS;
            }
        }
        else if(targetName.equalsIgnoreCase(cyCorePushrule))
        {
        	BaseResponse response = null;
        	try {
        		response = JSONObject.parseObject(jsonResp, BaseResponse.class);
			} catch (Exception e) {
			}
        	if(response != null && StringUtils.isNotEmpty(response.getCode()))
        	{
        		if("1".equals(response.getCode())) 
        		{
        			resultCode = CommonResultCode.FINAL_SUCCESS;
        		} else {
        			resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
        		}
        	}
        }
        else if(targetName.equalsIgnoreCase(cyBaseCommonParams) && "getAllPackage".equals(businessName))
        {
        	resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
        	if(StringUtils.isNotEmpty(jsonResp))
        	{
        		JSONArray jsonArray = JSONArray.parseArray(jsonResp);
        		if(null != jsonArray && jsonArray.size() > 0)
        		{
        			resultCode = CommonResultCode.FINAL_SUCCESS;
        		}
        	}
        }
        else if(targetName.equalsIgnoreCase(cyCoreUser) && "getPkgList".equals(businessName))
        {
        	resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
        	if(StringUtils.isNotEmpty(jsonResp))
        	{
        		JSONArray jsonArray = JSONArray.parseArray(jsonResp);
        		if(null != jsonArray && jsonArray.size() > 0)
        		{
        			resultCode = CommonResultCode.FINAL_SUCCESS;
        		}
        	}
        }
        else
        {
        	ResultObject resultObject = null;
        	try {
				resultObject = JSONObject.parseObject(jsonResp, ResultObject.class);
			} catch (Exception e) {
			}
        	if(resultObject != null)
        	{
        		if(StringUtils.isNotEmpty(resultObject.getStatus()))
        		{
        			resultCode = resultObject.getStatus();
        		}
        		if(StringUtils.isNotEmpty(resultObject.getCode()))
        		{
        			resultCode = resultObject.getCode();
        		}
        		if(StringUtils.isNotEmpty(resultObject.getResultCode()))
        		{
        			resultCode = resultObject.getResultCode();
        		}
        		if(StringUtils.isNotEmpty(resultCode))
        		{
        			if("0".equals(resultCode) || "200".equals(resultCode))
            		{
            			resultCode = CommonResultCode.FINAL_SUCCESS;
            		} 
            		else
            		{
            			resultCode = CommonResultCode.FINAL_SERVICE_ERROR;
            		}
        		}
        	}
    	}
        return resultCode;
    }
    
    /**
     * 报错后的操作
     * 
     * @auth cWX319470 2018年5月11日
     * @see [类、类#方法、类#成员]
     */
    @AfterThrowing(throwing = "e", pointcut = "webLog()")
    public void doAfterError(JoinPoint joinPoint, Throwable e)
    {
        // 记录下请求内容
        StringBuffer logMsgBuffer = new StringBuffer();
        // 唯一流水号
        logMsgBuffer.append("serialID_").append(ThreadContext.get("Feign_serialID")).append(Constants.Split.EUROPE_THREE);
        // 事物ID
        logMsgBuffer.append("sessionID_").append(ThreadContext.get("tranceID")).append(Constants.Split.EUROPE_THREE);
        // 链路ID
        logMsgBuffer.append("linkID_").append(ThreadContext.get("linkID")).append(Constants.Split.EUROPE_THREE);
        // 接口请求响应标识
        logMsgBuffer.append(LogConstant.LOG_TYPE_RSP + Constants.Split.EUROPE_THREE);
        //部件名称
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 服务名
        logMsgBuffer.append(LogConstant.LOG_MICROSERVICE_NAME + Constants.Split.EUROPE_THREE);
        // 接口名称
        logMsgBuffer.append(getUrl(joinPoint) + Constants.Split.EUROPE_THREE);
        // 源产品
        logMsgBuffer.append(localIp + Constants.Split.EUROPE_THREE);
        // 目标部件
        String targetName = joinPoint.getTarget().toString();
        if(StringUtils.isNotEmpty(targetName) && targetName.indexOf("name=") >= 0) {
        	targetName = targetName.substring(targetName.indexOf("name="), targetName.length());
            targetName = targetName.substring(targetName.indexOf("=")+1, targetName.indexOf(","));
        }
        logMsgBuffer.append(targetName + Constants.Split.EUROPE_THREE);
        // 错误描述
        logMsgBuffer.append(Constants.Split.EUROPE_THREE);
        // 返回码
        String resultCode = "";
        String resultMessage = null;
        if (e instanceof FeignException)
        {
        	resultCode = CommonResultCode.FINAL_SYSTEM_ERROR;
            resultMessage = e.getMessage();
        }
        else
        {
            resultCode = CommonResultCode.FINAL_SYSTEM_ERROR;
        }
        
        logMsgBuffer.append(resultCode + Constants.Split.EUROPE_THREE);
        // 耗时
        Long cost = System.currentTimeMillis() - startTime.get();
        logMsgBuffer.append(cost + Constants.Split.EUROPE_THREE);
        logMsgBuffer.append(StringUtils.isEmpty(resultMessage) ? "" : resultMessage.replace('\n', ' ').replace('\r', ' '));
        log.error(logMsgBuffer.toString());
        ThreadContext.remove("Feign_serialID");
    }
    
    private String getUrl(JoinPoint joinPoint)
    {
    	Signature signature = joinPoint.getSignature();
    	String businessName = signature.getName();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        PostMapping postMapping = method.getAnnotation(PostMapping.class);
        GetMapping getMapping = method.getAnnotation(GetMapping.class);
        RequestMapping requestMapping = method.getAnnotation(RequestMapping.class);
        if(postMapping != null || getMapping != null || requestMapping != null)
        {
        	String[] value = new String[]{};
            if(postMapping != null){
                value = postMapping.value();
            }
            if(getMapping != null){
                value = getMapping.value();
            }
            if(requestMapping != null){
                value = requestMapping.value();
            }
            if(value.length > 0) {
            	if(value[0].indexOf("/") > -1) {
            		businessName = value[0].substring(value[0].lastIndexOf('/') + 1, value[0].length());
            	} else {
            		businessName = value[0];
            	}
            }
        }
        return businessName;
    }
}