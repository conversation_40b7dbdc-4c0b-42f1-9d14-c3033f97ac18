<template>
  <div class="provincial">
    <div>
      <div class="user-titler">{{$route.name}}</div>
      <div style="margin: 20px;">
        <!--查询条件-->
        <div>
          <el-form :inline="true" class="demo-form-inline" size="small">
            <el-form-item label="指标监控:">
              <el-select v-model="quota" placeholder="请选择">
                <el-option v-for="item in quotaNameList" :label="item" :value="item" :key="item"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="quotaChange">查询</el-button>
            </el-form-item>
            <br>
          </el-form>
        </div>
        <!--表格-->
        <el-table
          :data="quotaList"
          border
          :header-cell-class-name="tableheaderClassNameZ"
          class="app-tab02"
        >
          <el-table-column prop="name" label="监控指标"/>
          <el-table-column label="阈值">
            <template slot-scope="scope">
              <span>{{scope.row.name == "订购关系差异量" ? scope.row.alertValue : scope.row.alertValue + '%'}}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="setthreshold(scope.row)">阈值配置</el-button>
              <el-button type="text" size="small" @click="setPeople(scope.row)">人员配置</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog :title="currentName + '阈值'" :visible.sync="dialogFormVisible" width="500px">
      <el-form :model="alertForm">
        <el-form-item
          label="阈值:"
          :label-width="formLabelWidth"
          prop="alertValue"
          :rules="[
            { required: true, trigger: 'blur', message: '请输入阈值'},
            { type: 'number', trigger: ['blur', 'change'], message: '请输入正确的阈值'}
          ]"
        >
          <el-input v-model.number="alertForm.alertValue">
            <template v-if="currentName != '订购关系差异量'" slot="append">%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="updateAppend">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="currentName + '应急人员'" :visible.sync="outerVisible">
      <col>
      <el-button
        type="primary"
        size="small"
        @click="addVisibleShow"
        style="float: right; margin-bottom: 10px;"
      >新增人员</el-button>
      <col>
      <!--表格-->
      <el-table
        :data="peopleTableData"
        border
        :header-cell-class-name="tableheaderClassNameZ"
        class="app-tab02"
      >
        <el-table-column prop="name" label="姓名" width="120"/>
        <el-table-column prop="phone" label="联系电话" width="150" />
        <el-table-column prop="mail" label="邮箱"></el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="editPeople(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="deletePeople(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog width="500px" title="新增告警人员" :visible.sync="addVisible" :before-close="handleDialogClose" append-to-body>
        <el-form :model="addForm" :rules="rules" ref="addForm">
          <el-form-item label="姓名:" prop="name" :label-width="formLabelWidth">
            <el-input v-model="addForm.name"></el-input>
          </el-form-item>
          <el-form-item label="联系电话:" prop="phone" :label-width="formLabelWidth">
            <el-input v-model="addForm.phone"></el-input>
          </el-form-item>
          <el-form-item label="邮箱:" prop="mail" :label-width="formLabelWidth">
            <el-input v-model="addForm.mail"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelPeople('addForm')">取 消</el-button>
          <el-button type="primary" @click="addPeople('addForm')">提交</el-button>
        </div>
      </el-dialog>
      <el-dialog width="500px" title="编辑告警人员" :visible.sync="editVisible" append-to-body>
        <el-form :model="editForm" :rules="rules" ref="editForm">
          <el-form-item label="姓名:" prop="name" :label-width="formLabelWidth">
            <el-input v-model="editForm.name"></el-input>
          </el-form-item>
          <el-form-item label="联系电话:" prop="phone" :label-width="formLabelWidth">
            <el-input v-model="editForm.phone"></el-input>
          </el-form-item>
          <el-form-item label="邮箱:" prop="mail" :label-width="formLabelWidth">
            <el-input v-model="editForm.mail"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="editVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitEditPeople('editForm')">提交</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { get, post, postFromData } from "@/servers/httpServer.js";
import { dowandFile } from "@/util/core.js";
export default {
  name: "provincial",
  data() {
    const phone = (rule, value, callback) => {
      if (value == "" || value == undefined) {
        callback(new Error("请输入手机号"));
      } else if (value.length != 11) {
        callback(new Error("请输入11位手机号"));
      } else if (!(/^1([0-9][0-9])\d{8}$/.test(value))) {
        callback(new Error("请输入正确格式的手机号"));
      } else {
        callback();
      }
    };
    return {
      rules: {
        name: [
          { required: true, message: "请输入姓名", trigger: ["blur", "change"] },
        ],
        phone: [
          { validator: phone, required: true, trigger: ["blur", "change"] },
        ],
        mail: [
          { required: true, message: "请输入邮箱", trigger: "blur" },
          { type: "email", trigger: ["blur", "change"], message: "请输入合法邮箱" }
        ]
      },
      quota: "全部",
      quotaNameList: ["全部", "订购成功率", "订购结果通知率", "订购关系差异量", "内容同步成功率", "规则同步成功率",
        "下行通道投递-本网成功率", "下行通道投递-号百成功率", "下行通道投递-联通在线成功率","下行通道投递-嘉讯成功率", "下行通道投递-彩讯成功率"],
      quotaList: [],
      dialogFormVisible: false,
      alertForm: {
        alertValue: ""
      },
      currentId: "",
      currentName: "",
      outerVisible: false,
      peopleTableData: [],
      addVisible: false,
      editVisible: false,
      addForm: {},
      editForm: {
        name: "",
        phone: "",
        mail: ""
      },
      currentPeopleId: "",
      pagation: {},
      formLabelWidth: "100px"
    };
  },
  created() {
    this.queryList();
  },
  methods: {
    queryList() {
      get("stat/alter", {}).then(res => {
        const data = res.data;
        if (data.code == 0) {
          if(!data.data) {
            this.quotaList = [];
            // this.quotaNameList = ['全部'];
          } else {
            this.quotaList = data.data;
            this.searchQuotaList = Object.assign([], data.data);
            // this.quotaNameList = ['全部'].concat(Array.from(new Set(data.data.map(_ => _.name))));
          }
        }
      });
    },
    quotaChange() {
      if(this.searchQuotaList.length == 0) return;
      if(this.quota == '全部') {
        this.quotaList = this.searchQuotaList;
        return
      }
      this.quotaList = this.searchQuotaList.reduce((prev, item, index) => {
        return item.name == this.quota ? prev.concat(item) : prev
      }, [])
    },
    setthreshold(row) {
      this.currentName = row.name;
      this.alertForm.alertValue = row.alertValue;
      this.dialogFormVisible = true;
      this.currentId = row.id;
    },
    updateAppend() {
      let formData = new FormData();
      formData.append("id", this.currentId);
      formData.append("v", this.alertForm.alertValue);
      postFromData("stat/alter/update", formData).then(res => {
        const data = res.data;
        if (data.code == 0) {
          this.dialogFormVisible = false;
          this.queryList();
          this.$message({
            type: "success",
            message: "修改成功!"
          });
        }
      });
    },
    setPeople(row) {
      this.peopleTableData = [];

      this.currentName = row.name;
      this.currentId = row.id;
      this.queryPeopleList(row.id);
    },
    queryPeopleList(id) {
      get("stat/alter/man/get", { id }).then(res => {
        const data = res.data;
        if (data.code == 0) {
          if(!data.data) {
            this.peopleTableData = [];
          } else {
            this.peopleTableData = data.data;
          }
        }
        this.outerVisible = true;

      });
    },
    editPeople(row) {
      this.editVisible = true;
      this.currentPeopleId = row.id;
      this.editForm = {
        name: row.name,
        mail: row.mail,
        phone: row.phone
      };
    },
    submitEditPeople(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          post("stat/alter/man/post", {
            id: this.currentPeopleId,
            alterId: this.currentId,
            ...this.editForm
          }).then(res => {
            const data = res.data;
            if (data.code == 0) {
              this.editVisible = false;
              this.queryPeopleList(this.currentId);
              this.$message({
                type: "success",
                message: "编辑成功!"
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    deletePeople(row) {
      this.$confirm("确认删除应急人员信息?", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      })
        .then(() => {
          post(`stat/alter/man/del?id=${row.id}`, {}).then(res => {
            const data = res.data;
            if (data.code == 0) {
              this.$message({
                type: "success",
                message: "删除成功!"
              });
              this.queryPeopleList(this.currentId);
            }
          });
        })
        .catch(() => {});
    },
    addVisibleShow() {
      this.addVisible = true;
      this.addForm = {
        name: "",
        phone: "",
        mail: ""
      };
    },
    addPeople(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          post("stat/alter/man/post", {
            id: null,
            alterId: this.currentId,
            ...this.addForm
          }).then(res => {
            const data = res.data;
            if (data.code == 0) {
              this.addVisible = false;
              this.queryPeopleList(this.currentId);
              this.$message({
                type: "success",
                message: "新增成功!"
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    cancelPeople(formName) {
      this.addVisible = false 
      this.$refs[formName].resetFields()
    },
    handleDialogClose() {
      this.addVisible = false 
      this.$refs['addForm'].resetFields()
    },
    tableheaderClassNameZ({ row, rowIndex }) {
      return "table-head-thz";
    }
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.el-form-item {
  margin-bottom: 22px !important;
}

.el-table .table-head-thz {
  background-color: #f5f5f5;
}

</style>
