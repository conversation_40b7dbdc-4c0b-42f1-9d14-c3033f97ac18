package com.cs.param.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.common.ParCountryCommon;
import com.cs.param.common.ParPartitionCommon;
import com.cs.param.common.ParProvinceCommon;
import com.cs.param.common.ParRegionCommon;
import com.cs.param.common.ResultCommon;
import com.cs.param.dao.ParCountryMapper;
import com.cs.param.dao.ParPartitionMapper;
import com.cs.param.dao.ParProvinceMapper;
import com.cs.param.dao.ParRegionMapper;
import com.cs.param.execl.ProvinceData;
import com.cs.param.model.ParCountryModel;
import com.cs.param.model.ParPartitionModel;
import com.cs.param.model.ParProvinceModel;
import com.cs.param.model.ParRegionModel;
import com.cs.param.utils.LogUtil;
import com.cy.common.CySysLog;
import com.github.crab2died.ExcelUtils;

/**
 * 
 * 省市管理Controller
 *
 */
@RequestMapping("/regionMgt")
@RestController
public class RegionMatController {

	private static final Logger log = LoggerFactory.getLogger(RegionMatController.class);

	@Autowired
	private ParCountryMapper parCountryMapper;

	@Autowired
	private ParPartitionMapper parPartitionMapper;

	@Autowired
	private ParProvinceMapper parProvinceMapper;

	@Autowired
	private ParRegionMapper parRegionMapper;

	/**
	 * 
	 * 获取国家列表
	 *
	 */
	@RequestMapping(value = "getCountry")
	public List<ParCountryModel> getCountry() throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getCountry", "获取国家列表");
		try {
			return parCountryMapper.getParCountryListByCond(new ParCountryCommon());
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 新增或修改国家列表
	 *
	 */
	@RequestMapping(value = "editorCountry")
	@CySysLog(methodName = "新增或修改国家列表", modularName = "公参模块", optContent = "新增或修改国家列表")
	public ResultCommon editorCountry(@ModelAttribute("common") ParCountryCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "editorCountry", "新增或修改国家列表", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			// 0：修改，1：添加
			if ("1".equals(common.getIsAdd())) {
				// 当为添加时校验code唯一性
				if (parCountryMapper.queryCountByCode(common) > 0) {
					result.setResText("添加失败，此国家code:" + common.getCountryCode() + ",已存在");
					return result;
				} else if (parCountryMapper.insertParCountry(common) > 0) {
					result.setResStatus(0);
				}
			} else if ("0".equals(common.getIsAdd())) {
				if (parCountryMapper.updateParCountryByPK(common) > 0) {
					result.setResStatus(0);
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除国家
	 *
	 */
	@RequestMapping(value = "deleteCountry")
	@CySysLog(methodName = "删除国家", modularName = "公参模块", optContent = "删除国家")
	public ResultCommon deleteCountry(@ModelAttribute("common") ParCountryCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteCountry", "删除国家", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			parCountryMapper.deleteParCountryByCode(common);
			ParPartitionCommon parPartitionCommon = new ParPartitionCommon();
			parPartitionCommon.setCountryCode(common.getCountryCode());
			parPartitionMapper.deleteParPartitionByCode(parPartitionCommon);
			ParProvinceCommon parProvinceCommon = new ParProvinceCommon();
			parProvinceCommon.setCountryCode(common.getCountryCode());
			parProvinceMapper.deleteParProvinceByCode(parProvinceCommon);
			ParRegionCommon parRegionCommon = new ParRegionCommon();
			// parRegionCommon.setCountryCode(common.getCountryCode());
			parRegionMapper.deleteParRegionByCode(parRegionCommon);
			result.setResStatus(0);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取分区列表
	 *
	 */
	@RequestMapping(value = "getPartition")
	public List<ParPartitionModel> getPartition(@ModelAttribute("common") ParPartitionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getPartition", "获取分区列表", common);
		try {
			return parPartitionMapper.getParPartitionListByCond(common);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 新增或修改分区列表
	 *
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@RequestMapping(value = "editorPartition")
	@CySysLog(methodName = "新增或修改分区列表", modularName = "公参模块", optContent = "新增或修改分区列表")
	public ResultCommon editorPartition(@ModelAttribute("common") ParPartitionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "editorPartition", "新增或修改分区列表", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			// 0：修改，1：添加,当为添加时校验code唯一性
			if ("1".equals(common.getIsAdd())) {
				// 当为添加时校验code唯一性
				if (parPartitionMapper.queryCountByCode(common) > 0) {
					result.setResText("添加失败，此分区code:" + common.getPartitionCode() + "已存在");
					return result;
				} else if (parPartitionMapper.insertParPartition(common) > 0) {
					String provinceCodes = common.getProvinceCodes();
					Map params = new HashMap();
					if (provinceCodes != null) {
						params.put("partitionCode", common.getPartitionCode());
						params.put("provinceCodes", provinceCodes.split(","));
						parPartitionMapper.insertProvinceCodes(params);
					}
					result.setResStatus(0);
				}
			} else if ("0".equals(common.getIsAdd())) {
				String provinceCodes = common.getProvinceCodes();
				Map params = new HashMap();
				if (provinceCodes != null) {
					params.put("partitionCode", common.getPartitionCode());
					params.put("provinceCodes", provinceCodes.split(","));
					parPartitionMapper.updateProvinceCodes(params);
					parPartitionMapper.insertProvinceCodes(params);
				}
				parPartitionMapper.updateParPartitionByPK(common);
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除分区
	 *
	 */
	@RequestMapping(value = "deletePartition")
	@CySysLog(methodName = "删除分区", modularName = "公参模块", optContent = "删除分区")
	public ResultCommon deletePartition(@ModelAttribute("common") ParPartitionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deletePartition", "删除分区", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			parPartitionMapper.deleteParPartitionByCode(common);
			parProvinceMapper.updatePartitionCode(common.getPartitionCode());
			result.setResStatus(0);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取省份列表
	 *
	 */
	@RequestMapping(value = "getProvince")
	public List<ParProvinceModel> getProvince(@ModelAttribute("common") ParProvinceCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getProvince", "获取省份列表", common);
		try {
			// 如果分区code为空，则返回国家code下所有省份
			if (null == common.getPartitionCode() || "null".equals(common.getPartitionCode())
					|| "".equals(common.getPartitionCode())) {
				return parProvinceMapper.getParProvinceListByCond(common);
			} else {// 如果分区code不为空，则返回分区下所有省份对象
				ParPartitionCommon pc = new ParPartitionCommon();
				pc.setPartitionCode(common.getPartitionCode());
				List<ParPartitionModel> plist = parPartitionMapper.getParPartitionListByCond(pc);
				if (plist.size() > 0) {
					ParPartitionModel pm = plist.get(0);
					if (null != pm.getProvinceCodes()) {
						String[] pCodes = pm.getProvinceCodes().split(",");
						return parProvinceMapper.getParProvinceListByCodes(pCodes);
					} else {
						return null;
					}
				}
				return null;
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 新增或修改省份
	 *
	 */
	@RequestMapping(value = "editorProvince")
	@CySysLog(methodName = "新增或修改省份", modularName = "公参模块", optContent = "新增或修改省份")
	public ResultCommon editorProvince(@ModelAttribute("common") ParProvinceCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "editorProvince", "新增或修改省份", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			// 0：修改，1：添加,当为添加时校验code唯一性
			if ("1".equals(common.getIsAdd())) {
				// common.setJoinTime(DateUtil.parseDate(new Date(),
				// DateUtil.DEFAULT_TIMESTAMP));
				// 当为添加时校验code唯一性
				if (parProvinceMapper.queryCountByCode(common) > 0) {
					result.setResText("添加失败，此省份code:" + common.getProvinceCode() + ",已存在");
					return result;
				} else if (parProvinceMapper.insertParProvince(common) > 0) {
					result.setResStatus(0);
				}
			} else if ("0".equals(common.getIsAdd())) {
				if (parProvinceMapper.updateParProvinceByPK(common) > 0) {
					result.setResStatus(0);
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除省份
	 *
	 */
	@RequestMapping(value = "deleteProvince")
	@CySysLog(methodName = "删除省份", modularName = "公参模块", optContent = "删除省份")
	public ResultCommon deleteProvince(@ModelAttribute("common") ParProvinceCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteProvince", "删除省份", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			parProvinceMapper.deleteParProvinceByCode(common);
			ParRegionCommon parRegionCommon = new ParRegionCommon();
			parRegionCommon.setProvinceCode(common.getProvinceCode());
			parRegionMapper.deleteParRegionByCode(parRegionCommon);
			result.setResStatus(0);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 获取地区列表
	 *
	 */
	@RequestMapping(value = "getRegion")
	public List<ParRegionModel> getRegion(@ModelAttribute("common") ParRegionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getRegion", "获取地区列表", common);
		try {
			return parRegionMapper.getParRegionListByCond(common);
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
			return null;
		}
	}

	/**
	 * 
	 * 新增或修改地区
	 *
	 */
	@RequestMapping(value = "editorRegion")
	@CySysLog(methodName = "新增或修改地区", modularName = "公参模块", optContent = "新增或修改地区")
	public ResultCommon editorRegion(@ModelAttribute("common") ParRegionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "editorRegion", "新增或修改地区", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			// 0：修改，1：添加,当为添加时校验code唯一性
			if ("1".equals(common.getIsAdd())) {
				// 当为添加时校验code唯一性
				if (parRegionMapper.queryCountByCode(common) > 0) {
					result.setResText("添加失败，此地区code:" + common.getRegionCode() + ",已存在");
					return result;
				} else if (parRegionMapper.insertParRegion(common) > 0) {
					result.setResStatus(0);
				}
			} else if ("0".equals(common.getIsAdd())) {
				if (parRegionMapper.updateParRegionByPK(common) > 0) {
					result.setResStatus(0);
				}
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 删除地区
	 *
	 */
	@RequestMapping(value = "deleteRegion")
	@CySysLog(methodName = "删除地区", modularName = "公参模块", optContent = "删除地区")
	public ResultCommon deleteRegion(@ModelAttribute("common") ParRegionCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "deleteRegion", "删除地区", common);
		ResultCommon result = new ResultCommon();
		result.setResStatus(1);
		try {
			if (parRegionMapper.deleteParRegionByCode(common) > 0) {
				result.setResStatus(0);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "addParOtherConf", "添加其他配置出错！", e);
		}
		return result;
	}

	/**
	 * 
	 * 省市数据导出Execl
	 *
	 */
	@RequestMapping(value = "exportExecl")
	@CySysLog(methodName = "省市数据导出Execl", modularName = "公参模块", optContent = "省市数据导出Execl")
	@ResponseBody
	public String exportExecl(HttpServletResponse response) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "exportExecl", "省市数据导出Execl");
		response.setContentType("application/binary;charset=UTF-8");
		ServletOutputStream out = response.getOutputStream();
		List<ProvinceData> list = parRegionMapper.queryExeclData();
		ExcelUtils.getInstance().exportObjects2Excel(list, ProvinceData.class, true, null, true, out);
		return "success";
	}

}
