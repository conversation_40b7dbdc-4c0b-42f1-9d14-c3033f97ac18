package com.cy.user.cs.request;

/**
 * 
 * <AUTHOR> @date
 * @Descriptionv 彩印平台请求一级能力平台订购/退订共同信息
 */
public class ContractReq {

	/**
	 * 请求头
	 */

	protected Header requestHeader;

	/**
	 * 登入账号类型默认手机号
	 */
	protected String userAccountType = "0";

	/**
	 * 登入账号即用户手机号
	 */
	protected String userAccount ;

	/**
	 * 推广渠道
	 */
	protected String channelId;

	/**
	 * 订购产品code
	 */

	protected String productCode;

	/**
	 * 是否自动生效 默认立即生效 1：立即生效 2：次月生效
	 * 
	 */
	protected String valdateType = "1";

	/**
	 * 支付方式 默认话费支付
	 */

	protected String payType = "0";

	/**
	 * 调用厂商来源
	 */
	private String source;

	private String isSecondConfirm;

	public void setIsSecondConfirm(String isSecondConfirm){
		this.isSecondConfirm = isSecondConfirm;
	}


	public String getIsSecondConfirm(){
		return isSecondConfirm;
	}


	/**
	 * @return the requestHeader
	 */
	public Header getRequestHeader() {
		return requestHeader;
	}

	/**
	 * @param requestHeader
	 *            the requestHeader to set
	 */
	public void setRequestHeader(Header requestHeader) {
		this.requestHeader = requestHeader;
	}

	/**
	 * @return the userAccountType
	 */
	public String getUserAccountType() {
		return userAccountType;
	}

	/**
	 * @param userAccountType
	 *            the userAccountType to set
	 */
	public void setUserAccountType(String userAccountType) {
		this.userAccountType = userAccountType;
	}

	/**
	 * @return the userAccount
	 */
	public String getUserAccount() {
		return userAccount;
	}

	/**
	 * @param userAccount
	 *            the userAccount to set
	 */
	public void setUserAccount(String userAccount) {
		this.userAccount = userAccount;
	}

	/**
	 * @return the channelId
	 */
	public String getChannelId() {
		return channelId;
	}

	/**
	 * @param channelId
	 *            the channelId to set
	 */
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	/**
	 * @return the productCode
	 */
	public String getProductCode() {
		return productCode;
	}

	/**
	 * @param productCode
	 *            the productCode to set
	 */
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	/**
	 * @return the valdateType
	 */
	public String getValdateType() {
		return valdateType;
	}

	/**
	 * @param valdateType
	 *            the valdateType to set
	 */
	public void setValdateType(String valdateType) {
		this.valdateType = valdateType;
	}

	/**
	 * @return the payType
	 */
	public String getPayType() {
		return payType;
	}

	/**
	 * @param payType
	 *            the payType to set
	 */
	public void setPayType(String payType) {
		this.payType = payType;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "requestHeader=" + requestHeader + ", userAccountType=" + userAccountType + ", userAccount="
				+ userAccount + ", channelId=" + channelId + ", productCode=" + productCode + ", valdateType="
				+ valdateType + ", payType=" + payType;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
}
