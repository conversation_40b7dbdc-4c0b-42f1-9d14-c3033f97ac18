<template scope="scope">
  <div>
    <div class="user-titler">咪咕名片审核明细</div>
    <!--企业彩印-->
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline" label-width="70px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="手机号码">
              <el-input v-model="searchReq.mobile" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="姓名">
              <el-input v-model="searchReq.name" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="公司名称">
              <el-input v-model="searchReq.companyName" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="审核时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核人">
              <el-input v-model="searchReq.auditBy" class="inputWidth" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="提交时间">
              <div class="block">
                <el-date-picker
                  v-model="searchReq.timearr"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :default-time="['00:00:00', '23:59:59']"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  size="small"
                ></el-date-picker>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column prop="mobile" label="手机号码" width="140"></el-table-column>
        <el-table-column prop="name" label="姓名" width="120"></el-table-column>
        <el-table-column prop="companyName" label="公司名称" width="250"></el-table-column>
        <el-table-column prop="reject_field" label="驳回字段" width="100"></el-table-column>
        <el-table-column prop="createTime" label="提交时间" width="200"></el-table-column>
        <el-table-column prop="auditBy" label="审核人" width="100"></el-table-column>
        <el-table-column prop="auditTime" label="审核时间" width="200"></el-table-column>
        <el-table-column fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="goDetail(scope.row)"
            >详情</el-button>
           <!-- <el-button
              type="text"
              size="small"
              @click="rejectVisible=true;row=scope.row"
            >撤销</el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.pageIndex"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>
    </div>
    <div>
      <el-dialog
        width="30%"
        title="撤销"
        :visible.sync="rejectVisible"
        append-to-body
        :close-on-click-modal="false"
      >
        <div>是否撤销该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="rejectVisible = false;cancel(row)">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import moment from "moment";

export default {
  data() {
    return {
      row: "",
      rejectVisible: false,
      tableLoading: false,
      pageTotal: 0, //总条数
      //查询条件
      searchReq: {
        name: "",
        companyName: "",
        mobile: "",
        startTime: "",
        endTime: "",
        startAuditTime: "",
        endAuditTime: "",
        auditBy: "", //审核人
        timearr: [], //提交时间
        timearr1: [], //审核时间
        pageSize: 10,
        pageIndex: 1
      },
      //数据表
      tableData: []
    };
  },
  created() {
    this.search();
  },
  methods: {
    //查询请求
    search: function() {
      this.tableLoading = true;
      //提交时间
      if (this.searchReq.timearr) {
        this.searchReq.startTime = this.searchReq.timearr[0]
          ? moment(new Date(this.searchReq.timearr[0])).format("YYYY-MM-DD")
          : "";
        this.searchReq.endTime = this.searchReq.timearr[1]
          ? moment(new Date(this.searchReq.timearr[1])).format("YYYY-MM-DD")
          : "";
      } else {
        this.searchReq.startTime = "";
        this.searchReq.endTime = "";
      }
      //审核时间
      if (this.searchReq.timearr1) {
        this.searchReq.startAuditTime = this.searchReq.timearr1[0]
          ? moment(new Date(this.searchReq.timearr1[0])).format(
              "YYYY-MM-DD"
            )
          : "";
        this.searchReq.endAuditTime = this.searchReq.timearr1[1]
          ? moment(new Date(this.searchReq.timearr1[1])).format(
              "YYYY-MM-DD"
            )
          : "";
      } else {
        this.searchReq.startAuditTime = "";
        this.searchReq.endAuditTime = "";
      }
      const { timearr, timearr1, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/card/list`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            this.tableData = res.data.map(item => ({
              ...item,
              auditTime: moment(new Date(item.auditTime)).format(
              "YYYY-MM-DD HH:mm:ss"
            )
            }));
            this.pageTotal = res.count;
          } else {
            this.tableData = [];
            this.pageTotal = 0;
          }
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageIndex = 1;
      //每页条数
      this.searchReq.pageSize = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.pageIndex = val;
      this.search();
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    cancel(row) {
      const { uuid, auditBy } = row;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/card/backout`,
          JSON.stringify({
            uuid,
            auditBy
          }),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          res = res.data;
          if (res.code == 0) {
            this.$message({
              type: 'success',
              message: '撤销成功!'
            });
            this.search();
          } else {
            this.$message("撤销失败");
          }
        });
    },
    goDetail(row) {
      this.$router.push({ path: "miguCardviewVerifyDetails", query: { uuid: row.uuid, auditBy: row.auditBy} })
    }
  }
};
</script>
<style scoped>
.inputWidth {
  width: 160px !important;
}
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}
.content-title {
  margin-top: 20px;
  margin-left: 20px;
  background-color: white;
}
.content-line {
  margin-top: 20px;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 20px;
  margin-left: 20px;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
</style>
<style>
.el-table .table-head-th {
  background-color: #f5f5f5;
}
</style>
