<template>
            <div>
                <h1 class="user-title">系统短信</h1>
                <div class="user-line"></div>
                <div class="app-search">
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                        <el-form-item label="模板ID">
                            <el-input  v-model="searchForm.id" placeholder="" size="small" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="searchForm.status" clearable placeholder="请选择" size="small" class="app-input">
                                <el-option
                                        v-for="item in statusList"
                                        :key="item.key"
                                        :label="item.value"
                                        :value="item.key">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="发送内容">
                            <el-input   v-model="searchForm.orderType" placeholder="" size="small" :maxlength="50" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <!--<el-form :model="searchForm" :inline="true" class="demo-form-inline">-->
                        <!--<el-form-item>-->
                            <!--<el-button type="primary" @click="addVisible = true">新增模板</el-button>-->
                        <!--</el-form-item>-->
                    <!--</el-form>-->

                </div>

                <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                          :header-cell-class-name="tableheaderClassName">
                    <el-table-column prop="id" label="模板ID" width="100"/>
                    <el-table-column prop="content" label="发送内容" width="500" :show-overflow-tooltip="true"/>
                    <el-table-column prop="status" label="状态" :formatter="formatStatus"  width="80"/>
                    <el-table-column prop="acceptNo" label="接收号码" :formatter="formatAccept" width="120"/>
                    <el-table-column prop="sendNo" label="发送源号码" width="120"/>
                    <el-table-column prop="triggerSceneId" label="触发场景" width="300" :show-overflow-tooltip="true"/>
                    <el-table-column prop="updateTime" label="更新时间"  width="180"/>
                </el-table>

                <div class="block app-pageganit">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>

                <div>
                    <el-dialog title="新增系统用户" :visible.sync="addVisible" :before-close="handleClose" @open="resetForm('addForm')">
                        <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                            <el-form-item label="触发场景 : " prop="triggerSceneId">
                                <el-select v-model="addForm.triggerSceneId" placeholder="请选择">
                                    <el-option
                                            v-for="item in triggerSceneList"
                                            :key="item.triggerSceneId"
                                            :label="item.triggerSceneName"
                                            :value="item.triggerSceneId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="接收号码 : " prop="acceptNo">
                                <el-select v-model="addForm.acceptNo" placeholder="请选择">
                                    <el-option
                                            v-for="item in acceptNoList"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="短信模板内容 : " prop="content">
                                <el-input v-model="addForm.content" type="textarea"></el-input>
                            </el-form-item>
                            <el-form-item label="发送源号码 : " prop="sendNo">
                                <el-select v-model="addForm.sendNo" placeholder="请选择">
                                    <el-option
                                            v-for="item in sendNoList"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer" style="text-align: right;">
                            <el-button @click="addVisible = false">取 消</el-button>
                            <el-button type="primary" @click="addSms('addForm')">确 定</el-button>
                        </div>
                    </el-dialog>
                </div>

                <div>
                    <el-dialog title="编辑系统用户" :visible.sync="editVisible" :before-close="handleClose">
                        <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                            <el-form-item label="短信模板ID : ">
                                {{editForm.id}}
                            </el-form-item>
                            <el-form-item label="触发场景 : " prop="triggerSceneId">
                                <el-select v-model="editForm.triggerSceneId" placeholder="请选择">
                                    <el-option
                                            v-for="item in triggerSceneList"
                                            :key="item.triggerSceneId"
                                            :label="item.triggerSceneName"
                                            :value="item.triggerSceneId">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="接收号码 : " prop="acceptNo">
                                <el-select v-model="editForm.acceptNo" placeholder="请选择">
                                    <el-option
                                            v-for="item in acceptNoList"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="短信模板内容 : " prop="content">
                                <el-input v-model="editForm.content" type="textarea"></el-input>
                            </el-form-item>
                            <el-form-item label="发送源号码 : " prop="sendNo">
                                <el-select v-model="editForm.sendNo" placeholder="请选择">
                                    <el-option
                                            v-for="item in sendNoList"
                                            :key="item.key"
                                            :label="item.value"
                                            :value="item.key">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer" style="text-align: right;">
                            <el-button @click="editVisible = false">取 消</el-button>
                            <el-button type="primary" @click="editSms('editForm')">确 定</el-button>
                        </div>
                    </el-dialog>
                </div>

                <el-dialog
                        title="提示"
                        :visible.sync="propVisible"
                        width="30%"
                        :before-close="handleCloseConfirm">
                    <span>{{propMsg}}</span>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="propVisible = false">确 定</el-button>
                    </span>
                </el-dialog>

            </div>

</template>
<script>
    export default {
        data() {
            return {
                activeName:'smsSysDef',
                propVisible:false,
                propMsg:'',
                editVisible:false,
                addVisible: false,
                searchForm: {
                    id:'',
                    status:'',
                    content:'',
                    pageSize:10,// 每页显示条数
                    pageNum :1
                },
                addForm:{
                    status:'',
                    acceptNo:'',
                    acceptPhone:'',
                    content:'',
                    sendNo:'',
                    triggerSceneId:''
                },
                //编辑form对象定义
                editForm: {
                    id:'',
                    status:"",//状态：0：关闭，1开启，新增时默认0，不可选
                    acceptNo:"",//接收号码：0：所有用户 ，1：彩印用户，2：指定号码
                    acceptPhone:"",//接收号码为2，则需要选择号码
                    content:"",
                    sendNo:"",//发送源号码:1065,8086
                    triggerSceneId:""
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'',
                },

                statusList:[
                    {
                        key:'',
                        value:'全部'
                    },
                    {
                        key:'0',
                        value:'禁用'
                    },{
                        key:'1',
                        value:'启用'
                    }
                ],
                sendNoList:[
                    {key:'1065', value:'1065'},
                    {key:'8086', value:'8086'},
                ],
                acceptNoList:[
                    {
                        key:'0',
                        value:'所有用户'
                    },{
                        key:'1',
                        value:'彩印用户'
                    },{
                        key:'2',
                        value:'指定号码'
                    }

                ],
                triggerSceneList:[],
                rules: {
                    acceptNo: [
                        { required: true, message: '请选择接收号码', trigger: 'blur' },
                    ],
                    triggerSceneId: [
                        { required: true, message: '请选择触发场景', trigger: 'blur' },
                    ],
                    sendNo: [
                        { required: true, message: '请选择发送源号码', trigger: 'blur' },
                    ],
                    content: [
                        { required: true, message: '请输入短信模板内容', trigger: 'blur' },
                    ]
                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
            this.getRiggerSceneList();
            this.search();
        },
        methods: {

            //开关开启关闭
            switchStatus(id){
                this.queryOrDelForm.id = id;
                this.$http.post(`${this.proxyUrl}/param/smsMgt/openOrCloseOpeSms`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    if(res.data.resStatus == 0){
                        this.propMsg = '修改成功！';
                        this.propVisible=true;
                        this.editVisible = false;
                        this.search(this.searchForm);
                    }else{
                        this.propMsg = '修改失败!'+ res.data.resText;;
                        this.propVisible=true;
                    }
                })
            },
            formatAccept: function (row, column, cellValue){
                if (cellValue === "0"){
                    return '所有';
                }else if (cellValue === "1"){
                    return '仅业务用户';
                }else if (cellValue === "2"){
                    return '系统用户';
                }
            },
            formatStatus: function (row, column, cellValue) {
                if (cellValue === "1"){
                    return '启用';
                }else if (cellValue === "0"){
                    return '禁用';
                }
            },
            //获取省份list
            getRiggerSceneList:function(){
                this.$http.get(`${this.proxyUrl}/param/smsMgt/getAllParTriSce`).then(function(res){
                    this.triggerSceneList=res.data;
                })
            },
            sendSms(id){
                this.queryOrDelForm.id = id;
                return;
                this.$http.post(`${this.proxyUrl}/param/s`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    if(res.data.resStatus == 0){
                        this.propMsg = '发送成功！';
                        this.propVisible=true;
                        this.editVisible = false;
                        this.search(this.searchForm);
                    }else{
                        this.propMsg = '发送失败!'+ res.data.resText;;
                        this.propVisible=true;
                    }
                })
            },
            //查询列表请求
            search:function(searchForm){
                this.$http.post(`${this.proxyUrl}/param/smsMgt/getOpeSmsPage`,searchForm,{emulateJSON:true}).then(function(res){
                    this.currentPage=res.data.pageNum;
                    this.total=res.data.pageTotal;
                    this.tableData=res.data.datas;
                })
            },
            // 弹出修改框
            showEditSms(editForm){
                this.editForm = Object.assign({},editForm);
                this.editVisible = true;
            },
            //修改请求
            editSms(editForm){
                this.$refs[editForm].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/smsMgt/updateParOpeSms`,this.editForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '修改成功！';
                                this.propVisible=true;
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '修改失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
                    } else {
                        return false;
            }
            });
            },
            //新增请求
            addSms(formName){
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/smsMgt/addParOpeSms`,this.addForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '新增成功！';
                                this.propVisible=true;
                                this.addVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '新增失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
                    } else {
                        return false;
            }
            });
            },
            //删除请求
            delSms(row){
                this.queryOrDelForm.id = row.id;
                this.$http.post(`${this.proxyUrl}/param/smsMgt/deleteParOpeSms`,this.queryOrDelForm,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
            },

            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            handleClick(activeName){
                this.$router.push(activeName);
            },

            //清空提示信息
            resetForm(formName){
                this.$nextTick(() => {
                    this.$refs[formName].resetFields();
            });
            },

            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .def-tab {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .el-tabs__item{
        font-size: 24px;
        -webkit-margin-before: 0.67em;
        -webkit-margin-after: 0.67em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        font-weight: bold;
    }

    .user-title{
        margin-top: 8px;
        margin-left: 16px;
        background-color: white;
    }
    .user-line{
        margin-top: 8px;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
