package com.cy.user.api.request;



public class QueryCallLogRequest {
    //主叫号码
    private String phoneNum;
    //分页页数，从1开始
    private Integer pageNum;
    //每页结果数量，从1开始上限100
    private Integer pageSize;

    @Override
    public String toString() {
        return "QueryCallLogRequest{" +
                "phoneNum='" + phoneNum + '\'' +
                ", pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                '}';
    }

    public String getPhoneNum() {
        return phoneNum;
    }

    public void setPhoneNum(String phoneNum) {
        this.phoneNum = phoneNum;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

}
