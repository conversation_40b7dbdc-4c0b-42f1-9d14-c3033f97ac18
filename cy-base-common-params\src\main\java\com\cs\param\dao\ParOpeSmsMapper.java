package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParOpeSmsCommon;
import com.cs.param.model.ParOpeSmsModel;

@Repository
public interface ParOpeSmsMapper {

	int insertParOpeSms(ParOpeSmsCommon common) throws SQLException;

	int updateParOpeSmsByPK(ParOpeSmsCommon common) throws SQLException;

	int deleteParOpeSmsByPK(ParOpeSmsCommon common) throws SQLException;

	int openOrCloseStatusById(ParOpeSmsCommon common) throws SQLException;

	ParOpeSmsModel queryStatusById(ParOpeSmsCommon common) throws SQLException;

	List<ParOpeSmsModel> queryPageInfo(ParOpeSmsCommon commons) throws SQLException;

	Integer queryPageCount(ParOpeSmsCommon common) throws SQLException;

}
