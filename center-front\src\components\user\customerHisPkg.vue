<template>
  <div>
  <div class="user-search" style="margin-top:10px;">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="套餐包">
        <el-select v-model="searchReq.pkgName" placeholder="请选择" clearable size="small">
          <el-option
              v-for="item in slideData.packageNames"
              :key="item.productMark"
              :label="item.setMealName"
              :value="item.productMark">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="searchReq.pkgStatus" placeholder="请选择" clearable size="small">
          <el-option
              v-for="item in slideData.csStatusTypes"
              :key="item.statuslId"
              :label="item.statusName"
              :value="item.statuslId">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(searchReq)" size="small">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
    <el-table
            :data="tableData"
            border
            style="width: 95%;margin:0 auto;">
      <el-table-column
              width="100"
              prop="bossId"
              label="企业代码">
      </el-table-column>
      <el-table-column
              prop="serviceId"
              width="140"
              label="业务代码">
      </el-table-column>
      <el-table-column
              prop="productId"
              width="170"
              label="产品代码">
      </el-table-column>
      <el-table-column
              prop="packageName"
              width="140"
              label="套餐包">
      </el-table-column>
      <el-table-column
              prop="serviceCost"
              width="120"
              label="业务资费（分）">
      </el-table-column>
      <el-table-column
              prop="payTypeName"
              width="100"
              label="支付方式">

      </el-table-column>
      <el-table-column
              label="状态">
              <template slot-scope="scope">
                  <div>{{getPkgStatus(scope.row)}}</div>
              </template>
      </el-table-column>
      <el-table-column
              prop="effectiveTime"
              width="180"
              label="彩印平台生效时间">
      </el-table-column>
      <el-table-column
              prop="unSubScriTtime"
              width="180"
              label="彩印平台退订时间">
      </el-table-column>
      <el-table-column
              prop="pkgChannelOnName"
              width="140"
              label="开户渠道">
      </el-table-column>
      <el-table-column
              prop="pkgChannelOffName"
              width="140"
              label="退订渠道">
      </el-table-column>
      <el-table-column
              fixed="right"
              label="操作">
              <template slot-scope="scope" justify="center" v-if="scope.row.pkgStatus == 1">
                  <el-button  type="text" size="small" @click="unsubscribe(scope.row)" >退订</el-button>
              </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="page.pageNum"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="page.pageTotal"  style="text-align: right;">
            </el-pagination>
        </div>
  </div>
</template>
<script>
  export default {
    name: 'userContentDIY',
    data() {
      return {
          row:{},
          propVisible:false,
          propMsg:'',
          delVisible:false,
          slideData:{
            packageNames:[],//套餐包
            csStatusTypes:[{statuslId:'2',statusName:'已退订'},{statuslId:'1',statusName:'正常'}]//状态
          },
          page: {
              pageNum: 1,
              pageSize: 10,
              pageTotal : 0
          },
          tableData:[],
          searchReq:{
            phone:sessionStorage.getItem("pkCurUserid"),
            pkgName:'',
            pkgStatus:'',
            pageNum:1,
            pageSize:10,

          }
      }
    },
    mounted(){
      this.getSlideData();
      this.search(this.searchReq);
    },
    methods: {
        getPkgStatus(row){
          return row.pkgStatus==1 ? '正常' : '已退订';
        },
        //下拉栏请求
        getSlideData:function(){
          this.$http
              .get(`${this.proxyUrl}/user/customerHisPkg/getSelectValues`,{emulateJSON:true})
              .then(function(res){
                  this.slideData.packageNames = res.data;
              })
        },
      //查询请求
        search:function(request){
          this.$http
              .post(`${this.proxyUrl}/user/customerHisPkg/getCustomerHisPkg`,request,{emulateJSON:true})
              .then(function(res){
                this.tableData=res.data.datas;
                this.page.pageTotal = res.data.pageTotal;
              })
        },
        //退订请求
        unsubscribe(row){
            this.$confirm('确认是否退订套餐包?')
                .then(_ => {
                    this.$http
                        .post(`${this.proxyUrl}/user/customerHisPkg/unsubscribe`,
                            {
                                phone:sessionStorage.getItem("pkCurUserid"),
                                pkgId:row.productId
                            },
                            {emulateJSON:true})
                        .then((res)=>{
                            if(res.data.status==0){
                                this.$message.success("退订请求发送成功");
                            }
                            else {
                                this.$message.error(res.data.resText);
                            }
                        })
                })
        },
        //分页
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.searchReq.pageSize=val;
            this.$http
                .post(`${this.proxyUrl}/user/customerHisPkg/getCustomerHisPkg`,this.searchReq,{emulateJSON:true})
                .then(function(res){
                this.tableData=res.data.datas;
            })
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.searchReq.pageNum=val;
            this.$http
              .post(`${this.proxyUrl}/user/customerHisPkg/getCustomerHisPkg`,this.searchReq,{emulateJSON:true})
              .then(function(res){
              this.tableData=res.data.datas;
            })
        }
    }
  }


</script>
<style scoped>
.user-title{
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  width:95%;
  margin:0 auto;
  border-bottom: 1px solid #439AE6;
}
.el-table{
  margin:0 auto;
  border:1px solid #ECEBE9;
}
</style>
