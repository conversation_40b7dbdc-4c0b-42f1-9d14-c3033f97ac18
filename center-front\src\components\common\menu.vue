<template>
  <div class="menu">
    <el-menu :default-active="$route.path==='/creatMaterial'?'/textCS':$route.path==='/creatBox'?'/CSbox':$route.path==='/detail'?'/list':
    $route.path==='/userPrint'?'/list':$route.path==='/newmediaPrint'?'/list': 
    $route.path==='/enterprisePrint'?'/list':
    $route.path==='/swindleNumber'?'/list': 
    $route.path==='/userBlackWhite'?'/list': 
    $route.path==='/contentHistory'?'/list': 
    $route.path==='/customerHisPkg'?'/list':$route.path
    
    " class="el-menu-vertical-demo"
             background-color="#434c53"
             text-color="#fff"
             active-text-color="#ffd04b" style="text-align:left;border-right:none;">
      <el-menu-item index="/home"  @click="locationHref('/home')">
        <i class="el-icon-menu"></i>
        <span slot="title">首页</span>
      </el-menu-item>
      <el-submenu :index="list.href?list.href:list.index" v-for="list of  menuList" :key='list.text'>
        <template slot="title">
          <i :class="list.icon"></i>
          <span slot="title" >{{list.name}}</span>
        </template>
        <el-menu-item-group>
            <el-menu-item :index="menu.href?menu.href:menu.index" v-for="menu of list.children" :key='menu.text' @click="locationHref(menu.href)" v-if="!menu.children">
                <span :title="menu.text" slot="title">{{menu.text}}</span>
            </el-menu-item>
            <el-submenu  v-for="menu of list.children" :index="menu.href?menu.href:menu.index" :key='menu.text' v-if="menu.children">
              <template slot="title">
                <span slot="title" :title="menu.text">{{menu.text}}</span>
              </template>
              <el-menu-item-group>
                <el-menu-item :title="chilent.text" v-for="chilent of menu.children" :key='chilent.text' :index="chilent.href?chilent.href:chilent.index" @click="locationHref(chilent.href)" v-if="chilent.children==null||chilent.children.length===0">
                  {{chilent.text}}
                </el-menu-item>
                <el-submenu  v-for="chilent of menu.children" :key='chilent.text' :index="chilent.href?chilent.href:chilent.index" v-if="chilent.children!=null&&chilent.children.length>0">
                  <template slot="title">
                    <span slot="title" :title="chilent.text">{{chilent.text}}</span>
                  </template>
                  <el-menu-item-group>
                    <el-menu-item :title="chilent2.text" v-for="chilent2 of chilent.children" :key='chilent2.text' :index="chilent2.href?chilent2.href:chilent2.index" @click="locationHref(chilent2.href)">
                      <span v-show="chilent2.show" style="margin: 0 10px"><span class="dot-class"></span></span>
                      {{chilent2.text}}
                    </el-menu-item>
                  </el-menu-item-group>
                </el-submenu>
              </el-menu-item-group>
            </el-submenu>
        </el-menu-item-group>
      </el-submenu>
    </el-menu>
  </div>
</template>
<script>
import {mapState} from 'vuex'
import {post} from './../../servers/httpServer.js'
  export default {
    name: "menu1",
    computed:{
      ...mapState(['menuList'])
    },
    data() {
      return {
        isCollapse: true,
        msg: "",
        show: false,
        menuWidth: 0,
        leftIndex: 0,
        imgGroup: {
          // userPic: require("../../..//static/images/userPic.png"),
          // sjPic: require("../../../static/images/icon/shezhi_icon.png"),
          // yyglPic: require("../../../static/images/icon/yunyingguanli_icon.png"),
          // sckPic: require("../../../static/images/icon/sucaiku_icon.png"),
          // examine: require("../../../static/images/icon/jigourenwu_icon.png"),
          // audit: require("../../../static/images/icon/shuju_icon.png")
        },
        hideFlag: false //左侧图标展开隐藏的标识
      };
    },
    beforeMount(){
      // this.menuList= JSON.parse(sessionStorage.getItem('menuList'))
    },
    methods: {
      locationHref(href) {
        this.$router.push(href);
      }
    }
  };
</script>
<style>
.dot-class {
  width: 10px;
  height: 10px;
  display: inline-block;
  background-color: #cc0000;
  border-radius: 50%;
}
</style>