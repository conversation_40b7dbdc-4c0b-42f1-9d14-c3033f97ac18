import store from "../../store/store";

let token
import md5 from 'blueimp-md5'
//import crypto from './../../util/crypto.js'
import { encrypt } from './../../util/rsa.js'
export default {
    data() {
        return {
            timer: null,
            timeCount: 0,
            token: "",
            overdueVisible: false,
            modPasswordVisible: false,
            loginForm: {name: '', password: '', captcha: ""},
            imgSrc: `${this.proxyUrl}/sys/sysUser/checkcode?` + Math.random(),
            getSmsVerifyCodeDisabled: false,
            modForm: {
                sysUserName: "",
                sysUserEmail: "",
                newPassword: "",
                newPassword2: "",
                captcha: "",
                token: ""
            },
            baseURL: process.env.NODE_ENV==='production' ? "https://nae.migudm.cn/nae/js/vCode.js" : "http://10.124.129.32:8000/nae/js/vCode.js"
        }


    },
    beforeMount() {
        console.log($)
        sessionStorage.removeItem('TOKEN');
        // sessionStorage.removeItem('menuList');
        this.store.dispatch('setMenuList', null);
        sessionStorage.removeItem('userInfo');

        //滑动图形验证码
        const _this = this
        var vCodeUrl = this.baseURL;
        if (window.location.protocol === 'https:') {
            vCodeUrl = vCodeUrl.replace('http:', 'https:')
            vCodeUrl = vCodeUrl.replace('**************:8000', 'nae.migudm.cn')
            vCodeUrl = vCodeUrl.replace(':8000', ':8001')
        }
        const url = vCodeUrl
        const head = document.getElementsByTagName('head')[0]
        const script = document.createElement('script')

        script.type = 'text/javascript'

        if (script.readyState) {
            script.onreadystatechange = function() {
                if (script.readyState === 'loaded' || script.readyState === 'complete') {
                    script.onreadystatechange = null
                    _this.loadDragCode(_this)
                }
            }
        } else {
            script.onload = function() {
                _this.loadDragCode(_this)
            }
        }
        script.src = url

        head.appendChild(script)



    },
    methods: {
        loadDragCode(_this){
            createDrapImgCode("drapImgCode", "123123123", function(token){
                //处理逻辑
                console.log(token)
                _this.modForm.token = token;
            });
        },
        getSysUser(){
            this.$http
                .post(`${this.proxyUrl}/sys/sysUser/getSysUserMsisdn`,
                    JSON.stringify({"sysUserName":this.modForm.sysUserName}))
                .then(function (res) {
                    res = res.body;
                    if (res!=null&&res.sysMobileNumber!=null&&res.sysMobileNumber ==='1') {
                        this.getSmsVerifyCodeDisabled = true;
                    } else {
                        this.getSmsVerifyCodeDisabled = false;

                        this.$message.error("账号不存在");
                    }
                });
        },
        getSmsVerifyCode(){
            this.$http
                .post(`${this.proxyUrl}/sys/sysUser/getSmsVerifyCode`,
                    JSON.stringify({"sysUserName":this.modForm.sysUserName}))
                .then(function (res) {
                    res = res.body;
                    this.timeChange();
                });
        },


        back(){
            this.$router.push('/login');
        },
        getCaptcha() {
            this.imgSrc = `${this.proxyUrl}/sys/sysUser/checkcode?` + Math.random();
        },
        mod() {
            if (this.modForm.sysUserName == null || this.modForm.sysUserName == "") {
                this.$message.error("请输入用户名");
                this.modPasswordVisible = true;
                return false;
            }

            if (this.modForm.newPassword == null || this.modForm.newPassword == "") {
                this.$message.error("请输入新密码");
                this.modPasswordVisible = true;
                return false;
            }
            if (this.modForm.newPassword2 == null || this.modForm.newPassword2 == "") {
                this.$message.error("请输入确认密码");
                this.modPasswordVisible = true;
                return false;
            }
            if (!this.isPwd(this.modForm.newPassword)) {
                this.$message.error("6-16位以字母开头的、由字母、数字或下划线组成");
                this.overdueVisible = true;
                return false;
            }
            if (this.modForm.newPassword != this.modForm.newPassword2) {
                this.$message.error("确认密码必填，与新密码保持一致");
                this.modPasswordVisible = true;
                return false;
            }
            if (this.modForm.token == null || this.modForm.token == "") {
                this.$message.error("请滑动图形验证码");
                this.modPasswordVisible = true;
                return false;
            }
            if (this.modForm.captcha == null || this.modForm.captcha == "") {
                this.$message.error("请输入验证码");
                this.modPasswordVisible = true;
                return false;
            }


            let subFrom = JSON.parse(JSON.stringify(this.modForm))
            subFrom.newPassword = encrypt(this.modForm.newPassword);
            subFrom.newPassword2 = encrypt(this.modForm.newPassword2);


            this.$http
                .post(`${this.proxyUrl}/sys/sysUser/forgetPwd`, JSON.stringify(subFrom))
                .then(function (res) {
                    this.loadDragCode(this);
                    this.modForm.token = '';
                    if (res.data.resStatus == 0) {
                        this.$message.success("密码修改成功");
                        sessionStorage.removeItem("username");
                        this.modForm.newPassword = '';
                        this.modForm.newPassword2 = '';
                        this.modPasswordVisible = false;
                        this.back();
                    } else {
                        this.$message.error(res.data.resText);
                        this.modPasswordVisible = true;
                    }
                });
        },
        isPwd(val) {
            let reg = /^[a-zA-Z][a-zA-Z0-9_!@#$^]{6,16}$/;
            return reg.test(val);
        },
        timeChange(){
            //定时器
            this.timeCount = 60;
            let _this = this;
            _this.timer = window.setInterval(() => {
                _this.timeCount -= 1;
                if (_this.timeCount <= 0) {
                    window.clearInterval(_this.timer);
                }
            }, 1000);
        },

    }
}