package com.cs.param.dao;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.cs.param.common.ParNumberCommon;
import com.cs.param.execl.SectionData;
import com.cs.param.model.ParNumberModel;
import com.cs.param.model.SectionModel;

@Repository
public interface ParNumberMapper {

	int insertParNumber(ParNumberCommon common) throws SQLException;

	int updateParNumberByPK(ParNumberCommon common) throws SQLException;

	List<ParNumberModel> queryPageInfo(ParNumberCommon common) throws SQLException;

	Integer queryPageCount(ParNumberCommon common) throws SQLException;

	int deleteByPK(ParNumberCommon common) throws SQLException;

	void insertBatch(List<ParNumberCommon> list);

	List<SectionModel> initRedis(ParNumberCommon number);

	List<SectionModel> queryIsDouble(String  phoneSection);

	List<SectionData> queryDataForExecl(ParNumberCommon common) throws SQLException;

	List<ParNumberModel> queryInfoForSync(@Param("startDate") Date startDate, @Param("endDate") Date endDate)
			throws SQLException;

	List<ParNumberModel> queryPageInfoList(@Param("phoneSectionList")List<String> phoneSectionList);

}
