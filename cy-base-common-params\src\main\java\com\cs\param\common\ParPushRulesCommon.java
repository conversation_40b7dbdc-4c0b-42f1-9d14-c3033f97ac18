
package com.cs.param.common;

public class ParPushRulesCommon {
	private Integer id; // id
	private String partitionCode;// 分区编号
	private String optObject;// 操作对象
	private String optType;// 操作类型",//0：新增，1：编辑，2：删除
	private String phone;// 号码
	private String status;// "状态",//1：成功、2：失败
	private String reqUrl;// 请求url
	private String handleNum;// 处理次数
	// private String createTime;// 创建时间
	private String lastTime;// 最后处理时间
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private String startTime;// 开始时间
	private String endTime;// 结束时间

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPartitionCode() {
		return partitionCode;
	}

	public void setPartitionCode(String partitionCode) {
		this.partitionCode = partitionCode;
	}

	public String getOptObject() {
		return optObject;
	}

	public void setOptObject(String optObject) {
		this.optObject = optObject;
	}

	public String getOptType() {
		return optType;
	}

	public void setOptType(String optType) {
		this.optType = optType;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getReqUrl() {
		return reqUrl;
	}

	public void setReqUrl(String reqUrl) {
		this.reqUrl = reqUrl;
	}

	public String getHandleNum() {
		return handleNum;
	}

	public void setHandleNum(String handleNum) {
		this.handleNum = handleNum;
	}

	// public String getCreateTime() {
	// return createTime;
	// }
	//
	// public void setCreateTime(String createTime) {
	// this.createTime = createTime;
	// }

	public String getLastTime() {
		return lastTime;
	}

	public void setLastTime(String lastTime) {
		this.lastTime = lastTime;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

}
