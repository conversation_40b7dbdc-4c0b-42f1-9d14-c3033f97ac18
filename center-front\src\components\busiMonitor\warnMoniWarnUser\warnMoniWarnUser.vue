<template>
    <div>
        <h1 class="user-title">告警人员管理</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <div id="user_admin" v-show="!edit">
            <el-form :inline="true" :model="searchForm" size="small" class="demo-form-inline">
                <el-row>
                    <el-col :span="21">
                    <el-form-item label="省份">
                        <el-select v-model="searchForm.provinceId" clearable @change="selectProvince(1)" class="app-input02">
                            <el-option v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="城市">
                        <el-select v-model="searchForm.cityId" clearable class="app-input02">
                            <el-option v-for="item in searchRegionList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                        <el-form-item label="负责人姓名">
                            <el-input v-model="searchForm.chargePersonName" class="app-input02"></el-input>
                        </el-form-item>
                        <el-form-item label="异常分类">
                            <el-select v-model="searchForm.exceptionType" class="app-input02" clearable placeholder="请选择">
                                <el-option
                                        v-for="item in exceptionTypeList"
                                        :key="item.key"
                                        :label="item.value"
                                        :value="item.key">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <br>
                        <el-form-item label="手机" class="app-add">
                            <el-input v-model="searchForm.phone"></el-input>
                        </el-form-item>
                        <el-form-item class="app-add">
                            <el-button type="primary"  @click="search(1)">查询</el-button>
                        </el-form-item>
                        <el-form-item class="app-add">
                            <el-button type="primary" plain @click="edit=!edit" >新增人员</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>

            </el-form>
            <div>
                <el-table :data="tableData" border max-height=500 v-loading="tableLoading" class="app-tab02">
                    <el-table-column prop="provinceName" label="省份"  width="100"/>
                    <el-table-column prop="cityName" label="地市" width="100"/>
                    <el-table-column prop="anomalyClassification" label="异常分类"  width="160"/>
                    <el-table-column prop="chargePersonName" label="负责人姓名" width="120"/>
                    <el-table-column prop="phone" label="手机号码" width="140"/>
                    <el-table-column prop="mail" label="邮箱"  width="220"/>
                    <el-table-column label="操作" fixed="right" width="120">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="updateUser(scope.row)">编辑</el-button>
                            <el-button type="text" size="small" @click="deleteUser(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="block app-pageganit">
                <el-pagination @size-change="handleSizeChange" @current-change="search" :current-page="searchForm.pageNo"
                               :page-sizes="[100, 200, 300, 400]" :page-size="searchForm.pageSize"
                               layout="total, sizes, prev, pager, next, jumper" :total="searchForm.total">
                </el-pagination>
                </div>
            </div>
        </div>
        <div id="user_update" v-show="edit">
            <h1 title="新增人员" v-if="isAdd"/>
            <h1 title="编辑人员" v-if="!isAdd"/>
            <el-form label-position="right" label-width="150px" :model="userForm" size="small" ref="userForm"  :rules="rules2" status-icon
                     style="width: 70%;margin-left: 10%;margin-top:50px;">
                <el-form-item label="负责人姓名：" prop="chargePersonName" class="app-add">
                    <el-input v-model="userForm.chargePersonName"/>
                </el-form-item>
                <el-form-item label="手机号码：" prop="phone" class="app-add">
                    <el-input v-model="userForm.phone" :maxlength="11"/>
                </el-form-item>
                <el-form-item label="邮箱：" prop="mail" class="app-add">
                    <el-input v-model="userForm.mail"/>
                </el-form-item>
                <el-form-item label="省份：" class="app-add">
                    <el-select v-model="userForm.provinceId" clearable @change="selectProvince(2)">
                        <el-option v-for="item in provinceList"
                            :key="item.provinceCode"
                            :label="item.provinceName"
                            :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="城市" class="app-add">
                    <el-select v-model="userForm.cityId" clearable>
                        <el-option v-for="item in userRegionList"
                            :key="item.regionCode"
                            :label="item.regionName"
                            :value="item.regionCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="异常分类：" prop="exceptionType" class="app-add">
                    <el-select v-model="userForm.exceptionType">
                        <el-option label="开销户异常" value="1"/>
                        <el-option label="推送量异常" value="2"/>
                        <el-option label="活跃数据异常" value="3"/>
                    </el-select>
                </el-form-item>
                <el-form-item class="app-add">
                    <el-button type="primary" @click="submitForm()" style="width: 150px">提交</el-button>
                    <el-button type="primary" @click="cancel()" style="width: 150px">取消</el-button>
                </el-form-item>
            </el-form>
        </div> </div>
    </div>
</template>

<script src='./warnMoniWarnUser.js'></script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 3%;
    }

</style>
