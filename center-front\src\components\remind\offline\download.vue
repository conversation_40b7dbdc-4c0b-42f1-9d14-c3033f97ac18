<template>
    <div class="download">
        <div class="user-titler">{{$route.name}}</div>
        <div class="contentbox">
            <!--查询条件-->
            <div class="app-norable">
                <el-form :model="searchForm" :inline="true" class="demo-form-inline" size="small">
                    <el-form-item label="企业名称">
                        <el-input  v-model="searchForm.enterpriseName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="时间">
                        <el-date-picker
                                v-model="searchForm.startTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search">查询</el-button>
                    </el-form-item>
                    <br>
                    <br>
                    <el-form-item>
                        <el-button type="primary" @click="exportcl" plain>导出excel</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                <el-table-column prop="enterpriseId" label="企业编号" />
                <el-table-column prop="enterpriseName" label="企业名称" />
                <el-table-column prop="tornonTime" label="接入时间" />
                <el-table-column prop="offlinePkgName" label="离线包名称" />
                <el-table-column prop="statsDate" label="时间" />
                <el-table-column prop="downloadCount" label="下载次数" />
            </el-table>
            <!--分页-->
            <div class="block app-pageganit" v-show="total">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'download',
        data(){
            return{
                //查询form对象定义
                searchForm: {
                    enterpriseName:'',//企业名称
                    startDate:'', //开始时间
                    endDate: '', //
                    startTime:[],//时间
                    pageNo: 1, //页数
                    pageSize: 10,//每页条数
                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },
        components: {

        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            //查询请求
            search() {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                postHeader('queryEnterpriseRecord', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.numEnterpriseRecordList;
                        vm.total = data.data.total;
                    }
                })
            },
            exportcl(){
                postDownloadHeader('exportEnterpriseRecord', JSON.stringify(this.searchForm)).then(res=>{
                    dowandFile(res.data,'企业下载记录.xlsx');
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .contentbox{
        margin:0 15px;
    }
    .el-table{
        margin-left: 0;
        margin-top: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
