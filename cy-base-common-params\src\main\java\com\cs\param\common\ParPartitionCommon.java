
package com.cs.param.common;

public class ParPartitionCommon {
	private Integer partitionId;// 分区id
	private String partitionCode;// 分区code
	private String partitionName;// 分区名称
	private String partitionType; // 分区类型
	private String countryCode;// 国家code
	private String isAdd; // 添加标识，0：修改，1：添加
	private String provinceCodes;// 省份code，多个省份code，以逗号隔开
	private String provinceArr[];

	public Integer getPartitionId() {
		return partitionId;
	}

	public void setPartitionId(Integer partitionId) {
		this.partitionId = partitionId;
	}

	public String getPartitionCode() {
		return partitionCode;
	}

	public void setPartitionCode(String partitionCode) {
		this.partitionCode = partitionCode;
	}

	public String getPartitionName() {
		return partitionName;
	}

	public void setPartitionName(String partitionName) {
		this.partitionName = partitionName;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getIsAdd() {
		return isAdd;
	}

	public void setIsAdd(String isAdd) {
		this.isAdd = isAdd;
	}

	public String getPartitionType() {
		return partitionType;
	}

	public void setPartitionType(String partitionType) {
		this.partitionType = partitionType;
	}

	public String getProvinceCodes() {
		return provinceCodes;
	}

	public void setProvinceCodes(String provinceCodes) {
		this.provinceCodes = provinceCodes;
	}

	public String[] getProvinceArr() {
		return provinceArr;
	}

	public void setProvinceArr(String[] provinceArr) {
		this.provinceArr = provinceArr;
	}

}
