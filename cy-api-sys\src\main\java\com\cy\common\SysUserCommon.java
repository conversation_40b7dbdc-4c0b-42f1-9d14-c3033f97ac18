package com.cy.common;

public class SysUserCommon {
	private Integer sysUserId; // 用户id
	private String sysUserName; // 用户名
	private String sysUserPassword; // 用户密码
	private String sysStaffName; // 姓名
	private String sysUserSex; // 性别
	private String sysMobileNumber; // 手机号码
	private String sysUserEmail; // 电子邮箱
	private String sysUserCompany; // 公司
	private String provinceCode; // 负责省份id
	private String provinceCodeArr[]; // 负责省份id数组
	private String isDelete; // 状态
	private String sysCreateTime; // 创建时间
	private Integer sysRoleId; // 角色id
	private Integer pageSize; // 每页显示的条数
	private Integer pageNum; // 显示的页数-传给后台为数据的序列数
	private String startTime;// 开始时间
	private String endTime;// 结束时间
	private String captcha;//验证码
	private String token;//token


	// 主页修改密码
	private String oldPassword;
	private String newPassword;

	public SysUserCommon(String name) {
		this.setSysUserName(name);
	}

	public SysUserCommon() {

	}

	public Integer getSysUserId() {
		return sysUserId;
	}

	public void setSysUserId(Integer sysUserId) {
		this.sysUserId = sysUserId;
	}

	public String getSysUserName() {
		return sysUserName;
	}

	public void setSysUserName(String sysUserName) {
		this.sysUserName = sysUserName;
	}

	public String getSysUserPassword() {
		return sysUserPassword;
	}

	public void setSysUserPassword(String sysUserPassword) {
		this.sysUserPassword = sysUserPassword;
	}

	public String getSysStaffName() {
		return sysStaffName;
	}

	public void setSysStaffName(String sysStaffName) {
		this.sysStaffName = sysStaffName;
	}

	public String getSysUserSex() {
		return sysUserSex;
	}

	public void setSysUserSex(String sysUserSex) {
		this.sysUserSex = sysUserSex;
	}

	public String getSysMobileNumber() {
		return sysMobileNumber;
	}

	public void setSysMobileNumber(String sysMobileNumber) {
		this.sysMobileNumber = sysMobileNumber;
	}

	public String getSysUserEmail() {
		return sysUserEmail;
	}

	public void setSysUserEmail(String sysUserEmail) {
		this.sysUserEmail = sysUserEmail;
	}

	public String getSysUserCompany() {
		return sysUserCompany;
	}

	public void setSysUserCompany(String sysUserCompany) {
		this.sysUserCompany = sysUserCompany;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getSysCreateTime() {
		return sysCreateTime;
	}

	public void setSysCreateTime(String sysCreateTime) {
		this.sysCreateTime = sysCreateTime;
	}

	public Integer getSysRoleId() {
		return sysRoleId;
	}

	public void setSysRoleId(Integer sysRoleId) {
		this.sysRoleId = sysRoleId;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String[] getProvinceCodeArr() {
		return provinceCodeArr;
	}

	public String getOldPassword() {
		return oldPassword;
	}

	public void setOldPassword(String oldPassword) {
		this.oldPassword = oldPassword;
	}

	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public void setProvinceCodeArr(String[] provinceCodeArr) {
		this.provinceCodeArr = provinceCodeArr;
	}

	public void setToken(String token) {
		this.token = token;
	}
	public String getToken() {
		return token;
	}

	public void setCaptcha(String captcha) {
		this.captcha = captcha;
	}
	public String getCaptcha() {
		return captcha;
	}

	@Override
	public String toString() {
		return "SysUserCommon [sysUserId=" + sysUserId + ", sysUserName=" + sysUserName + ", sysUserPassword="
				+ sysUserPassword + ", sysStaffName=" + sysStaffName + ", sysUserSex=" + sysUserSex
				+ ", sysMobileNumber=" + sysMobileNumber + ", sysUserEmail=" + sysUserEmail + ", sysUserCompany="
				+ sysUserCompany + ", provinceCode=" + provinceCode + ", sysState=" + isDelete + ", sysCreateTime="
				+ sysCreateTime + ", sysRoleId=" + sysRoleId + ", pageSize=" + pageSize + ", pageNum=" + pageNum
				+ ", startTime=" + startTime + ", endTime=" + endTime + "]";
	}

}
