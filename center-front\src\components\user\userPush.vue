<template>
  <div>
    <h1 class="user-title">用户推送规则</h1>
    <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="用户号码">
        <el-input v-model="searchReq.phone" size="small"></el-input>
      </el-form-item>
      <el-form-item label="规则ID">
        <el-input v-model="searchReq.ruleId" size="small"></el-input>
      </el-form-item>

      <el-form-item label="分组ID">
        <el-input v-model="searchReq.groupId" size="small"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="pageQuery()" size="small" class="app-bnt">查询</el-button>
      </el-form-item>
    </el-form>
  </div>
    <el-table
            :data="tableData"
            border class="app-tab">
      <el-table-column
              width="150"
              prop="phone"
              label="用户号码">
      </el-table-column>
      <el-table-column
              prop="ruleId"
              width="310"
              label="规则ID/分组ID">
      </el-table-column>
      <el-table-column
              prop="subService"
              width="120"
              label="子业务">
      </el-table-column>
      <el-table-column
              prop="ruleStatus"
              width="100"
              label="状态">
      </el-table-column>
      <el-table-column
              prop="sendType"
              width="120"
              label="呼叫类型">
      </el-table-column>
      <el-table-column
              prop="recTypeName"
              width="120"
              label="接收方类型">
      </el-table-column>
      <el-table-column
          width="120"
          label="接收号码">
        <template slot-scope="scope">
          <div v-if="scope.row.recType==0" style="text-align: center">所有</div>
          <div v-else-if="scope.row.recType==2" style="text-align: center">{{scope.row.recPhone}}</div>
          <el-button @click="recPhones=true;row=scope.row" type="text" size="small" v-else-if="scope.row.recType==1" style="text-align: center">号码详情</el-button>
        </template>
      </el-table-column>
      <el-table-column
              width="120"
              prop="sendMode"
              label="推送类型">
      </el-table-column>
      <el-table-column
              prop="csType"
              width="130"
              label="彩印内容类型">
      </el-table-column>
      <el-table-column
              width="180"
              prop="sendTime"
              label="每天推送时间">
      </el-table-column>
      <el-table-column
              width="180"
              prop="updateTime"
              label="更新时间">
      </el-table-column>
       <el-table-column
              width="180"
              prop="crEndDate"
              label="结束时间">
      </el-table-column>
      <el-table-column
              width="160"
              fixed="right"
              prop="dsSubmitTime"
              label="操作">
        <template slot-scope="scope">
          <el-button @click="sync(scope.row)" type="text" size="small">用户数据同步</el-button>
          <el-button @click="deltr(scope.row);" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
     <el-dialog
        width="30%"
        title="接收号码详情"
        :close-on-click-modal="false"
        :visible.sync="recPhones"
        append-to-body>
      <ul>
        <li v-for="item in row.recPhones">{{item}}</li>
      </ul>
      <el-button type="primary" @click="recPhones=false">确 定</el-button>
    </el-dialog>

    <!-- 分页 -->
    <div class="block app-pageganit">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableData.pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageTotal"  style="text-align: right;">
      </el-pagination>
    </div>

      <el-dialog
              title="提示"
              :visible.sync="propVisible"
              :close-on-click-modal="false"
              width="30%"
              :before-close="handleCloseConfirm">
          <span>{{propMsg}}</span>
          <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
      </el-dialog>
  </div>
</template>
<script type="text/ecmascript-6">
  export default {
    name: 'userPush',
    data() {
      return {
         propMsg:'',
         propVisible:false,
         row:{},
         recPhones:false,
         delVisible:false,
         pageTotal:0,
        //  tableData: [
        //    {
        //      phone: "1231",
        //      ruleId: "asd",
        //      subService: "asdas",
        //      ruleStatus: "sdfsd",
        //      sendType: "1",
        //      recType: "1",
        //      recPhone: "sdfsdf",
        //      recPhones:"52165156",
        //      sendMode: "asdas",
        //      csType: "asdasd",
        //      sendTime:"2018-1-2",
        //      updateTime:"2018-1-1"
        //    },
        //    {
        //      phone: "1231",
        //      ruleId: "asd",
        //      subService: "asdas",
        //      ruleStatus: "sdfsd",
        //      sendType: "2",
        //      recType: "0",
        //      recPhone: "sdfsdf",
        //      recPhones:"52165156",
        //      sendMode: "asdas",
        //      csType: "asdasd",
        //      sendTime:"2018-1-2",
        //      updateTime:"2018-1-1"
        //    },
        //    {
        //      phone: "1231",
        //      ruleId: "asd",
        //      subService: "asdas",
        //      ruleStatus: "sdfsd",
        //      sendType: "4",
        //      recType: "1",
        //      recPhone: "sdfsdf",
        //      recPhones:"52165156",
        //      sendMode: "asdas",
        //      csType: "asdasd",
        //      sendTime:"2018-1-2",
        //      updateTime:"2018-1-1"
        //    },
        //    {
        //      phone: "1231",
        //      ruleId: "asd",
        //      subService: "asdas",
        //      ruleStatus: "sdfsd",
        //      sendType: "1",
        //      recType: "0",
        //      recPhone: "sdfsdf",
        //      recPhones:"52165156",
        //      sendMode: "asdas",
        //      csType: "asdasd",
        //      sendTime:"2018-1-2",
        //      updateTime:"2018-1-1"
        //    }
        //  ],
         tableData:[],
          searchReq:{
            phone:'',
            ruleId:'',
            groupId:'',
            pageNum:1,
            pageSize:10
          },
//          syncReq :{
//            ruleId:'',
//            phone:'',
              groupId:''
//          }
      }
    },
    methods:{
        //loading
        openFullScreen2() {
          const loading = this.$loading({
            lock: true,
            text: '正在同步',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          setTimeout(() => {
            loading.close();
        }, 1000);
        },
        //查询请求
        pageQuery:function(){
            if(this.searchReq.phone == ''){
                this.$message.error('请输入用户号码!');
                return ;
            }
          this.$http
            .post(`${this.proxyUrl}/user/customerRule/getCustomerRule`,this.searchReq,{emulateJSON:true})
            .then(function(res){
              this.tableData=res.data.datas;
              this.pageTotal=res.data.pageTotal;
            })
        },
        //同步
        sync(request){
            this.$confirm('确认同步此规则?')
                .then(_ => {
                    const loading = this.$loading({
                        lock: true,
                        text: '正在同步',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    this.$http
                        .post(`${this.proxyUrl}/user/customerRule/syncCustomerRule`,request,{emulateJSON:true})
                        .then((res)=>{
                            if(res.data.status==0){
                                setTimeout(() => {
                                    loading.close();
                                }, 1000);
                                this.$message.success("同步成功!");
                                this.pageQuery();
                            } else {
                                this.$message.error("同步失败!");
                                setTimeout(() => {
                                    loading.close();
                                }, 1000);
                            }
                        })
                })
        },
        //删除
        deltr(request){
            this.$confirm('确认删除此规则?')
                .then(_ => {
                    this.$http
                        .post(`${this.proxyUrl}/user/customerRule/delCustomerRule`,request,{emulateJSON:true})
                        .then((res)=>{
                            if(res.data.status==0){
                                this.$message.success("删除成功!");
                                this.pageQuery();
                            }
                            else {
                                this.$message.error(res.data.resText);
                                this.pageQuery();

                            }
                        })
                })
        },
        //分页
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.searchReq.pageSize=val;
            this.$http
                .post(`${this.proxyUrl}/user/customerRule/getCustomerRule`,this.searchReq,{emulateJSON:true})
                .then(function(res){
                this.tableData=res.data.datas;
            })
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.searchReq.pageNum=val;
            this.$http
              .post(`${this.proxyUrl}/user/customerRule/getCustomerRule`,this.searchReq,{emulateJSON:true})
              .then(function(res){
              this.tableData=res.data.datas;
            })
        },
        handleCloseConfirm(done) {
            done();
        }
    },
      mounted() {
          //this.pageQuery();
      },
  }


</script>
<style scoped>
.user-title{
      padding: 10px 0px 0px 0px;
  }
.user-line{
  width:100%;
  margin:0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #DDDFE6;
}
.user-search{
    width: 100%;
}
.user-push-serch {
    margin-top: 10px;
    padding-left:24px !important;
}
.el-table{
  border:1px solid #ECEBE9;
  margin: 0 auto;
  width:99%;
}
.el-th {
  border-right: 1px solid #DDDFE6 !important;
}
</style>
