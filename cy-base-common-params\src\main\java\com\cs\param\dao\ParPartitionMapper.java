package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParPartitionCommon;
import com.cs.param.model.ParPartitionModel;

@Repository
@SuppressWarnings("rawtypes")
public interface ParPartitionMapper {

	int insertParPartition(ParPartitionCommon common) throws SQLException;

	int updateParPartitionByPK(ParPartitionCommon common) throws SQLException;

	int deleteParPartitionByCode(ParPartitionCommon common) throws SQLException;

	List<ParPartitionModel> getParPartitionListByCond(ParPartitionCommon common) throws SQLException;

	Integer queryCountByCode(ParPartitionCommon common) throws SQLException;

	void updateProvinceCodes(Map params) throws SQLException;

	void insertProvinceCodes(Map params) throws SQLException;

}
