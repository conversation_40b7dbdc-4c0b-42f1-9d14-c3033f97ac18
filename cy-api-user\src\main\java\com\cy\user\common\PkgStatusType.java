package com.cy.user.common;

import java.util.ArrayList;
import java.util.List;

import com.cy.user.model.CsStatusModel;

/**
 * 彩印状态
 * <AUTHOR>
 *
 */
public enum PkgStatusType {
	t1(1,"正常"),t2(2,"退订");
	private int typeId;  
    private String typeName;  
  
    PkgStatusType(int typeId, String typeName) {  
        this.typeId = typeId;  
        this.typeName = typeName;
    }  
  
    public int getTypeId() {  
        return typeId;  
    }  
  
    public String getTypeName() {  
        return typeName;  
    }
    
    public static List<CsStatusModel> getAllStatus() {
    	List<CsStatusModel> item = new ArrayList<CsStatusModel>();
        PkgStatusType[] values = values();
        for (int i = 0; i < values.length; ++i) {
        	CsStatusModel model = new CsStatusModel();
        	model.setStatuslId(values[i].getTypeId());
        	model.setStatusName(values[i].getTypeName());
        	item.add(model);
        }  
        return item;  
    }  
  
    public static String getNameById(int typeId) {  
        String value = null;
        PkgStatusType[] values = values();  
  
        for(int i = 0; i < values.length; ++i) {  
        	PkgStatusType type = values[i];
            if(type.getTypeId() == typeId) {  
                value = type.getTypeName();  
                break;  
            }  
        }  
        return value;  
    } 
}
