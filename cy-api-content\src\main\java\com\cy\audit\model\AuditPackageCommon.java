package com.cy.audit.model;

import java.io.Serializable;

public class AuditPackageCommon  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 彩印盒唯一编号
	 */
	private String csPackageNo;
	private String csPkgName;
	private String csPkgGroupId;
	private String csPkgLabelId;
	private String  csPkgContent1,csPkgContent2,csPkgContent3,csPkgContent4,csPkgContent5;
	public String getCsPackageNo() {
		return csPackageNo;
	}
	public void setCsPackageNo(String csPackageNo) {
		this.csPackageNo = csPackageNo;
	}
	public String getCsPkgName() {
		return csPkgName;
	}
	public void setCsPkgName(String csPkgName) {
		this.csPkgName = csPkgName;
	}
	
	public String getCsPkgGroupId() {
		return csPkgGroupId;
	}
	public void setCsPkgGroupId(String csPkgGroupId) {
		this.csPkgGroupId = csPkgGroupId;
	}
	public String getCsPkgLabelId() {
		return csPkgLabelId;
	}
	public void setCsPkgLabelId(String csPkgLabelId) {
		this.csPkgLabelId = csPkgLabelId;
	}
	public String getCsPkgContent1() {
		return csPkgContent1;
	}
	public void setCsPkgContent1(String csPkgContent1) {
		this.csPkgContent1 = csPkgContent1;
	}
	public String getCsPkgContent2() {
		return csPkgContent2;
	}
	public void setCsPkgContent2(String csPkgContent2) {
		this.csPkgContent2 = csPkgContent2;
	}
	public String getCsPkgContent3() {
		return csPkgContent3;
	}
	public void setCsPkgContent3(String csPkgContent3) {
		this.csPkgContent3 = csPkgContent3;
	}
	public String getCsPkgContent4() {
		return csPkgContent4;
	}
	public void setCsPkgContent4(String csPkgContent4) {
		this.csPkgContent4 = csPkgContent4;
	}
	public String getCsPkgContent5() {
		return csPkgContent5;
	}
	public void setCsPkgContent5(String csPkgContent5) {
		this.csPkgContent5 = csPkgContent5;
	}
	
}
