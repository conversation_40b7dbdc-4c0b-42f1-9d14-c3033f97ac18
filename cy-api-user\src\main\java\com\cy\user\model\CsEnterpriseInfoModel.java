package com.cy.user.model;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 名片号企业
 */
@Data
public class CsEnterpriseInfoModel {

    /**
     * 编号
     */
    private Integer id;

    /**
     * 企业名称
     */

    private String enterpriseName;

    /**
     * 企业描述
     */
    private String enterpriseDesc;

    /**
     * 企业类型，1.默认企业
     */

    private Integer enterpriseType;

    /**
     * 联系人号码，多个使用半角逗号分隔
     */
    private String msisdn;

    /**
     * 联系人名
     */
    private String contract;

    /**
     * 接收方号码
     */
    private String receiveNumber;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

}
