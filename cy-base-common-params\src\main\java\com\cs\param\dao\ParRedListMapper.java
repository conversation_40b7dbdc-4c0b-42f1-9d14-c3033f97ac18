package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParRedListCommon;
import com.cs.param.model.ParRedListModel;

@Repository
public interface ParRedListMapper {

	int insertParRedList(ParRedListCommon common) throws SQLException;

	int deleteParRedListByPK(ParRedListCommon common) throws SQLException;

	List<ParRedListModel> queryPageInfo(ParRedListCommon common) throws SQLException;

	List<ParRedListModel> queryAllRedList() throws SQLException;

	Integer queryPageCount(ParRedListCommon common) throws SQLException;

	Integer deleteParRedListBatch(String[] phones) throws SQLException;

	void insertBatch(List<ParRedListCommon> list) throws SQLException;

}
