<template>
    <div class="exporteffect">
        <div class="content">
            <el-form :model="addForm" ref="addForm" class="demo-form-inline app-form-item" size="small" label-width="35%" style="width: 100%;">
                <el-form-item label="到期时间：">
                    <el-date-picker
                            v-model="addForm.startTime"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
            </el-form>
        </div>
        <div class="content1">
            <el-button type="primary" @click="exportexcle" size="small">导出excel</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'name',
        data(){
            return{
                addForm:{
                    numType:3,//号码库
                    startTime:[],
                    startDate:'',//开始时间
                    endDate:'',//结束时间
                },
            }
        },
        created(){

        },
        methods:{
            //导出
            exportexcle(){
                if(this.addForm.startTime){
                    this.addForm.startDate = this.addForm.startTime[0];
                    this.addForm.endDate = this.addForm.startTime[1];
                }else{
                    this.addForm.startDate = '';
                    this.addForm.endDate = '';
                }
                postDownloadHeader('exportWhiteNumExcel',JSON.stringify(this.addForm)).then(res=>{
                    dowandFile(res.data,'白名单.xlsx');
                })
            },

        },
        components: {}
    }
</script>

<style scoped>
    .content{
        width: 640px;
    }
    .content1{
        margin-top: 50px;
        text-align: center;
    }
</style>
