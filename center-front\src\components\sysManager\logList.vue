<template>
    <div>
        <h1 class="user-title">日志管理</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="操作对象">
                    <el-input  v-model="searchForm.sysUseObject" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="操作模块">
                    <el-input  v-model="searchForm.sysUseModule" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <!--<el-form-item label="操作模块">-->
                    <!--<el-select v-model="searchForm.sysUseModule" placeholder="请选择" size="small">-->
                        <!--<el-option-->
                                <!--v-for="item in moduleList"-->
                                <!--:key="item.sysResourcesId"-->
                                <!--:label="item.sysResourcesName "-->
                                <!--:value="item.sysResourcesId">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                <!--</el-form-item>-->
                <!--<el-form-item label="操作类型">-->
                    <!--<el-select v-model="searchForm.sysUesType" placeholder="请选择" size="small">-->
                        <!--<el-option-->
                                <!--v-for="item in sysUesTypeList"-->
                                <!--:key="item.id"-->
                                <!--:label="item.value "-->
                                <!--:value="item.id">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                <!--</el-form-item>-->
                <el-form-item label="操作者">
                    <el-input v-model="searchForm.sysUseName" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="操作结果">
                    <el-select v-model="searchForm.sysUseResult" placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in resultList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">

                <el-form-item label="操作时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchForm.createTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyyMMddHHmmss" size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="exportExcel(searchForm)" size="small">导出excel</el-button>
                </el-form-item>
            </el-form>

        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
                  class="app-tab" :header-cell-class-name="tableheaderClassName"
                  v-loading="dataHiddent"
                  element-loading-text="数据量比较大!正拼命加载中……">
            <!--<el-table-column type="index" label="序号" width="50px"/>-->
            <el-table-column prop="sysUseName" label="操作者" width="100"/>
            <el-table-column prop="sysUseModule" label="操作模块" width="180"/>
            <el-table-column prop="sysUseTime" label="操作时间" width="180"/>
            <el-table-column prop="sysUseObject" label="操作对象" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="sysUseContent" label="操作内容" width="160" :show-overflow-tooltip="true"/>
            <el-table-column prop="sysUseResult" label="操作结果"/>
            <el-table-column prop="sysUseIp" label="IP" width="160"/>
            <el-table-column prop="oper" label="操作" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showLog(scope.row)" >查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div>
        <el-dialog title="操作详情" :visible.sync="outerVisible" :close-on-click-modal="false">
            <el-form :model="showForm" class="demo-form-inline" label-width="25%"  style="width: 80%;">
                <el-form-item label="操作者 : ">{{showForm.sysUseName}}</el-form-item>
                <el-form-item label="操作模块">
                    {{showForm.sysUseModule}}
                </el-form-item>
                <el-form-item label="操作时间">
                    {{showForm.sysUseTime}}
                </el-form-item>
                <el-form-item label="操作对象">
                    {{showForm.sysUseObject}}
                </el-form-item>
                <el-form-item label="操作内容">
                    {{showForm.sysUseContent}}
                </el-form-item>
                <el-form-item label="操作结果">
                    {{showForm.sysUseResult}}
                </el-form-item>
                <el-form-item label="IP">
                    {{showForm.sysUseIp}}
                </el-form-item>
            </el-form>
        </el-dialog>
        </div>

        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

    </div>




</template>
<script>
    import {post,postDownload} from './../../servers/httpServer.js';
    import {dowandFile} from './../../util/core.js';
    export default {
        name: 'UserList',
        data() {
            return {
                dataHiddent:false,
                outerVisible: false,
                //查询form对象定义
                searchForm: {
                    sysUseName:'',
                    sysUseModule:'',
                    sysUseObject: '',
                    sysUseResult: '',
                    sysUseType: '',//需要加个字段
                    startTime:'',//操作时间
                    endTime:'',//创建时间
                    createTime:'',
                    pageSize:10,
                    pageNum :1,// 查询的页码

                },
                showForm:'',
                sysUesTypeList:[],
                moduleList:[],
                resultList:[
                    {
                        key:'成功',
                        value:'成功'
                    },
                    {
                        key: '失败',
                        value: '失败'
                    }
                ],
                tableData:[],
                currentPage: 1,
                total:0,
            }
        },

        mounted(){
//            this.slideData(this.proxyUrl);
            this.getModulelList();
        },
        methods: {

            //查询请求
            getModulelList:function(){
                this.$http.get(`${this.proxyUrl}/sys/sysLog/getAllSysResources`)
                        .then(function(res){
                            this.moduleList=res.data;
                        })
            },
            // 导出excel
            exportExcel(){
                this.dataHiddent=true;
                postDownload('/sys/sysLog/exportExecl',this.searchForm).then(res=>{
                    dowandFile(res.data,'日志管理.xlsx');
                    this.dataHiddent=false;
                })
            },

            //查询请求
            search:function(searchForm){
                if(searchForm.createTime != ''){
                    searchForm.startTime=searchForm.createTime[0];
                    searchForm.endTime=searchForm.createTime[1];
                }
                console.log(searchForm.startTime);
                this.$http.post(`${this.proxyUrl}/sys/sysLog/getSysLogPage`,searchForm,{emulateJSON:true})
                        .then(function(res){
                            this.currentPage=res.data.pageNum;
                            this.total=res.data.pageTotal;
                            this.tableData=res.data.datas;
                        })
            },
            // 弹出修改框
            showLog(editForm){
                this.showForm = Object.assign({},editForm);
                this.outerVisible = true;
            },
            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
            // 关闭弹出框
            handleClose(done) {
                done();
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
