package com.cs.param.config;

import com.cs.param.constants.QueueNames;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 队列初始化
 *
 * <AUTHOR>
 * @date 2021/9/1 16:32
 */
@Configuration
public class MqConfig {

    /**
     * 黑名单同步队列
     * @return
     */
    @Bean
    public Queue syncBlackListQueue(){ return new Queue(QueueNames.BLACKLIST_SYNC);}
}
