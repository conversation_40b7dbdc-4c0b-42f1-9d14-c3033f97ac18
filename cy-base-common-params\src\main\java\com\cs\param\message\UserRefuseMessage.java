package com.cs.param.message;

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: UserRefuse.proto

public final class UserRefuseMessage {
	private UserRefuseMessage() {
	}

	public static void registerAllExtensions(com.google.protobuf.ExtensionRegistry registry) {
	}

	public interface UserRefuseEntityOrBuilder extends com.google.protobuf.MessageOrBuilder {

		// required string phoneNo = 1;
		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		boolean hasPhoneNo();

		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		java.lang.String getPhoneNo();

		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		com.google.protobuf.ByteString getPhoneNoBytes();
	}

	/**
	 * Protobuf type {@code UserRefuseEntity}
	 */
	public static final class UserRefuseEntity extends com.google.protobuf.GeneratedMessage
			implements UserRefuseEntityOrBuilder {
		// Use UserRefuseEntity.newBuilder() to construct.
		private UserRefuseEntity(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
			super(builder);
			this.unknownFields = builder.getUnknownFields();
		}

		private UserRefuseEntity(boolean noInit) {
			this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance();
		}

		private static final UserRefuseEntity defaultInstance;

		public static UserRefuseEntity getDefaultInstance() {
			return defaultInstance;
		}

		public UserRefuseEntity getDefaultInstanceForType() {
			return defaultInstance;
		}

		private final com.google.protobuf.UnknownFieldSet unknownFields;

		@java.lang.Override
		public final com.google.protobuf.UnknownFieldSet getUnknownFields() {
			return this.unknownFields;
		}

		private UserRefuseEntity(com.google.protobuf.CodedInputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			initFields();
			int mutable_bitField0_ = 0;
			com.google.protobuf.UnknownFieldSet.Builder unknownFields = com.google.protobuf.UnknownFieldSet
					.newBuilder();
			try {
				boolean done = false;
				while (!done) {
					int tag = input.readTag();
					switch (tag) {
					case 0:
						done = true;
						break;
					default: {
						if (!parseUnknownField(input, unknownFields, extensionRegistry, tag)) {
							done = true;
						}
						break;
					}
					case 10: {
						bitField0_ |= 0x00000001;
						phoneNo_ = input.readBytes();
						break;
					}
					}
				}
			} catch (com.google.protobuf.InvalidProtocolBufferException e) {
				throw e.setUnfinishedMessage(this);
			} catch (java.io.IOException e) {
				throw new com.google.protobuf.InvalidProtocolBufferException(e.getMessage()).setUnfinishedMessage(this);
			} finally {
				this.unknownFields = unknownFields.build();
				makeExtensionsImmutable();
			}
		}

		public static final com.google.protobuf.Descriptors.Descriptor getDescriptor() {
			return UserRefuseMessage.internal_static_UserRefuseEntity_descriptor;
		}

		protected com.google.protobuf.GeneratedMessage.FieldAccessorTable internalGetFieldAccessorTable() {
			return UserRefuseMessage.internal_static_UserRefuseEntity_fieldAccessorTable
					.ensureFieldAccessorsInitialized(UserRefuseMessage.UserRefuseEntity.class,
							UserRefuseMessage.UserRefuseEntity.Builder.class);
		}

		public static com.google.protobuf.Parser<UserRefuseEntity> PARSER = new com.google.protobuf.AbstractParser<UserRefuseEntity>() {
			public UserRefuseEntity parsePartialFrom(com.google.protobuf.CodedInputStream input,
					com.google.protobuf.ExtensionRegistryLite extensionRegistry)
					throws com.google.protobuf.InvalidProtocolBufferException {
				return new UserRefuseEntity(input, extensionRegistry);
			}
		};

		@java.lang.Override
		public com.google.protobuf.Parser<UserRefuseEntity> getParserForType() {
			return PARSER;
		}

		private int bitField0_;
		// required string phoneNo = 1;
		public static final int PHONENO_FIELD_NUMBER = 1;
		private java.lang.Object phoneNo_;

		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		public boolean hasPhoneNo() {
			return ((bitField0_ & 0x00000001) == 0x00000001);
		}

		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		public java.lang.String getPhoneNo() {
			java.lang.Object ref = phoneNo_;
			if (ref instanceof java.lang.String) {
				return (java.lang.String) ref;
			} else {
				com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
				java.lang.String s = bs.toStringUtf8();
				if (bs.isValidUtf8()) {
					phoneNo_ = s;
				}
				return s;
			}
		}

		/**
		 * <code>required string phoneNo = 1;</code>
		 *
		 * <pre>
		 *必须字段，在后面的使用中必须为该段设置值
		 * </pre>
		 */
		public com.google.protobuf.ByteString getPhoneNoBytes() {
			java.lang.Object ref = phoneNo_;
			if (ref instanceof java.lang.String) {
				com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((java.lang.String) ref);
				phoneNo_ = b;
				return b;
			} else {
				return (com.google.protobuf.ByteString) ref;
			}
		}

		private void initFields() {
			phoneNo_ = "";
		}

		private byte memoizedIsInitialized = -1;

		public final boolean isInitialized() {
			byte isInitialized = memoizedIsInitialized;
			if (isInitialized != -1)
				return isInitialized == 1;

			if (!hasPhoneNo()) {
				memoizedIsInitialized = 0;
				return false;
			}
			memoizedIsInitialized = 1;
			return true;
		}

		public void writeTo(com.google.protobuf.CodedOutputStream output) throws java.io.IOException {
			getSerializedSize();
			if (((bitField0_ & 0x00000001) == 0x00000001)) {
				output.writeBytes(1, getPhoneNoBytes());
			}
			getUnknownFields().writeTo(output);
		}

		private int memoizedSerializedSize = -1;

		public int getSerializedSize() {
			int size = memoizedSerializedSize;
			if (size != -1)
				return size;

			size = 0;
			if (((bitField0_ & 0x00000001) == 0x00000001)) {
				size += com.google.protobuf.CodedOutputStream.computeBytesSize(1, getPhoneNoBytes());
			}
			size += getUnknownFields().getSerializedSize();
			memoizedSerializedSize = size;
			return size;
		}

		private static final long serialVersionUID = 0L;

		@java.lang.Override
		protected java.lang.Object writeReplace() throws java.io.ObjectStreamException {
			return super.writeReplace();
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(com.google.protobuf.ByteString data)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(com.google.protobuf.ByteString data,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data, extensionRegistry);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(byte[] data)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(byte[] data,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry)
				throws com.google.protobuf.InvalidProtocolBufferException {
			return PARSER.parseFrom(data, extensionRegistry);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(java.io.InputStream input)
				throws java.io.IOException {
			return PARSER.parseFrom(input);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(java.io.InputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseFrom(input, extensionRegistry);
		}

		public static UserRefuseMessage.UserRefuseEntity parseDelimitedFrom(java.io.InputStream input)
				throws java.io.IOException {
			return PARSER.parseDelimitedFrom(input);
		}

		public static UserRefuseMessage.UserRefuseEntity parseDelimitedFrom(java.io.InputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseDelimitedFrom(input, extensionRegistry);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(com.google.protobuf.CodedInputStream input)
				throws java.io.IOException {
			return PARSER.parseFrom(input);
		}

		public static UserRefuseMessage.UserRefuseEntity parseFrom(com.google.protobuf.CodedInputStream input,
				com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
			return PARSER.parseFrom(input, extensionRegistry);
		}

		public static Builder newBuilder() {
			return Builder.create();
		}

		public Builder newBuilderForType() {
			return newBuilder();
		}

		public static Builder newBuilder(UserRefuseMessage.UserRefuseEntity prototype) {
			return newBuilder().mergeFrom(prototype);
		}

		public Builder toBuilder() {
			return newBuilder(this);
		}

		@java.lang.Override
		protected Builder newBuilderForType(com.google.protobuf.GeneratedMessage.BuilderParent parent) {
			Builder builder = new Builder(parent);
			return builder;
		}

		/**
		 * Protobuf type {@code UserRefuseEntity}
		 */
		public static final class Builder extends com.google.protobuf.GeneratedMessage.Builder<Builder>
				implements UserRefuseMessage.UserRefuseEntityOrBuilder {
			public static final com.google.protobuf.Descriptors.Descriptor getDescriptor() {
				return UserRefuseMessage.internal_static_UserRefuseEntity_descriptor;
			}

			protected com.google.protobuf.GeneratedMessage.FieldAccessorTable internalGetFieldAccessorTable() {
				return UserRefuseMessage.internal_static_UserRefuseEntity_fieldAccessorTable
						.ensureFieldAccessorsInitialized(UserRefuseMessage.UserRefuseEntity.class,
								UserRefuseMessage.UserRefuseEntity.Builder.class);
			}

			// Construct using UserRefuseMessage.UserRefuseEntity.newBuilder()
			private Builder() {
				maybeForceBuilderInitialization();
			}

			private Builder(com.google.protobuf.GeneratedMessage.BuilderParent parent) {
				super(parent);
				maybeForceBuilderInitialization();
			}

			private void maybeForceBuilderInitialization() {
				if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
				}
			}

			private static Builder create() {
				return new Builder();
			}

			public Builder clear() {
				super.clear();
				phoneNo_ = "";
				bitField0_ = (bitField0_ & ~0x00000001);
				return this;
			}

			public Builder clone() {
				return create().mergeFrom(buildPartial());
			}

			public com.google.protobuf.Descriptors.Descriptor getDescriptorForType() {
				return UserRefuseMessage.internal_static_UserRefuseEntity_descriptor;
			}

			public UserRefuseMessage.UserRefuseEntity getDefaultInstanceForType() {
				return UserRefuseMessage.UserRefuseEntity.getDefaultInstance();
			}

			public UserRefuseMessage.UserRefuseEntity build() {
				UserRefuseMessage.UserRefuseEntity result = buildPartial();
				if (!result.isInitialized()) {
					throw newUninitializedMessageException(result);
				}
				return result;
			}

			public UserRefuseMessage.UserRefuseEntity buildPartial() {
				UserRefuseMessage.UserRefuseEntity result = new UserRefuseMessage.UserRefuseEntity(this);
				int from_bitField0_ = bitField0_;
				int to_bitField0_ = 0;
				if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
					to_bitField0_ |= 0x00000001;
				}
				result.phoneNo_ = phoneNo_;
				result.bitField0_ = to_bitField0_;
				onBuilt();
				return result;
			}

			public Builder mergeFrom(com.google.protobuf.Message other) {
				if (other instanceof UserRefuseMessage.UserRefuseEntity) {
					return mergeFrom((UserRefuseMessage.UserRefuseEntity) other);
				} else {
					super.mergeFrom(other);
					return this;
				}
			}

			public Builder mergeFrom(UserRefuseMessage.UserRefuseEntity other) {
				if (other == UserRefuseMessage.UserRefuseEntity.getDefaultInstance())
					return this;
				if (other.hasPhoneNo()) {
					bitField0_ |= 0x00000001;
					phoneNo_ = other.phoneNo_;
					onChanged();
				}
				this.mergeUnknownFields(other.getUnknownFields());
				return this;
			}

			public final boolean isInitialized() {
				if (!hasPhoneNo()) {

					return false;
				}
				return true;
			}

			public Builder mergeFrom(com.google.protobuf.CodedInputStream input,
					com.google.protobuf.ExtensionRegistryLite extensionRegistry) throws java.io.IOException {
				UserRefuseMessage.UserRefuseEntity parsedMessage = null;
				try {
					parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
				} catch (com.google.protobuf.InvalidProtocolBufferException e) {
					parsedMessage = (UserRefuseMessage.UserRefuseEntity) e.getUnfinishedMessage();
					throw e;
				} finally {
					if (parsedMessage != null) {
						mergeFrom(parsedMessage);
					}
				}
				return this;
			}

			private int bitField0_;

			// required string phoneNo = 1;
			private java.lang.Object phoneNo_ = "";

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public boolean hasPhoneNo() {
				return ((bitField0_ & 0x00000001) == 0x00000001);
			}

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public java.lang.String getPhoneNo() {
				java.lang.Object ref = phoneNo_;
				if (!(ref instanceof java.lang.String)) {
					java.lang.String s = ((com.google.protobuf.ByteString) ref).toStringUtf8();
					phoneNo_ = s;
					return s;
				} else {
					return (java.lang.String) ref;
				}
			}

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public com.google.protobuf.ByteString getPhoneNoBytes() {
				java.lang.Object ref = phoneNo_;
				if (ref instanceof String) {
					com.google.protobuf.ByteString b = com.google.protobuf.ByteString
							.copyFromUtf8((java.lang.String) ref);
					phoneNo_ = b;
					return b;
				} else {
					return (com.google.protobuf.ByteString) ref;
				}
			}

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public Builder setPhoneNo(java.lang.String value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000001;
				phoneNo_ = value;
				onChanged();
				return this;
			}

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public Builder clearPhoneNo() {
				bitField0_ = (bitField0_ & ~0x00000001);
				phoneNo_ = getDefaultInstance().getPhoneNo();
				onChanged();
				return this;
			}

			/**
			 * <code>required string phoneNo = 1;</code>
			 *
			 * <pre>
			 *必须字段，在后面的使用中必须为该段设置值
			 * </pre>
			 */
			public Builder setPhoneNoBytes(com.google.protobuf.ByteString value) {
				if (value == null) {
					throw new NullPointerException();
				}
				bitField0_ |= 0x00000001;
				phoneNo_ = value;
				onChanged();
				return this;
			}

			// @@protoc_insertion_point(builder_scope:UserRefuseEntity)
		}

		static {
			defaultInstance = new UserRefuseEntity(true);
			defaultInstance.initFields();
		}

		// @@protoc_insertion_point(class_scope:UserRefuseEntity)
	}

	private static com.google.protobuf.Descriptors.Descriptor internal_static_UserRefuseEntity_descriptor;
	private static com.google.protobuf.GeneratedMessage.FieldAccessorTable internal_static_UserRefuseEntity_fieldAccessorTable;

	public static com.google.protobuf.Descriptors.FileDescriptor getDescriptor() {
		return descriptor;
	}

	private static com.google.protobuf.Descriptors.FileDescriptor descriptor;
	static {
		java.lang.String[] descriptorData = { "\n\020UserRefuse.proto\"#\n\020UserRefuseEntity\022\017"
				+ "\n\007phoneNo\030\001 \002(\tB\023B\021UserRefuseMessage" };
		com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner = new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
			public com.google.protobuf.ExtensionRegistry assignDescriptors(
					com.google.protobuf.Descriptors.FileDescriptor root) {
				descriptor = root;
				internal_static_UserRefuseEntity_descriptor = getDescriptor().getMessageTypes().get(0);
				internal_static_UserRefuseEntity_fieldAccessorTable = new com.google.protobuf.GeneratedMessage.FieldAccessorTable(
						internal_static_UserRefuseEntity_descriptor, new java.lang.String[] { "PhoneNo", });
				return null;
			}
		};
		com.google.protobuf.Descriptors.FileDescriptor.internalBuildGeneratedFileFrom(descriptorData,
				new com.google.protobuf.Descriptors.FileDescriptor[] {}, assigner);
	}

	// @@protoc_insertion_point(outer_class_scope)
}
