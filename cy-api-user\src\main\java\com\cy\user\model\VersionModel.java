package com.cy.user.model;

public class VersionModel {

	/**
	 * 版本号
	 */
	private String version;
	/**
	 * 最新版本的升级url地址(apple应用地址)
	 */
	private String upgradeUrl;
	/**
	 * 升级版本状态： 0:可选升级版本 1:需强制升级到最新版本 2:当前版本已经是最新版本
	 * 
	 */
	private String upgradeStatus;
	/**
	 * 版本描述简要信息
	 */
	private String desc;
	/**
	 * 最新版本变化
	 */
	private String changeLog;
	/**
	 * 终端类型
	 */
	private String type;

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getUpgradeUrl() {
		return upgradeUrl;
	}

	public void setUpgradeUrl(String upgradeUrl) {
		this.upgradeUrl = upgradeUrl;
	}

	public String getUpgradeStatus() {
		return upgradeStatus;
	}

	public void setUpgradeStatus(String upgradeStatus) {
		this.upgradeStatus = upgradeStatus;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getChangeLog() {
		return changeLog;
	}

	public void setChangeLog(String changeLog) {
		this.changeLog = changeLog;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

}
