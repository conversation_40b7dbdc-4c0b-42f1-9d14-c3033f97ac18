package com.cy.content.model;

import java.io.Serializable;

public class CsTextModel  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 主键id
	 */
	private String csTextInfoNo;
	/**
	 * 彩印编号，id
	 */
	private String csNumber;
	/**
	 * 类别id
	 */
	private String csGroupId;
	/**
	 * 类别名称
	 */
	private String csGroupName;
	/**
	 * 标签id
	 */
	private String csLabelId;
	/**
	 * 标签名称
	 */
	private String csLabelName;
	/**
	 * 彩印内容
	 */
	private String csTextContent;
	/**
	 * 彩印状态
		1：上架
		2：待上架
		3：删除
		4：下架
	 */
	private int csTextStatus;
	
	private String csStatusName;
	/**
	 * 提交时间
	 */
	private String csSubmitTime;
	/**
	 * 彩印来源0：短信1：WEB2：SMC3：WAP4：手机客户端5：第三方

	 */
	private int csSubmitType;
	/**
	 * 提交人
	 */
	private String csSubmitUser;
	/**
	 * 修改时间
	 */
	private String csUpdateTime;
	/**
	 * 审核人
	 */
	private String auditor;
	/**
	 * 通过审核时间
	 */
	private String passTime;
	/**
	 * 使用人数
	 */
	private int useNumber;
	/**
	 * 鸡蛋
	 */
	private String egg;
	/**
	 * 鲜花
	 */
	private String flowers;
	/**
	 * 过期时间
	 */
	private String expiredDate;
	/**
	 * 查询起始位置
	 */
	private int offset = 0;
	/**
	 * 查询个数
	 * limit #{offset},#{range}
	 */
	private int range = 0;
	/**
	 * 0按最新上架的排序，1按最热门排序
	 */
	private int orderIndex = 0;
	
	private int actionType;
	
	private String startUpdateTime;
	
	private String endUpdateTime;
	
	private String startSubmitTime;
	
	private String endSubmitTime;
	
	private int tempId;



	private String contentType;

	private Integer serviceType;

	private String operator;

	private String qualificationsFiles;

	/**
	 * 电信投递通道，5：号百；8：彩讯
	 */
	private String telecomDeliveryWay;

	/**
	 * 联通投递通道，6：联通在线；8：彩讯
	 */
	private String unicomDeliveryWay;

	private String deliveryType;

	public String getDeliveryType() {
		return deliveryType;
	}

	public void setDeliveryType(String deliveryType) {
		this.deliveryType = deliveryType;
	}

	public String getTelecomDeliveryWay() {
		return telecomDeliveryWay;
	}

	public void setTelecomDeliveryWay(String telecomDeliveryWay) {
		this.telecomDeliveryWay = telecomDeliveryWay;
	}

	public String getUnicomDeliveryWay() {
		return unicomDeliveryWay;
	}

	public void setUnicomDeliveryWay(String unicomDeliveryWay) {
		this.unicomDeliveryWay = unicomDeliveryWay;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}
	public int getTempId() {
	    return tempId;
	}
	public void setTempId(int tempId) {
	    this.tempId = tempId;
	}
	public int getActionType() {
	    return actionType;
	}
	public void setActionType(int actionType) {
	    this.actionType = actionType;
	}
	public String getStartUpdateTime() {
	    return startUpdateTime;
	}
	public void setStartUpdateTime(String startUpdateTime) {
	    this.startUpdateTime = startUpdateTime;
	}
	public String getEndUpdateTime() {
	    return endUpdateTime;
	}
	public void setEndUpdateTime(String endUpdateTime) {
	    this.endUpdateTime = endUpdateTime;
	}
	public String getEgg() {
	    return egg;
	}
	public void setEgg(String egg) {
	    this.egg = egg;
	}
	public String getFlowers() {
	    return flowers;
	}
	public void setFlowers(String flowers) {
	    this.flowers = flowers;
	}
	public String getExpiredDate() {
	    return expiredDate;
	}
	public void setExpiredDate(String expiredDate) {
	    this.expiredDate = expiredDate;
	}
	public int getOrderIndex() {
	    return orderIndex;
	}
	public void setOrderIndex(int orderIndex) {
	    this.orderIndex = orderIndex;
	}
	public int getOffset() {
	    return offset;
	}
	public void setOffset(int offset) {
	    this.offset = offset;
	}
	public int getRange() {
	    return range;
	}
	public void setRange(int range) {
	    this.range = range;
	}
	public int getCsSubmitType() {
	    return csSubmitType;
	}
	public void setCsSubmitType(int csSubmitType) {
	    this.csSubmitType = csSubmitType;
	}
	public String getCsSubmitUser() {
	    return csSubmitUser;
	}
	public void setCsSubmitUser(String csSubmitUser) {
	    this.csSubmitUser = csSubmitUser;
	}
	public String getCsUpdateTime() {
	    return csUpdateTime;
	}
	public void setCsUpdateTime(String csUpdateTime) {
	    this.csUpdateTime = csUpdateTime;
	}
	public String getCsNumber() {
	    return csNumber;
	}
	public void setCsNumber(String csNumber) {
	    this.csNumber = csNumber;
	}
	public String getCsTextInfoNo() {
		return csTextInfoNo;
	}
	public void setCsTextInfoNo(String csTextInfoNo) {
		this.csTextInfoNo = csTextInfoNo;
	}
	public String getCsGroupId() {
		return csGroupId;
	}
	public void setCsGroupId(String csGroupId) {
		this.csGroupId = csGroupId;
	}
	public String getCsGroupName() {
		return csGroupName;
	}
	public void setCsGroupName(String csGroupName) {
		this.csGroupName = csGroupName;
	}
	public String getCsLabelId() {
		return csLabelId;
	}
	public void setCsLabelId(String csLabelId) {
		this.csLabelId = csLabelId;
	}
	public String getCsLabelName() {
		return csLabelName;
	}
	public void setCsLabelName(String csLabelName) {
		this.csLabelName = csLabelName;
	}
	public String getCsTextContent() {
		return csTextContent;
	}
	public void setCsTextContent(String csTextContent) {
		this.csTextContent = csTextContent;
	}
	public String getCsSubmitTime() {
		return csSubmitTime;
	}
	public void setCsSubmitTime(String csSubmitTime) {
		this.csSubmitTime = csSubmitTime;
	}
	public String getAuditor() {
		return auditor;
	}
	public void setAuditor(String auditor) {
		this.auditor = auditor;
	}
	public String getPassTime() {
		return passTime;
	}
	public void setPassTime(String passTime) {
		this.passTime = passTime;
	}
	public int getUseNumber() {
		return useNumber;
	}
	public void setUseNumber(int useNumber) {
		this.useNumber = useNumber;
	}
	
	public int getCsTextStatus() {
		return csTextStatus;
	}
	public void setCsTextStatus(int csTextStatus) {
		this.csTextStatus = csTextStatus;
	}
	public String getCsStatusName() {
		return csStatusName;
	}
	public void setCsStatusName(String csStatusName) {
		this.csStatusName = csStatusName;
	}
	public String getStartSubmitTime() {
	    return startSubmitTime;
	}
	public void setStartSubmitTime(String startSubmitTime) {
	    this.startSubmitTime = startSubmitTime;
	}
	public String getEndSubmitTime() {
	    return endSubmitTime;
	}
	public void setEndSubmitTime(String endSubmitTime) {
	    this.endSubmitTime = endSubmitTime;
	}

	public Integer getServiceType() {
		return serviceType;
	}

	public void setServiceType(Integer serviceType) {
		this.serviceType = serviceType;
	}

	public String getOperator() {
		return operator;
	}

	public void setOperator(String operator) {
		this.operator = operator;
	}

	public String getQualificationsFiles() {
		return qualificationsFiles;
	}

	public void setQualificationsFiles(String qualificationsFiles) {
		this.qualificationsFiles = qualificationsFiles;
	}
}
