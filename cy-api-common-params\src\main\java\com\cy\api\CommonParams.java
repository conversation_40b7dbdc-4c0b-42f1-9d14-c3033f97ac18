package com.cy.api;

import java.util.List;

import com.cy.model.request.QueryNpInfoReq;
import com.cy.model.response.QueryAttributionByPhoneSectionResp;
import com.cy.model.response.QueryNpInfoResp;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.cy.model.CityModel;
import com.cy.model.DefaultSignModel;
import com.cy.model.NumberModel;
import com.cy.model.PackageModel;
import com.cy.model.PageModel;
import com.cy.model.ProvinceModel;

public interface CommonParams {

	/**
	 * 根据产品编码获取套餐信息。
	 * 
	 * @param productNo
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getPackageByProductNo", method = RequestMethod.POST)
	PackageModel getPackageByProductNo(String productNo);

	/**
	 * 获取所有套餐信息。
	 * 
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getAllPackage")
	List<PackageModel> getAllPackage();

	/**
	 * 根据手机号码获取号段信息
	 * 
	 * @param phoneNo
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getSectionByPhone", method = RequestMethod.POST)
	NumberModel getSectionByPhone(String phoneNo);

	/**
	 * 获取默认彩印信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getDefaultSign", method = RequestMethod.POST)
	DefaultSignModel getDefaultSign(String provinceCode);

	/**
	 * 根据号段获取默认彩印信息
	 *
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getDefaultSignBySectionNo", method = RequestMethod.POST)
	DefaultSignModel getDefaultSignBySectionNo(String sectionNo);

	/**
	 * 获取所有省份信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getAllProvinces")
	List<ProvinceModel> getAllProvinces();

	/**
	 * 获取城市信息，省份为空则获得所有城市信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getCityByProvince")
	List<CityModel> getCityByProvince(CityModel model);

	/**
	 * 根据城市编码获取省份信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/queryAttributionByPhoneSection", method = RequestMethod.POST)
	QueryAttributionByPhoneSectionResp queryAttributionByPhoneSection(List<String> phoneSectionList);

	/**
	 * 根据城市编码获取省份信息
	 *
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getProvinceByCity", method = RequestMethod.POST)
	List<CityModel> getProvinceByCity(String cityCode);

	/**
	 * 根据手机号编码获取属于分区信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getZoneByPhoneNo", method = RequestMethod.POST)
	String getZoneByPhoneNo(String phoneNo);

	/**
	 * 判断号码是否存在红名单里面
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/isRedNumber", method = RequestMethod.POST)
	boolean isRedNumber(String phoneNo);

	/**
	 * 获取所有红名单的数量
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getRedNumberCount")
	int getRedNumberCount();

	/**
	 * 获取红名单 index 开始位置 pageSize 每次查询数量
	 * 
	 * @return
	 */
	@RequestMapping(value = "/commonParam/getRedNumber")
	List<String> getRedNumberByPage(PageModel page);

	/**
	 * 获取携号转网信息
	 *
	 * @return
	 */
	@RequestMapping(value = "/carryTurn/query/npInfo")
	QueryNpInfoResp queryNpInfo(@RequestBody QueryNpInfoReq queryNpInfoReq);

	/**
	 * 查询携转用户对应的分区
	 *
	 * @return
	 */
	@RequestMapping(value = "/carryTurn/query/area")
	String getAreaByCarrayTurnNum(@RequestBody String phoneNo);
}
