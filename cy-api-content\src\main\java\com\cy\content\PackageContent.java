package com.cy.content;

import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import com.cy.content.model.CsPackageModel;


public interface PackageContent {
    
    /**
     * 获取所有审批通过的彩印盒总条数
     * @param CsPackageModel
     * @return List<CsPackageModel>
     */
    @PostMapping("/pkgContent/getPkgSize")
    int getPkgSize(CsPackageModel pkgModel);
    
    /**
     * 获取所有审批通过的彩印盒
     * @param CsPackageModel
     * @return List<CsPackageModel>
     */
    @PostMapping("/pkgContent/getPkgContent")
    List<CsPackageModel> getPkgContent(CsPackageModel pkgModel);
    
    /**
     * 获取所有彩印盒
     * @param CsPackageModel
     * @return List<CsPackageModel>
     */
    @PostMapping("/pkgContent/getAllPkgList")
    List<CsPackageModel> getAllPkgList(CsPackageModel pkgModel);
    
    /**
     * 查询单个彩印盒详情
     * @param CsPackageModel
     * @return CsPackageModel
     */
    @PostMapping("/pkgContent/getContent")
    CsPackageModel getContent(CsPackageModel pkgModel);
    
    /**
     * 用户选择的彩印盒
     * @param String
     * @return 
     */
    @PostMapping("/pkgContent/usedCsPackage")
    void usedCsPackage(String csPkgNumber);
}
