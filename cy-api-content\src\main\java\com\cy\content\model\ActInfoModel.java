package com.cy.content.model;

import java.util.Date;

public class ActInfoModel {
    private Integer id;
    private String activityId;
    private String activityName;
    private Date createTime;
    private String packageName;
    private Integer packageUserCount=0;
    private Integer txtUserCount=0;
    private Integer diyUserCount=0;
    private Integer browerCount=0;
    private Integer loginUserCount=0;
    private Integer shareCount=0;
    private int pageNum;
    private int pageSize;

    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getActivityId() {
        return activityId;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId == null ? null : activityId.trim();
    }

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName == null ? null : activityName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    public Integer getPackageUserCount() {
        return packageUserCount;
    }

    public void setPackageUserCount(Integer packageUserCount) {
        this.packageUserCount = packageUserCount;
    }

    public Integer getTxtUserCount() {
        return txtUserCount;
    }

    public void setTxtUserCount(Integer txtUserCount) {
        this.txtUserCount = txtUserCount;
    }

    public Integer getDiyUserCount() {
        return diyUserCount;
    }

    public void setDiyUserCount(Integer diyUserCount) {
        this.diyUserCount = diyUserCount;
    }

    public Integer getBrowerCount() {
        return browerCount;
    }

    public void setBrowerCount(Integer browerCount) {
        this.browerCount = browerCount;
    }

    public Integer getLoginUserCount() {
        return loginUserCount;
    }

    public void setLoginUserCount(Integer loginUserCount) {
        this.loginUserCount = loginUserCount;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }
}