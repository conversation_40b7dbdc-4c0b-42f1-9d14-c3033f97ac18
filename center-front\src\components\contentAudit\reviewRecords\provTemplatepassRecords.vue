<template>
    <div class="provinces">
        <div class="user-titler">{{$route.name}}</div>
        <div class="app-norbox">
            <!--查询条件-->
            <div>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline" size="small" label-width="70px">
                    <el-form-item label="模版ID">
                        <el-input  v-model="searchForm.templateId" placeholder="" class="app-input"></el-input>
                    </el-form-item>
                    <el-form-item label="模版名称">
                        <el-input  v-model="searchForm.templateName" placeholder="" class="app-input"></el-input>
                    </el-form-item>
                </el-form>
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-bottom" size="small" label-width="70px">
                    <el-form-item label="提交时间" style="margin-top: 10px;">
                        <el-date-picker
                                v-model="searchForm.startTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item style="margin-top: 10px;">
                        <el-button type="primary" @click="search(1)">查询</el-button>
                    </el-form-item>
                </el-form>
                <el-form :inline="true" class="demo-form-inline" size="small">
                    <el-form-item>
                        <!--<el-button :type="typeoff" :disabled="clickoff" size="small" @click="alowList('')">批量通过</el-button>-->
                        <el-button :type="typeoff" :disabled="clickoff" size="small" @click="rejectList('')">批量撤销</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border
                      @selection-change="handleSelectionChange"
                      :header-cell-class-name="tableheaderClassNameZ"
                      class="app-tab02">
                <el-table-column
                        type="selection"
                        width="55">
                </el-table-column>
                <el-table-column prop="templateId" label="模版ID" />
                <el-table-column prop="callingRemindContent" label="主叫提醒内容" />
                <el-table-column prop="calledRemindContent" label="被叫提醒内容" />
                <!--<el-table-column prop="sysUserEmail" label="模版内容">-->
                    <!--<template slot-scope="scope">-->
                        <!--<el-button type="text" @click="details(scope.row)" size="small">查看详情</el-button>-->
                    <!--</template>-->
                <!--</el-table-column>-->
                <el-table-column prop="auditStatus" label="审核意见"/>
                <el-table-column prop="submitUserAccount" label="提交人"/>
                <el-table-column prop="submitTime" label="提交时间"/>
                <el-table-column prop="submitUserAccount" label="审核人"/>
                <el-table-column prop="auditTime" label="审核时间"/>
                <el-table-column label="操作" width="110">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="rejectList(scope.row)">撤销</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!--分页-->
            <div class="block app-pageganit" v-show="total">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
        <!--查看详情-->
        <el-dialog
                title="模版详情"
                :visible.sync="dialogVisible"
                width="40%" :close-on-click-modal="false">
            <ul class="detbox">
                <li>
                    <p>拨打号码提醒内容:</p>
                    <p class="fontcol">{{callingRemindContent}}</p>
                </li>
                <li>
                    <p>接听号码提醒内容:</p>
                    <p class="fontcol">{{calledRemindContent}}</p>
                </li>
                <li>
                    <p>描述:</p>
                    <p class="fontcol">{{description}}</p>
                </li>
            </ul>
        </el-dialog>
        <!--驳回-->
        <el-dialog
                width="30%"
                title="撤销"
                :visible.sync="rejectVisible"
                append-to-body :close-on-click-modal="false">
            <el-form :model="ReasonFrom" :rules="rules" ref="ReasonFrom" label-width="100px" justify="center">
                <el-form-item label="撤销原因" prop="rejectReason">
                    <el-input v-model="ReasonFrom.rejectReason"
                              type="textarea"
                              :rows="2"
                              placeholder="" style="width: 200px;"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" style="text-align: right;">
                <el-button @click="rejectVisible = false;resetForm('ReasonFrom')"size="small">取 消</el-button>
                <el-button type="primary" @click="rejectbtn"size="small">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
    //    import editprovinces from './editprovinces'
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'provinces',
        data(){
            return{
                dialogVisible:false,
                rejectVisible:false,
                passVisible:false,
                clickoff:true,
                typeoff:'info',
                rowData:'',//操作列表
                //查询form对象定义
                searchForm: {
                    templateId:'', //模版id
                    templateName:'',//模版名称,
                    startDate:'',
                    endDate:'',
                    startTime:'',//创建时间
                    pageSize:10,// 每页显示条数
                    pageNo:1, // 查询的页码
                },
                tableData:[],
                currentPage: 1,
                total:0,
                callingRemindContent:'',//主叫提醒
                calledRemindContent:'',//被叫提醒
                description:'',//描述
                checkFrom:{
                    auditNumList:[
                        {
                            templateId:'',//模版ID
                            auditStatus:'',//审核状态0:待审核,1:审核通过,2:审核驳回,3:撤销
                            auditReason:'',//原因
                            auditUserAccount:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//审核人
                        }
                    ]
                },//单个审核参数
                checkallFrom:{
                    auditNumList:[]
                },
                ReasonFrom:{
                    rejectReason:'',//驳回原因
                },
                rules: {
                    rejectReason: [
                        { required: true, message: '请输入撤销原因', trigger: 'blur' },
                    ]
                },
                corpInfoIds:[],//选择框
                allclick:false
            }
        },
        components: {
//            editprovinces
        },
        created(){
            this.search();
        },
        watch:{
            rejectVisible(){
                if(!this.rejectVisible){
                    this.resetForm('ReasonFrom');
                }
            },
            corpInfoIds(){
                if(this.corpInfoIds.length){
                    this.clickoff = false;
                    this.typeoff = 'primary';
                }else{
                    this.clickoff = true;
                    this.typeoff = 'info'
                }
            }
        },
        methods:{
            //多选框
            handleSelectionChange(val) {
                this.corpInfoIds=[];
                for (var i = 0; i < val.length; i++) {
                    this.corpInfoIds.push(val[i].templateId);
                }
            },
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search()
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search()
            },
            //查看详情
            details(row){
                let vm = this;
                vm.dialogVisible = true;
                vm.callingRemindContent = row.callingRemindContent;
                vm.calledRemindContent = row.calledRemindContent;
                vm.description = row.description;
            },
            //撤销
            rejectList(row){
                let vm = this;
                if(row){
                    this.rowData=row;
                    this.allclick = false;
                }else{
                    this.rowData='';
                    this.allclick = true;
                }
                this.rejectVisible = !this.rejectVisible;
            },
            //撤销确定
            rejectbtn() {
                let vm = this;
                this.$refs['ReasonFrom'].validate((valid) => {
                    if(valid){
                        if(this.allclick){
                            this.passAll(3);
                            return;
                        }
                        this.checkFrom.auditNumList[0].templateId=this.rowData.templateId;
                        this.checkFrom.auditNumList[0].auditStatus = 3;//撤销
                        this.checkFrom.auditNumList[0].auditReason = this.ReasonFrom.rejectReason;//撤销原因
                        postHeader('auditNumTemplateProvince',JSON.stringify(this.checkFrom)).then(res=>{
                            let data = res.data;
                        if(data.code==0){
                            vm.$message.success("撤销成功");
                            this.search(1);
                        }else{
                            vm.$message.error("撤销失败");
                        }
                    })
                        this.resetForm('ReasonFrom');
                        this.rejectVisible = !this.rejectVisible;
                    }
                })
            },
            //批量审核
            passAll(int){
                let vm = this;
                let auditNumList = [];
                this.corpInfoIds.forEach(item=>{
                    auditNumList.push({
                    templateId:item,//模版ID
                    auditStatus:int,//审核状态0:待审核,1:审核通过,2:审核驳回,3:撤销
                    auditReason:int==3?this.ReasonFrom.rejectReason:'',//原因
                    auditUserAccount:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//审核人
                })
            })
                this.checkallFrom.auditNumList = auditNumList;
                console.log(this.checkallFrom.auditNumList)
                postHeader('auditNumTemplateProvince',JSON.stringify(this.checkallFrom)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.$message.success("撤销成功");
                    this.search(1);
                }else{
                    vm.$message.error("撤销失败");
                }
            })
                vm.passVisible = false;
                vm.rejectVisible = false;
            },

            //查询请求
            search(pg) {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                postHeader('queryNumTemplateRecordPassList', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.tableData = data.data.list;
                    if(vm.tableData&&vm.tableData.length){
                        vm.tableData.forEach(item=>{
                            switch(Number(item.auditStatus)){
                        case 0:
                            item.auditStatus = '未审核';
                            break;
                        case 1:
                            item.auditStatus = '已通过';
                            break;
                        case 2:
                            item.auditStatus = '驳回';
                            break;
                        case 3:
                            item.auditStatus = '已撤销';
                            break;
                        default:
                            break;
                        }
                    })
                    }
                    vm.total = data.data.total;
                }
            })
            },

            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style scoped>
    .detbox{
        margin-left: 30px;
        margin-right: 20px;
    }
    .detbox li{
        border-bottom: 1px solid #cccccc;
    }
    .fontcol{
        color: #cccccc;
    }
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
