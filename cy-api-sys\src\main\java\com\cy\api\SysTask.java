package com.cy.api;

import com.cy.common.ResultListCommon;
import com.cy.common.SysTaskCommon;
import com.cy.common.SysUserCommon;
import com.cy.model.SysTaskModel;
import com.cy.model.SysUserModel;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

public interface SysTask {

	/**
	 *
	 * 分页查询异步任务
	 *
	 */
	@RequestMapping(value = "/sysTaskCore/getSysTaskPage")
	ResultListCommon getSysTaskPage(SysTaskCommon common);


	/**
	 *
	 * 开始上传
	 *
	 */
	@RequestMapping(value = "/sysTaskCore/startUpload")
	int startUpload(SysTaskModel common);


	/**
	 *
	 * 上传失败
	 *
	 */
	@RequestMapping(value = "/sysTaskCore/failUpload")
	int failUpload(SysTaskModel common);


	/**
	 * 完成上传
	 */
	@RequestMapping(value = "/sysTaskCore/endUpload")
	int endUpload(SysTaskModel common);

	@RequestMapping(value = "/sysTaskCore/get/id")
	SysTaskModel getSysTask(@RequestBody SysTaskModel sysModel);

	@RequestMapping(value = "/sysUser/get/userName")
	SysUserModel getSysUserId(@RequestBody SysUserCommon sysUserCommon);
}
