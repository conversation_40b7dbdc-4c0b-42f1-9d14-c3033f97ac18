package com.cy.sensitive;

import com.cy.sensitive.common.CsSensitiveWordsRecordReq;
import com.cy.sensitive.model.CsSensitiveWordsRecord;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * @ClassName SensitiveWordsRecordContent
 * @Desciption
 * <AUTHOR>
 * @Date 2024/4/23 18:48
 * @Version 1.0
 */
public interface SensitiveWordsRecord {

    @RequestMapping(value = "sensitive/words/record")
    List<CsSensitiveWordsRecord> getSensitiveWordsRecord(@RequestBody CsSensitiveWordsRecordReq csSensitiveWordsRecordReq);
}
