package com.cy.user.model;

import java.util.List;

/**
 * 
 * <AUTHOR>
 *
 */
public class PersonCsSetModel {
	/**
	 * 彩印编号
	 */
	private String csNumber;
	/**
	 * 彩印盒编号
	 */
	private String csPkgNumber;
	/**
	 * 设置类型: //1文本彩印，2彩印盒，3图片彩印，4视频彩印
	 */

	private String setCsType;

	private String setCsTypeName;

	private String setCsTypeId;
	/**
	 * 设置内容
	 */
	private String setCsContent;
	/**
	 * 接收方类型://2.所有人 1号码分组 0指定好友
	 */
	private String setRecType;

	private String setRecTypeName;
	/**
	 * 接收号码
	 */
	private String setRecPhone;
	/**
	 * 接收号码
	 */
	private List<String> setRecPhones;
	/**
	 * 设置时间
	 */
	private String setTime;
	/**
	 * 渠道来源
	 */
	private String channel;
	//结束时间
	private String crEndDate;
	//内容类型
	private String setCsContentType;

	public String getSetCsContentType() {
		return setCsContentType;
	}

	public void setSetCsContentType(String setCsContentType) {
		this.setCsContentType = setCsContentType;
	}

	/**
	 * 彩印盒内容
	 */
	private String csContent1, csContent2, csContent3, csContent4, csContent5;

	private String ruleId;

	private String serviceCode;

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public List<String> getSetRecPhones() {
		return setRecPhones;
	}

	public void setSetRecPhones(List<String> setRecPhones) {
		this.setRecPhones = setRecPhones;
	}

	public String getCsPkgNumber() {
		return csPkgNumber;
	}

	public void setCsPkgNumber(String csPkgNumber) {
		this.csPkgNumber = csPkgNumber;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public String getCsContent1() {
		return csContent1;
	}

	public void setCsContent1(String csContent1) {
		this.csContent1 = csContent1;
	}

	public String getCsContent2() {
		return csContent2;
	}

	public void setCsContent2(String csContent2) {
		this.csContent2 = csContent2;
	}

	public String getCsContent3() {
		return csContent3;
	}

	public void setCsContent3(String csContent3) {
		this.csContent3 = csContent3;
	}

	public String getCsContent4() {
		return csContent4;
	}

	public void setCsContent4(String csContent4) {
		this.csContent4 = csContent4;
	}

	public String getCsContent5() {
		return csContent5;
	}

	public void setCsContent5(String csContent5) {
		this.csContent5 = csContent5;
	}

	public String getCsNumber() {
		return csNumber;
	}

	public void setCsNumber(String csNumber) {
		this.csNumber = csNumber;
	}

	public String getSetCsType() {
		return setCsType;
	}

	public void setSetCsType(String setCsType) {
		this.setCsType = setCsType;
	}

	public String getSetCsTypeId() {
		return setCsTypeId;
	}

	public void setSetCsTypeId(String setCsTypeId) {
		this.setCsTypeId = setCsTypeId;
	}

	public String getSetCsContent() {
		return setCsContent;
	}

	public void setSetCsContent(String setCsContent) {
		this.setCsContent = setCsContent;
	}

	public String getSetRecType() {
		return setRecType;
	}

	public void setSetRecType(String setRecType) {
		this.setRecType = setRecType;
	}

	public String getSetTime() {
		return setTime;
	}

	public void setSetTime(String setTime) {
		this.setTime = setTime;
	}

	public String getSetRecPhone() {
		return setRecPhone;
	}

	public void setSetRecPhone(String setRecPhone) {
		this.setRecPhone = setRecPhone;
	}

	public String getSetRecTypeName() {
		return setRecTypeName;
	}

	public void setSetRecTypeName(String setRecTypeName) {
		this.setRecTypeName = setRecTypeName;
	}

	public String getSetCsTypeName() {
		return setCsTypeName;
	}

	public void setSetCsTypeName(String setCsTypeName) {
		this.setCsTypeName = setCsTypeName;
	}

	public String getServiceCode() {
		return serviceCode;
	}

	public void setServiceCode(String serviceCode) {
		this.serviceCode = serviceCode;
	}

	public String getCrEndDate() {
		return crEndDate;
	}

	public void setCrEndDate(String crEndDate) {
		this.crEndDate = crEndDate;
	}
}
