package com.cs.param.execl;

import com.github.crab2died.annotation.ExcelField;

public class SectionData {

	@ExcelField(title = "*号段", order = 1)
	private String phoneSection;

	@ExcelField(title = "运营商编码", order = 2)
	private String optCode;

	@ExcelField(title = "*所在网络", order = 3)
	private String netCode;

	@ExcelField(title = "*省份", order = 4)
	private String provinceCode;

	@ExcelField(title = "*城市", order = 5)
	private String regionCode;

	@ExcelField(title = "所属HLR", order = 6)
	private String hlrAddr;

	@ExcelField(title = "*终端类型", order = 7)
	private String terminalCode;

	@ExcelField(title = "状态", order = 8)
	private String status;

	public String getPhoneSection() {
		return phoneSection;
	}

	public void setPhoneSection(String phoneSection) {
		this.phoneSection = phoneSection;
	}

	public String getOptCode() {
		return optCode;
	}

	public void setOptCode(String optCode) {
		this.optCode = optCode;
	}

	public String getNetCode() {
		return netCode;
	}

	public void setNetCode(String netCode) {
		this.netCode = netCode;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getRegionCode() {
		return regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getHlrAddr() {
		return hlrAddr;
	}

	public void setHlrAddr(String hlrAddr) {
		this.hlrAddr = hlrAddr;
	}

	public String getTerminalCode() {
		return terminalCode;
	}

	public void setTerminalCode(String terminalCode) {
		this.terminalCode = terminalCode;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

}
