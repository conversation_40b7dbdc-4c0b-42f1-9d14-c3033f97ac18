package com.cy.user.cs.request;

/**
 * 
 * <AUTHOR> @date
 * @Description 彩印平台请求一级能力平台退订彩印信息
 */
public class ContractUnsubscribeReq extends ContractReq {

	private String cpCode;

	public ContractUnsubscribeReq() {

	}

	public ContractUnsubscribeReq(Header header) {
		this.requestHeader = header;
	}

	/**
	 * @return the cpCode
	 */
	public String getCpCode() {
		return cpCode;
	}

	/**
	 * @param cpCode
	 *            the cpCode to set
	 */
	public void setCpCode(String cpCode) {
		this.cpCode = cpCode;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "ContractUnsubscribeReq [cpCode=" + cpCode + "," + super.toString() +  "]";
	}
}
