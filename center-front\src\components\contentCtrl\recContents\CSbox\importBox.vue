<template>
<div>
    <h1 class="user-title">导入文本素材</h1>
    <div class="user-line"></div>
    <el-form style="text-align:center">
      <el-form-item>
        <el-upload
            class="upload-demo"
            ref="upload"
            action=""
            :auto-upload='false'
            :on-remove='fileRemove'
            :on-change="handleChange"
            accept=".xls, .xlsx">
          <el-button  type="primary" size="small">上传excel表</el-button>
        </el-upload>
        <a href="javaScript:;"  @click="getTemp();">下载模板文件</a>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  @click="submitForm()" size="small">提 交</el-button>
      </el-form-item>
    </el-form>
</div>
</template>
<script>
import {post} from './../../../../servers/httpServer.js';
export default {
  name: "importPacakge",
  data() {
    return {
       file:'',
      fileList:[],
      request: {
        phone: "",
        status: "",
        csContent: "",
        startTime: "",
        endTime: ""
      }
    };
  },
  methods: {
    // 上传移除
    fileRemove(file){
      this.file='';
    },
    handleChange(file,fileList){
      this.file=file.raw;
    },
    //提交批量用户信息
    submitForm(){
      if(!this.file){
        this.$message({
          message:'请上传excel文件',
          type:'warning'
        })
        return;
      }
      //let formData=new FormData();
      //formData.append('file',this.file);
      // post(`/content/csPackage/uploadPackage`,formData).then(res=>{
      //   if(res.data.result==='success'){
      //     this.$message({
      //       message:'提交成功',
      //       type:'success'
      //     })
      //   }
      // })
    },
    //下载模板文件
    getTemp(){
      window.open(`${this.proxyUrl}/content/csPackage/getTemplate`);
    }
  }
};
</script>
<style>
.fl {
  float: left;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 40%;
  margin: 0 auto;
  margin-top: 3%;
}
/* 弹窗checkbox样式 */
.el-form-item__content{
  margin:0 !important;
}
.el-checkbox{
  margin-left:30px;
}
</style>
