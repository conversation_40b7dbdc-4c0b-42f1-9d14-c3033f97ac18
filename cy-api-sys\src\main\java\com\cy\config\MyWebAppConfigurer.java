package com.cy.config;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import com.cy.token.interceptor.JWTInterceptor;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
public class MyWebAppConfigurer implements WebMvcConfigurer {

	@Value("${jwt.excludePathPatterns:/sysUser/auth/token}")
	private String excludePathPatterns;

	@Bean // 把我们的拦截器注入为bean
	public HandlerInterceptor getMyInterceptor() {
		return new JWTInterceptor();
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		// addPathPatterns 用于添加拦截规则, 这里假设拦截 /url 后面的全部链接
		// excludePathPatterns 用户排除拦截
		String excludePaths[] = null;
		if (!StringUtils.isBlank(excludePathPatterns)) {
			excludePaths = excludePathPatterns.split(",");
		}
		registry.addInterceptor(getMyInterceptor()).excludePathPatterns(excludePaths);
	}
}