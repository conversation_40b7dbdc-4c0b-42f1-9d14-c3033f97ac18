<template>
<div class="login-margin">
    <el-row>
        <el-col :span="13" style="margin-top: 3px"></el-col>
        <el-col :span="6" style="margin-top:11%">
            <div class="login-warp-box">
                <div class="user-login">
                    <h1>和彩印中央管理平台</h1>
                    <ul>
                        <li>
                            <el-input v-model="modForm.sysUserName" @blur="getSysUser()" placeholder="请输入用户名" size="small" :maxlength="15"></el-input>
                        </li>

                        <li>
                            <span class="jiaoyan">
                              <el-input placeholder="请输入短信验证码"  v-model="modForm.captcha" size="small" :maxlength="6" @keyup.native.enter="mod"></el-input></span>
                            <span class="yan-img"  v-show="timeCount === 0">
                              <el-button type="success" @click="getSmsVerifyCode()" :disabled="!getSmsVerifyCodeDisabled" style="width: 100%;padding: 12px 0;" >获取验证码</el-button>
                            </span>

                            <span class="yan-img" style="background-color: #67c23a;color: #FFFFFF;text-align: center;font-size: 16px;line-height: 40px;" v-show="timeCount > 0">
                              {{timeCount + 's'}}
                              </span>
                        </li>
                        <li>
                          <el-input type="password" placeholder="请输入新密码" v-model="modForm.newPassword" size="small" :maxlength="16"  @keyup.native.enter="mod"></el-input>
                        </li>
                        <li>
                          <el-input type="password" placeholder="请输入确认密码" v-model="modForm.newPassword2" size="small" :maxlength="16"  @keyup.native.enter="mod"></el-input>
                        </li>

                        <li id="drapImgCode" style="background: #ebedef;">
                        </li>
                    </ul>

                    <div class="button-box" style="width: 50%;float: left;"><el-button type="success" @click="mod()" :disabled="!modForm.sysUserName || !modForm.newPassword|| !modForm.newPassword2||!modForm.captcha||!modForm.token||!this.getSmsVerifyCodeDisabled" style="width: 100%;">保存修改</el-button></div>
                    <div class="button-box" style="width: 30%;float: right;"><el-button type="success" @click="back()"  style="width: 100%;">返回</el-button></div>

                </div>
            </div>
        </el-col>
    </el-row>

</div>
</template>
<script src="./forgetPwd.js"></script>

<style>
    /*.lo_reg_s{ margin-bottom:100px; clear:both; overflow:hidden}*/
    /*.lo_reg_s img.fl{ margin-top:60px;}*/
    .user-search{


    }
    .login-line{
        height: 1px;
        background: #e5e5e5;
        box-shadow: 0 3px 3px #e5e5e5;
    }
    p{color:red;}
    .yt{
      margin-top: 0 !important;
    }
</style>
