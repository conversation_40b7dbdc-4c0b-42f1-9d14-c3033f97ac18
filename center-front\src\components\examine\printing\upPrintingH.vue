<template>
  <div>
    <h1 class="user-title">我的代办-编辑个人彩印盒</h1>
    <hr class="user-line"/>
    <div id="printingUpTab">
  <el-form ref="form" :model="form" label-width="80px">
  <el-form-item label="彩印ID：">
    {{this.$route.params.name}}
  </el-form-item>
  <el-form-item label="彩印盒名称">
    <el-input v-model="form.name"></el-input>
  </el-form-item>
  <el-form-item label="分类：">
    <el-select v-model="form.province" placeholder="请选择活动区域">
      <el-option label="区域一" value="shanghai"></el-option>
      <el-option label="区域二" value="beijing"></el-option>
    </el-select>
  </el-form-item>
  <el-form-item label="标签：">
    
  </el-form-item>
  <el-form-item label="彩印1">
    <el-input type="textarea" v-model="form.city"></el-input>
  </el-form-item>
  <el-form-item label="彩印2">
    <el-input type="textarea" v-model="form.date"></el-input>
  </el-form-item>
  <el-form-item>
    <el-button type="primary" @click="onSubmit">提交</el-button>
  </el-form-item>
</el-form>
</div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        form: this.$route.params
      }
    },
    methods: {
      onSubmit() {
        console.log('submit!');
        console.log(this.$route.params.name);
        this.$http.post('/caiyin/examine/printing/upprintingH', this.form).then((response) => {
              console.log(response.data);
              this.$notify({
                title: '成功',
                message: '修改成功',
                type: 'success'
              });
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '修改失败'
              });
          });
      }
    }
  }
</script>
<style>


  .user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingUpTab{
    margin-top: 3%;
    width:400px;
    margin-left: auto;
    margin-right: auto;
  }

</style>
