package com.cy.content.model;

import java.util.Date;

public class CsMphSgpkgRecord {
    private Long id;
    private String number;
    private String variables;
    private String groupIds;
    private String labelIds;
    private String sgpkgId;
    private String status;
    private Date createTime;
    private Date updateTime;

    private String msisdn;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getVariables() {
        return variables;
    }

    public void setVariables(String variables) {
        this.variables = variables;
    }

    public String getSgpkgId() {
        return sgpkgId;
    }

    public void setSgpkgId(String sgpkgId) {
        this.sgpkgId = sgpkgId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }



    public String getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(String groupIds) {
        this.groupIds = groupIds;
    }

    public String getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(String labelIds) {
        this.labelIds = labelIds;
    }


    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = msisdn;
    }

    @Override
    public String toString() {
        return "CsMphSgpkgRecord{" +
                "id=" + id +
                ", number='" + number + '\'' +
                ", variables='" + variables + '\'' +
                ", groupIds='" + groupIds + '\'' +
                ", labelIds='" + labelIds + '\'' +
                ", sgpkgId='" + sgpkgId + '\'' +
                ", status='" + status + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", msisdn='" + msisdn + '\'' +
                '}';
    }
}
