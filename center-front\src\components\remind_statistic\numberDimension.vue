<template>
    <div class="fun_page">
        <h1 class="user-title">号码维度</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <el-form :inline="true" :model="form" size="small" class="demo-form-inline">
            <el-row>
                <el-col :span="21">
                    <el-form-item label="号码：">
                        <el-input v-model="form.number"/>
                    </el-form-item>
                    <el-form-item label="提醒日期：">
                        <el-date-picker type="daterange" placeholder="选择日期" v-model="form.date"
                                        style="width: 215px;" :picker-options="pickerOptions"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain @click="download">导出excel</el-button>
                    </el-form-item>
                </el-col>

            </el-row>
        </el-form>
        <div>
            <el-table :data="tableData" border max-height=500 v-loading="tableLoading" class="app-tab02">
                <el-table-column prop="mobile" label="号码"/>
                <el-table-column prop="date" label="日期"/>
                <el-table-column prop="category" label="分类"/>
                <el-table-column prop="type" label="标准类型"/>
                <el-table-column prop="mobileSource" label="号码来源"/>
                <el-table-column prop="markCount" label="标记次数"/>
                <el-table-column prop="remindCount" label="提醒次数"/>
                <el-table-column prop="remindUserCount" label="提醒人数"/>
            </el-table>
            <div class="block app-pageganit">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="form.page.pageNo"
                            :page-sizes="[100, 200, 300, 400]" :page-size="form.page.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="form.page.total">
                </el-pagination>
            </div>
        </div> </div>
    </div>
</template>

<script>
    import {formatDate} from '../../../static/js/date.js'
    import axios from '../../../node_modules/axios/dist/axios'
    import {dowandFile} from './../../util/core.js'
    export default {
        name: 'procMoni',
        data() {
            return {
                form: {
                    number:'',
                    date: '',
                    startDate:'',
                    endDate:'',
                    page:{
                        pageNo:1,
                        pageSize:100,
                        total:0
                    }
                },
                tableData: [],
                tableLoading:false,
                pickerOptions:{
                    disabledDate:function (today) {
                        return today.getTime()>Date.now();
                    }
                }
            }
        },
        methods: {
            check(vm){
                if (vm.form.provinceId == '' || vm.form.provinceId == undefined) {
                    vm.$message.error("请选择省份");
                    return false;
                }
                return true;
            },
            search() {
                const vm = this;
                vm.tableLoading=true;
                vm.form.startDate = vm.form.date[0];
                vm.form.endDate = vm.form.date[1];
                var params = {
                    startDate:vm.form.date[0],
                    endDate:vm.form.date[1],
                    mobile:vm.form.number,
                    pageNo:vm.form.page.pageNo,
                    pageSize:vm.form.page.pageSize,
                }
                //从服务器获取数据
                axios
                        .post(`${this.proxyUrl}/oper/sop`,params,{

                            headers: {
                                'CY-operation': 'queryRemindDataByMobile'
                            }
                        })
                        .then(function(response) {
                            vm.tableData = response.data.data.mobileDimensionInfoList;
                            vm.form.page.total = response.data.data.total;
                            vm.chartLoading=false;
                            console.log(response);
                        })
                        .catch(function (error) {
                            console.log(error);
                        }).finally(function () {
                            vm.tableLoading=false;
                        });
            },
            handleSizeChange(val) {
                this.form.page.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                this.form.page.pageNo=val;
                this.search();
            },
            download() {
                 const vm = this;
                // if(!vm.check(vm)){
                //     return false;
                // }
                 var params = {
                    startDate:vm.form.date[0],
                    endDate:vm.form.date[1],
                    mobile:vm.form.number
                }
                //发送下载请求
                axios.post(`${this.proxyUrl}/oper/sop`,params,{
                    headers: {
                        'CY-operation': 'exportMobileDimension'
                    },
                    responseType:'blob'
                })
                .then(function(response) {
                    dowandFile(response.data,'号码维度.xlsx');
                })
                .catch(function (error) {
                    console.log(error);
                })
            }
        }
    }
</script>
<style scoped>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
</style>
