<template>
<div>
    <div v-if="!addVisible && !editVisible">
        <h1 class="user-title">其他配置</h1>
        <div class="user-line"></div>
        <div class="app-search" id="addForm">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="省份">
                    <el-select v-model="searchForm.provinceCode" clearable  placeholder="请选择" size="small"  @change="getRegionList" class="app-input">
                        <el-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="地市" prop="regionCode">
                    <el-select v-model="searchForm.regionCode" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in regionList"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" @click="changeProvince">查询</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="small" @click="showAdddConf">新增</el-button>
                </el-form-item>
            </el-form>
        </div>

        <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                  :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="provinceName" label="省份" width="100"/>
            <el-table-column prop="regionName" label="地市" width="100"/>
            <el-table-column prop="sendType" label="彩印发送方式" :formatter="formatSendType" width="100"/>
            <el-table-column prop="domainSelection" label="是否开启域选开关"  :formatter="formatTrueOrFalse" width="120"/>
            <el-table-column prop="isCronSend" label="是否正式向用户发送定时短信通知"  :formatter="formatCronSend" width="120"/>
            <el-table-column prop="isFirst" label="首次接收彩印是否短信通知" :formatter="formatFirst" width="120"/>
            <el-table-column prop="sendOptionCode" label="彩印发送选项" :formatter="formatOption" width="140"/>
            <el-table-column prop="defPerContant" label="默认个人彩印内容"  width="140" :show-overflow-tooltip="true"/>
            <el-table-column label="默认个人彩印盒内容"  width="120">
                <template slot-scope="scope">
                    <el-button type="text" @click="otherDetails(scope.row)">查看详情</el-button>
                </template>
            </el-table-column>
            <el-table-column prop="suffixVariable" label="短信模板后缀变量内容" width="120"/>
            <el-table-column prop="createTime" label="创建时间" width="190"/>
            <el-table-column prop="oper" label="操作" width="120" fixed="right">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="showEditConf(scope.row)" >编辑</el-button>
                    <el-button type="text" size="small" @click="delConf(scope.row)" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

        <div>
            <el-dialog title="配置详情" :visible.sync="showVisible" :close-on-click-modal="false">
                <el-form :model="editForm"  ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 100%">
                    <el-row :gutter="24" class="el-row-style">
                        <el-col :span="8" class="title-text-style">省份 : {{editForm.provinceName}}</el-col>
                        <el-col :span="8" class="title-text-style">地区 : {{editForm.regionName}}</el-col>
                        <el-col :span="8"></el-col>
                    </el-row>
                    <hr class="el-row-line"/>
                    <el-row :gutter="24">
                        <el-col :span="7" style="display: inline-block;">彩印发送方式</el-col>
                        <el-col :span="7" style="display: inline-block;">是否开启域选开关</el-col>
                        <el-col :span="10" style="display: inline-block;">是否正式向用户发送定时短信通知</el-col>
                    </el-row>
                    <el-row :gutter="24" class="el-row-style">
                        <el-col :span="7">
                            <div v-if="editForm.sendType==1">闪信</div>
                            <div v-else-if="editForm.sendType==2">短信</div>
                            <div v-else-if="editForm.sendType==3">彩漫</div>
                            <div v-else-if="editForm.sendType==4">彩漫</div>
                            <div v-else>USSD</div>
                        </el-col>
                        <el-col :span="7" >
                            <div v-if="editForm.domainSelection==1">开启</div>
                            <div v-else>关闭</div>
                        </el-col>
                        <el-col :span="10">
                            <div v-if="editForm.isCronSend==1">是</div>
                            <div v-else>否</div>
                        </el-col>
                    </el-row>

                    <hr class="el-row-line"/>
                    <el-row :gutter="24" class="el-row-style">
                        <el-col :span="7" style="display: inline-block;">首次接收彩印是否短信通知</el-col>
                        <el-col :span="7" style="display: inline-block;">彩印发送选项</el-col>
                    </el-row>
                    <el-row :gutter="24">
                        <el-col :span="7">
                            <div v-if="editForm.isFirst==1">是</div>
                            <div v-else>否</div>
                        </el-col>
                        <el-col :span="7">
                            <div v-if="editForm.sendOptionName==1">作为被叫时发送</div>
                            <div v-else-if="editForm.sendOptionName==2">作为主被时都发送</div>
                            <div v-else>作为主叫时发送</div>
                        </el-col>
                    </el-row>

                    <hr class="el-row-line"/>
                    <el-row :gutter="24" class="el-row-style">
                        <el-col :span="7" style="display: inline-block;">默认个人彩印内容</el-col>
                        <el-col :span="7" style="display: inline-block;">默认个人彩印盒</el-col>
                        <el-col :span="10" style="display: inline-block;">短信模板后缀变量内容</el-col>
                    </el-row>
                    <el-row :gutter="24">
                        <el-col :span="7" >
                            {{editForm.defPerContant}}
                        </el-col>
                        <el-col :span="7">
                            {{editForm.defPerBox}}
                        </el-col>
                        <el-col :span="10">
                            {{editForm.suffixVariable}}
                        </el-col>
                    </el-row>
                </el-form>
            </el-dialog>
        </div>
        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="propVisible = false">确 定</el-button>
            </span>
        </el-dialog>
    <!--查看详情-->
    <el-dialog :visible.sync="otherDetailsHiddent" :before-close="handleClose">
        <el-row style="margin-bottom:20px;">
            <el-col :span="12">
                <label>彩印盒ID:</label>
                <span class="orther-detail">{{ortherDetailList.id}}</span>
            </el-col>
            <el-col :span="12">
                <label>彩印盒名称:</label>
                <span class="orther-detail">{{ortherDetailList.name}}</span>
            </el-col>
        </el-row>
        <el-table :data="ortherDetailList.list" border style="width: 100%">
            <el-table-column prop="id" label="彩印ID"></el-table-column>
            <el-table-column prop="content" label="彩印内容"></el-table-column>
        </el-table>
    </el-dialog>
    </div>
    <div v-if="addVisible">
        <div style="padding: 10px 0px 10px 0px;border-bottom: 1px solid #cccc;"><h1>新增其他配置</h1></div>
        <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" label-width="40%"  style="width: 100%;padding-top:2%">
            <el-form-item label="省份">
                <el-select v-model="addForm.provinceCode" placeholder="请选择" @change="getAddRegionList" size="small">
                    <el-option
                            v-for="item in provinceList"
                            :key="item.provinceCode"
                            :label="item.provinceName"
                            :value="item.provinceCode">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="地市" prop="regionCode">
                <el-select v-model="addForm.regionCode" placeholder="请选择" size="small">
                    <el-option
                            v-for="item in regionList"
                            :key="item.regionCode"
                            :label="item.regionName"
                            :value="item.regionCode">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="彩印发送方式 : ">
                <el-select v-model="addForm.sendType" placeholder="请选择"  size="small">
                    <el-option
                            v-for="item in sendTypeList"
                            :key="item.key"
                            :label="item.value"
                            :value="item.key">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="域选 : ">
                <el-select v-model="addForm.domainSelection" placeholder="请选择"  size="small">
                    <el-option
                            v-for="item in domainList"
                            :key="item.key"
                            :label="item.value"
                            :value="item.key">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="是否向用户发送定时短信通知 : ">
                <el-select v-model="addForm.isCronSend" placeholder="请选择" size="small">
                    <el-option
                            v-for="item in trueOrFalse"
                            :key="item.key"
                            :label="item.value"
                            :value="item.key">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="首次接收彩印是否短信通知 : ">
                <el-select v-model="addForm.isFirst" placeholder="请选择" size="small">
                    <el-option
                            v-for="item in trueOrFalse"
                            :key="item.key"
                            :label="item.value"
                            :value="item.key">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="彩印发送选项 : ">
                <el-select v-model="addForm.sendOptionCode" placeholder="请选择" size="small">
                    <el-option
                            v-for="item in sendOptionList"
                            :key="item.sendOptionCode"
                            :label="item.sendOptionName"
                            :value="item.sendOptionCode">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="默认个人彩印内容 : ">
                <el-select v-model="addForm.defPerContantId"
                           placeholder="请选择"
                           filterable
                           clearable
                           remote
                           :remote-method="getDefPerContantList"
                           size="small">
                    <el-option
                            v-for="item in defPerContantList"
                            :key="item.id"
                            :label="item.defPerContant"
                            :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="">
                <el-date-picker
                        v-model="defPerContantTime"
                        type="datetimerange"
                        :default-time="['00:00:00', '23:59:59']"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至"
                        start-placeholder="生效时间"
                        end-placeholder="失效时间" style="width:70%" size="small">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="默认个人彩印盒 : ">
                <el-select v-model="addForm.defPerBoxId"
                           placeholder="请选择"
                           filterable
                           remote
                           :remote-method="getDefPerBoxList"
                           size="small">
                    <el-option
                            v-for="item in defPerBoxList"
                            :key="item.id"
                            :label="item.defPerBox"
                            :value="item.id">
                        <span style="float: left">{{ item.defPerBox }}</span>
                        <span style="float: right; color: #8492a6; font-size: 10px">{{ item.id }}</span>
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="">
                <el-date-picker
                        v-model="defPerBoxTime"
                        type="datetimerange"
                        :default-time="['00:00:00', '23:59:59']"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至"
                        start-placeholder="生效时间"
                        end-placeholder="失效时间" style="width:70%" size="small">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="短信模板后缀变量内容 : ">
                <el-input v-model="addForm.suffixVariable"  placeholder="" style="width:50%" size="small"></el-input>
            </el-form-item>

        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center;margin-top: 20px;">
            <el-button  @click="addVisible=false;" size="small">取消</el-button>
            <el-button type="primary" @click="addConf('addForm')" size="small">提交</el-button>
        </div>
    </div>
    <div v-if="editVisible">
        <div style="padding: 10px 0px 10px 0px;border-bottom: 1px solid #cccc;"><h1>编辑其他配置</h1></div>
            <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="40%"  style="width: 100%">
                <el-form-item label="省份 : ">
                    {{editForm.provinceName}}
                </el-form-item>
                <el-form-item label="地市 : ">
                    {{editForm.regionName}}
                </el-form-item>
                <el-form-item label="彩印发送方式 : ">
                    <el-select v-model="editForm.sendType" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in sendTypeList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="域选 : ">
                    <el-select v-model="editForm.domainSelection" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in domainList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="是否向用户发送定时短信通知 : ">
                    <el-select v-model="editForm.isCronSend" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in trueOrFalse"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="首次接收彩印是否短信通知 : ">
                    <el-select v-model="editForm.isFirst" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in trueOrFalse"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="彩印发送选项 : ">
                    <el-select v-model="editForm.sendOptionCode" placeholder="请选择" size="small">
                        <el-option
                                v-for="item in sendOptionList"
                                :key="item.sendOptionCode"
                                :label="item.sendOptionName"
                                :value="item.sendOptionCode">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="默认个人彩印内容 : ">
                    <el-select v-model="editForm.defPerContantId"
                               placeholder="请选择"
                               filterable
                               clearable
                               remote
                               :remote-method="getDefPerContantList"
                               size="small">
                        <el-option
                                v-for="item in defPerContantList"
                                :key="item.id"
                                :label="item.defPerContant"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-date-picker
                            v-model="defPerContantTime"
                            type="datetimerange"
                            :default-time="['00:00:00', '23:59:59']"
                            format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="生效时间"
                            end-placeholder="失效时间"
                            style="width: 360px"
                            size="small">
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="默认个人彩印盒 : ">
                    <el-select v-model="editForm.defPerBoxId"
                               placeholder="请选择"
                               filterable
                               clearable
                               remote
                               :remote-method="getDefPerBoxList"
                               size="small">
                        <el-option
                                v-for="item in defPerBoxList"
                                :key="item.id"
                                :label="item.defPerBox"
                                :value="item.id">
                            <span style="float: left">{{ item.defPerBox }}</span>
                            <span style="float: right; color: #8492a6; font-size: 10px">{{ item.id }}</span>
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-date-picker
                            v-model="defPerBoxTime"
                            type="datetimerange"
                            :default-time="['00:00:00', '23:59:59']"
                            format="yyyy-MM-dd HH:mm:ss"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            range-separator="至"
                            start-placeholder="生效时间"
                            end-placeholder="失效时间"
                            style="width: 360px"
                            size="small">
                    </el-date-picker>
                </el-form-item>


                <el-form-item label="短信模板后缀变量内容 : ">
                    <el-input v-model="editForm.suffixVariable"  placeholder="" style="width:50%" size="small"></el-input>
                </el-form-item>


            </el-form>
            <div slot="footer" class="dialog-footer" style="text-align: center;margin-top: 20px;">
                <el-button @click="editVisible=false;" size="small">取消</el-button>
                <el-button type="primary" @click="editConf('editForm')" size="small">提交</el-button>
            </div>
    </div>
</div>
</template>
<script>
import {post} from './../../servers/httpServer.js';
    export default {
        data() {
            return {
                activeName:'otherConf',
                addVisible:false,
                editVisible:false,
                showVisible:false,
                propVisible:false,
                otherDetailsHiddent:false,
                ortherDetailList:new Array(),
                propMsg:'',
                defPerBoxTime:[],
                defPerContantTime:[],
                searchForm: {
                    provinceCode:'',
                    regionCode:'',
                    domainSelection:'',
                    pageSize:10,// 每页显示条数
                    pageNum :1
                },
                //编辑form对象定义
                addForm: {
                    provinceCode:'',
                    provinceName:'',
                    regionCode:'',
                    regionName	:'',
                    createTime:'',//创建时间
                    sendType:'',//彩印发送方式，0：USSD、1：闪信、2：短信、3：彩漫、默认的发送方式；业务地区配置可以为空，为空表示取上级配置
                    domainSelection:'',//是否开启域选开关，0关闭，1开启
                    isCronSend:'',//是否正式向用户发送定时短信通知，0否，1是
                    isFirst:'',//首次接收彩印是否短信通知，0否，1是
                    sendOptionCode:'',//彩印发送选项
                    sendOptionName:'',//彩印发送选项
                    defPerContantId:'',//默认个人彩印ID
                    defPerContant:'',//默认个人彩印内容
                    defPerContantSt:'',//默认个人彩印内容生效时间
                    defPerContantEt:'',//默认个人彩印内容失效时间
                    defPerBoxId:'',//默认个人彩印盒ID
                    defPerBoxName:'',//默认个人彩印盒名称
                    defPerBoxSt:'',//默认个人彩印盒生效时间
                    defPerBoxEt:'',//默认个人彩印盒失效时间
                    suffixVariable:''
                },
                //编辑form对象定义
                editForm: {
                    id:'',//阈值配置id
                    provinceCode:'',
                    provinceName:'',
                    regionCode:'',
                    regionName	:'',
                    createTime:'',//创建时间
                    sendType:'',//彩印发送方式，0：USSD、1：闪信、2：短信、3：彩漫、默认的发送方式；业务地区配置可以为空，为空表示取上级配置
                    domainSelection:'',//是否开启域选开关，0关闭，1开启
                    isCronSend:'',//是否正式向用户发送定时短信通知，0否，1是
                    isFirst:'',//首次接收彩印是否短信通知，0否，1是
                    sendOptionCode:'',//彩印发送选项
                    sendOptionName:'',//彩印发送选项
                    defPerContantId:'',//默认个人彩印ID
                    defPerContant:'',//默认个人彩印内容
                    defPerContantSt:'',//默认个人彩印内容生效时间
                    defPerContantEt:'',//默认个人彩印内容失效时间
                    defPerBoxId:'',//默认个人彩印盒ID
                    defPerBoxName:'',//默认个人彩印盒名称
                    defPerBoxSt:'',//默认个人彩印盒生效时间
                    defPerBoxEt:'',//默认个人彩印盒失效时间
                    suffixVariable:''
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'', //id
                },

                statusList:[
                    {
                        key:'',
                        value:'请选择'
                    },
                    {
                        key:'0',
                        value:'禁用'
                    },{
                        key:'1',
                        value:'启用'
                    }
                ],
                domainList:[
                    {
                        key:'0',
                        value:'关闭'
                    },{
                        key:'1',
                        value:'开启'
                    }
                ],
                sendTypeList:[
                    {
                        key:'0',
                        value:'USSD'
                    },{
                        key:'1',
                        value:'闪信'
                    },{
                        key:'2',
                        value:'短信'
                    },{
                        key:'3',
                        value:'彩漫'
                    },{
                        key:'4',
                        value:'微信'
                    }
                ],
                provinceList:[],
                regionList:[],
                queryRegion:{
                    provinceCode: ''
                },
                defPerBoxList:[],
                defPerContantList:[],
                //0：作为主叫时发送1：作为被叫时发送2：作为主被时都发送
                sendOptionList:[
                    {
                        sendOptionCode:'0',
                        sendOptionName:'作为主叫时发送'
                    },{
                        sendOptionCode:'1',
                        sendOptionName:'作为被叫时发送'
                    },{
                        sendOptionCode:'2',
                        sendOptionName:'作为主被时都发送'
                    }
                ],
                trueOrFalse:[
                    {
                        key:'0',
                        value:'否'
                    },{
                        key:'1',
                        value:'是'
                    }
                ],
                isCronSendList:[],
                rules: {},
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
            this.search(this.searchForm);
            this.getProvinceList();
            // this.getDefPerContantList();

            //获取个人彩印盒列表
            // this.getDefPerBoxList();
        },
        methods: {
            //获取省份list
            getProvinceList:function(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getProvince`).then(function(res){
                    this.provinceList=res.data;
                })
            },
            changeProvince:function () {
                this.search(this.searchForm);
            },

            //获取省份list
            getRegionList:function(){
                this.queryRegion.provinceCode = this.searchForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.regionList=res.data;
                    })
            },
            getAddRegionList:function(){
                this.queryRegion.provinceCode = this.addForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.regionList=res.data;
                    })
            },
            getAllRegionList:function(){
                this.queryRegion.provinceCode = this.addForm.provinceCode;
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,this.queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.regionList=res.data;
                    })
            },
            //获取个人彩印内容
            getDefPerContantList:function(content){
                if(content!=''){
                    this.$http.post(`${this.proxyUrl}/param/regConf/getAllParDefperContant`,{csTextContent:content})
                            .then(function(res){
                                this.defPerContantList=res.data;
                            })
                    }
            },

            //获取个人彩印盒列表
            getDefPerBoxList(pkgName){
                if(pkgName!=''){
                    this.$http.post(`${this.proxyUrl}/param/regConf/getAllParDefperBox`,{csPkgName:pkgName})
                        .then(function(res){
                            this.defPerBoxList=res.data;
                        })
                }

            },


            //查询列表请求
            search:function(searchForm){
                this.$http.post(`${this.proxyUrl}/param/regConf/getOtherConfPage`,searchForm,{emulateJSON:true}).then(function(res){
                    this.currentPage=res.data.pageNum;
                    this.total=res.data.pageTotal;
                    this.tableData=res.data.datas;
                })
            },
            // 弹出修改框
            showEditConf(editForm){
                this.editVisible = true;
                this.queryOrDelForm.id=editForm.id;

                this.$http.post(`${this.proxyUrl}/param/regConf/getParOtherConfDetail`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    this.editForm = res.data;
                    var arr = {id:this.editForm.defPerBoxId,defPerBox:this.editForm.defPerBoxName};
                    this.defPerBoxList=[];
                    this.defPerBoxList.push(arr);
                    this.defPerBoxTime=[this.editForm.defPerBoxSt,this.editForm.defPerBoxEt]
                    this.defPerContantTime=[this.editForm.defPerContantSt,this.editForm.defPerContantEt];
               })
            },
            // 弹出新增页面
            showAdddConf(){
                this.addForm.provinceCode = '';
                this.addForm.provinceName = '';
                this.addForm.regionCode = '';
                this.addForm.regionName = '';
                this.addForm.createTime = '';
                this.addForm.sendType = '';
                this.addForm.domainSelection = '';
                this.addForm.isCronSend = '';
                this.addForm.isFirst = '';
                this.addForm.sendOptionCode = '';
                this.addForm.sendOptionName = '';
                this.addForm.defPerContantId = '';
                this.addForm.defPerContant = '';
                this.addForm.defPerContantSt = '';
                this.addForm.defPerContantEt = '';
                this.addForm.defPerBoxId = '';
                this.addForm.defPerBoxName = '';
                this.addForm.defPerBoxSt = '';
                this.addForm.defPerBoxEt = '';
                this.addForm.suffixVariable = '';
                this.addVisible=true;
                this.defPerBoxTime=new Array();
                this.defPerContantTime=new Array();
            },
            //修改请求
            editConf(editForm){
                this.$refs[editForm].validate((valid) => {
                    if(this.defPerBoxTime != ''){
                        this.editForm.defPerBoxSt=this.defPerBoxTime[0];
                        this.editForm.defPerBoxEt=this.defPerBoxTime[1];
                    }
                    if(this.defPerContantTime != ''){
                        this.editForm.defPerContantSt=this.defPerContantTime[0];
                        this.editForm.defPerContantEt=this.defPerContantTime[1];
                    }
                    let list=this.defPerContantList.filter(item=>{
                        return item.id===this.editForm.defPerContantId;
                    })
                    if(list[0]!=undefined){
                        this.editForm.defPerContant=list[0].defPerContant;
                    }
                    let defList=this.defPerBoxList.filter(item=>{
                        return item.id===this.editForm.defPerBoxId;
                    })
                    if(defList[0]!=undefined){
                        this.editForm.defPerBoxName=defList[0].defPerBox;
                    }
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/regConf/updateParOtherConf`,this.editForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.$message({
                                    message: '修改成功！',
                                    type: 'success'
                                });
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.$message.error('修改失败!'+ res.data.resText);
                            }
                        })
                    } else {
                        return false;
                    }
            });
            },

            //修改请求
            addConf(formName){

                if(this.defPerBoxTime != ''){
                    this.addForm.defPerBoxSt=this.defPerBoxTime[0];
                    this.addForm.defPerBoxEt=this.defPerBoxTime[1];
                }
                if(this.defPerContantTime != ''){
                    this.addForm.defPerContantSt=this.defPerContantTime[0];
                    this.addForm.defPerContantEt=this.defPerContantTime[1];
                }
                let list=this.defPerContantList.filter(item=>{
                    return item.id===this.addForm.defPerContantId;
                })
                if(list[0]!=undefined){
                    this.addForm.defPerContant=list[0].defPerContant;
                }else{
                    this.addForm.defPerContant="";
                }
                let defList=this.defPerBoxList.filter(item=>{
                    return item.id===this.addForm.defPerBoxId;
                })
                if(defList[0]!=undefined){
                    this.addForm.defPerBoxName=defList[0].defPerBox;
                }else{
                    this.addForm.defPerBoxName="";
                }
                this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$http.post(`${this.proxyUrl}/param/regConf/addParOtherConf`,this.addForm,{emulateJSON:true}).then(function(res){
                        if(res.data.resStatus == 0){
                            this.$message({
                                message: '新增成功！',
                                type: 'success'
                            });
                            this.addVisible = false;
                            this.search(this.searchForm);
                        }else{
                            this.$message.error(res.data.resText);
                        }
                    })
                } else {
                    return false;
                }

            });
            },
            //删除请求
            delConf(role){
                this.queryOrDelForm.id = role.id;
                this.$confirm('确认要删除吗？')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/param/regConf/deleteParOtherConf`, this.queryOrDelForm, {emulateJSON: true})
                            .then(function (res) {
                                if (res.data.resStatus == 0) {
                                    this.$message({
                                        message: '删除成功！',
                                        type: 'success'
                                    });
                                    this.editVisible = false;
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('删除失败!'+ res.data.resText);
                                }
                            })
                    })
            },

            formatSendType:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return 'USSD';
                }else if (cellValue === "1"){
                    return '闪信';
                }else if (cellValue === "2"){
                    return '短信';
                }else if (cellValue === "3"){
                    return '彩漫';
                }else if (cellValue === "4"){
                    return '微信';
                }
            },
            formatTrueOrFalse:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return '关闭';
                }else if (cellValue === "1"){
                    return '开启';
                }
            },
            formatCronSend:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return '否';
                }else if (cellValue === "1"){
                    return '是';
                }
            },
            formatFirst:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return '否';
                }else if (cellValue === "1"){
                    return '是';
                }
            },
            formatOption:function (row, column, cellValue) {
                if (cellValue === "0"){
                    return '作为主叫时发送';
                }else if (cellValue === "1"){
                    return '作为被叫时发送';
                }else if (cellValue === "2"){
                    return '作为主被时都发送';
                }
            },
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            handleClick(activeName){
                this.$router.push(activeName);
            },

            //清空提示信息
            resetForm(formName){
                this.$nextTick(() => {
                    this.$refs[formName].resetFields();
            });
            },

            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            //查看详情
            otherDetails(list){
                this.otherDetailsHiddent=true;
              post('/param/regConf/getDefperBoxById',{csPkgNumber:list.defPerBoxId}).then(res=>{

                    if(res.status===200){
                        this.ortherDetailList=res.data[0];
                    }
              })
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }
        },
        created() {
        },
        components: {}
    }


</script>
<style>

    .title-text-style{
        font-weight:bold;
    }

    .el-row-style {
        margin-top: 2%;
        margin-bottom: 2%;
    }
    .def-tab {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .el-tabs__item{
        font-size: 24px;
        -webkit-margin-before: 0.67em;
        -webkit-margin-after: 0.67em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        font-weight: bold;
    }
    .el-row-line{
        margin-top: 1%;
        margin-bottom: 1%;
        background-color: #D6DBDF;
        height:1px;border:none;
        border-top:1px
    }
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .orther-detail{
        width: 110px;
        white-space: nowrap;
        font-weight: bold;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
