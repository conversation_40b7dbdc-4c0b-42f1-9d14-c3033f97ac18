package com.cs.aspect;

import java.util.UUID;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import com.cs.param.utils.WriteThreadContextUtil;



/**
 * 
 * 定时任务日志切面
 * 
 * <AUTHOR>
 * @version [版本号, 2022年5月11日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Aspect
@Component
public class TaskLogAspect
{
    /**
     * 检查点配置 resdcm接口
     * 
     * <AUTHOR> 2018年5月16日
     * @see [类、类#方法、类#成员]
     */
	@Pointcut("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    public void webLog()
    {
    }
    
	private String getTaskName(JoinPoint joinPoint){
        return joinPoint.getTarget().getClass().getSimpleName()+"."+joinPoint.getSignature().getName();
    }
	
    /**
     * 拦截前操作
     * 
     * <AUTHOR> 2018年5月11日
     * @param joinPoint 切入点
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint)
    {
    	String taskName = getTaskName(joinPoint);
    	String linkID = UUID.randomUUID().toString();
    	WriteThreadContextUtil.writeThreadContext(taskName, linkID);
    }
    
    
    /**
     * 拦截后的操作
     * 
     * <AUTHOR> 2018年5月16日
     * @param ret 返回值
     * @throws Exception 异常
     * @see [类、类#方法、类#成员]
     */
    @AfterReturning(returning = "ret", pointcut = "webLog()")
    public void doAfterReturning(JoinPoint joinPoint, Object ret)
    {
        
    }
    
    /**
     * 报错后的操作
     * 
     * @auth cWX319470 2018年5月11日
     * @see [类、类#方法、类#成员]
     */
    @AfterThrowing(throwing = "e", pointcut = "webLog()")
    public void doAfterError(JoinPoint joinPoint, Throwable e)
    {
    	
    }
}