<template scope="scope">
  <div>
    <div class="user-titler">详情</div>
    <div class="wrap">
      <!-- <el-checkbox-group v-model="type"> -->
        <el-row>
          <!-- <el-checkbox-group v-model="checkList"> -->
            <el-col v-for="(item, index) in pageData.fields" :key="index">
              <el-row class="rowItem">
                <el-col :span="3">
                  <!-- <el-checkbox :label="item.id">{{item.fieldName}}</el-checkbox> -->
                  {{item.fieldName}}
                </el-col>
                <el-col v-if="!item.fieldType || item.fieldType === 'text'" :span="21" class="valueFontSize">
                  {{item.fieldValue}}
                  <el-radio-group v-model="checkStatus[item.id]" @change="val => handleChange(val, item.id)" style="margin-left: 20px;">
                    <el-radio disabled :label="0">通过</el-radio>
                    <el-radio disabled :label="1">不通过</el-radio>
                  </el-radio-group>
                  <div class="reason1" v-show="checkStatus[item.id] == 1"><span :title="checkReason[item.id]">不通过原因：{{checkReason[item.id]}}</span></div>
                </el-col>
                <el-col v-else-if="item.fieldType === 'photo'" :span="18" class="valueFontSize0">
                  <img :src="item.fieldValue" alt />
                  <el-radio-group v-model="checkStatus[item.id]" @change="val => handleChange(val, item.id)" style="vertical-align: top; margin-left: 20px;">
                    <el-radio disabled :label="0">通过</el-radio>
                    <el-radio disabled :label="1">不通过</el-radio>
                  </el-radio-group>
                  <div class="reason" v-show="checkStatus[item.id] == 1"><span :title="checkReason[item.id]">不通过原因：{{checkReason[item.id]}}</span></div>
                </el-col>
                <el-col v-else-if="item.fieldType === 'video'" :span="18" class="valueFontSize0">
                  <video controls="">
                    <source :src="item.fieldValue" type="video/mp4">
                    你的浏览器不支持H5播放器
                  </video>
                  <el-radio-group v-model="checkStatus[item.id]" @change="val => handleChange(val, item.id)" style="vertical-align: top; margin-left: 20px;">
                    <el-radio disabled :label="0">通过</el-radio>
                    <el-radio disabled :label="1">不通过</el-radio>
                  </el-radio-group>
                  <div class="reason" v-show="checkStatus[item.id] == 1"><span :title="checkReason[item.id]">不通过原因：{{checkReason[item.id]}}</span></div>
                </el-col>
              </el-row>
            </el-col>
          <!-- </el-checkbox-group> -->
          
          <!-- <el-col :span="12">
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="姓名"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize">{{pageData.name}}</el-col>
            </el-row>
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="昵称"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize">张三</el-col>
            </el-row>
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="公司名称"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize">{{pageData.companyName}}</el-col>
            </el-row>
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="公司地址"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize">张三</el-col>
            </el-row>
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="职位"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize">张三</el-col>
            </el-row>
          </el-col>
          <el-col :span="12">
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="头像图片"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize0">
                <img src="https://cn.vuejs.org/images/logo.png" alt />
              </el-col>
            </el-row>
            <el-row class="rowItem">
              <el-col :span="6">
                <el-checkbox label="公司LOGO图片"></el-checkbox>
              </el-col>
              <el-col :span="18" class="valueFontSize0">
                <img src="https://cn.vuejs.org/images/logo.png" alt />
              </el-col>
            </el-row>
          </el-col> -->
        </el-row>
        <!-- <el-row class="rowItem">
          <el-col :span="4">
            <el-checkbox label="企业定制模板图片"></el-checkbox>
          </el-col>
          <el-col :span="20" class="valueFontSize">
            <el-carousel indicator-position="outside" arrow="always">
              <el-carousel-item v-for="item in 4" :key="item">
                <h3>{{ item }}</h3>
              </el-carousel-item>
            </el-carousel>
          </el-col>
        </el-row>
        <el-row class="rowItem">
          <el-col :span="4">
            <el-checkbox label="企业微站图片"></el-checkbox>
          </el-col>
          <el-col :span="20" class="valueFontSize">
            <el-carousel indicator-position="outside" arrow="always">
              <el-carousel-item v-for="item in 4" :key="item">
                <h3>{{ item }}</h3>
              </el-carousel-item>
            </el-carousel>
          </el-col>
        </el-row>
        <el-row class="rowItem">
          <el-col :span="4">
            <el-checkbox label="企业微站视频"></el-checkbox>
          </el-col>
          <el-col :span="20" class="valueFontSize">
            <el-carousel indicator-position="outside" arrow="always">
              <el-carousel-item v-for="item in 4" :key="item">
                <h3>{{ item }}</h3>
              </el-carousel-item>
            </el-carousel>
          </el-col>
        </el-row> -->
      <!-- </el-checkbox-group> -->
    </div>
    <div class="bottom">
      <el-button size="small" :loading="queryLoading" @click="reject">返回上级</el-button>
      <!-- <el-button size="small" :loading="queryLoading" type="primary" @click="backout">撤销审核</el-button> -->
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      radio: '1',
      queryLoading: true,
      type: [],
      uuid: '',
      pageData: {
        // fields: [
        //   {
        //       "id":41,
        //       "fieldId":"102454",
        //       "fieldType":"photo",
        //       "fieldName":"video_file",
        //       "fieldValue":"http://a.hiphotos.baidu.com/image/pic/item/d000baa1cd11728bdfff9d9dc2fcc3cec2fd2cba.jpg"
        //   },
        //   {
        //       "id":41,
        //       "fieldId":"102454",
        //       "fieldType": "text",
        //       "fieldName":"video_file",
        //       "fieldValue":"http://a.hiphotos.baidu.com/image/pic/item/d000baa1cd11728bdfff9d9dc2fcc3cec2fd2cba.jpg"
        //   },
        //   {
        //       "id":41,
        //       "fieldId":"102454",
        //       "fieldType": "video",
        //       "fieldName":"video_file",
        //       "fieldValue":"http://cmsweb.bmcc.com.cn/lehuo/lehuo/weather.mp4"
        //   },
        // ]
      },
      checkList: [],
      checkStatus: {},
      checkReason: {}
    };
  },
  created() {
    this.uuid = this.$route.query.uuid;
    this.auditBy = this.$route.query.auditBy;
    this.query();
  },
  watch: {},
  methods: {
    handleChange(val, id) {
      console.log(val,id)
      console.log(this.checkStatus)
      this.$set(this.checkStatus, id, val)
      if(val == 1) {
        this.$prompt('请填写不通过原因', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
          // inputErrorMessage: '邮箱格式不正确'
        }).then(({value}) => {
          console.log(value)
          if(value == '') {
            this.$set(this.checkStatus, id, 0)
          } else {
            this.$set(this.checkReason, id, value)
          }
        }).catch((action) => {
          console.log(action)
          if(action === 'cancel') {
            this.$set(this.checkStatus, id, 0)
          }
        });
      } else {
        this.$set(this.checkReason, id, '')
      }
      // console.log(value)
    },
    query() {
      this.queryLoading = true;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/get/card/detail`,
          JSON.stringify({uuid: this.uuid})
        )
        .then(function(res) {
          if (res.data.code == "0") {
            this.pageData = res.data.data;
            this.pageData.fields.forEach((item, index) => {
              this.$set(this.checkStatus, item.id, item.rejectStatus)
              this.$set(this.checkReason, item.id, item.rejectReason)
            })
            this.queryLoading = false;
          }
        });
    },
    backout(val) {
        this.$http
        .post(
          `${this.proxyUrl}/entContent/audit/card/backout`,
          JSON.stringify({
            uuid: this.uuid,
            auditBy: this.auditBy
          }),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          res = res.data;
          if (res.code == 0) {
            this.$message({
              type: 'success',
              message: '撤销成功!'
            });
            this.$router.push({path: "miguCardviewDetails"})
          } else {
            this.$message("撤销失败");
          }
        });
    },
    reject(val) {
      this.$router.push({path: "miguCardviewDetails"})
    },
  }
};
</script>
<style scoped>
.user-titler {
  font-size: 20px;
  padding-left: 24px;
  height: 56px;
  line-height: 56px;
  font-family: PingFangSC-Medium;
  color: #333333;
  letter-spacing: -0.57px;
  border-bottom: 1px solid #d9d9d9;
}

.wrap {
  padding: 0 24px;
  margin-bottom: 10px;
}

.el-col-21 {
  width: auto !important;
}

.rowItem {
  margin-top: 10px;
}

.bottom {
  text-align: center;
  padding-bottom: 20px;
}

.valueFontSize {
  font-size: 14px;
  min-height: 30px;
}

.valueFontSize0 img {
  height: 200px;
}
.valueFontSize0 video {
  height: 200px;
}
.el-carousel__item h3 {
  color: #475669;
  font-size: 18px;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.reason {
  display: inline-block;
  font-size: 0;
  vertical-align: top;
  line-height: 1;
  margin-left: 20px;
  color: #409EFF;
  width: 400px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  vertical-align: top;
}
.reason,.reason1 span {
  font-size: 14px;
}

.reason1 {
  display: inline-block;
  font-size: 14px;
  margin-left: 20px;
  color: #409EFF;
  width: 400px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.wrap>>>.el-radio__label {
  color: #409EFF;
}
</style>
