<template>
  <div>
    <h1 class="user-title">个人彩印</h1>
   <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="彩印ID">
        <el-input v-model="print.svNumber"  placeholder="彩印ID" size="small" :maxlength="32" clearable></el-input>
      </el-form-item>
      <el-form-item label="彩印内容">
        <el-input v-model="print.svCard" placeholder="彩印内容" size="small" :maxlength="50" clearable></el-input>
      </el-form-item>

      <el-form-item label="审批状态">
          <el-select v-model="print.svStatus" placeholder="请选择" size="small" clearable>
              <el-option
                      v-for="item in sildeData"
                      :key="item.auditStatusNo"
                      :label="item.auditStatusName"
                      :value="item.auditStatusNo">
              </el-option>
          </el-select>
      </el-form-item>
   

        <el-form-item label="提交时间">
          <el-date-picker v-model="dateTime"
                type="datetimerange"
                range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              style="width:355px"
              size="small"
          />
        </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit(print)" size="small">查询</el-button>
          </el-form-item>

    </el-form>
</div>
<div>
    <el-table
    v-loading="tableLoading"
    :data="tableData"
    border
    class="app-tab"
    :header-cell-class-name="tableheaderClassName">
    <el-table-column
      prop="svNumber"
      label="彩印ID"
      width="240">
    </el-table-column>
    <el-table-column
      prop="svCard"
      label="彩印内容"
       width="400"
       :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column
      prop="groupName"
      label="内容分类"
      width="200">
    </el-table-column>
    <el-table-column
      prop="labelName"
      label="内容标签"
      width="200"
      :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column
      prop="submitTime"
      label="提交时间"
      width="200">
    </el-table-column>
    <el-table-column
      prop="svStatusName"
      label="审核状态"
      width="120">
         <template slot-scope="scope">
            <span v-show="(scope.row.svStatus==1)">待审批</span>
            <span v-show="(scope.row.svStatus==2)">审批不通过</span>
            <span v-show="(scope.row.svStatus==3)">审批通过</span>
            <span v-show="(scope.row.svStatus==4)">审批不通过</span>
            <span v-show="(scope.row.svStatus==5)">已撤销</span>
         </template>
    </el-table-column>
    <el-table-column
      prop="svAssessor"
      label="审核人"
      width="200">
    </el-table-column>
    <el-table-column
      prop="svSssesstime"
      label="审核时间"
      width="200">
    </el-table-column>
  </el-table>

       <!-- 分页 -->
      <div class="block app-pageganit">
      <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tableData.pageNum"
              :page-sizes="[10, 20, 30, 50]"
              :page-size="10"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pageTotal"  style="text-align: right;">
      </el-pagination>
     </div>
</div>
</div>

</template>
<script>
import {formDate} from './../../../util/core.js';
  export default {
    name: 'UserList',
    data() {
      return {
        tableLoading: false,
        pageTotal:0,
        sildeData:[
                  {auditStatusNo:'1',auditStatusName:'待审批'},
                  {auditStatusNo:'2',auditStatusName:'审批不通过'},
                  {auditStatusNo:'3',auditStatusName:'审批通过'},
                  // {auditStatusNo:'4',auditStatusName:'失效'},
                  {auditStatusNo:'5',auditStatusName:'已撤销'}
        ],
        // tableData: [{
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }],
        tableData:[],
        dateTime:[],
        value6: '',
        print:{
            svNumber:"",
            svCard:"",
            svStatus:"",
            startTime:"",
            endTime:"",
            pageNum:1,
            pageSize:10
        }

      }
    },
    mounted() {
      // this.statusSilde();
    },
    methods: {
      //状态选项请求
      // statusSilde: function() {
      //   this.$http
      //     .get(`${this.proxyUrl}/content/content/getVsStatus`, { emulateJSON: true })
      //     .then(function(res) {
      //       if (res.data.resStatus == "0") {
      //         this.sildeData = res.data.datas;
      //       } else if (res.data.resStatus == "1") {
      //         console.log("请求失败");
      //       }
      //     });
      // },
      handleSizeChange(val) {
      this.print.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyCreateCs`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
    handleCurrentChange(val) {
      this.print.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.print.startTime='';
        this.print.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/getMyCreateCs`, JSON.stringify(this.print))
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
      onSubmit(val) {
        if(this.dateTime && this.dateTime.length>0){
          this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          this.print.startTime='';
          this.print.endTime='';
        }
        this.tableLoading=true;
        this.$http.post(`${this.proxyUrl}/content/auditBox/getMyCreateCs`, this.print).then((response) => {
              this.tableData=response.data.datas;
              this.pageTotal=response.data.pageTotal;
              this.tableLoading=false;
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '查询异常'
              });
               this.tableLoading=false;
          });
      },
        tableheaderClassName({ row, rowIndex }) {
            return "table-head-th";
        }
    },
    created() {
    },
    components: {}
  }


</script>
<style>
.user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }
  .el-pagination{
    margin-left:270px;
  }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
