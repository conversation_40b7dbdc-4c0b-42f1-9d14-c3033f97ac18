package com.cy.content;

import java.util.List;

import com.cy.content.model.TemplateVariablesRecord;
import org.springframework.web.bind.annotation.PostMapping;

import com.cy.content.model.CsTextModel;


public interface TextContent {
    
    /**
     * 获取所有审批通过的文本彩印总条数
     * @param CsTextModel
     * @return int
     */
    @PostMapping("/textContent/getPkgSize")
    int getTextSize(CsTextModel csTextModel);
    
    
    /**
     * 获取所有审批通过的文本彩印
     * @param CsTextModel
     * @return List<CsTextModel>
     */
    @PostMapping("/textContent/getTextContent")
    List<CsTextModel> getTextContent(CsTextModel csTextModel);
    
    /**
     * 获取所有审的文本彩印
     * @param CsTextModel
     * @return List<CsTextModel>
     */
    @PostMapping("/textContent/getAllTextList")
    List<CsTextModel> getAllTextList(CsTextModel csTextModel);
    
    /**
     * 用户选择的文本彩印
     * @param String
     * @return 
     */
    @PostMapping("/textContent/usedCsText")
    void usedCsText(String csTextNumber);


    @PostMapping("/textContent/queryTemplateVariables")
    List<TemplateVariablesRecord> selectTemplateVariablesRecordByTemplateContent(String templateContent);


    @PostMapping("/textContent/addTemplateVariables")
    // 新增操作
    Integer insertTemplateVariablesRecord(TemplateVariablesRecord record);
}
