package com.cy.model;

import java.util.ArrayList;
import java.util.List;

public class Menu {

	
	private Integer id;
	
	
	private String name;
	
	private String index;
	
	private String icon;

	private String type;
	
	private List<Children> children = new ArrayList<Children>();



	public Menu(Integer id,String name, String index, String icon) {
		super();
		this.id = id;
		this.name = name;
		this.index = index;
		this.icon = icon;
	}
	

	public Menu(Integer id,String name, String index, String icon, String type) {
		super();
		this.id = id;
		this.name = name;
		this.index = index;
		this.icon = icon;
		this.type = type;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return name;
	}

	/**
	 * @param name the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the index
	 */
	public String getIndex() {
		return index;
	}

	/**
	 * @param index the index to set
	 */
	public void setIndex(String index) {
		this.index = index;
	}

	/**
	 * @return the icon
	 */
	public String getIcon() {
		return icon;
	}

	/**
	 * @param icon the icon to set
	 */
	public void setIcon(String icon) {
		this.icon = icon;
	}

	/**
	 * @return the children
	 */
	public List<Children> getChildren() {
		return children;
	}

	/**
	 * @param children the children to set
	 */
	public void setChildren(List<Children> children) {
		this.children = children;
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @Title: toString
	 * @Description:
	 * @return
	 */
	@Override
	public String toString() {
		return "Menu{" +
				"id=" + id +
				", name='" + name + '\'' +
				", index='" + index + '\'' +
				", icon='" + icon + '\'' +
				", type='" + type + '\'' +
				", children=" + children +
				'}';
	}
}
