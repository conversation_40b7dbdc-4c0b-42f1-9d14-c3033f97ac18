<template>
    <div class="fun_page">
        <h1 class="user-title">呼叫推送统计</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :inline="true" :model="searchForm1" size="small">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="时间">
                            <el-select v-model="searchForm1.type" style="width: 60px">
                                <el-option label="天" value="date"></el-option>
                                <el-option label="月" value="month"></el-option>
                            </el-select>
                        </el-form-item>
                         <el-form-item>
                            <el-date-picker placeholder="开始日期" v-model="searchForm1.startDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="searchForm1.type"/>
                            至
                            <el-date-picker placeholder="结束日期" v-model="searchForm1.endDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="searchForm1.type"/>
                        </el-form-item>
                        <el-form-item label="省份">
                            <el-select v-model="selectProvinceIds" multiple collapse-tags clearable style="width: 150px" @change="selectAll2($event,provinceList)">
                                <el-option  value="全选" label="全选" style="font-weight:bold;"></el-option>
                                <el-option v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="城市">
                            <el-select v-model="searchForm1.showCounty" clearable  style="width: 120px">
                                <el-option label="展示" :value="1"></el-option>
                                <el-option label="不展示" :value="2"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="子业务ID">
                            <el-select v-model="selectServiceCodes" multiple collapse-tags clearable style="width: 280px" @change="selectAll">
                                <el-option v-for="item in serviceCodeList"
                                    :key="item.codeId"
                                    :label="item.codeName"
                                    :value="item.codeId">
                                     <span style="float: left">{{ item.codeId }}</span>
                                     <span style="float: left">&nbsp;&nbsp;</span>
                                    <span style="color: #8492a6; font-size: 6px">{{ item.codeName }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col style="padding-top:10px;">
                        <el-form-item>
                            <el-button type="primary" @click="search" v-bind:disabled="btnhide">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain @click="downloadCheck" v-bind:disabled="btnhide">导出CSV</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!--数据信息-->
            <div style="margin-top: 30px;">
                <el-table :data="tableData" border style="width: 100%" max-height=500 v-loading="tableLoading">
                    <el-table-column prop="statsDate" label="时间"  width="240"/>
                    <el-table-column prop="provinceName" label="省份"  width="100"/>
                    <el-table-column prop="countyName" label="地市"  width="100"/>

                    <el-table-column prop="serviceId" label="子业务ID"  width="120"/>

                    <el-table-column prop="dialingCount" label="主叫次数"  width="120"/>
                    <el-table-column prop="calledCount" label="被叫次数"  width="120"/>
                    <el-table-column label="USSD">
                        <el-table-column prop="ussdPushCount" label="推送次数" width="120"/>
                        <el-table-column prop="ussdSuccessCount" label="推送成功数" width="120"/>
                    </el-table-column>
                    <el-table-column label="闪信">
                        <el-table-column prop="flashPushCount" label="推送次数" width="120"/>
                        <el-table-column prop="flashSuccessCount" label="推送成功数" width="120"/>
                    </el-table-column>
                    <el-table-column label="挂机彩漫">
                        <el-table-column prop="hangPushCount" label="推送次数" width="120"/>
                        <el-table-column prop="hangSuccessCount" label="推送成功数" width="120"/>
                    </el-table-column>
                    <el-table-column prop="cyUser" label="发送彩印用户数" width="140"/>
                    <el-table-column prop="cmUser" label="发送彩漫用户数" width="140"/>
                </el-table>
                <div class="block app-pageganit">
                    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="searchForm1.page.pageNo"
                                :page-sizes="[100, 200, 300, 400]" :page-size="searchForm1.page.pageSize"
                                layout="total, sizes, prev, pager, next, jumper" :total="searchForm1.page.total">
                    </el-pagination>
                </div>
            </div>
        </div>
      <el-dialog
          @open="exportClick"
          title="导出"
          :visible.sync="propVisible"
          :close-on-click-modal="false"
          width="45%">
        <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
          <el-form-item label="文件名" prop="fileName">
            <el-input v-model="addReq.fileName" type="input" size="small" placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字" style="width: 90%;"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256" style="width: 90%;"></el-input>
          </el-form-item>
        </el-form>

        <div style=" margin-left: 80px; color: red;">
          导出后请到系统管理-导出文件下载对应文件
        </div>

        <div slot="footer" class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="confirmExport">确定</el-button>
          <el-button @click="cancelExport">取消</el-button>
        </div>
      </el-dialog>
        <jkAuth menuCode="CPUserList" @buttonDisable="setButton"></jkAuth>
    </div>

</template>

<script>
    import Vue from 'vue'
    import VeLine from 'v-charts/lib/line'
    import axios from '../../../node_modules/axios/dist/axios'
    import {dowandFile,formDate} from './../../util/core.js';
    import jkAuth from '@/components/common/jkAuth';


    Vue.component(VeLine.name, VeLine)

    export default {
        components:{
            jkAuth
        },
        name: 'procMoni',
        data() {
            return {
              propVisible:false,
              fileName: '',
              remark: '',
              addReq:{
                fileName: '',
                remark: ''
              },
              rules:{
                fileName: [
                  { required: true, message: '请输入文件名', trigger: 'blur' },
                  { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
                  { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
                ],
                remark: [
                  { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
                ]
              },
              isReSearch: true,
              btnhide:true,
                functionCode: '030302',
                operationCode: '3002',
                searchForm1:{
                    dataType:'cyUser',
                    date1:'last_seven_days',
                    type:'date',
                    date2:[],
                    startDate:'',
                    endDate:'',
                    provinceIds:new Array(),
                    serviceCodes:[],
                    showCounty:1,
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                searchForm2:{
                    date:'',
                    startDate:'',
                    endDate:'',
                    page: {
                        pageNo: 1,
                        pageSize: 100,
                        total: 0
                    }
                },
                //省份列表
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),


                serviceCodeList:[{codeId:'全选',codeName:''},
                                {codeId:'00000',codeName:'提醒彩印-特殊号码提醒'},
                                {codeId:'00001',codeName:'提醒彩印-黑名单号码提醒'},
                                {codeId:'00002',codeName:'提醒彩印-黄页号码提醒'},
                                {codeId:'00003',codeName:'提醒彩印-标记号码提醒'},
                                {codeId:'00004',codeName:'提醒彩印-市民防诈骗提醒'},
                                {codeId:'00005',codeName:'提醒彩印-境外来电提醒'},
                                {codeId:'00006',codeName:'亲情彩印-亲情彩印提醒'},
                                {codeId:'01100',codeName:'企业彩印-主叫名片'},
                                {codeId:'01101',codeName:'企业彩印-成员彩印'},
                                {codeId:'01102',codeName:'企业彩印-主被叫彩印'},
                                {codeId:'01103',codeName:'企业彩印-热线彩印'},
                                {codeId:'01104',codeName:'企业彩印-挂机彩漫'},
                                {codeId:'01105',codeName:'企业彩印-挂机短信'},
                                {codeId:'01106',codeName:'企业彩印-商户彩印'},
                                {codeId:'01107',codeName:'企业彩印-个人挂机'},
                                {codeId:'01108',codeName:'省内版企业彩印-主叫名片'},
                                {codeId:'01109',codeName:'省内版企业彩印-成员名片'},
                                {codeId:'01110',codeName:'省内版企业彩印-主被叫彩印'},
                                {codeId:'01111',codeName:'省内版企业彩印-热线彩印'},
                                {codeId:'01112',codeName:'省内版企业彩印-挂机彩漫'},
                                {codeId:'01113',codeName:'省内版企业彩印-挂机短信'},
                                {codeId:'01114',codeName:'省内版企业彩印-主叫通讯录'},
                                {codeId:'01115',codeName:'杭研小移人家-主叫通讯录'},
                                {codeId:'01119',codeName:'企业彩印-商户彩印-挂机短信'},
                                {codeId:'02100',codeName:'新媒彩印-主动代言'},
                                {codeId:'02101',codeName:'新媒彩印-合作伙伴'},
                                {codeId:'02102',codeName:'新媒彩印-挂机彩漫'},
                                {codeId:'02103',codeName:'新媒彩印-挂机短信'},
                                {codeId:'03000',codeName:'个人彩印-被叫默认彩印'},
                                {codeId:'03001',codeName:'个人彩印-主叫默认彩印'},
                                {codeId:'03002',codeName:'个人彩印-被叫DIY彩印'},
                                {codeId:'03003',codeName:'个人彩印-主叫DIY彩印'},
                                {codeId:'03004',codeName:'个人彩印-情景彩印'},
                                {codeId:'03005',codeName:'个人彩印-动态彩印'}
                ],
                oldOptions:[],
                selectServiceCodes:[],
                selectProvinceIds:[],

                regionList:new Array(),//城市列表
                chartData:{},
                chartLoading:false,
                chartSettings:{},
                tableData: [],
                tableLoading:false,
                pickerOptions:{
                    disabledDate:function (today) {
                        return today.getTime()>Date.now();
                    }
                }
            }
        },
        created: function () {
            this.chartColors=["#5ab1ef"];
            this.legend_visible=false;
            this.chartSettings = {
                area: false
            };
        },
        watch: {
            'searchForm1.dataType':function (n, o) {
                this.search();
             },
            'searchForm1.date1':function (n, o) {
                if(n!=''){
                    this.searchForm1.date2='';
                    this.searchForm1.date2='';
                }
            },
            'searchForm1.date2':function (n, o) {
                if(n!=''){
                    this.searchForm1.date1='';
                }
            },
        },
        methods: {
            setButton(data){
                this.btnhide = data;
            },
            check(vm){
                if (vm.searchForm1.provinceIds.length===0) {
                    vm.$message("请选择省份");
                    return false;
                }
                return true;
            },
            search() {
                const vm = this;
                if(!vm.check(vm)){
                    return false;
                }
              if (this.isReSearch){
                vm.searchForm1.page.pageNo = 1;
              }
              this.isReSearch = true;

              var params = {
                    startDate:'',
                    endDate:'',
                    showType:this.searchForm1.type==='date'?1:2,
                    provinceIds:vm.searchForm1.provinceIds.join(','),
                    showCounty:vm.searchForm1.showCounty,
                    pageNo:vm.searchForm1.page.pageNo,
                    pageSize:vm.searchForm1.page.pageSize,
                    serviceIds:vm.searchForm1.serviceCodes.join(','),
                }
                if(!this.searchForm1.startDate){
                    this.$message('请输入开始时间');
                    return;
                }
                if(!this.searchForm1.endDate){
                    this.$message('请输入结束时间');
                    return;
                }
                if(new Date(this.searchForm1.startDate).getTime()>new Date(this.searchForm1.endDate).getTime()){
                    this.$message('结束时间不得小于开始时间');
                    return
                }
                if(this.searchForm1.type=='month'){
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM');
                }else{
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM-dd');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM-dd');
                }
                vm.tableLoading=true;
                axios.post(`${this.proxyUrl}/oper/sop`,params,{

                            headers: {
                                'CY-operation': 'callPushStats'
                            }
                        }).then(function(response) {
                            vm.tableData = response.data.data.callPushStatsList;
                            if(response.data.data.total != null){
                              vm.searchForm1.page.total = response.data.data.total;
                            }
                            vm.chartData= {
                                columns: ['statsDate',vm.searchForm1.dataType],
                                rows: response.data.data.callPushStatsDaily
                            };
                            vm.chartLoading=false;
                        })
                        .catch(function (error) {
                            console.log(error);
                        }).finally(function () {
                            vm.tableLoading=false;
                        });

            },
            exportClick(){
              this.$refs.addReqForm.resetFields();
            },
            confirmExport(){
              this.$refs.addReqForm.validate(valid => {
                if (valid) {
                  const vm = this;
                  if(!vm.check(vm)){
                    return false;
                  }
                  var params = {
                    startDate:'',
                    endDate:'',
                    showType:this.searchForm1.type==='date'?1:2,
                    provinceIds:vm.searchForm1.provinceIds.join(','),
                    showCounty:vm.searchForm1.showCounty,
                    serviceIds:vm.searchForm1.serviceCodes.join(','),
                  }
                  if(!this.searchForm1.startDate){
                    this.$message('请输入开始时间');
                    return;
                  }
                  if(!this.searchForm1.endDate){
                    this.$message('请输入结束时间');
                    return;
                  }
                  if(new Date(this.searchForm1.startDate).getTime()>new Date(this.searchForm1.endDate).getTime()){
                    this.$message('结束时间不得小于开始时间');
                    return
                  }
                  if(this.searchForm1.type=='month'){
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM');
                  }else{
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM-dd');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM-dd');
                  }
                  var req = {
                    fileName:this.addReq.fileName,
                    remark:this.addReq.remark,
                    taskType:2,
                    params:JSON.stringify(params)
                  }
                  this.propVisible = !this.propVisible;
                  axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`,req).then(function(res) {
                    let data = res.data;
                    if(data.code==0){
                      vm.$message.success("系统将生成文件名为"+vm.addReq.fileName+"的文件");
                    }else{
                      vm.$message.error(data.msg);
                    }
                  })
                } else {
                  return false;
                }
              });
            },
            cancelExport() {
              this.propVisible = !this.propVisible;
              this.$refs.addReqForm.resetFields();
            },
            downloadCheck(){
              const vm = this;
              if(!vm.check(vm)){
                return false;
              }
              if(!this.searchForm1.startDate){
                this.$message('请输入开始时间');
                return;
              }
              if(!this.searchForm1.endDate){
                this.$message('请输入结束时间');
                return;
              }
              if(new Date(this.searchForm1.startDate).getTime()>new Date(this.searchForm1.endDate).getTime()){
                this.$message('结束时间不得小于开始时间');
                return
              }
              this.propVisible=true;
            },
            download() {
                const vm = this;

                var params = {
                    startDate:'',
                    endDate:'',
                    showType:this.searchForm1.type==='date'?1:2,
                    provinceIds:vm.searchForm1.provinceIds.join(','),
                    showCounty:vm.searchForm1.showCounty,
                    serviceIds:vm.searchForm1.serviceCodes.join(','),
                }

                if(this.searchForm1.type=='month'){
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM');
                }else{
                    params.startDate=formDate(this.searchForm1.startDate,'yyyy-MM-dd');
                    params.endDate=formDate(this.searchForm1.endDate,'yyyy-MM-dd');
                }
                //发送下载请求
                axios.post(`${this.proxyUrl}/oper/sop`,params,{
                    headers: {
                        'CY-operation': 'exportCallPushStats'
                    },
                    responseType:'blob'
                })
                .then(function(response) {
                    dowandFile(response.data,'呼叫推送统计数据统计.xlsx');
                })
                .catch(function (error) {
                    console.log(error);
                })
            },
            handleSizeChange(val) {
                this.searchForm1.page.pageSize = val;
                this.isReSearch = false
                this.search();
            },
            handleCurrentChange(val) {
              this.isReSearch = false
              this.searchForm1.page.pageNo=val;
                this.search();
            },
             //根据省份查询城市列表
            selectProvince(){
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.searchForm1.provinceId},{emulateJSON:true})
                            .then((res)=>{
                                this.regionList=res.data;
                })
            },
            selectAll(val) {
                let allCodeName='全选';
                let allValues = []
                //保留所有值
                for (let item of this.serviceCodeList) {
                    allValues.push(item.codeId)
                }

                // 用来储存上一次的值，可以进行对比
                //const oldVal = this.oldOptions.length === 1 ? [] : this.oldOptions[1]

                // 若是全部选择
                if (val.includes(allCodeName)) this.selectServiceCodes = allValues

                // 取消全部选中  上次有 当前没有 表示取消全选
                if (this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) this.selectServiceCodes = []

                // 点击非全部选中  需要排除全部选中 以及 当前点击的选项
                // 新老数据都有全部选中
                if (this.oldOptions.includes(allCodeName) && val.includes(allCodeName)) {
                    const index = val.indexOf(allCodeName)
                    val.splice(index, 1) // 排除全选选项
                    this.selectServiceCodes = val
                }

                //全选未选 但是其他选项全部选上 则全选选上 上次和当前 都没有全选
                if (!this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) {
                    if (val.length === allValues.length - 1) this.selectServiceCodes = [allCodeName].concat(val)
                }

                //储存当前最后的结果 作为下次的老数据
                this.oldOptions = this.selectServiceCodes;

                //设置到form表单
                this.searchForm1.serviceCodes = [];
                for (let codeId of this.selectServiceCodes) {
                    if(codeId != allCodeName){
                        this.searchForm1.serviceCodes.push(codeId)
                    }

                }
            },
            selectAll2(val,optionList) {
                let allCodeName='全选';
                let allValues = ["全选"]
                //保留所有值
                for (let item of optionList) {
                    allValues.push(item.provinceCode)
                }

                // 若是全部选择
                if (val.includes(allCodeName)) this.selectProvinceIds = allValues;


                 // 取消全部选中  上次有 当前没有 表示取消全选
                if (this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) this.selectProvinceIds = []

                // 点击非全部选中  需要排除全部选中 以及 当前点击的选项
                // 新老数据都有全部选中
                if (this.oldOptions.includes(allCodeName) && val.includes(allCodeName)) {
                    const index = val.indexOf(allCodeName)
                    val.splice(index, 1) // 排除全选选项
                    this.selectProvinceIds = val
                }

                //全选未选 但是其他选项全部选上 则全选选上 上次和当前 都没有全选
                if (!this.oldOptions.includes(allCodeName) && !val.includes(allCodeName)) {
                    if (val.length === allValues.length - 1) this.selectProvinceIds = [allCodeName].concat(val)
                }

                //储存当前最后的结果 作为下次的老数据
                this.oldOptions = this.selectProvinceIds;

                //设置到form表单
                this.searchForm1.provinceIds = [];
                for (let provinceCode of this.selectProvinceIds) {
                    if(provinceCode != allCodeName){
                        this.searchForm1.provinceIds.push(provinceCode)
                    }

                }
            }
        }
    }
</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
    .el-radio-button:first-child .el-radio-button__inner,.el-radio-button:last-child .el-radio-button__inner{
        border-radius:0;
    }
    .el-radio-button__orig-radio:checked+.el-radio-button__inner {
        background: #434343;
        border-color:#DDDFE6;
        box-shadow:-1px 0 0 0 #DDDFE6;
        color:#fff;
    }
    .chartArea{
        border:1px solid #DDDFE6;
        margin-top:-1px;
        background-color: #F2F2F2;
    }
    .chartArea form{
        margin:20px 0 0 20px;
    }
    .chartArea .el-radio-button__inner{
        background:transparent;
        border: 1px solid transparent;
    }
    .chartArea .el-radio-button__orig-radio:checked+.el-radio-button__inner{
        background-color:#3B9ED8;
    }
</style>
