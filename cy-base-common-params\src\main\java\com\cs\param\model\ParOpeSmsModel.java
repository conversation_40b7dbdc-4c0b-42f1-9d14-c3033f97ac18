
package com.cs.param.model;

public class ParOpeSmsModel {
	private static final long serialVersionUID = 1L;
	private Integer id;
	private String status;
	private String acceptNo;
	private String acceptPhone;
	private String content;
	private String sendNo;
	private String triggerSceneId;
	private String isDelete;
	private String triggerSceneName;
	private String updateTime;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAcceptNo() {
		return acceptNo;
	}

	public void setAcceptNo(String acceptNo) {
		this.acceptNo = acceptNo;
	}

	public String getAcceptPhone() {
		return acceptPhone;
	}

	public void setAcceptPhone(String acceptPhone) {
		this.acceptPhone = acceptPhone;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getSendNo() {
		return sendNo;
	}

	public void setSendNo(String sendNo) {
		this.sendNo = sendNo;
	}

	public String getTriggerSceneId() {
		return triggerSceneId;
	}

	public void setTriggerSceneId(String triggerSceneId) {
		this.triggerSceneId = triggerSceneId;
	}

	public String getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(String isDelete) {
		this.isDelete = isDelete;
	}

	public String getTriggerSceneName() {
		return triggerSceneName;
	}

	public void setTriggerSceneName(String triggerSceneName) {
		this.triggerSceneName = triggerSceneName;
	}

	public String getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}

}
