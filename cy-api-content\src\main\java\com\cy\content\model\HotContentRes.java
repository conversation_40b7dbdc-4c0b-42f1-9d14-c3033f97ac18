package com.cy.content.model;

import lombok.Data;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

@Data
public class HotContentRes {
    private Integer id;
    /**
     * 企彩印唯一编号，不为空的编号不能重复
     */
    private String hcNumber;
    private Integer servType;

    /**
     * 彩印内容或模板内容
     */
    private String content;

    /**
     * 企业名称
     */
    private String enterpriseName;



    // 行业类型
    private String industryType;

    // 签名
    private String sign;

    // 审核时间
    private Timestamp auditTime;

    // 内容支持的运营商：1移动，2联通，3电信，多个时用逗号分割，默认支持移动
    private String operator;

    // 联通审核状态：未使用、待提交审核、审核中、审核通过、审核驳回
    private String unicomApproveStatus;

    // 联通审核意见
    private String unicomApproveIdea;

    // 联通审核时间
    private Timestamp unicomApproveTime;

    // 电信审核状态：未使用、待提交审核、审核中、审核通过、审核驳回
    private String telecomApproveStatus;

    // 电信审核意见
    private String telecomApproveIdea;

    // 电信审核时间
    private Timestamp telecomApproveTime;

    /**
     * 企业ID
     */
    private Integer enterpriseId;


    /**
     * 内容类型1:普通内容
     */
    private String contentType;


    /**
     * 投递类型 1、闪信，2、短信
     */
    private String deliveryType;

    /**
     * 提交时间
     */
    private Timestamp createTime;
    private Timestamp updateTime;

    /**
     * "审核状态：
     * 1、待提交审核；
     * 2、审核中；
     * 3、审核通过
     * 4、审核驳回"
     */
    private String approveStatus;

    private String approveIdea;

    /**
     * 热线号码List
     */
    private List<String> hotLineNoList;


    /**
     * 热线号码
     */
    private String hotlineNo;

    /**
     * 中央彩印内部的模板的id
     */
    private String templateId;
    /**
     * 资质上传url，支持批量，以“;”分隔
     */
    private String certificateUrl;
    /**
     * 查询的页码
     */
    private int pageNum;

    /**
     * 每页显示条数
     */
    private int countSize;

    /**
     * 电信投递通道，5：号百；8：彩讯
     */
    private String telecomDeliveryWay;

    /**
     * 联通投递通道，6：联通在线；8：彩讯
     */
    private String unicomDeliveryWay;

}
