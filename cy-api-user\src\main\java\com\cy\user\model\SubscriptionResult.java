package com.cy.user.model;

import jakarta.xml.bind.annotation.XmlRootElement;

/**
 * 
 * <AUTHOR> @date
 * @Description 彩印平台响应客户端参数
 */
@XmlRootElement
public class SubscriptionResult {

	/**
	 * 返回码
	 */

	private String code;

	/**
	 * 描述信息
	 */
	private String text;

	/**
	 * 额外返回的数据
	 */
	private Object data;

	private String orderId;

	/**
	 * @return the code
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @param code
	 *            the code to set
	 */
	public void setCode(String code) {
		this.code = code;
	}

	/**
	 * @return the text
	 */
	public String getText() {
		return text;
	}

	/**
	 * @param text
	 *            the text to set
	 */
	public void setText(String text) {
		this.text = text;
	}

	public SubscriptionResult(){}
	
	public SubscriptionResult(String code, String text) {
		super();
		this.code = code;
		this.text = text;
	}

	/**
	 * @Title: toString
	 * @Description: 
	 * @return
	 */
	@Override
	public String toString() {
		return "SubscriptionResult [code=" + code + ", text=" + text + "]";
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
}
