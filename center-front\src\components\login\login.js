import store from "../../store/store";

let token
import md5 from 'blueimp-md5'
//import crypto from './../../util/crypto.js';
import { encrypt } from './../../util/rsa.js'
export default {
    data() {
        return {
        	closeLogin:false,
            overdueVisible: false,
            modPasswordVisible: false,
            loginForm: {name: '', password: '', captcha: ""},
            imgSrc: `${this.proxyUrl}/sys/sysUser/checkcode?` + Math.random(),
            modForm: {
                sysUserName: "",
                sysUserEmail: "",
                newPassword: "",
                newPassword2: ""
            },
            list: [],
            overdueForm: {
                sysUserName: "",
                oldPassword: "",
                newPassword: "",
                newPassword2: ""
            }
        }


    },
    beforeMount() {
        sessionStorage.removeItem('TOKEN');
        // sessionStorage.removeItem('menuList');
        this.$store.dispatch('setMenuList', null);
        sessionStorage.removeItem('userInfo');
        const _that = this
        var xhr = new XMLHttpRequest();
	    xhr.open("HEAD", '/login', true);
	    xhr.onreadystatechange = function () {
	        var res = xhr.getAllResponseHeaders();
            if(res.indexOf('potallogin: close')>-1 || res.indexOf('potalLogin: close')>-1){
				_that.$router.push({path: '/404'});
				_that.closeLogin = true
			}
	    }
	    xhr.send();
    },
    methods: {
        //验证账号密码
        vistory() {
            if (this.loginForm.name === '') {
                this.$message('用户名不能为空');
                return false;
            }
            if (this.loginForm.password === '') {
                this.$message('用户密码不能为空');
                return false;
            }
            if (this.loginForm.captcha === '') {
                this.$message('验证码不能为空');
                return false;
            }
            return true;
        },
        //登录
        login() {
            if (this.vistory()) {
                let params = {
                    name: this.loginForm.name,
                    password: encrypt(this.loginForm.password),
                    captcha: this.loginForm.captcha
                }
                this.$store.dispatch('loginModule/login', params).then(res => {
                    console.info(res);
                    if (res.result === 'success') {
                        sessionStorage.setItem('TOKEN', res.token);
                        sessionStorage.setItem('userInfo', JSON.stringify(res.sysUser));
                        this.menuListQuery();
                    } else if (res.result === 'over') {
                        this.getCaptcha();
                        this.overdueVisible = true;
                        this.$message({
                            message: res.message,
                            type: 'error'
                        })
                    } else {
                        this.getCaptcha();
                        this.$message({
                            message: res.message,
                            type: 'error'
                        })
                    }
                })
            }
        },
        getCaptcha() {
            this.imgSrc = `${this.proxyUrl}/sys/sysUser/checkcode?` + Math.random();
        },
        cleanForm() {
            this.modPasswordVisible = false;
            this.overdueVisible = false;
            this.modForm = {};
            this.overdueForm = {};
        },
        menuListQuery() {
            this.$store.dispatch('coreModule/menuListQuery', {}).then(res => {
                if (res.result === 'success') {
                    // res.data.list.unshift({
                    //     icon: "el-icon-user",
                    //     id: 990,
                    //     index: "11",
                    //     name: "个人彩印内容",
                    //     children: [
                    //         {
                    //             children: null,
                    //             href: "/subjectLabel",
                    //             id: 991,
                    //             index: "11-2",
                    //             text: "专题标签",
                    //         },
                    //         {
                    //             children: null,
                    //             href: "/subjectPrint",
                    //             id: 992,
                    //             index: "11-3",
                    //             text: "专题彩印",
                    //         },
                    //         {
                    //             children: null,
                    //             href: "/contentAudit/colourPrint",
                    //             id: 993,
                    //             index: "11-2",
                    //             text: "彩印专题审核",
                    //         },
                    //         {
                    //             children: null,
                    //             href: "/contentAudit/colourPrintviewDetails",
                    //             id: 994,
                    //             index: "11-3",
                    //             text: "专题已通过审核记录",
                    //         }
                    //     ]
                    // })

                    for (let i = 0; i < res.data.list.length; i++) {
                        if ("menu" == res.data.list[i].type){
                            this.list.push(res.data.list[i])
                        }
                    }
                    this.$store.dispatch('setMenuList', this.list);
                    // sessionStorage.setItem('menuList', JSON.stringify(res.data.list));
                    this.$http
                        .post(`${this.proxyUrl}/sys/sysUser/sysReject/getList`)
                        .then(function (refuseList) {
                         //   console.log(refuseList.body);
                            sessionStorage.setItem('refuseList', JSON.stringify(refuseList.body));
                            //console.log(JSON.parse(sessionStorage.getItem("refuseList")))
                        })
                    this.$router.push('/home');
                }
            })
        },
        forgetPassWord(){
            this.$router.push('/forgetPwd');
        },
        mod() {
            if (this.modForm.sysUserName == null || this.modForm.sysUserName == "") {
                this.$message.error("请输入用户名");
                this.modPasswordVisible = true;
                return false;
            }
            if (this.modForm.sysUserEmail == null || this.modForm.sysUserEmail == "") {
                this.$message.error("请输入邮箱");
                this.modPasswordVisible = true;
                return false;
            }
            if (this.modForm.newPassword == null || this.modForm.newPassword == "") {
                this.$message.error("请输入新密码");
                this.modPasswordVisible = true;
                return false;
            }

            if (!this.isPwd(this.modForm.newPassword)) {
                this.$message.error("6-16位以字母开头的、由字母、数字或下划线组成、长度最多16位字符");
                this.overdueVisible = true;
                return false;
            }

            if (this.modForm.newPassword != this.modForm.newPassword2) {
                this.$message.error("两次密码不一致，请重新输入！");
                this.modPasswordVisible = true;
                return false;
            }
            this.modForm.newPassword = encrypt(this.modForm.newPassword);
            this.modForm.newPassword2 = encrypt(this.modForm.newPassword2);
            this.$http
                .post(`${this.proxyUrl}/sys/sysUser/forgetPwd`, JSON.stringify(this.modForm))
                .then(function (res) {
                    if (res.data.resStatus == 0) {
                        this.$message.success("密码修改成功");
                        sessionStorage.removeItem("username");
                        this.modForm.newPassword = '';
                        this.modForm.newPassword2 = '';
                        this.modPasswordVisible = false;
                    } else {
                        this.$message.error(res.data.resText);
                        this.modForm.newPassword = '';
                        this.modForm.newPassword2 = '';
                        this.modPasswordVisible = true;
                    }
                });
        },
        isPwd(val) {
            let reg = /^[a-zA-Z][a-zA-Z0-9_!@#$^]{6,16}$/;
            return reg.test(val);
        },
        overdueMod() {

            if (this.overdueForm.sysUserName == null || this.overdueForm.sysUserName == "") {
                this.$message.error("请输入用户名");
                this.overdueVisible = true;
                return false;
            }
            if (this.overdueForm.oldPassword == null || this.overdueForm.oldPassword == "") {
                this.$message.error("请输入旧密码");
                this.overdueVisible = true;
                return false;
            }
            if (this.overdueForm.newPassword == null || this.overdueForm.newPassword == "") {
                this.$message.error("请输入新密码");
                this.overdueVisible = true;
                return false;
            }

            if (!this.isPwd(this.overdueForm.newPassword)) {
                this.$message.error("6-16位以字母开头的、由字母、数字或下划线组成、长度最多16位字符");
                this.overdueVisible = true;
                return false;
            }
            if (this.overdueForm.newPassword != this.overdueForm.newPassword2) {
                this.$message.error("两次密码不一致，请重新输入！");
                this.overdueVisible = true;
                return false;
            }
            this.overdueForm.newPassword = encrypt(this.overdueForm.newPassword);
            this.overdueForm.newPassword2 = encrypt(this.overdueForm.newPassword);
            this.overdueForm.oldPassword = encrypt(this.overdueForm.oldPassword);
            this.$http

                .post(`${this.proxyUrl}/sys/sysUser/forgetPwd`, JSON.stringify(this.overdueForm))
                .then(function (res) {
                    if (res.data.resStatus == 0) {
                        this.$message.success("密码修改成功");
                        sessionStorage.removeItem("username");
                        this.overdueForm.oldPassword = '';
                        this.overdueForm.newPassword = '';
                        this.overdueForm.newPassword2 = '';
                        this.overdueVisible = false;
                    } else {
                        this.$message.error(res.data.resText);
                        this.overdueForm.oldPassword = '';
                        this.overdueForm.newPassword = '';
                        this.overdueForm.newPassword2 = '';
                        this.overdueVisible = true;
                    }
                });
        }
    }
}