package com.cy.audit.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.crab2died.annotation.ExcelField;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

public class AuditPackageModel implements Serializable {
    /**
	 * 
	 */
    private static final long serialVersionUID = 1L;
    private int svMold;// 1用户diy彩印 2单条彩印3彩印盒

    private String svId;

    private String cspSignPkgid;
    @ExcelField(title = "内容ID", order = 2)
    private String svNumber;
    /**
     * 0：新增 1：删除 2：修改
     */
    private int svType;
    private String svName;// 彩印盒名称
    @ExcelField(title = "内容", order = 3)
    private String svCard;
    @ExcelField(title = "提交人", order = 4)
    private String submitUser;
    @ExcelField(title = "提交时间", order = 5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String submitTime;
    private int submitCount;// 审核提交次数
    private int svStatus;
    @ExcelField(title = "驳回原因", order = 9)
    private String svCause;// 驳回原因
    @ExcelField(title = "审核人", order = 6)
    private String assessor;// 审核人
    private String revoke;// 撤销人
    @ExcelField(title = "审核时间", order = 7)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String svSssesstime;
    @ExcelField(title = "类型", order = 1)
    private String svMoldName;
    @ExcelField(title = "撤销原因", order = 10)
    private String revokeCause;// 撤销原因
    @ExcelField(title = "撤销时间", order = 11)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String revokeTime;// 撤销时间

    private String svEndTime;// 失效时间

    public String getPassContentNum() {
        return passContentNum;
    }

    public void setPassContentNum(String passContentNum) {
        this.passContentNum = passContentNum;
    }

    //审核通过的内容编号
    private String passContentNum;

    private String content1, content2, content3, content4, content5, content6, content7, content8, content9, content10,
	    content11, content12, content13, content14, content15, content16, content17, content18, content19,
	    content20, content21, content22, content23, content24, content25, content26, content27, content28,
	    content29, content30;

    private String contentId1, contentId2, contentId3, contentId4, contentId5, contentId6, contentId7, contentId8,
	    contentId9, contentId10, contentId11, contentId12, contentId13, contentId14, contentId15, contentId16,
	    contentId17, contentId18, contentId19, contentId20, contentId21, contentId22, contentId23, contentId24,
	    contentId25, contentId26, contentId27, contentId28, contentId29, contentId30;

    private String svPkgLabelId;
    private String svPkgGroupId;
    @ExcelField(title = "审核意见状态", order = 8)
    private String svStatusName;

    private String groupName;
    private String labelName;

    private String sensitiveWords;

    private String svProId;

    private String svProName;
    private String qualificationsUrls;

    private List<String> qualificationsUrlList;
    /**
     * 联通审核状态
     * 1：待审批
     * 2：审批不通过
     * 3：审核通过
     */
    private Integer unicomStatus;

    /**
     * 联通审核意见
     */
    private String unicomMessage;

    /**
     * 电信审核状态
     * 1：待审批
     * 2：审批不通过
     * 3：审核通过
     */
    private Integer telecomStatus;

    /**
     * 电信审核意见
     */
    private String telecomMessage;


    private Integer notNeedSyncAudit = 0;


    public Integer getTelecomStatus() {
        return telecomStatus;
    }

    public void setTelecomStatus(Integer telecomStatus) {
        this.telecomStatus = telecomStatus;
    }

    public String getTelecomMessage() {
        return telecomMessage;
    }

    public void setTelecomMessage(String telecomMessage) {
        this.telecomMessage = telecomMessage;
    }

    public Integer getUnicomStatus() {
        return unicomStatus;
    }

    public void setUnicomStatus(Integer unicomStatus) {
        this.unicomStatus = unicomStatus;
    }

    public String getUnicomMessage() {
        return unicomMessage;
    }

    public void setUnicomMessage(String unicomMessage) {
        this.unicomMessage = unicomMessage;
    }

    public List<String> getQualificationsUrlList() {
        return qualificationsUrlList;
    }

    public void setQualificationsUrlList(List<String> qualificationsUrlList) {
        this.qualificationsUrlList = qualificationsUrlList;
    }

    public String getQualificationsUrls() {
        return qualificationsUrls;
    }

    public void setQualificationsUrls(String qualificationsUrls) {
        this.qualificationsUrls = qualificationsUrls;
    }

    public String getRevoke() {
        return revoke;
    }

    public void setRevoke(String revoke) {
        this.revoke = revoke;
    }

    public String getSvProId() {
	return svProId;
    }

    public void setSvProId(String svProId) {
	this.svProId = svProId;
    }

    public String getSvProName() {
	return svProName;
    }

    public void setSvProName(String svProName) {
	this.svProName = svProName;
    }

    public String getSvEndTime() {
	return svEndTime;
    }

    public void setSvEndTime(String svEndTime) {
	this.svEndTime = svEndTime;
    }

    public String getSensitiveWords() {
	return sensitiveWords;
    }

    public void setSensitiveWords(String sensitiveWords) {
	this.sensitiveWords = sensitiveWords;
    }

    public String getGroupName() {
	return groupName;
    }

    public void setGroupName(String groupName) {
	this.groupName = groupName;
    }

    public String getLabelName() {
	return labelName;
    }

    public void setLabelName(String labelName) {
	this.labelName = labelName;
    }

    public String getSvStatusName() {
	return svStatusName;
    }

    public void setSvStatusName(String svStatusName) {
	this.svStatusName = svStatusName;
    }

    public String getSvPkgLabelId() {
	return svPkgLabelId;
    }

    public void setSvPkgLabelId(String svPkgLabelId) {
	this.svPkgLabelId = svPkgLabelId;
    }

    public String getSvPkgGroupId() {
	return svPkgGroupId;
    }

    public void setSvPkgGroupId(String svPkgGroupId) {
	this.svPkgGroupId = svPkgGroupId;
    }

    public int getSvType() {
	return svType;
    }

    public void setSvType(int svType) {
	this.svType = svType;
    }

    public String getAssessor() {
	return assessor;
    }

    public void setAssessor(String assessor) {
	this.assessor = assessor;
    }

    public String getSvName() {
	return svName;
    }

    public void setSvName(String svName) {
	this.svName = svName;
    }

    public String getSvNumber() {
	return svNumber;
    }

    public void setSvNumber(String svNumber) {
	this.svNumber = svNumber;
    }

    public String getSvMoldName() {
	return svMoldName;
    }

    public void setSvMoldName(String svMoldName) {
	this.svMoldName = svMoldName;
    }

    public String getRevokeCause() {
	return revokeCause;
    }

    public void setRevokeCause(String revokeCause) {
	this.revokeCause = revokeCause;
    }

    public String getRevokeTime() {
	return revokeTime;
    }

    public void setRevokeTime(String revokeTime) {
	this.revokeTime = revokeTime;
    }

    public int getSvStatus() {
	return svStatus;
    }

    public void setSvStatus(int svStatus) {
	this.svStatus = svStatus;
    }

    public String getSvSssesstime() {
	return svSssesstime;
    }

    public void setSvSssesstime(String svSssesstime) {
	this.svSssesstime = svSssesstime;
    }

    public String getSvCause() {
	return svCause;
    }

    public void setSvCause(String svCause) {
	this.svCause = svCause;
    }

    public int getSvMold() {
	return svMold;
    }

    public void setSvMold(int svMold) {
	this.svMold = svMold;
    }

    public String getSvId() {
	return svId;
    }

    public void setSvId(String svId) {
	this.svId = svId;
    }

    public String getSvCard() {
	return svCard;
    }

    public void setSvCard(String svCard) {
	this.svCard = svCard;
    }

    public String getSubmitUser() {
	return submitUser;
    }

    public void setSubmitUser(String submitUser) {
	this.submitUser = submitUser;
    }

    public String getSubmitTime() {
	return submitTime;
    }

    public void setSubmitTime(String submitTime) {
	this.submitTime = submitTime;
    }

    public int getSubmitCount() {
	return submitCount;
    }

    public void setSubmitCount(int submitCount) {
	this.submitCount = submitCount;
    }

    public String getContent1() {
	return content1;
    }

    public void setContent1(String content1) {
	this.content1 = content1;
    }

    public String getContent2() {
	return content2;
    }

    public void setContent2(String content2) {
	this.content2 = content2;
    }

    public String getContent3() {
	return content3;
    }

    public void setContent3(String content3) {
	this.content3 = content3;
    }

    public String getContent4() {
	return content4;
    }

    public void setContent4(String content4) {
	this.content4 = content4;
    }

    public String getContent5() {
	return content5;
    }

    public void setContent5(String content5) {
	this.content5 = content5;
    }

    public String getContent6() {
	return content6;
    }

    public void setContent6(String content6) {
	this.content6 = content6;
    }

    public String getContent7() {
	return content7;
    }

    public void setContent7(String content7) {
	this.content7 = content7;
    }

    public String getContent8() {
	return content8;
    }

    public void setContent8(String content8) {
	this.content8 = content8;
    }

    public String getContent9() {
	return content9;
    }

    public void setContent9(String content9) {
	this.content9 = content9;
    }

    public String getContent10() {
	return content10;
    }

    public void setContent10(String content10) {
	this.content10 = content10;
    }

    public String getContent11() {
	return content11;
    }

    public void setContent11(String content11) {
	this.content11 = content11;
    }

    public String getContent12() {
	return content12;
    }

    public void setContent12(String content12) {
	this.content12 = content12;
    }

    public String getContent13() {
	return content13;
    }

    public void setContent13(String content13) {
	this.content13 = content13;
    }

    public String getContent14() {
	return content14;
    }

    public void setContent14(String content14) {
	this.content14 = content14;
    }

    public String getContent15() {
	return content15;
    }

    public void setContent15(String content15) {
	this.content15 = content15;
    }

    public String getContent16() {
	return content16;
    }

    public void setContent16(String content16) {
	this.content16 = content16;
    }

    public String getContent17() {
	return content17;
    }

    public void setContent17(String content17) {
	this.content17 = content17;
    }

    public String getContent18() {
	return content18;
    }

    public void setContent18(String content18) {
	this.content18 = content18;
    }

    public String getContent19() {
	return content19;
    }

    public void setContent19(String content19) {
	this.content19 = content19;
    }

    public String getContent20() {
	return content20;
    }

    public void setContent20(String content20) {
	this.content20 = content20;
    }

    public String getContent21() {
	return content21;
    }

    public void setContent21(String content21) {
	this.content21 = content21;
    }

    public String getContent22() {
	return content22;
    }

    public void setContent22(String content22) {
	this.content22 = content22;
    }

    public String getContent23() {
	return content23;
    }

    public void setContent23(String content23) {
	this.content23 = content23;
    }

    public String getContent24() {
	return content24;
    }

    public void setContent24(String content24) {
	this.content24 = content24;
    }

    public String getContent25() {
	return content25;
    }

    public void setContent25(String content25) {
	this.content25 = content25;
    }

    public String getContent26() {
	return content26;
    }

    public void setContent26(String content26) {
	this.content26 = content26;
    }

    public String getContent27() {
	return content27;
    }

    public void setContent27(String content27) {
	this.content27 = content27;
    }

    public String getContent28() {
	return content28;
    }

    public void setContent28(String content28) {
	this.content28 = content28;
    }

    public String getContent29() {
	return content29;
    }

    public void setContent29(String content29) {
	this.content29 = content29;
    }

    public String getContent30() {
	return content30;
    }

    public void setContent30(String content30) {
	this.content30 = content30;
    }

    public String getContentId1() {
	return contentId1;
    }

    public void setContentId1(String contentId1) {
	this.contentId1 = contentId1;
    }

    public String getContentId2() {
	return contentId2;
    }

    public void setContentId2(String contentId2) {
	this.contentId2 = contentId2;
    }

    public String getContentId3() {
	return contentId3;
    }

    public void setContentId3(String contentId3) {
	this.contentId3 = contentId3;
    }

    public String getContentId4() {
	return contentId4;
    }

    public void setContentId4(String contentId4) {
	this.contentId4 = contentId4;
    }

    public String getContentId5() {
	return contentId5;
    }

    public void setContentId5(String contentId5) {
	this.contentId5 = contentId5;
    }

    public String getContentId6() {
	return contentId6;
    }

    public void setContentId6(String contentId6) {
	this.contentId6 = contentId6;
    }

    public String getContentId7() {
	return contentId7;
    }

    public void setContentId7(String contentId7) {
	this.contentId7 = contentId7;
    }

    public String getContentId8() {
	return contentId8;
    }

    public void setContentId8(String contentId8) {
	this.contentId8 = contentId8;
    }

    public String getContentId9() {
	return contentId9;
    }

    public void setContentId9(String contentId9) {
	this.contentId9 = contentId9;
    }

    public String getContentId10() {
	return contentId10;
    }

    public void setContentId10(String contentId10) {
	this.contentId10 = contentId10;
    }

    public String getContentId11() {
	return contentId11;
    }

    public void setContentId11(String contentId11) {
	this.contentId11 = contentId11;
    }

    public String getContentId12() {
	return contentId12;
    }

    public void setContentId12(String contentId12) {
	this.contentId12 = contentId12;
    }

    public String getContentId13() {
	return contentId13;
    }

    public void setContentId13(String contentId13) {
	this.contentId13 = contentId13;
    }

    public String getContentId14() {
	return contentId14;
    }

    public void setContentId14(String contentId14) {
	this.contentId14 = contentId14;
    }

    public String getContentId15() {
	return contentId15;
    }

    public void setContentId15(String contentId15) {
	this.contentId15 = contentId15;
    }

    public String getContentId16() {
	return contentId16;
    }

    public void setContentId16(String contentId16) {
	this.contentId16 = contentId16;
    }

    public String getContentId17() {
	return contentId17;
    }

    public void setContentId17(String contentId17) {
	this.contentId17 = contentId17;
    }

    public String getContentId18() {
	return contentId18;
    }

    public void setContentId18(String contentId18) {
	this.contentId18 = contentId18;
    }

    public String getContentId19() {
	return contentId19;
    }

    public void setContentId19(String contentId19) {
	this.contentId19 = contentId19;
    }

    public String getContentId20() {
	return contentId20;
    }

    public void setContentId20(String contentId20) {
	this.contentId20 = contentId20;
    }

    public String getContentId21() {
	return contentId21;
    }

    public void setContentId21(String contentId21) {
	this.contentId21 = contentId21;
    }

    public String getContentId22() {
	return contentId22;
    }

    public void setContentId22(String contentId22) {
	this.contentId22 = contentId22;
    }

    public String getContentId23() {
	return contentId23;
    }

    public void setContentId23(String contentId23) {
	this.contentId23 = contentId23;
    }

    public String getContentId24() {
	return contentId24;
    }

    public void setContentId24(String contentId24) {
	this.contentId24 = contentId24;
    }

    public String getContentId25() {
	return contentId25;
    }

    public void setContentId25(String contentId25) {
	this.contentId25 = contentId25;
    }

    public String getContentId26() {
	return contentId26;
    }

    public void setContentId26(String contentId26) {
	this.contentId26 = contentId26;
    }

    public String getContentId27() {
	return contentId27;
    }

    public void setContentId27(String contentId27) {
	this.contentId27 = contentId27;
    }

    public String getContentId28() {
	return contentId28;
    }

    public void setContentId28(String contentId28) {
	this.contentId28 = contentId28;
    }

    public String getContentId29() {
	return contentId29;
    }

    public void setContentId29(String contentId29) {
	this.contentId29 = contentId29;
    }

    public String getContentId30() {
	return contentId30;
    }

    public void setContentId30(String contentId30) {
	this.contentId30 = contentId30;
    }

    public String getCspSignPkgid() {
        return cspSignPkgid;
    }

    public void setCspSignPkgid(String cspSignPkgid) {
        this.cspSignPkgid = cspSignPkgid;
    }

    public Integer getNotNeedSyncAudit() {
        return notNeedSyncAudit;
    }

    public void setNotNeedSyncAudit(Integer notNeedSyncAudit) {
        this.notNeedSyncAudit = notNeedSyncAudit;
    }
}
