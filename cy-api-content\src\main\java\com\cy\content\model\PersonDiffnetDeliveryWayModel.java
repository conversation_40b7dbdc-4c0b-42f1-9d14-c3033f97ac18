package com.cy.content.model;

import java.util.Date;

/**
 * 个彩异网通道设置实体类
 */
public class PersonDiffnetDeliveryWayModel {

    private Integer id; // 主键
    private Integer isDefault; // 是否默认通道：0-默认设置；1-定制设置
    private String platforms; // 运营商（2-联通；3-电信）
    private String servType; // 业务类型：1-名片号/视宣号；2-热线彩印；3...
    private Integer wayType; // 通道类型（5-号百；6-联通在线；8-彩讯）
    private Integer isUsed; // 是否启用：1-启用；2-禁用
    private String wayName; // 通道名称
    private Date createTime; // 创建时间
    private Date updateTime; // 修改时间

    // Getters and Setters

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getPlatforms() {
        return platforms;
    }

    public void setPlatforms(String platforms) {
        this.platforms = platforms;
    }

    public String getServType() {
        return servType;
    }

    public void setServType(String servType) {
        this.servType = servType;
    }

    public Integer getWayType() {
        return wayType;
    }

    public void setWayType(Integer wayType) {
        this.wayType = wayType;
    }

    public Integer getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(Integer isUsed) {
        this.isUsed = isUsed;
    }

    public String getWayName() {
        return wayName;
    }

    public void setWayName(String wayName) {
        this.wayName = wayName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
