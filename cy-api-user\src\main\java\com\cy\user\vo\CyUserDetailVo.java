package com.cy.user.vo;

import java.util.HashMap;
import java.util.Map;


public class CyUserDetailVo {
	private String pkCurUserid;
	private String curNick;
	private String curProvinceName;
	private String curAreaName;

	/**
	 * 当前彩印推送业务:1个人彩印2新媒彩印3提醒彩印4企业彩印
	 */
	private Map<String, String> pushTypeMap = new HashMap<String, String>();
	/**
	 * 彩印接收类型：0默认1USSD短信发送2免提短信(闪信)发送3普通短信发送
	 */
	private int recvMode;
	/**
	 * 当前彩印拒接业务:1个人彩印2新媒彩印3提醒彩印4企业彩印
	 */
	private Map<String, String> rejectTypeMap = new HashMap<String, String>();
	/**
	 * //2：黑名单分组;3：白名单分组 ;
	 */
	private Integer blackWhite;
	/**
	 * 是否在红名单
	 */
	private String atRed;
	/**
	 * 是否为VOLTE用户
	 */
	private String isVolteUser;
	/**
	 * 是否在VOLTE域
	 */
	private String isVolteRegion;

	public int getRecvMode() {
		return recvMode;
	}

	public void setRecvMode(int recvMode) {
		this.recvMode = recvMode;
	}

	public Integer getBlackWhite() {
		return blackWhite;
	}

	public void setBlackWhite(Integer blackWhite) {
		this.blackWhite = blackWhite;
	}

	public String getAtRed() {
		return atRed;
	}

	public void setAtRed(String atRed) {
		this.atRed = atRed;
	}

	public String getIsVolteUser() {
		return isVolteUser;
	}

	public void setIsVolteUser(String isVolteUser) {
		this.isVolteUser = isVolteUser;
	}

	public String getIsVolteRegion() {
		return isVolteRegion;
	}

	public void setIsVolteRegion(String isVolteRegion) {
		this.isVolteRegion = isVolteRegion;
	}

	public Map<String, String> getPushTypeMap() {
		return pushTypeMap;
	}

	public void setPushTypeMap(Map<String, String> pushTypeMap) {
		this.pushTypeMap = pushTypeMap;
	}

	public Map<String, String> getRejectTypeMap() {
		return rejectTypeMap;
	}

	public void setRejectTypeMap(Map<String, String> rejectTypeMap) {
		this.rejectTypeMap = rejectTypeMap;
	}

	/**
	 * 包装彩印类型
	 * 
	 * @return
	 */


	public String getPkCurUserid() {
		return pkCurUserid;
	}

	public void setPkCurUserid(String pkCurUserid) {
		this.pkCurUserid = pkCurUserid;
	}

	public String getCurNick() {
		return curNick;
	}

	public void setCurNick(String curNick) {
		this.curNick = curNick;
	}

	public String getCurProvinceName() {
		return curProvinceName;
	}

	public void setCurProvinceName(String curProvinceName) {
		this.curProvinceName = curProvinceName;
	}

	public String getCurAreaName() {
		return curAreaName;
	}

	public void setCurAreaName(String curAreaName) {
		this.curAreaName = curAreaName;
	}

}
