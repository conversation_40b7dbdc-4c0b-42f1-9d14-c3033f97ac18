import {post} from './../../../servers/httpServer.js';
const state={
   
};
const mutations={
  
};
const actions={
  //获取省份列表
  getProvinceList:async({},params)=>{
    let result=await post('/regionMgt/getProvince',params);
    return result.data;
  },
  //获取查询条件角色列表
  getRoleList:async({},params)=>{
    let result=await post('/sysRole/getSysRoleList',params);
    return result.data;
  },
  //获取用户管理列表
  sysUserList:async({},params)=>{
    let result=await post('/sysUser/getSysUserPage',params);
    return result.data;
  },
  //新增
  addSysUser:async({},params)=>{
    let result=await post('/sysUser/addSysUser',params);
    return result.data;
  },
  //修改
  updateSysUser:async({},params)=>{
    let result=await post('/sysUser/updateSysUser',params);
    return result.data;
  },
};
const sysUserListModule={
  namespaced:true,
  state:state,
  mutations:mutations,
  actions:actions
};
export default sysUserListModule;