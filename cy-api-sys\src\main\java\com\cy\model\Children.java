package com.cy.model;

import java.util.ArrayList;
import java.util.List;

public class Children {
	
	private Integer id;
	
	private String text;
	
	private String index;
	
	private String href;

	private String type;

	private List<Children> children = new ArrayList<Children>();

	public Children(Integer id,String text, String index, String href) {
		super();
		this.id = id;
		this.text = text;
		this.index = index;
		this.href = href;
	}
	
	public Children(Integer id,String text, String index, String href, String type) {
		super();
		this.id = id;
		this.text = text;
		this.index = index;
		this.href = href;
		this.type = type;
	}

	/**
	 * @return the text
	 */
	public String getText() {
		return text;
	}

	/**
	 * @param text the text to set
	 */
	public void setText(String text) {
		this.text = text;
	}

	/**
	 * @return the index
	 */
	public String getIndex() {
		return index;
	}

	/**
	 * @param index the index to set
	 */
	public void setIndex(String index) {
		this.index = index;
	}

	/**
	 * @return the href
	 */
	public String getHref() {
		return href;
	}

	/**
	 * @param href the href to set
	 */
	public void setHref(String href) {
		this.href = href;
	}

	/**
	 * @return the id
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Integer id) {
		this.id = id;
	}

	/**
	 * @return the children
	 */
	public List<Children> getChildren() {
		return children;
	}

	/**
	 * @param children the children to set
	 */
	public void setChildren(List<Children> children) {
		this.children = children;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @Title: toString
	 * @Description:
	 * @return
	 */
	@Override
	public String toString() {
		return "Children{" +
				"id=" + id +
				", text='" + text + '\'' +
				", index='" + index + '\'' +
				", href='" + href + '\'' +
				", type='" + type + '\'' +
				", children=" + children +
				'}';
	}
}
