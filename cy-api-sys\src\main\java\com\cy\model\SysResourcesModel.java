
package com.cy.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @date 2018年4月20日 - 上午11:37:01
 * @Description 资源model
 */
public class SysResourcesModel {
	private static final long serialVersionUID = 1L;
	/**
	 * 资源ID
	 */
	private Integer sysResourcesId;
	/**
	 * 资源名称
	 */

	private String sysResourcesName;
	/**
	 * 父类资源ID
	 */
	private Integer sysParentResourcesId;
	/**
	 * 资源描述
	 */
	private String sysResourcesDescs;
	/**
	 * 资源url
	 */
	private String url;
	/**
	 * 菜单位置
	 */
	private String index;
	/**
	 * 资源类型
	 */
	private String type;
	/**
	 * 
	 */
	private String iconCls;
	private List<SysResourcesModel> children = new ArrayList<SysResourcesModel>();

	public Integer getSysResourcesId() {
		return sysResourcesId;
	}

	public void setSysResourcesId(Integer sysResourcesId) {
		this.sysResourcesId = sysResourcesId;
	}

	public String getSysResourcesName() {
		return sysResourcesName;
	}

	public void setSysResourcesName(String sysResourcesName) {
		this.sysResourcesName = sysResourcesName;
	}


	/**
	 * @return the sysParentResourcesId
	 */
	public Integer getSysParentResourcesId() {
		return sysParentResourcesId;
	}

	/**
	 * @param sysParentResourcesId the sysParentResourcesId to set
	 */
	public void setSysParentResourcesId(Integer sysParentResourcesId) {
		this.sysParentResourcesId = sysParentResourcesId;
	}

	public String getSysResourcesDescs() {
		return sysResourcesDescs;
	}

	public void setSysResourcesDescs(String sysResourcesDescs) {
		this.sysResourcesDescs = sysResourcesDescs;
	}

	public List<SysResourcesModel> getChildren() {
		return children;
	}

	public void setChildren(List<SysResourcesModel> children) {
		this.children = children;
	}

	public void addChildren(SysResourcesModel sysResourcesModel) {
		this.children.add(sysResourcesModel);
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getIconCls() {
		return iconCls;
	}

	public void setIconCls(String iconCls) {
		this.iconCls = iconCls;
	}

	/**
	 * @return the index
	 */
	public String getIndex() {
		return index;
	}

	/**
	 * @param index
	 *            the index to set
	 */
	public void setIndex(String index) {
		this.index = index;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type
	 *            the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @Title: toString
	 * @Description: TODO
	 * @return
	 */
	@Override
	public String toString() {
		return "SysResourcesModel [sysResourcesId=" + sysResourcesId + ", sysResourcesName=" + sysResourcesName
				+ ", sysParentResourcesId=" + sysParentResourcesId + ", sysResourcesDescs=" + sysResourcesDescs
				+ ", url=" + url + ", index=" + index + ", type=" + type + ", iconCls=" + iconCls + ", children="
				+ children + "]";
	}

}
