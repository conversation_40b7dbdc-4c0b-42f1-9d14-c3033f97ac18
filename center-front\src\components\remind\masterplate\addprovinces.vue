<template>
    <div class="addprovinces">
        <div class="content">
            <el-form :model="addForm" ref="addForm" :rules="rules" class="demo-form-inline app-form-item" label-width="35%" size="small" style="width: 80%">
                <el-form-item label="模板名称：" prop="templateName">
                    <el-input v-model="addForm.templateName"></el-input>
                </el-form-item>
                <el-form-item label="分类：" prop="categoryId">
                    <el-select v-model="addForm.categoryId" placeholder="请选择" class="app-input">
                        <el-option
                                v-for="item in categoryList"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="来源：" prop="sourceId">
                    <el-select v-model="addForm.sourceId" placeholder="请选择" class="app-input">
                        <el-option
                                v-for="item in sourceList"
                                :key="item.sourceId"
                                :label="item.sourceName"
                                :value="item.sourceId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="省份：" prop="provinceId">
                    <el-select v-model="addForm.provinceId" @change="querySearchRegionList" placeholder="请选择" class="app-input">
                        <el-option
                                v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="地市：" prop="countryId">
                    <el-select v-model="addForm.countryId" placeholder="请选择" class="app-input">
                        <el-option
                                v-for="item in city"
                                :key="item.regionCode"
                                :label="item.regionName"
                                :value="item.regionCode">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="拨打号码提醒内容：" prop="callingRemindContent">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.callingRemindContent">
                    </el-input>
                </el-form-item>
                <el-form-item label="接听号码提醒内容：" prop="calledRemindContent">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.calledRemindContent">
                    </el-input>
                </el-form-item>
                <el-form-item label="描述：">
                    <el-input
                            type="textarea"
                            :rows="2"
                            placeholder=""
                            v-model="addForm.description">
                    </el-input>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="submit" size="small">取 消</el-button>
            <el-button type="primary" @click="addProvince" size="small">确 定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    export default {
        name: 'addprovinces',
        data(){
          return{
              name:'',
              addForm:{
                  templateName:'',//模版名称
                  categoryId:'',//分类
                  description:'',//描述
                  sourceId:'',//来源id
                  provinceId:'',//省份id
                  countryId:'',//地市id
                  callingRemindContent:'',//主叫提醒
                  calledRemindContent:'',//被叫提醒
                  provinceName:'',
                  countryName:'',
                  submitUserAccount:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//提交人
              },
              sourceList:[],//来源
              categoryList:[
                  {
                      name:'诈骗',
                      id:1
                  },
                  {
                      name:'黄页',
                      id:2
                  },
                  {
                      name:'标记',
                      id:3
                  }
              ],//分类
              city:[],
              rules: {
                  templateName: [
                      { required: true, message: '请输入模版名称', trigger: 'blur' },
                  ],
                  categoryId: [
                      { required: true, message: '请选择分类', trigger: 'change' }
                  ],
                  sourceId: [
                      { required: true, message: '请选择来源', trigger: 'change' }
                  ],
                  provinceId: [
                      { required: true, message: '请选择省份', trigger: 'change' }
                  ],
//                  countryId: [
//                      { required: true, message: '请选择地市', trigger: 'change' }
//                  ],
                  callingRemindContent: [
                      { required: true, message: '请输入主叫提醒内容', trigger: 'blur' },
                  ],
                  calledRemindContent: [
                      { required: true, message: '请输入被叫提醒内容', trigger: 'blur' },
                  ],
              },
              provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
          }
        },
        components: {},
        props:['addVisible'],
        created(){
            this.provinceList.unshift({provinceCode: "10000",provinceName: "全国"})
            this.qusourceList();
        },
        watch:{
            addVisible(){
                if(!this.addVisible){
                    this.resetForm('addForm');
                    this.addForm.description = '';
                }
            }
        },
        methods:{
            submit(){
                this.$emit('addList');
            },
            //查看来源
            qusourceList(){
                let vm = this;
                postHeader('querySource',JSON.stringify({categoryId:0})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.sourceList = data.data.sourceList;
                    }
                })
            },
            //地市
            querySearchRegionList(){
                var queryRegion={
                    provinceCode: this.addForm.provinceId
                };
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.city=res.data;
                        this.addForm.countryId='';
                        this.addForm.countryName='';
                    })
            },
            addProvince(){
                let vm = this;
                this.$refs['addForm'].validate((valid) => {
                    if(valid){
                        this.provinceList.forEach(item=>{
                            if(this.addForm.provinceId==item.provinceCode){
                                this.addForm.provinceName = item.provinceName;
                            }
                        })
                        if(this.addForm.countryId){
                            this.city.forEach(item=>{
                                if(this.addForm.countryId==item.regionCode){
                                    this.addForm.countryName = item.regionName;
                                }
                            })
                        }
                        this.addForm.categoryId = this.addForm.categoryId+'';
                        this.addForm.sourceId = this.addForm.sourceId+'';
                        postHeader('addNumTemplateProvince',JSON.stringify(this.addForm)).then(res=>{
                            let data = res.data;
                            if(data.code==0){
                                vm.$message.success("新增成功");
                            }else{
                                vm.$message.error("新增失败");
                            }
                            this.submit();
                        })
                    }
                })
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style scoped>
    .addtitle{
        font-size: 18px;
        margin-left: 20px;
        margin-top: -20px;
    }
    .content{
        width: 640px;
        margin: 0px auto;
    }
    .content1{
        text-align: center;
    }
</style>
