<template scope="scope">
  <div>
    <div class="user-titler">新媒彩印</div>
    <!--企业彩印-->
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label="企业编号">
          <el-input v-model="searchReq.corpId" size="small"></el-input>
        </el-form-item>
        <el-form-item label="企业名称">
          <el-input v-model="searchReq.corpName" size="small"></el-input>
        </el-form-item>
        <el-form-item label="提交时间">
          <div class="block">
            <el-date-picker
                    v-model="searchReq.timearr"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    format="yyyy-MM-dd HH:mm:ss"
                    value-format="yyyy-MM-dd HH:mm:ss" size="small">
            </el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchReq.pageIndex = 1;search()" size="small">查询</el-button>
        </el-form-item>
      </el-form>
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button type="primary" :type="typeoff" :disabled="clickoff" size="small" @click="passVisible=true;passType=2;">批量通过</el-button>
          <el-button type="primary" :type="typeoff" :disabled="clickoff" size="small" @click="rejectVisible=true;rejectType=2;">批量驳回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
              v-loading="tableLoading"
              :data="tableData"
              border
              class="app-tab"
              @selection-change="handleSelectionChange"
              :header-cell-class-name="tableheaderClassName">
        <el-table-column
                type="selection"
                width="55">
        </el-table-column>
        <el-table-column
                prop="serviceId"
                label="子业务ID"
                width="200">
        </el-table-column>
        <el-table-column
                prop="corpId"
                label="企业编号"
                width="200">
        </el-table-column>
        <el-table-column
                prop="corpName"
                label="企业名称"
                width="200">
        </el-table-column>
        <el-table-column
                prop="content"
                label="新媒屏显"
                width="400"
                :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
                prop="submitDate"
                label="提交时间"
                width="200">
        </el-table-column>
        <el-table-column
                prop="province"
                label="省份"
                width="100">
        </el-table-column>
		<el-table-column
                prop="city"
                label="地市"
                width="100">
        </el-table-column>
        <el-table-column
                prop="submitNumber"
                label="提交次数"
                width="100">
        </el-table-column>
        <el-table-column
                fixed="right"
                label="操作"
                width="120">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="passVisible=true;rowData=scope.row;passType=1;">通过</el-button>
            <el-button @click="rejectVisible=true;rowData=scope.row;rejectType=1;" type="text" size="small">驳回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination v-show="pageTotal"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page="searchReq.pageIndex"
                       :page-sizes="[10, 20, 30, 50]"
                       :page-size="10"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="pageTotal"  style="text-align: right;">
        </el-pagination>
      </div>
    </div>
    <div>
      <el-dialog
              width="30%"
              title="通过"
              :visible.sync="passVisible"
              append-to-body
              :close-on-click-modal="false">
        <div>是否通过该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
              width="30%"
              title="驳回"
              :visible.sync="rejectVisible"
              append-to-body
              :close-on-click-modal="false">
        <el-form label-width="80px" justify="center">
          <el-form-item label="驳回原因">
            <el-radio v-model="radio" label="1">手动输入</el-radio>
            <el-radio v-model="radio" label="2">系统预设</el-radio>
          </el-form-item>
          <el-form-item label="" v-show="radio==1">
            <el-input v-model="rejectReq.rejectReason"
                      type="textarea"
                      :rows="2"
                      placeholder="" style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="" v-show="radio==2">
            <el-select v-model="rejectReq.rejectReason" clearable placeholder="请选择" size="small">
              <el-option
                      v-for="item in refuse"
                      :key="item.id"
                      :label="item.name"
                      :value="item.name">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false"size="small">取 消</el-button>
          <el-button type="primary" @click="rejectVisible = false;rejectCheck(rowData)"size="small">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
    export default {
        data() {
            return {
                tableLoading:false,
                clickoff:true,
                typeoff:'info',
                //驳回原因
                radio: '1',
                pageTotal: 0,//总条数
                //彩印类型
                caiyinType:[
                    {
                        id:0,
                        name:'主叫彩印',
                    },
                    {
                        id:1,
                        name:'被叫彩印',
                    },
                    {
                        id:2,
                        name:'热线彩印',
                    },
                    {
                        id:3,
                        name:'挂机彩印',
                    },
                    {
                        id:4,
                        name:'挂机彩漫',
                    }
                ],
                //系统驳回原因
                refuse: [],
                //查询条件
                searchReq: {
                    corpId: "",//企业编号
                    corpName: "",//企业名称
                    caiyinType: "",
                    content: "",//彩印内容
                    submitStartDate: "",
                    submitEndDate: "",
                    provinceId:'',//省份
                    cityId:'',//地市
                    type:'FEINNO',//企业彩印
                    timearr:[],
                    pageSize: 10,
                    pageIndex: 1
                },
                //数据表
                tableData: [],
                //操作列表
                rowData: "",
                checked: [],
                passVisible:false,
                rejectVisible: false,
                //通过单或批量
                passType:0,
                //驳回单或批量
                rejectType:0,
                multipleSelection: [],
                //通过请求参数
                passReq: {
                    corpInfoId:'',//id
                    corpInfoIds:[],//多id
                    reviewer:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName//操作者
                },
                //驳回请求参数
                rejectReq: {
                    corpInfoId:'',
                    corpInfoIds:[],
                    reviewer: JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//操作者
                    rejectReason:''//驳回原因
                },
				clickPassFlag: false,
      			clickRejectFlag: false                
            };
        },
        created(){
            this.search();
          this.refuse=JSON.parse(sessionStorage.getItem("refuseList"))
        },
        watch:{
            'rejectReq.corpInfoIds'(){
                if(this.rejectReq.corpInfoIds.length){
                    this.clickoff = false;
                    this.typeoff = 'primary';
                }else{
                    this.clickoff = true;
                    this.typeoff = 'info'
                }
            },
            radio(){
                if(this.radio==2||this.radio==1){
                    this.rejectReq.rejectReason = '';
                }
            },
            rejectVisible(){
                if(!this.rejectVisible){
                    this.rejectReq.rejectReason = '';
                }
            }
        },
        methods: {
            //多选框
            handleSelectionChange(val) {
                this.rejectReq.corpInfoIds=[];
                this.passReq.corpInfoIds=[];
                for (var i = 0; i < val.length; i++) {
                    this.rejectReq.corpInfoIds.push(val[i].id);
                    this.passReq.corpInfoIds.push(val[i].id);
                }
            },
            //查询请求
            search: function() {
                this.tableLoading = true;
                if(this.searchReq.timearr){
                    this.searchReq.submitStartDate = this.searchReq.timearr[0];
                    this.searchReq.submitEndDate = this.searchReq.timearr[1];
                }else{
                    this.searchReq.submitStartDate = '';
                    this.searchReq.submitEndDate = '';
                }
                this.$http.post(`${this.proxyUrl}/entContent/corp/content/query`, JSON.stringify(this.searchReq), {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "contentType": "application/json",
                        "charset":"utf-8"
                    },
                    emulateJSON: true,
                    timeout: 5000,
                }).then(res => {
                    this.tableLoading = false;
                    var res = res.data;
                if(res.code==0){
                    this.tableData = res.data;
                    this.tableData.forEach(function (val,index) {
                        switch(val.caiyinType) {
                            case 0:
                                val.caiyinType = '主叫彩印'
                                break;
                            case 1:
                                val.caiyinType = '被叫彩印'
                                break;
                            case 2:
                                val.caiyinType = '热线彩印'
                                break;
                            case 3:
                                val.caiyinType = '挂机彩印'
                                break;
                            case 4:
                                val.caiyinType = '挂机彩漫'
                                break;
                            default:
                                break;
                        }
                    })
                    this.pageTotal = res.totalCount;
                }
            });
            },
            //查询地市
            querySearchRegionList(){
                var queryRegion={
                    provinceCode: this.searchReq.provinceId
                };
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,queryRegion,{emulateJSON:true})
                    .then(function(res){
                        this.city=res.data;
                        this.searchReq.cityId='';
                    })
            },
            //通过单
            passOne(row){
                var parms = {
                    "corpInfoId":30,
                    "reviewer":"test"
                }
                this.$http.post(`${this.proxyUrl}/entContent/audit/approve`, JSON.stringify(parms), {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        "contentType": "application/json",
                        "charset":"utf-8"
                    },
                    emulateJSON: true,
//                    timeout: 5000,
                }).then(res => {
                    var res = res.data;
                if(res.code==0){

                }
            });
            },
            //判断是批量还是单操作，发相应请求
            passCheck(val){
            	if(this.clickPassFlag) {
		          return
		        }
		        this.clickPassFlag = true;
                //1为单，2为多
                if(this.passType==1){
                    this.pass(val);
                }
                else if(this.passType==2){
                    this.passlist();
                }
            },
            //判断是批量但是单操作，发相应请求
            rejectCheck(val){
            	if(this.clickRejectFlag) {
		          return
		        }
		        this.clickRejectFlag = true;
                //1为单，2为多
                if(this.rejectType==1){
                    this.reject(val);
                }
                else if(this.rejectType==2){
                    this.rejectlist();
                }
            },
            //通过请求---单
            pass: function(val) {
                this.passReq.corpInfoId = val.id;
                const loading = this.$loading({
		          lock: true,
		          text: 'Loading',
		          spinner: 'el-icon-loading',
		          background: 'rgba(0, 0, 0, 0.7)'
		        });
                this.$http
                    .post(`${this.proxyUrl}/entContent/audit/approve`, JSON.stringify(this.passReq))
                    .then(function(res) {
                    	loading.close();
          				this.clickPassFlag = false;
                        if (res.data.code == "0") {
                            this.$message.success("通过成功");
                            this.search(this.searchReq);
                            this.passReq.corpInfoId='';
                        } else{
                            this.$message("通过失败");
                            this.passReq.corpInfoId='';
                        }
                    });
            },
            //通过请求---多
            passlist: function() {
                if(!this.passReq.corpInfoIds.length>0){
                    this.$message.error('请选择批量通过的内容');
                    this.clickPassFlag = false;
                    return false;
                }
                const loading = this.$loading({
		          lock: true,
		          text: 'Loading',
		          spinner: 'el-icon-loading',
		          background: 'rgba(0, 0, 0, 0.7)'
		        });
                this.$http.post(`${this.proxyUrl}/entContent/audit/approve/batch`, JSON.stringify(this.passReq))
                    .then(function(res) {
                    	loading.close();
          				this.clickPassFlag = false;
                        if (res.data.code == "0") {
                            let reslist = res.data.data;
                            let count = 0;
                            reslist.forEach(list=>{
                                if(list.success){
                                    count++;
                                }
                            });
                            this.$message.success(`审核通过成功记录${count}条，失败记录${reslist.length-count}条，详细情况请查询企业明细`);
                            this.search(this.searchReq);
                            this.passReq.corpInfoIds.length=0;
                        } else{
                            this.$message("通过失败");
                            this.passReq.corpInfoIds.length=0;
                        }
                    });
            },
            //驳回请求---单
            reject: function(val) {
                if(!this.rejectReq.rejectReason){
                    this.$message.error("请填写驳回原因");
                    this.rejectVisible=true;
                    this.clickRejectFlag = false;
                    return false;
                }
                this.rejectReq.corpInfoId = val.id;
                const loading = this.$loading({
		          lock: true,
		          text: 'Loading',
		          spinner: 'el-icon-loading',
		          background: 'rgba(0, 0, 0, 0.7)'
		        });
                this.$http
                    .post(`${this.proxyUrl}/entContent/audit/reject`, JSON.stringify(this.rejectReq))
                    .then(function(res) {
                    	loading.close();
                    	this.clickRejectFlag = false;
                        if (res.data.code == "0") {
                            this.$message.success("驳回成功");
                            this.search(this.searchReq);
                            this.rejectReq.corpInfoId = '';
                        } else{
                            this.$message("驳回失败");
                            this.rejectReq.corpInfoId = '';
                        }
                    });
            },
            //驳回请求---多
            rejectlist: function() {
                if(!this.rejectReq.rejectReason){
                    this.$message.error("请填写驳回原因");
                    this.rejectVisible=true;
                    this.clickRejectFlag = false;
                    return false;
                }
                if(!this.rejectReq.corpInfoIds.length>0){
                    this.$message.error("请选择批量驳回的内容");
                    this.clickRejectFlag = false;
                    return false;
                }
                const loading = this.$loading({
		          lock: true,
		          text: 'Loading',
		          spinner: 'el-icon-loading',
		          background: 'rgba(0, 0, 0, 0.7)'
		        });
                this.$http
                    .post(`${this.proxyUrl}/entContent/audit/reject/batch`, JSON.stringify(this.rejectReq))
                    .then(function(res) {
                    	loading.close();
                    	this.clickRejectFlag = false;
                        if (res.data.code == "0") {
                            let reslist = res.data.data;
                            let count = 0;
                            reslist.forEach(list=>{
                                if(list.success){
                                    count++;
                                }
                            });
                            this.$message.success(`驳回成功记录${count}条，失败记录${reslist.length-count}条，详细情况请查询企业明细`);
                            this.search(this.searchReq);
                            this.rejectReq.corpInfoIds.length=0;
                        } else{
                            this.$message("驳回失败");
                            this.rejectReq.corpInfoIds.length=0;
                        }
                    });
            },
            handleSizeChange(val) {
                this.searchReq.pageIndex = 1;
                //每页条数
                this.searchReq.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                //当前页
                this.searchReq.pageIndex = val;
                this.search();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
        }
    };
</script>
<style scoped>
  .user-titler{
    font-size: 20px;
    padding-left: 24px;
    height: 56px;
    line-height: 56px;
    font-family: PingFangSC-Medium;
    color: #333333;
    letter-spacing: -0.57px;
    border-bottom: 1px solid #D9D9D9;
  }
  .content-title{
    margin-top: 20px;
    margin-left: 20px;
    background-color: white;
  }
  .content-line{
    margin-top: 20px;
  }
  .user-search {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    margin-left: 20px;
  }
  .el-table {
    margin-left: 3%;
    margin-top: 3%;
    border: 1px solid #ecebe9;
  }
</style>
<style>
  .el-table .table-head-th{
    background-color: #F5F5F5;
  }
</style>
