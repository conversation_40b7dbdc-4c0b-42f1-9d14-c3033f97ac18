package com.cy.user.contsnt;

import java.util.HashMap;
import java.util.Map;

public class Constant {

	// 0未开通，1开通
	public static final int REMIND_SUB = 1, REMIND_UNSUB = 0;

	public static final int SUCESS = 0, FAIL = 1;
	// 0用户DIY,1个人彩印，2彩印盒
	public static final int CS_MOLD_DIY = 0, CS_MOLD_SIGN = 1, CS_MOLD_PKG = 2;
	// 提交审核状态：0新增，1删除，2修改，3下架
	public static final int AUDIT_TYPE_NEW = 0, AUDIT_TYPE_DEL = 1, AUDIT_TYPE_MOD = 2, AUDIT_TYPE_OFF = 3;


	public static final int AUDIT_STATUS_WAIT = 1,AUDIT_STATUS_REJECT = 2,AUDIT_STATUS_PASS = 3,AUDIT_STATUS_INVALID = 4;

	// 彩印状态 1：上架 2：待上架 3：删除 4：下架
	public static final int CS_TYPE_UP = 1, CS_TYPE_WAIT = 2, CS_TYPE_DEL = 3, CS_TYPE_DOWN = 4;
	// 彩印来源：0：短信 1：WEB 2：SMC 3：WAP 4：手机客户端 5：第三方6其他
	public static final int CS_SOURCE_0 = 0, CS_SOURCE_1 = 1, CS_SOURCE_2 = 2, CS_SOURCE_3 = 3, CS_SOURCE_4 = 4,
			CS_SOURCE_5 = 5, CS_SOURCE_6 = 6;

	// 彩印类型开关
	public static final int CS_TYPE_ON = 0, CS_TYPE_OFF = 1;

	// 0：所有1：号码分组2：指定号码当彩印显示类型为他显时生效优先级从上往下递增
	public static final String REC_TYPE_PHONE_ALL = "0", REC_TYPE_PHONE_LIST = "1", REC_TYPE_PHONE = "2";

	public static final String REC_PHONE_NAME = "指定号码", REC_LIST_NAME = "号码分组", REC_ALL_NAME = "所有人";

	// 彩印内容类型，0：用户DIY1：文本彩印2：彩印盒3：模板
	public static final String CS_DIY_TYPE = "0", CS_TEXT_TYPE = "1", CS_BOX_TYPE = "2", CS_TEMPLATE_TYPE = "3" , CS_VIDEO_HANGUP_TYPE = "5"
			, CS_VIDEO_CALLING_TYPE = "6", CS_VIDEO_CALLED_TYPE = "7", CS_TEXT_CALLING_TYPE = "8", CS_TEXT_CALLED_TYPE = "9",CS_DIY_CALLING_TYPE = "10", CS_DIY_CALLED_TYPE = "11"
			, CS_BOX_NESTING = "12"

			;

	public static final String RULE_RULESTATUS_0 = "使用中", RULE_RULESTATUS_1 = "未使用", RULE_RULESTATUS_2 = "过期";

	public static final String RULE_SENDTYPE_0 = "主叫", RULE_SENDTYPE_1 = "被叫", RULE_SENDTYPE_2 = "主被叫";

	public static final String RULE_SENDMODE_0 = "USSD", RULE_SENDMODE_1 = "闪信", RULE_SENDMODE_2 = "短信",
			RULE_SENDMODE_3 = "彩漫", RULE_SENDMODE_4 = "彩漫";

	public static final String RULE_CSTYPE_0 = "用户DIY", RULE_CSTYPE_1 = "文本彩印", RULE_CSTYPE_2 = "彩印盒",
			RULE_CSTYPE_3 = "模板", RULE_CSTYPE_4 = "彩信";
	public static final String HIS_CHANNEL_0 = "短信", HIS_CHANNEL_1 = "门户", HIS_CHANNEL_16 = "客户端",
			HIS_CHANNEL_22 = "中央平台";

	public static final String HIS_STATUS_0 = "正常", HIS_STATUS_1 = "暂停";

	public static final int PKG_PAYTYPE_0 = 0, PKG_PAYTYPE_1 = 1, PKG_PAYTYPE_2 = 2, PKG_PAYTYPE_3 = 3,
			PKG_PAYTYPE_4 = 4;

	public static final String PKG_PAYTYPE_NAME_0 = "话费", PKG_PAYTYPE_NAME_1 = "第三方支付", PKG_PAYTYPE_NAME_2 = "配额类",
			PKG_PAYTYPE_NAME_3 = "苹果支付", PKG_PAYTYPE_NAME_4 = "赠送";

	public static final String PKG_CHANNEL_01 = "01", PKG_CHANNEL_02 = "02", PKG_CHANNEL_03 = "03",
			PKG_CHANNEL_06 = "06", PKG_CHANNEL_07 = "07", PKG_CHANNEL_08 = "08", PKG_CHANNEL_09 = "09",
			PKG_CHANNEL_10 = "10", PKG_CHANNEL_11 = "11", PKG_CHANNEL_12 = "12", PKG_CHANNEL_15 = "15",
			PKG_CHANNEL_16 = "16", PKG_CHANNEL_22 = "22", PKG_CHANNEL_99 = "99", PKG_CHANNEL_20 = "20",
			PKG_CHANNEL_23 = "23", PKG_CHANNEL_24 = "24", PKG_CHANNEL_25 = "25";

	public static final String PKG_CHANNELNANE_01 = "01-CRM/BOSS(由系统发起)", PKG_CHANNELNANE_02 = "WAP",
			CHANNELNANE_03 = "SMS", PKG_CHANNELNANE_06 = "网上营业厅", PKG_CHANNELNANE_07 = "掌上营业厅",
			PKG_CHANNELNANE_08 = "短信营业厅", PKG_CHANNELNANE_09 = "10086人工", PKG_CHANNELNANE_10 = "10086IVR",
			PKG_CHANNELNANE_11 = "营业前台", PKG_CHANNELNANE_12 = "WWW网站", PKG_CHANNELNANE_15 = "MobileMarket",
			PKG_CHANNELNANE_16 = "手持终端", PKG_CHANNELNANE_22 = "平台系统", PKG_CHANNELNANE_99 = "SMC", PKG_CHANNELNANE_20 = "企管平台",
			PKG_CHANNELNANE_23 = "中央平台", PKG_CHANNELNANE_24 = "视宣号/名片号", PKG_CHANNELNANE_25 = "H5平台";

	public static Map<String, String> PKG_CHANNEL_MAP = new HashMap<String, String>();

	static {
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_01, PKG_CHANNELNANE_01);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_02, PKG_CHANNELNANE_02);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_03, CHANNELNANE_03);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_06, PKG_CHANNELNANE_06);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_07, PKG_CHANNELNANE_07);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_08, PKG_CHANNELNANE_08);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_09, PKG_CHANNELNANE_09);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_10, PKG_CHANNELNANE_10);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_11, PKG_CHANNELNANE_11);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_12, PKG_CHANNELNANE_12);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_15, PKG_CHANNELNANE_15);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_16, PKG_CHANNELNANE_16);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_22, PKG_CHANNELNANE_22);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_99, PKG_CHANNELNANE_99);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_20, PKG_CHANNELNANE_20);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_23, PKG_CHANNELNANE_23);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_24, PKG_CHANNELNANE_24);
		PKG_CHANNEL_MAP.put(PKG_CHANNEL_25, PKG_CHANNELNANE_25);

	}
}
