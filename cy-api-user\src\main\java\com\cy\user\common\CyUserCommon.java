package com.cy.user.common;

public class CyUserCommon {

	private int pageNum = 1;// 查询的页码

	private int pageSize = 20;// 每页显示条数

	private int startNum;// 起始查询行
	/**
	 * 用户id，手机号码
	 */
	private String pkCurUserid;

	private String phone;

	/*
	 * 用户昵称
	 */
	private String curNickName;

	/**
	 * 个人彩印状态
	 */
	private String personStatus;
	/**
	 * 新媒彩印状态
	 */
	private String mediaStatus;
	/**
	 * 提醒彩印状态
	 */
	private String remindStatus;
	/**
	 * 企业彩印状态
	 */
	private String cdpStatus;

	private int recvMode;

	private Integer blackWhite;

	/**
	 * 描述
	 */
	private String remark;

	private String setCsType;

	private String ruleId;

	private String recPhone;

	private String oldPkCgmMsisdn;

	private String groupId;

	private String proId;

	private String pkgId;

	// 彩印类型 1.企业 2.新煤 3.提醒 4.个人
	private String cyType;

	private String csContent;

	private String cbwId;

	public String getOperatorNumber() {
		return operatorNumber;
	}

	public void setOperatorNumber(String operatorNumber) {
		this.operatorNumber = operatorNumber;
	}

	public String getOperatorName() {
		return operatorName;
	}

	public void setOperatorName(String operatorName) {
		this.operatorName = operatorName;
	}

	private String operatorNumber;

	private String  operatorName;

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public void setStartPage() {
		this.startNum = (pageNum - 1) * pageSize;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public int getStartNum() {
		return startNum;
	}

	public void setStartNum(int startNum) {
		this.startNum = startNum;
	}

	public String getPkCurUserid() {
		return pkCurUserid;
	}

	public void setPkCurUserid(String pkCurUserid) {
		this.pkCurUserid = pkCurUserid;
	}

	public String getCurNickName() {
		return curNickName;
	}

	public void setCurNickName(String curNickName) {
		this.curNickName = curNickName;
	}

	public String getPersonStatus() {
		return personStatus;
	}

	public void setPersonStatus(String personStatus) {
		this.personStatus = personStatus;
	}

	public String getMediaStatus() {
		return mediaStatus;
	}

	public void setMediaStatus(String mediaStatus) {
		this.mediaStatus = mediaStatus;
	}

	public String getRemindStatus() {
		return remindStatus;
	}

	public void setRemindStatus(String remindStatus) {
		this.remindStatus = remindStatus;
	}

	public String getCdpStatus() {
		return cdpStatus;
	}

	public void setCdpStatus(String cdpStatus) {
		this.cdpStatus = cdpStatus;
	}

	public Integer getBlackWhite() {
		return blackWhite;
	}

	public void setBlackWhite(Integer blackWhite) {
		this.blackWhite = blackWhite;
	}

	public String getSetCsType() {
		return setCsType;
	}

	public void setSetCsType(String setCsType) {
		this.setCsType = setCsType;
	}

	public String getRuleId() {
		return ruleId;
	}

	public void setRuleId(String ruleId) {
		this.ruleId = ruleId;
	}

	public String getCsUserParam() {
		return "pkCurUserid:" + this.pkCurUserid + "," + "pageSize:" + this.pageSize + "," + "pageNum:" + this.pageNum
				+ "," + "startNum:" + this.startNum;
	}

	public String getPushParam() {
		return "pkCurUserid:" + this.pkCurUserid + "," + "personStatus:" + this.personStatus + "," + "mediaStatus:"
				+ this.mediaStatus + "," + "remindStatus:" + this.remindStatus + "," + "cdpStatus:" + this.cdpStatus
				+ ",";
	}

	public String getBlackWriteParam() {
		return "pkCurUserid:" + this.pkCurUserid + "," + "blackWhite:" + this.blackWhite;
	}

	public int getRecvMode() {
		return recvMode;
	}

	public void setRecvMode(int recvMode) {
		this.recvMode = recvMode;
	}

	public String getRecPhone() {
		return recPhone;
	}

	public void setRecPhone(String recPhone) {
		this.recPhone = recPhone;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getProId() {
		return proId;
	}

	public void setProId(String proId) {
		this.proId = proId;
	}

	public String getPkgId() {
		return pkgId;
	}

	public void setPkgId(String pkgId) {
		this.pkgId = pkgId;
	}

	public String getCyType() {
		return cyType;
	}

	public void setCyType(String cyType) {
		this.cyType = cyType;
	}

	public String getCsContent() {
		return csContent;
	}

	public void setCsContent(String csContent) {
		this.csContent = csContent;
	}

	public String getCbwId() {
		return cbwId;
	}

	public void setCbwId(String cbwId) {
		this.cbwId = cbwId;
	}

	public String getOldPkCgmMsisdn() {
		return oldPkCgmMsisdn;
	}

	public void setOldPkCgmMsisdn(String oldPkCgmMsisdn) {
		this.oldPkCgmMsisdn = oldPkCgmMsisdn;
	}

}
