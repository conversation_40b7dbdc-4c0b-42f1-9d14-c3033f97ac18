<template>
  <div>
    <h1 class="user-title">用户DIY内容</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :inline="true" class="demo-form-inline">
        <!--<el-form-item label="用户号码">-->
          <!--<el-input v-model="request.phone"  placeholder="用户号码" size="small" clearable></el-input>-->
        <!--</el-form-item>-->

        <el-form-item label="彩印内容">
          <el-input v-model="request.csContent" placeholder="彩印内容" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-select v-model="request.contentType" placeholder="请选择" size="small" style="width: 150px" clearable>
            <el-option
                v-for="item in contentTypeData"
                :key="item.value"
                :label="item.name"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="request.status" placeholder="请选择" size="small" style="width: 150px" clearable>
            <el-option
                v-for="item in sildeData"
                :key="item.csStatusNo"
                :label="item.csStatusName"
                :value="item.csStatusNo">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="提交时间">
          <el-date-picker v-model="dateTime"
            type="datetimerange"
            range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
          style="width:355px" size="small"/>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchrc(request)" size="small" >查询</el-button>
          </el-form-item>
      </el-form>
    </div>
    <el-table
            v-loading="tableLoading"
            :data="tableData"
            border
            :header-cell-class-name="tableheaderClassName"
            class="app-tab">
      <el-table-column
          prop="contentType"
          label="内容类型"
          width="240">
        <template slot-scope="scope">
          {{ getContentName(scope.row.contentType) }}
        </template>
      </el-table-column>
      <el-table-column
              prop="dsSerialNumber"
              label="彩印ID"
              width="240">
      </el-table-column>
      <el-table-column
              prop="dsSubmitUser"
              label="用户号码"
               width="150" :formatter="formmatPhone">
      </el-table-column>
      <el-table-column
              prop="dsCard"
              label="彩印内容"
              :show-overflow-tooltip="true"
               width="400"
              >
      </el-table-column>
      <el-table-column
              prop="dsStatusName"
              label="状态"
              width="100">
      </el-table-column>
      <el-table-column
              prop="dsSubmitTime"
              label="提交时间"
              width="200">
      </el-table-column>
      <el-table-column
              prop="svVerifyTime"
              label="通过时间"
              width="200">
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.pageNum"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageTotal"  style="text-align: right;">
            </el-pagination>
        </div>
  </div>
</template>
<script>
import {replacePhone} from './../../util/core.js';
import {formDate} from './../../util/core.js';

  export default {
    name: 'userContentDIY',
    data() {
      return {
          tableLoading:false,
          pageTotal:0,
          sildeData:[],
          contentTypeData:[
              {value:"1",name:"用户DIY"},{value:"2",name:"用户主叫DIY"},{value:"3",name:"用户被叫DIY"}],
          tableData:[],
          dateTime:[],
          request:{
            phone:'',
            status:'',
            csContent:'',
            startTime:'',
            endTime:'',
            pageNum:1,
            pageSize:10
          }
      }
    },
    mounted(){
      this.slideData();
    },
    methods: {
      getContentName: function (contentType) {
        if(contentType == null || contentType == 1){
          return "用户DIY";
        }else if(contentType == 2){
          return "用户主叫DIY";
        }else if(contentType == 3){
          return "用户被叫DIY";
        }
      },
        formmatPhone(row, column){
            return replacePhone(row.dsSubmitUser);
        },
        check(vm){
          if (!(vm.request.startTime)) {
            return true;
          }
          if(!(vm.request.endTime)){
            return true;
          }
          if(vm.request.startTime>vm.request.endTime){
            vm.$message.error("开始时间不能晚于结束时间");
            return false;
          }
          return true;
        },
        //下拉栏请求
        slideData:function(){
          this.$http
              .get(`${this.proxyUrl}/content/csText/getCsStatus`,{emulateJSON:true})
              .then(function(res){
                if(res.data.resStatus=='0'){
                  for(let i=0;i<res.data.datas.length;i++){
                    if(res.data.datas[i].csStatusName=="待上架"){
                      res.data.datas.splice(i,1);
                    }
                  }
                  this.sildeData = res.data.datas;
                 
                }
                else if(res.data.resStatus=='1') {
                  console.log('下拉栏请求失败')
                }
              })
        },
      //查询请求
        searchrc:function(request){
          if(this.dateTime && this.dateTime.length>0){
            request.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
            request.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
          }else{
            request.startTime='';
            request.endTime='';
          }
          this.tableLoading=true;
          this.$http
              .post(`${this.proxyUrl}/content/content/getDiy`,request,{emulateJSON:true})
              .then(function(res){
                // if(res.data.datas.length==0){
                //   this.$message('查无数据');
                //   this.tableLoading=false;
                //   return false;
                // }
                this.tableData=res.data.datas;
                this.pageTotal=res.data.pageTotal;
                this.tableLoading=false;
              })
        },
        //分页
            handleSizeChange(val) {
                // console.log(`每页 ${val} 条`);
                if(this.dateTime && this.dateTime.length>0){
                  this.request.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
                  this.request.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss')
                }else{
                  this.request.startTime='';
                  this.request.endTime='';
                }
                this.request.pageSize=val;
                 this.tableLoading=true;
                this.$http
                    .post(`${this.proxyUrl}/content/content/getDiy`,this.request,{emulateJSON:true})
                    .then(function(res){
                    this.tableData=res.data.datas;
                     this.tableLoading=false;
                })
            },
            handleCurrentChange(val) {
                // console.log(`当前页: ${val}`);
                this.request.pageNum=val;
                if(this.dateTime && this.dateTime.length>0){
                  this.request.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
                  this.request.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss')
                }else{
                  this.request.startTime='';
                  this.request.endTime='';
                }
                 this.tableLoading=true;
                this.$http
                  .post(`${this.proxyUrl}/content/content/getDiy`,this.request,{emulateJSON:true})
                  .then(function(res){
                  this.tableData=res.data.datas;
                   this.tableLoading=false;
                })
            },
        tableheaderClassName({ row, rowIndex }) {
            return "table-head-th";
        },
    }
  }


</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
  background-color: #F5F5F5;
}
</style>
