<template scope="scope">
    <div>
        <h1 class="user-title">彩印盒</h1>
       <div class="user-line"></div>
        <!--彩印盒-->
          <div class="app-search">
            <el-form :inline="true" class="demo-form-inline">
              <el-form-item label="彩印盒ID">
                <el-input v-model="searchReq.svNumber"size="small" clearable></el-input>
              </el-form-item>
              <el-form-item label="彩印盒名称">
                <el-input v-model="searchReq.svName"size="small" clearable></el-input>
              </el-form-item>
              <el-form-item label="提交人">
                <el-input v-model="searchReq.svSubmitUser"size="small" clearable></el-input>
              </el-form-item>
              <el-form-item label="提交时间">
                <el-date-picker v-model="dateTime"
                      type="datetimerange"
                      range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :default-time="['00:00:00', '23:59:59']"
                    style="width:355px"
                    size="small"
                    />
              </el-form-item>
               <el-form-item>
                    <el-button type="primary" @click="searchReq.pageNum = 1;search(searchReq)"size="small">查询</el-button>
               </el-form-item>
            </el-form>
             <el-form :inline="true" class="demo-form-inline">
                <el-form-item>
<!--                   <el-button v-bind:disabled="btnhide" v-bind:type="btype" size="small"@click="passVisible=true;passType=2;">批量通过</el-button>-->
<!--                    <el-button v-bind:disabled="btnhide" v-bind:type="btype" size="small"@click="rejectVisible=true;rejectReq.svCause='';rejectType=2;">批量驳回</el-button>-->
                </el-form-item>
             </el-form>
          </div>
          <div class="user-table">
            <el-table
                v-loading="tableLoading"
                :data="tableData"
                border
                class="app-tab"
                @selection-change="handleSelectionChange"
                :header-cell-class-name="tableheaderClassName">
              <el-table-column
                  type="selection"
                  width="55">
              </el-table-column>
              <el-table-column
                  prop="svNumber"
                  label="彩印盒ID"
                  width="273">
              </el-table-column>
               <el-table-column
                  prop="svName" 
                  label="彩印盒名称"
                  width="183">
              </el-table-column>
              <el-table-column
                label="彩印盒内容"
                width="233">
                <template slot-scope="scope">
                  <el-button type="text" @click="detailVisible=true;rowData=scope.row;">内容详情</el-button>
                </template>
              </el-table-column>
              <el-table-column
                  prop="submitUser"
                  label="提交人"
                  width="233">
              </el-table-column>
              <el-table-column
                  prop="submitCount"
                  label="提交次数"
                  width="133">
              </el-table-column>
              <el-table-column
                  prop="submitTime"
                  label="提交时间"
                  width="236">
              </el-table-column>
              <el-table-column
                  fixed="right"
                  label="操作"
                  width="120">
                <template slot-scope="scope">
<!--                  <el-button type="text" size="small" @click="passVisible=true;rowData=scope.row;passType=1;">通过</el-button>-->
<!--                  <el-button type="text" size="small" @click="rejectVisible=true;rowData=scope.row;rejectReq.svCause='';rejectType=1;">驳回</el-button>-->
                </template>
              </el-table-column>
            </el-table>
            <el-dialog
                width="30%"
                title="通过"
                :visible.sync="passVisible"
                :close-on-click-modal="false"
                append-to-body>
                <div>是否通过该内容？</div>
              <div slot="footer" style="text-align: right;">
                <el-button @click="passVisible = false" size="small">取 消</el-button>
                <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
              </div>
            </el-dialog>
            <el-dialog
                width="30%"
                title="驳回"
                :visible.sync="rejectVisible"
                :close-on-click-modal="false"
                append-to-body>
              <el-form>
                 <el-form-item>
                  <el-radio v-model="radio" label="1">手动输入</el-radio>
                  <el-radio v-model="radio" label="2">系统预设</el-radio>
                </el-form-item>

                <el-form-item label="驳回原因">
                  <el-input type="textarea" v-if="radio=='1'" v-model="rejectReq.svCause" size="small" style="width:250px;"></el-input>
                  <p v-if="rejectReq.svCause.length > 1024" style="color: red">不能超过1024个字</p>
                  <el-select v-if="radio=='2'" v-model="rejectReq.svCause" placeholder="请选择">
                    <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.value"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>


              </el-form>
              <div slot="footer" style="text-align: right;">
                <el-button @click="rejectVisible = false" size="small">取 消</el-button>
                <el-button type="primary"  size="small" @click="rejectVisible = false;rejectCheck(rowData)" :disabled="rejectReq.svCause.length > 1024">确 定</el-button>
              </div>
            </el-dialog>
            <!-- 分页 -->
            <div class="block app-pageganit">
              <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="searchReq.pageNum"
                  :page-sizes="[10, 20, 30, 50]"
                  :page-size="10"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="pageTotal"  style="text-align: right;">
              </el-pagination>
            </div>
          </div>

          <div>
      
    <el-dialog
        width="40%"
        title="内容详情"
        :visible.sync="detailVisible"
        :close-on-click-modal="false"
        append-to-body>
        <el-row>
          <el-col :span="12">彩印ID</el-col>
          <el-col :span="12">内容</el-col>
        </el-row>
         <div style="height:300px;overflow:auto;">

          <el-row v-if="rowData.content1">
            <el-col :span="12"><div v-html="rowData.svNumber+'1'"></div></el-col>
            <el-col :span="12"><div v-html="highLight(rowData.sensitiveWords,rowData.content1)"></div></el-col>
          </el-row>

         <el-row v-if="rowData.content2">
            <el-col :span="12"><div v-html="rowData.svNumber+'2'"></div></el-col>
            <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content2)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content3">
            <el-col :span="12"><div v-html="rowData.svNumber+'3'"></div></el-col>
            <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content3)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content4">
            <el-col :span="12"><div v-html="rowData.svNumber+'4'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content4)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content5">
            <el-col :span="12"><div v-html="rowData.svNumber+'5'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content5)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content6">
            <el-col :span="12"><div v-html="rowData.svNumber+'6'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content6)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content7">
            <el-col :span="12"><div v-html="rowData.svNumber+'7'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content7)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content8">
            <el-col :span="12"><div v-html="rowData.svNumber+'8'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content8)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content9">
            <el-col :span="12"><div v-html="rowData.svNumber+'9'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content9)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content10">
            <el-col :span="12"><div v-html="rowData.svNumber+'10'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content10)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content11">
            <el-col :span="12"><div v-html="rowData.svNumber+'11'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content11)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content12">
            <el-col :span="12"><div v-html="rowData.svNumber+'12'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content12)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content13">
            <el-col :span="12"><div v-html="rowData.svNumber+'13'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content13)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content14">
            <el-col :span="12"><div v-html="rowData.svNumber+'14'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content14)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content15">
            <el-col :span="12"><div v-html="rowData.svNumber+'15'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content15)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content16">
            <el-col :span="12"><div v-html="rowData.svNumber+'16'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content16)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content17">
            <el-col :span="12"><div v-html="rowData.svNumber+'17'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content17)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content18">
            <el-col :span="12"><div v-html="rowData.svNumber+'18'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content18)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content19">
            <el-col :span="12"><div v-html="rowData.svNumber+'19'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content19)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content20">
            <el-col :span="12"><div v-html="rowData.svNumber+'20'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content20)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content21">
            <el-col :span="12"><div v-html="rowData.svNumber+'21'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content21)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content22">
            <el-col :span="12"><div v-html="rowData.svNumber+'22'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content22)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content23">
            <el-col :span="12"><div v-html="rowData.svNumber+'23'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content23)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content24">
            <el-col :span="12"><div v-html="rowData.svNumber+'24'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content24)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content25">
            <el-col :span="12"><div v-html="rowData.svNumber+'25'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content25)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content26">
            <el-col :span="12"><div v-html="rowData.svNumber+'26'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content26)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content27">
            <el-col :span="12"><div v-html="rowData.svNumber+'27'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content27)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content28">
            <el-col :span="12"><div v-html="rowData.svNumber+'28'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content28)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content29">
            <el-col :span="12"><div v-html="rowData.svNumber+'29'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content29)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content30">
             <el-col :span="12"><div v-html="rowData.svNumber+'30'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content30)"></div></el-col>
          </el-row>
         </div>
      <div slot="footer" style="text-align: right;">
        <el-button @click="detailVisible = false">确 定</el-button>
      </div>
    </el-dialog>
    </div>
    </div>


</template>
<script>
import {formDate} from './../../../util/core.js';
export default {
  data() {
    return {
      tableLoading: false,
      radio:'1',
      btype:'info',
      btnhide:true,
      pageTotal: 0,
      detailVisible:false,
      rowData: "",
      sildeData: [],
      checked: [],
      passVisible: false,
      rejectVisible: false,
      passType:0,
      rejectType:0,
      options: [{
          value: '出现敏感词',
        }, {
          value: '缺少必填项',
        }, {
          value: '已存在相同彩印',
        }, {
          value: '内容不友好',
        }, {
          value: '其他问题',
        }],
      // tableData: [
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "1",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "2",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "2",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "3",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "4",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    },
      //    {
      //      csTextInfoNo: "1231",
      //      csGroupName: "asd",
      //      svId: "4",
      //      csTextContent: "sdfsd",
      //      csTextStatus: "1",
      //      csSubmitTime: "2323",
      //      auditor: "sdfsdf",
      //      passTime: "asdas",
      //      useNumber: "asdasd"
      //    }
      //  ],
      tableData: [],
      multipleSelection: [],
      dateTime:[],
      searchReq: {
        svNumber: "",
        svName: "",
        svSubmitUser: "",
        startTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1
      },
      //通过请求参数
      passReq: {
        ids: []
      },
      //驳回请求参数
      rejectReq: {
        svCause:'',
        ids: []
      }
    }
  },
  methods: {
    //多选框
    handleSelectionChange(val) {
      this.rejectReq.ids.length=0;
      this.passReq.ids.length=0;
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.ids.push(val[i].svId);
        this.passReq.ids.push(val[i].svId);
      }
      if(this.rejectReq.ids.length>0){
        this.btnhide=false;
        this.btype='primary'
      }else{
        this.btnhide=true;
         this.btype='info';
      }
    },
    verifyId(csId){
      if(!csId){
        return '&nbsp;';
      }else{
        return csId;
      }
    },
    highLight(sensitiveWords,svCard){
      if(!sensitiveWords || !svCard){
        return svCard;
      }else{
        var specalKey=sensitiveWords.replace(new RegExp(',','g'),'|');
        var scCard=svCard.replace(new RegExp(specalKey,'g'),`<span style="color:red">$&</span>`);
        return scCard;
      }
    },
    //查询请求
    search: function(searchReq) {
      if(this.dateTime && this.dateTime.length>0){
        searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        searchReq.startTime='';
        searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/getAuditPackage`, searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
          this.tableLoading = false;
        });
    },
    //判断是批量但是单操作，发相应请求
    passCheck(val){
      //1为单，2为多
      if(this.passType==1){
        this.pass(val);
      }
      else if(this.passType==2){
        this.passlist();
      }
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val){
      //1为单，2为多
      if(this.rejectType==1){
        this.reject(val);
      }
      else if(this.rejectType==2){
        this.rejectlist();
      }
    },
    //通过请求---单
    pass: function(val) {
      this.passReq.ids = [];
      this.passReq.ids[0]=val.svId;
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/passAuditPackage`, JSON.stringify(this.passReq))
        .then(function(res) {
          if (res.data.resStatus == "0") {
              this.$message.success("通过成功");
              this.search(this.searchReq);
              this.passReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("通过失败 "+res.data.resText);
              this.passReq.ids.length=0;
            }
        });
    },
    //通过请求---多
    passlist: function() {
      if(!this.passReq.ids.length>0){
        this.$message.error('请选择批量通过的内容');
        return false;
      }
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/passAuditPackage`, JSON.stringify(this.passReq))
        .then(function(res) {
          if (res.data.resStatus == "0") {
              this.$message.success("通过成功");
              this.search(this.searchReq);
              this.passReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("通过失败 "+res.data.resText);
              this.passReq.ids.length=0;
            }
        });
    },
    //驳回请求---单
    reject: function(val) {
      if(!this.rejectReq.svCause){
        this.$message.error("请填写驳回原因");
        this.rejectVisible=true;
        return false;
      }
      this.rejectReq.ids = [];
      this.rejectReq.ids[0] = val.svId;
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/rejectAuditPackage`, JSON.stringify(this.rejectReq))
        .then(function(res) {
          if (res.data.resStatus == "0") {
              this.$message.success("驳回成功");
              this.search(this.searchReq);
              this.rejectReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("驳回失败 "+res.data.resText);
              this.rejectReq.ids.length=0;
            }
        });
    },
    //驳回请求---多
    rejectlist: function() {
      if(!this.rejectReq.svCause){
        this.$message.error("请填写驳回原因");
        this.rejectVisible=true;
        return false;
      }
      if(!this.rejectReq.ids.length>0){
        this.$message.error("请选择批量驳回的内容");
        return false;
      }
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/rejectAuditPackage`, JSON.stringify(this.rejectReq))
        .then(function(res) {
          if (res.data.resStatus == "0") {
              this.$message.success("驳回成功");
              this.search(this.searchReq);
              this.rejectReq.ids.length=0;
            } else if (res.data.resStatus == "1") {
              this.$message.error("驳回失败 "+res.data.resText);
              this.rejectReq.ids.length=0;
            }
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageNum = 1;
      // console.log(`每页 ${val} 条`);
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/getAuditPackage`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading=true;
      this.$http
        .post(`${this.proxyUrl}/content/auditPkg/getAuditPackage`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading=false;
        });
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}

.el-table .table-head-th{
    background-color: #F5F5F5;
}
</style>
