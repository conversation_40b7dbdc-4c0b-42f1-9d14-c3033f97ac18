<template>
    <div>
        <h1 class="user-title">告警短信</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="模板ID">
                    <el-input  v-model="searchForm.id" placeholder="" size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" clearable placeholder="请选择" size="small" class="app-input">
                        <el-option
                                v-for="item in statusList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="发送内容">
                    <el-input   v-model="searchForm.content" placeholder="" size="small" :maxlength="50" class="app-input"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
            </el-form>
            <!--<el-form :model="searchForm" :inline="true" class="demo-form-inline">-->
                <!--<el-form-item>-->
                    <!--<el-button type="primary" @click="addVisible = true">新增模板</el-button>-->
                <!--</el-form-item>-->
            <!--</el-form>-->

        </div>

        <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                  :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="id" label="模板ID" width="100"/>
            <el-table-column prop="content" label="发送内容" width="420"/>
            <el-table-column prop="status" label="状态" :formatter="formatStatus" width="100"/>
            <el-table-column prop="sendNo" label="发送源号码" width="140"/>
            <el-table-column prop="exceptionType" label="异常类型" width="160"/>
            <el-table-column prop="updateTime" label="更新时间" width="180"/>
            <!--<el-table-column prop="oper" label="操作" width="200px">-->
                <!--<template slot-scope="scope">-->
                    <!--<el-button type="text" size="small" @click="switchStatus(scope.row.id)" v-show="scope.row.status==0">启用</el-button>-->
                    <!--<el-button type="text" size="small" @click="switchStatus(scope.row.id)" v-show="scope.row.status==1">禁用</el-button>-->
                    <!--<el-button type="text" size="small" @click="sendSms(scope.row)" >发送</el-button>-->
                <!--</template>-->
            <!--</el-table-column>-->
        </el-table>

        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

        <div>
            <el-dialog title="新增告警短信" :visible.sync="addVisible" :before-close="handleClose"  @open="resetForm('addForm')">
                <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                    <el-form-item label="接收号码 : " prop="acceptPhone">
                        <el-select v-model="addForm.acceptPhone" placeholder="请选择">
                            <el-option
                                    v-for="item in phoneList"
                                    :key="item.monUserMobile"
                                    :label="item.monUserName"
                                    :value="item.monUserMobile">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="短信模板内容 : " prop="content">
                        <el-input v-model="addForm.content" type="textarea"></el-input>
                    </el-form-item>
                    <el-form-item label="发送源号码 : " prop="sendNo">
                        <el-select v-model="addForm.sendNo" placeholder="请选择">
                            <el-option
                                    v-for="item in sendNoList"
                                    :key="item.key"
                                    :label="item.value"
                                    :value="item.key">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="异常类型 : " prop="exceptionType">
                        <el-select v-model="addForm.exceptionType" placeholder="请选择">
                            <el-option
                                    v-for="item in exceptionList"
                                    :key="item.exceptionType"
                                    :label="item.exceptionName"
                                    :value="item.exceptionType">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addVisible = false">取 消</el-button>
                    <el-button type="primary" @click="addSmsSubmit('addForm')">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="propVisible = false">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<script>
    export default {
        data() {
            return {
                activeName:'smsAlarm',
                propVisible:false,
                propMsg:'',
                addVisible: false,
                searchForm: {
                    id:'',
                    status:'',
                    content:'',
                    pageSize:10,// 每页显示条数
                    pageNum :1
                },
                // 新增form对象定义
                addForm: {
                    status:"0",//状态：0：关闭，1开启，新增时默认0，不可选
                    acceptNo:"2",//接收号码2：指定号码,固定
                    acceptPhone:'',//下拉框，选择号码
                    content:'',
                    sendNo:'',//发送源号码:1065,8086
                    exceptionType:''
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'',
                },

                exceptionList:[],
                phoneList:[],
                sendNoList:[
                    {
                        key:'1',
                        value:'发送源号码1'
                    },
                    {
                        key:'2',
                        value:'发送源号码2'
                    },{
                        key:'3',
                        value:'发送源号码3'
                    }
                ],
                statusList:[
                    {
                        key:'',
                        value:'全部'
                    },
                    {
                        key:'0',
                        value:'禁用'
                    },{
                        key:'1',
                        value:'启用'
                    }
                ],
                rules: {
                    acceptPhone: [
                        { required: true, message: '请选择接收号码', trigger: 'blur' },
                    ],
                    content: [
                        { required: true, message: '请输入短信模板内容', trigger: 'blur' },
                    ],
                    sendNo: [
                        { required: true, message: '请选择发送源号码', trigger: 'blur' },
                    ],
                    exceptionType: [
                        { required: true, message: '请选择异常类型', trigger: 'blur' },
                    ]


                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
//            this.slideData(this.proxyUrl);
//             this.getPhoneList();
//             this.getExceptionList();
            this.search();
        },
        methods: {
            //开关开启关闭
            switchStatus(id){
                this.queryOrDelForm.id = id;
                this.$http.post(`${this.proxyUrl}/param/smsMgt/openOrCloseAlarmSms`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    if(res.data.resStatus == 0){
                        this.propMsg = '修改成功！';
                        this.propVisible=true;
                        this.editVisible = false;
                        this.search(this.searchForm);
                    }else{
                        this.propMsg = '修改失败!'+ res.data.resText;;
                        this.propVisible=true;
                    }
                })
            },
            formatStatus: function (row, column, cellValue) {
                if (cellValue === "1"){
                    return '启用';
                }else if (cellValue === "0"){
                    return '禁用';
                }
            },
            sendSms(id){
                this.queryOrDelForm.id = id;
                return;
                this.$http.post(`${this.proxyUrl}/param/`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    if(res.data.resStatus == 0){
                        this.propMsg = '发送成功！';
                        this.propVisible=true;
                        this.editVisible = false;
                        this.search(this.searchForm);
                    }else{
                        this.propMsg = '发送失败!'+ res.data.resText;;
                        this.propVisible=true;
                    }
                })
            },

            //获取电话list
            // getPhoneList:function(){
            //     this.$http.get(`${this.proxyUrl}/param/smsMgt/getAllMonUser`).then(function(res){
            //         this.phoneList=res.data;
            //     })
            // },
            //获取异常list
            // getExceptionList:function(){
            //     this.$http.get(`${this.proxyUrl}/param/smsMgt/getAllParExcType`).then(function(res){
            //         this.exceptionList=res.data;
            //     })
            // },

            //查询列表请求
            search:function(searchForm){
                console.log(searchForm);
                this.$http.post(`${this.proxyUrl}/param/smsMgt/getAlarmSmsPage`,searchForm,{emulateJSON:true}).then(function(res){
                    this.currentPage=res.data.pageNum;
                    this.total=res.data.pageTotal;
                    this.tableData=res.data.datas;
                })
            },

            //新增请求
            addSmsSubmit(formName){
                console.log(this.addForm);
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/smsMgt/addParAlarmSms`,this.addForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '新增成功！';
                                this.propVisible=true;
                                this.addVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '新增失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
                    } else {
                        return false;
            }
            });
            },


            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            handleClick(activeName){
                this.$router.push(activeName);
            },

            //清空提示信息
            resetForm(formName){
                this.$nextTick(() => {
                    this.$refs[formName].resetFields();
            });
            },
            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .def-tab {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .el-tabs__item{
        font-size: 24px;
        -webkit-margin-before: 0.67em;
        -webkit-margin-after: 0.67em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        font-weight: bold;
    }
    .user-title{
        margin-top: 8px;
        margin-left: 16px;
        background-color: white;
    }
    .user-line{
        margin-top: 8px;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
