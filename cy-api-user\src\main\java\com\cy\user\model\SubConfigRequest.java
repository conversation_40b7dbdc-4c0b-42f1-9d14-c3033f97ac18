package com.cy.user.model;



/**
 * 体验周期订购配置bean
 */
public class SubConfigRequest {
    /**
     * 用户手机号
     */
    private String userAccount;

    /**
     * 订购体验的彩印业务包，以局数据（bossID）为准
     */
    private String experienceBoosId;

    /**
     * 到期续订的彩印业务包，以局数据（bossID）为准,如果不续订，则不填
     */
    private String officialBoosId;

    /**
     * 到期可选择：0）下发短信提示用户续订、1）直接为用户退订体验默认为下发提示用户续订
     */
    private String selectFlag;

    /**
     * 体验周期 单位为天
     */
    private Integer trialPeriod;


    public String getUserAccount() {
        return userAccount;
    }

    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    public String getExperienceBoosId() {
        return experienceBoosId;
    }

    public void setExperienceBoosId(String experienceBoosId) {
        this.experienceBoosId = experienceBoosId;
    }

    public String getOfficialBoosId() {
        return officialBoosId;
    }

    public void setOfficialBoosId(String officialBoosId) {
        this.officialBoosId = officialBoosId;
    }

    public String getSelectFlag() {
        return selectFlag;
    }

    public void setSelectFlag(String selectFlag) {
        this.selectFlag = selectFlag;
    }

    public Integer getTrialPeriod() {
        return trialPeriod;
    }

    public void setTrialPeriod(Integer trialPeriod) {
        this.trialPeriod = trialPeriod;
    }
}
