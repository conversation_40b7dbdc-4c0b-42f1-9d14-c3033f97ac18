package com.cs.param.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cs.param.common.ParPushRulesCommon;
import com.cs.param.common.ResultListCommon;
import com.cs.param.model.ParPushRulesModel;
import com.cs.param.services.RulesService;
import com.cs.param.utils.DateUtil;
import com.cs.param.utils.LogUtil;
import com.cs.param.utils.Util;
import com.cy.common.CySysLog;

import cn.caiyin.rule.api.entity.ConditionData;
import cn.caiyin.rule.api.entity.CrPendingMessageEntity;
import cn.caiyin.rule.api.entity.PageData;

/**
 * 
 * 推送规则同步Controller
 *
 */
@RequestMapping("/pushRules")
@RestController
public class PushRulesController {

	private static final Logger log = LoggerFactory.getLogger(PushRulesController.class);

	@Autowired
	private RulesService rulesService;

	/**
	 * 
	 * 获取推送规则列表
	 *
	 */
	@RequestMapping(value = "getPushRulesPage")
	@CySysLog(methodName = "获取推送规则列表", modularName = "公参模块", optContent = "获取推送规则列表")
	public ResultListCommon getPushRulesPage(@ModelAttribute("common") ParPushRulesCommon common) throws Exception {
		LogUtil.info(log, LogUtil.BIZ, "getPushRulesPage", "获取推送规则列表", common);
		ResultListCommon result = new ResultListCommon();
		try {
			if (!Util.isEmpty(common.getPageSize()) && !Util.isEmpty(common.getPageNum())) {
				ConditionData condition = new ConditionData();
				condition.setPageNum(common.getPageNum());
				condition.setPageSize(common.getPageSize());
				Map<String, String> map = new HashMap<String, String>();
				map.put("phoneNumber", common.getOptObject());
				map.put("target", common.getPhone());
				map.put("status", common.getStatus());
				map.put("type", common.getOptType());
				map.put("startTime", common.getStartTime());
				map.put("endTime", common.getEndTime());
				condition.setCondition(map);
				PageData<CrPendingMessageEntity> pageData = rulesService.getPendingMessageByPage(condition);

				List<CrPendingMessageEntity> list = pageData.getList();
				List<ParPushRulesModel> modelList = new ArrayList<ParPushRulesModel>();
				for (CrPendingMessageEntity entity : list) {
					ParPushRulesModel model = new ParPushRulesModel();
					model.setOptObject(entity.getCpmPhoneNum());
					model.setOptType(entity.getCpmOprType());
					model.setPartitionCode(entity.getCmpZoneId());
					model.setReqUrl(entity.getUrl());
					model.setPhone(entity.getCpmOprTarge());
					model.setStatus(String.valueOf(entity.getCpmStatus()));
					model.setHandleNum(String.valueOf(entity.getCpmTriedTime()));
					model.setCreateTime(DateUtil.parseDate(entity.getCpmCreateTime()));
					model.setLastTime(DateUtil.parseDate(entity.getCpmLastTime()));
					model.setDetails(entity.getCpmContent());
					modelList.add(model);
				}
				result.setPageNum(pageData.getPageNum());
				// 获取总条数
				result.setPageTotal(pageData.getTotalRecord());
				// 数据分页数据
				result.setDatas(modelList);
			}
		} catch (Exception e) {
			LogUtil.error(log, LogUtil.BIZ, "getPushRulesPage", "获取推送规则列表出错！", e);
		}
		return result;
	}

}
