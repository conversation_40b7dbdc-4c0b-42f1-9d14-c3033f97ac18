<template>
    <div>
        <h1 class="user-title">黑名单</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                <el-form-item label="用户号码">
                    <el-input  v-model="searchForm.phone" placeholder="请输入用户号码" size="small"></el-input>
                </el-form-item>
                <el-form-item label="操作人员">
                    <el-input  v-model="searchForm.sysUserName" placeholder="请输入操作人员名字" size="small"></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                </el-form-item>
                <el-form :inline="true" class="demo-form-inline">
                    <el-form-item>
                        <el-button type="primary" @click="addVisible = true" size="small">添加号码</el-button>
                        <el-button type="primary" plain @click="addListVisible = true;clearAddListForm();" size="small">批量导入</el-button>
                        <el-button v-bind:disabled="bindDisabled" v-bind:type="bindType" @click="delList()" size="small">批量删除</el-button>
                    </el-form-item>
                </el-form>
            </el-form>
        </div>
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border class="app-tab"
                  @selection-change="handleSelectionChange"  :header-cell-class-name="tableheaderClassName">
            <el-table-column type="selection" width="55" />
            <el-table-column prop="phone" label="用户号码" />
            <el-table-column prop="sysUserName" label="操作人" />
            <el-table-column prop="oper" label="操作" >
                <template slot-scope="scope" justify="center">
                    <el-button type="text" size="small"  @click="showEdit(scope.row)" >编辑</el-button>
                    <el-button type="text" size="small"  @click="del(scope.row)" >删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div>
        <el-dialog title="新增黑名单" :visible.sync="addVisible" :close-on-click-modal="false">
            <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" label-width="160px" justify="center">
                <el-form-item label="用户号码" prop="phone">
                    <el-input v-model.number="addForm.phone" size="small" :maxlength="11"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer" style="text-align: right;">
                <el-button @click="addVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="add('addForm')" size="small">确 定</el-button>
            </div>
        </el-dialog>
        </div>

        <div>
            <el-dialog title="修改黑名单" :visible.sync="editVisible" :close-on-click-modal="false">
                <el-form :model="editForm" ref="editForm" class="demo-form-inline" label-width="160px" justify="center">
                    <el-form-item label="旧号码">
                        {{editForm.phone}}
                    </el-form-item>
                </el-form>
                <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="160px" justify="center">
                    <el-form-item label="新号码" prop="newPhone">
                        <el-input v-model.number="editForm.newPhone" size="small" :maxlength="11"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: center;">
                    <el-button @click="editVisible = false">取 消</el-button>
                    <el-button type="primary" @click="edit('editForm')">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <div>
            <el-dialog title="批量导入" :visible.sync="addListVisible" :close-on-click-modal="false">
                <el-form :model="addListForm" :rules="rules" ref="addListForm" class="demo-form-inline" label-width="160px" justify="center">
                    <el-form-item label="模板文件">
                        <el-input v-model="addListForm.fileName" disabled size="small"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-upload
                                class="upload-demo"
                                ref="upload"
                                action=""
                                :auto-upload='false'
                                :on-remove='fileRemove'
                                :on-change="handleChange"
                                accept=".xls, .xlsx"
                                :before-upload="beforeAvatarUpload"
                            >
                            <el-button size="small" type="primary">上传excel表</el-button>
                            <div slot="tip" class="el-upload__tip"></div>
                        </el-upload>
                    </el-form-item>
                    <el-form-item>
                        <a :href="`${this.proxyUrl}/param/blackList/downloadTemplate`">下载模板</a>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer" style="text-align: right;">
                    <el-button @click="addListVisible=false;clearAddListForm()" size="small">取 消</el-button>
                    <el-button type="primary" @click="addBlackList()" size="small">确 定</el-button>
                </div>
            </el-dialog>
        </div>

        <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"  style="text-align: right;">
            </el-pagination>
        </div>

        <el-dialog
                title="提示"
                :visible.sync="propVisible"
                width="30%"
                :before-close="handleCloseConfirm">
            <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
        </el-dialog>

    </div>




</template>
<script>
import {post} from './../../servers/httpServer.js';
    export default {
        data() {
            return {
                bindDisabled:true,
                bindType:'info',
                propMsg:'',
                propVisible:false,
                addVisible: false,
                editVisible:false,
                addListVisible:false,
                multipleSelection: [],
                delForm:{
                    ids:[],
                    phones:[]
                },
                //查询form对象定义
                searchForm: {
                    phone:'',
                    sysUserName:'',
                    pageSize:10,// 每页显示条数
                    pageNum:1,
                },
                addForm: {
                    phone:'',
                    sysUserName:'',
                    isPersonal :0,//0：否、1：是
                    isNewMedia:0,//0：否、1：是
                    isRemind:0,//0：否、1：是
                    isBusiness:0//0：否、1：是
                },
                addListForm: {
                    fileName:'',
                    isPersonal :0,//0：否、1：是
                    isNewMedia:0,//0：否、1：是
                    isRemind:0,//0：否、1：是
                    isBusiness:0//0：否、1：是
                },
                //编辑form对象定义
                editForm: {
                    id:'',
                    phone:'',
                    newPhone:'',
                    sysUserName:'',
                    isPersonal :0,//0：否、1：是
                    isNewMedia:0,//0：否、1：是
                    isRemind:0,//0：否、1：是
                    isBusiness:0//0：否、1：是
                },
                //编辑form对象定义
                subForm: {
                    id:'',
                    phone:'',
                    sysUserName:'',
                    isPersonal :0,//0：否、1：是
                    isNewMedia:0,//0：否、1：是
                    isRemind:0,//0：否、1：是
                    isBusiness:0//0：否、1：是
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'',
                    phone:'',
                },
                rules: {
                    phone: [
                        { required: true, message: '请输入11位手机号码', trigger: 'blur' },
                        { type: 'number',message: '请输入11位手机号码', trigger: 'blur' },
                    ],
                    newPhone:[
                        { required: true, message: '请输入11位手机号码', trigger: 'blur' },
                        { type: 'number',message: '请输入11位手机号码', trigger: 'blur' },
                    ]
                },
                cities:[],
                tableData:[
                ],
                currentPage: 1,
                total:0,
                fileList:[]
            }
        },

        mounted(){
            this.search();
        },
        methods: {
            bool2Num(bool){
                if(bool==true){
                    return 1;
                }
                return 0;
            },
            num2Bool(num){
                if(num==1){
                    return true;
                }
                return false;
            },
            // 选择行数
            toggleSelection(rows) {
                if (rows) {
                    rows.forEach(row => {
                        this.$refs.multipleTable.toggleRowSelection(row);
                });
                } else {
                    this.$refs.multipleTable.clearSelection();
                }
            },
            handleSelectionChange(val) {
                this.delForm.ids = [];
                this.delForm.phones = [];
                console.log(val);
                val.forEach(element =>{
                    this.delForm.ids.push(element.id);
                    this.delForm.phones.push(element.phone);
                });
                this.multipleSelection = val;
                if(this.delForm.ids.length>0){
                    this.bindDisabled=false;
                    this.bindType="primary";
                }else{
                    this.bindDisabled=true;
                    this.bindType="info";
                }
            },
            //批量删除
            delList(){
                if(this.delForm.phones.length===0){
                    this.$message({
                        message: '请至少选中一条删除记录！',
                        type: 'warning'
                    });
                    return;
                }

                this.$confirm('确认要删除吗？')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/param/blackList/delBatch`, JSON.stringify(this.delForm),{
                            headers: {
                                "X-Requested-With": "XMLHttpRequest",
                                contentType: "application/json",
                                charset: "utf-8"
                            },
                            emulateJSON: true,
                        })
                            .then(function (res) {
                                console.log(res)
                                if (res.data.resStatus == 0) {
                                    /*this.$message({
                                        message: '删除成功！',
                                        type: 'success'
                                    });*/
                                    this.$message.success('成功删除: '+res.data.resText+'条');
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('删除失败!'+ res.data.resText);
                                }
                            })
                    })
            },
            //查询请求
            search:function(){
                this.$http.post(`${this.proxyUrl}/param/blackList/getBlackListPage`,JSON.stringify(this.searchForm),{
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                        contentType: "application/json",
                        charset: "utf-8"
                    },
                    emulateJSON: true,
                    timeout: 5000
                })
                        .then(function(res){
                            console.log(res);
                            this.currentPage=res.data.pageNum;
                            this.total=res.data.pageTotal;
                            this.tableData=res.data.datas;
                        })
            },
            downloadTemplate:function(){
                window.location.href=`${this.proxyUrl}/param/redList/downloadTemplate`;
            },
            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            // 上传移除
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            fileRemove(file,fileList) {
                
            },
            //文件类型限制
            beforeAvatarUpload(file){
                var testmsg=file.name.substring(file.name.lastIndexOf('.')+1)
                const extension = testmsg === 'xls'
                const extension2 = testmsg === 'xlsx'
                const isLt2M = file.size / 1024 / 1024 < 10
                if(!extension && !extension2) {
                    this.$message({
                        message: '上传文件只能是 xls、xlsx格式!',
                        type: 'warning'
                    });
                }
                if(!isLt2M) {
                    this.$message({
                        message: '上传文件大小不能超过 10MB!',
                        type: 'warning'
                    });
                }
                return extension || extension2 && isLt2M
            },
            //选择文件
            handleChange(file,fileList){
                this.addListForm.fileName=file.name;
                this.addListForm.file=file.raw;
            },
            //确认添加
            addBlackList(){
                if(this.addListForm.fileName==='' || this.addListForm.fileName===undefined || this.addListForm.fileName===null){
                    this.$message({  
                        message: '请选择上传文件',  
                        type: 'warning'  
                    }); 
                    return;
                }
                let formData=new FormData();
                formData.append('file',this.addListForm.file);
                formData.append('fileName',this.addListForm.fileName);
                this.$store.dispatch('coreModule/addSysBlack',formData).then(res=>{
                    if(res.resStatus==0){
                       this.$message({
                            message:'导入成功',
                            type:'success'
                        })
                        this.clearAddListForm();
                        this.addListVisible = false;
                        this.search(this.searchForm);
                    }else {
                        this.$message.error(res.resText);
                    }
                })
            },
            //清空新增数据
            clearAddListForm(){
                this.addListForm={
                    fileName:'',
                    isPersonal :0,//0：否、1：是
                    isNewMedia:0,//0：否、1：是
                    isRemind:0,//0：否、1：是
                    isBusiness:0//0：否、1：是
                };
                if(this.$refs.upload){
                    this.$refs.upload.clearFiles();
                }
            },
            // 弹出修改框
            showEdit(editForm){
                this.editForm = Object.assign({},editForm);
                this.editVisible = true;
            },
            //修改请求
            edit(editForm){
                this.$refs[editForm].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/blackList/updateBlackList`,JSON.stringify(this.editForm),
                            {headers: {
                                    "X-Requested-With": "XMLHttpRequest",
                                    contentType: "application/json",
                                    charset: "utf-8"
                                },
                                emulateJSON: true,
                                timeout: 5000}).then(function(res){
                            console.log(res);
                            if(res.data.resStatus == 0){
                                this.$message({
                                    message: '修改成功！',
                                    type: 'success'
                                });
                                this.editVisible = false;
                                this.editForm.phone = '';
                                this.search(this.searchForm);
                            }else{
                                this.$message.error('修改失败!'+ res.data.resText);
                            }
                        })
                    } else {
                        return false;
            }
            });
            },
            // 新增
            add(formName) {
                console.log(this.addForm);
                this.$refs[formName].validate((valid) => {
                    this.subForm =Object.assign({},this.addForm);
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/blackList/addBlackList`,JSON.stringify(this.subForm),
                            {
                                headers: {
                                    "X-Requested-With": "XMLHttpRequest",
                                    contentType: "application/json",
                                    charset: "utf-8"
                                },
                                emulateJSON: true,
                                timeout: 5000
                            })
                                .then(function(res){
                                    console.log(res);
                                    if(res.data.resStatus == 0){
                                        this.$message.success('新增成功!');
                                        this.addVisible = false;
                                        this.addForm.phone='';
                                        this.search(this.searchForm);
                                    }else{
                                        this.addForm.phone='';
                                        this.$message.error('新增失败!'+ res.data.resText);
                                    }
                                })
                    } else {
                        return false;
            }
            });
            },
            //删除请求
            del(role){
                console.log(role);
                this.queryOrDelForm.id = role.id;
                this.queryOrDelForm.phone = role.phone;
                console.log(this.queryOrDelForm);
                this.$confirm('确认要删除吗？')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/param/blackList/delBlackList`, JSON.stringify(this.queryOrDelForm),
                            {headers: {
                                    "X-Requested-With": "XMLHttpRequest",
                                    contentType: "application/json",
                                    charset: "utf-8"
                                },
                                emulateJSON: true,
                                timeout: 5000})
                            .then(function (res) {
                                console.log(res)
                                if (res.data.resStatus == 0) {
                                    this.$message.success('删除成功!');
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('删除失败!'+ res.data.resText);
                                }
                            })
                    })
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },

            // 关闭弹出框
            handleClose(done) {
                this.$confirm('确认关闭？')
                        .then(_ => {
                    done();
            })
            .catch(_ => {});
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 8px;
        margin-left: 16px;
        background-color: white;
    }
    .user-line{
        margin-top: 8px;
        background-color: blue;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
