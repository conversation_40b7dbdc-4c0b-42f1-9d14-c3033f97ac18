<template>
    <div>
        <div>
            <el-row style="width: 94%;margin: 10px auto">
                <span style="font-weight:bold">新媒彩印状态:</span>
                <span>{{getPersonStatus}}</span>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <div style="font-weight:bold;margin-bottom: 10px">套餐包订购列表:</div>
                <el-table :data="tableData.datas" border max-height="300" style="margin:0 auto;width: 100%;">
                    <el-table-column prop="bossId" label="企业代码">
                    </el-table-column>
                    <el-table-column prop="serviceId" label="业务代码">
                    </el-table-column>
                    <el-table-column prop="productId" label="产品代码">
                    </el-table-column>
                    <el-table-column prop="packageName" label="套餐包名称">
                    </el-table-column>
                    <el-table-column prop="serviceCost" label="业务资费">
                    </el-table-column>
                    <el-table-column prop="serviceChannel" label="开户渠道">
                    </el-table-column>
                    <el-table-column prop="submitTime" label="订购时间" width="180">
                    </el-table-column>
                </el-table>
            </el-row>
            <el-row style="width: 94%;margin: 10px auto">
                <div style="font-weight:bold;margin-bottom: 10px">彩印内容设置列表:</div>
                <el-table :data="tableData.datas2" border max-height="300" style="margin:0 auto;width: 100%;">
                    <el-table-column prop="crUserNum" label="分组ID">
                    </el-table-column>
                    <el-table-column prop="crServiceCode" label="子业务">
                    </el-table-column>
                    <el-table-column prop="crCyContent" label="屏显彩印内容">
                    </el-table-column>
                    <el-table-column prop="" label="挂机彩漫内容">
                        <!--<template slot-scope="scope">-->
                            <!--<el-button @click="openNums(scope.row)" type="text" size="small">查看详情</el-button>-->
                        <!--</template>-->
                    </el-table-column>
                    <el-table-column prop="channel" label="代言渠道">
                    </el-table-column>
                    <el-table-column prop="crCreateTime" label="代言时间">
                    </el-table-column>
                </el-table>
            </el-row>
            <div class="block app-pageganit">
            <el-pagination v-if="page.pageTotal>0"  class="user-page" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="page.pageNum" :page-sizes="[10,20,30,50]" :page-size="page.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="page.pageTotal">
            </el-pagination>
            </div>
        </div>
        <div>
            <!-- 详情 -->
            <el-dialog
                    title="详情"
                    :visible.sync="dialogVisible"
                    width="40%">
                <div>
                    <p>豆腐块计分卡简单</p>
                    <p><img src="" alt=""></p>
                </div>
            </el-dialog>
        </div>
    </div>
</template>
<script>
    export default {
        name: 'UserList',
        data() {
            return {
                dialogVisible:false,
                totalCount: 0,
                labels: {
                    setCsType: [],
                    setRecType: [],
                    csIdLabel: [],
                    channel: {}
                },
                page: {
                    cyType:'2',
                    phone: '',
                    pageNum: 1,
                    pageSize: 10
                },
                tableData: {
                    datas2:[1]
                },
                modals: {}
            }
        },
        methods: {
            pageQuery: function(param) {
               this.$http
                   .post(`${this.proxyUrl}/user/enterprise/newmediaPrint`, param, {
                       emulateJSON: true
                   })
                   .then(function(res) {
                       this.tableData = res.data;
                       console.log(res.data);
                       this.$set(this.page,'pageTotal',res.data.pageTotal);
                   })
            },
            handleSizeChange: function(size) {
               this.$set(this.page, 'pageSize', size);
               this.$set(this.page, 'pageNum', 1);
            },
            handleCurrentChange: function(currentPage) {
               this.$set(this.page, 'pageNum', currentPage);
               this.pageQuery(this.page);
            },
            openNums: function(row) {
                this.dialogVisible = true;
            },
        },
        mounted() {
           this.$set(this.page, 'phone', sessionStorage.getItem('pkCurUserid'));
           this.pageQuery(this.page);
        },
        computed: {
            getPersonStatus: function() {
                if(this.tableData.mediaStatus==0){
                    return '未订购';
                }
                if(this.tableData.mediaStatus==1){
                    return '已开通';
                }
                if(this.tableData.mediaStatus==2){
                    return '已退订';
                }
            }
        },
        components: {}
    }
</script>
<style>
</style>