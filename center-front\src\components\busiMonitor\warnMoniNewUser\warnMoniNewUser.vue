<template>
    <div>
        <h1 class="user-title">开销户预警</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :inline="true" :model="warnMoni" size="small" class="demo-form-inline">
                <el-row>
                    <el-col :span="21">
                        <el-form-item label="时间">
                            <el-select v-model="warnMoni.type" style="width: 60px">
                                <el-option label="天" value="date"></el-option>
                                <el-option label="月" value="month"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker placeholder="开始日期" v-model="warnMoni.startDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="warnMoni.type"/>
                            至
                            <el-date-picker placeholder="结束日期" v-model="warnMoni.endDate"
                                            style="width: 130px" :picker-options="pickerOptions" :type="warnMoni.type"/>
                        </el-form-item>
                        <el-form-item label="省份">
                            <el-select v-model="warnMoni.provinceId" clearable @change="selectProvince()" class="app-input02">
                                <el-option v-for="item in provinceList"
                                    :key="item.provinceCode"
                                    :label="item.provinceName"
                                    :value="item.provinceCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="城市">
                            <el-select v-model="warnMoni.cityId" clearable class="app-input02">
                                <el-option v-for="item in regionList"
                                    :key="item.regionCode"
                                    :label="item.regionName"
                                    :value="item.regionCode">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="warnMoniSearch(1)">查询</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain @click="warnMoniDownload()">导出excel</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div>
                <el-table :data="accountPrewarningInfoList" border max-height=500 v-loading="tableLoading" class="app-tab02">
                    <el-table-column prop="statsDate" label="时间" width="240"/>
                    <el-table-column prop="provinceName" label="省份" width="100"/>
                    <el-table-column prop="cityName" label="地市" width="100"/>
                    <el-table-column prop="openStats" label="开户量" width="100"/>
                    <el-table-column prop="openYoy" label="开户比增长率" :formatter="formatYoy" width="100"/>
                        <!--<template slot-scope="scope">-->
                            <!--{{scope.row.openYoy+'%'}}-->
                        <!--</template>-->
                    <!--</el-table-column>-->
                    <el-table-column prop="openChannel" label="开户主要渠道" width="120"/>
                    <el-table-column prop="logoffStats" label="销户量" width="100"/>
                    <el-table-column prop="logoffYoy" label="销户同比增长率" :formatter="formatOffYoy" width="100"/>
                        <!--<template slot-scope="scope">-->
                            <!--{{scope.row.logoffYoy+'%'}}-->
                        <!--</template>-->
                    <!--</el-table-column>-->
                    <el-table-column prop="logoffChannel" label="销户主要渠道" width="120"/>
                    <el-table-column label="开销户警示状态" width="100">
                          <template slot-scope="scope">
                                {{scope.row.openPrewarningStatus | status}}
                            </template>
                    </el-table-column>
                    <el-table-column label="操作" fixed="right" min-width="160px">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="sendSms(scope.row)">下发短信</el-button>
                            <el-button type="text" size="small" @click="sendEmail(scope.row)">下发邮件</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="block app-pageganit">
                <el-pagination @size-change="handleSizeChange" @current-change="warnMoniSearch"
                               :current-page="warnMoni.page.pageNo"
                               :page-sizes="[20, 50, 100, 150]" :page-size="warnMoni.page.pageSize"
                               layout="total, sizes, prev, pager, next, jumper" :total="warnMoni.page.total">
                </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>
<script src='./warnMoniNewUser.js'></script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
</style>