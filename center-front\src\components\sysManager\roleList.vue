<template>
    <div>
        <div v-if="!addVisible">
            <h1 class="user-title">角色管理</h1>
            <hr class="user-line"/>
            <div class="app-search">
                <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                    <el-form-item label="角色ID">
                        <el-input v-model.number="searchForm.sysRoleId" placeholder="" size="small"
                                  class="app-input"></el-input>
                    </el-form-item>
                    <el-form-item label="角色名称">
                        <el-input v-model="searchForm.sysRoleName" placeholder="" size="small" max="40"
                                  class="app-input"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                        <el-button type="primary" @click="showAddRole()" size="small">新增角色</el-button>
                    </el-form-item>
                </el-form>

            </div>
            <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
                      class="app-tab" :header-cell-class-name="tableheaderClassName">
                <el-table-column prop="sysRoleId" label="角色ID"/>
                <el-table-column prop="sysRoleName" label="角色名称"/>
                <el-table-column prop="sysResourcesName" label="角色权限" :show-overflow-tooltip="true"/>
                <el-table-column prop="sysDescs" label="适用对象"/>
                <el-table-column prop="oper" label="操作" fixed="right">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="showEditRole(scope.row)">编辑</el-button>
                        <el-button v-show="scope.row.isDelete != '2'" type="text" size="small"
                                   @click="delRole(scope.row)">删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="block app-pageganit">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total" style="text-align: right;">
                </el-pagination>
            </div>
            <div>
                <el-dialog title="修改角色" :visible.sync="editVisible" :close-on-click-modal="false">
                    <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline app-form-item"
                             label-width="25%" style="width: 80%">
                        <el-form-item label="角色名称" prop="sysRoleName">
                            <el-input v-model="editForm.sysRoleName" placeholder="请输入角色名称" :minlength="3"
                                      :maxlength="40"></el-input>
                        </el-form-item>
                        <el-form-item label="适用对象" prop="sysDescs">
                            <el-input v-model="editForm.sysDescs" type="textarea" :rows="2" placeholder="请输入适用对象"
                                      max="40"></el-input>
                        </el-form-item>
                        <el-form-item label="设置角色权限" prop="editFormSysResourcesIds">
                            <el-tree :data="treeDate" show-checkbox default-expand-all node-key="sysResourcesId"
                                     ref="tree1" :default-checked-keys="editForm.sysResourcesIds" highlight-current
                                     :props="defaultProps">
                            </el-tree>
                        </el-form-item>
                    </el-form>
                    <div slot="footer" class="dialog-footer" style="text-align: right;">
                        <el-button type="primary" @click="editRole('editForm')">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
        </div>
        <div v-if="addVisible">
            <div style="padding: 10px 0px 10px 0px;border-bottom: 1px solid #cccc;"><h1>新增角色</h1></div>
            <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline app-form-item"
                     label-width="25%" style="width: 80%;padding-top:2%">
                <el-form-item label="角色名称" prop="sysRoleName">
                    <el-input v-model="addForm.sysRoleName" placeholder="请输入角色名称" :minlength="3"
                              :maxlength="40"></el-input>
                </el-form-item>
                <el-form-item label="适用对象" prop="sysDescs">
                    <el-input v-model="addForm.sysDescs" type="textarea" :rows="2" placeholder="请输入适用对象"
                              max="40"></el-input>
                </el-form-item>
                <el-form-item label="设置角色权限" prop="addFormSysResourcesIds">
                    <el-tree :data="treeDate" show-checkbox default-expand-all node-key="sysResourcesId" ref="tree"
                             :default-checked-keys="addForm.sysResourcesIds" highlight-current :props="defaultProps">
                    </el-tree>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer" style="padding-left:45%">
                <el-button @click="addVisible = false" size="small">取 消</el-button>
                <el-button type="primary" @click="addRole('addForm')" size="small">确 定</el-button>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            let validIsExistRoleName = (rule, value, callback) => {
                this.addForm.sysRoleName = value;
                console.log(JSON.stringify(this.addForm));
                this.$http.post(`${this.proxyUrl}/sys/sysRole/isExistRoleName`, JSON.stringify(this.addForm)).then(function (res) {
                    if (res.data == 1 && this.addVisible) {
                        callback(new Error('角色名已存在!'))
                    } else {
                        callback()
                    }
                });

            };
            let validRoleName = (rule, value, callback) => {
                if (!/^([A-Za-z]|[\u4E00-\u9FA5])+$/.test(value)) {
                    callback(new Error('请输入字母或汉字!'))
                } else {
                    callback()
                }
            }; let addFormSysResourcesIds = (rule, value, callback) => {
                if (this.addForm.sysResourcesIds.length<=0) {
                    callback(new Error('请至少设置一个角色权限!'))
                } else {
                    callback()
                }
            }; let editFormSysResourcesIds = (rule, value, callback) => {
                if (this.editForm.sysResourcesIds.length<=0) {
                    callback(new Error('请至少设置一个角色权限!'))
                } else {
                    callback()
                }
            };

            return {
                editVisible: false,
                addVisible: false,
                propVisible: false,
                delVisible: false,
                row: {},
                propMsg: '',
                //查询form对象定义
                searchForm: {
                    sysRoleId: '',
                    sysRoleName: '',
                    pageSize: 10,// 每页显示条数
                    pageNum: 1 // 查询的页码
                },
                //新增form
                addForm: {
                    sysRoleName: '',
                    sysDescs: '',
                    sysResourcesIds: [] //资源id
                },
                //修改form
                editForm: {
                    sysRoleId: '', //修改的角色id
                    sysRoleName: '',
                    sysDescs: '',
                    sysResourcesIds: [] //资源id
                },
                //查询或删除form
                queryOrDelForm: {
                    sysRoleId: '' //角色id
                },
                rules: {
                    sysRoleName: [
                        {required: true, message: '请输入角色名称', trigger: 'blur'},
                        {min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur'},
                        {trigger: 'blur', validator: validRoleName},
                        {trigger: 'blur', validator: validIsExistRoleName}
                    ],
                    sysDescs: [
                        {required: true, message: '请输入适用对象', trigger: 'blur'},
                        {min: 2, max: 40, message: '长度在 2 到 40 个字符', trigger: 'blur'}
                    ],
                    addFormSysResourcesIds: [
                        {trigger: 'blur', validator: addFormSysResourcesIds}
                    ] ,
                    editFormSysResourcesIds: [
                        {trigger: 'blur', validator: editFormSysResourcesIds}
                    ]
                },
                tableData: [],
                currentPage: 1,
                total: 0,
                treeDate: [],
                defaultProps: {
                    children: 'children',
                    label: 'sysResourcesName'
                }
            }
        },

        mounted() {
            this.searchResource();
//            this.slideData(this.proxyUrl);
        },
        methods: {
            //查询列表请求
            searchResource: function () {
                this.$http.get(`${this.proxyUrl}/sys/sysRole/getAllSysResources`)
                    .then(function (res) {
                        console.log(res.data);
                        if(res.data.sysResourcesId == 999999){
                            setTimeout(
                                function() {
                                    this.$router.push("/noPermission");
                                }.bind(this),
                                50
                            );
                        } else {
                            res.data.children.forEach(children => {
                                if(children.sysResourcesId=="67"){
                                    children.children = children.children.filter(c => {
                                        return c.sysResourcesId!="68"&&c.sysResourcesId!="69"
                                    });
                                }
                            });
                            this.treeDate = res.data.children;
                        }
                    })
            },
            //查询列表请求
            search: function (searchForm) {
                this.searchForm = searchForm;
                this.$http.post(`${this.proxyUrl}/sys/sysRole/getSysRoleList`, searchForm, {emulateJSON: true})
                    .then(function (res) {
                        this.currentPage = res.data.pageNum;
                        this.total = res.data.pageTotal;
                        console.log(res.data.datas);
                        this.tableData = res.data.datas;
                    })
            },

            searchRoleInfo(sysResourcesIds) {
                this.searchResource();
                this.queryOrDelForm.sysRoleId = sysResourcesIds;
                this.$http.post(`${this.proxyUrl}/sys/sysRole/getSysRoleDetail`, this.queryOrDelForm, {emulateJSON: true})
                    .then(function (res) {
                        console.log(res);
                        this.editForm = res.data;
                        this.setCheckedKeys();
                    })
            },
            nextTick(list, type) {
                this.$nextTick(() => {
                    if (type === 1) {
                        this.$refs.tree.setCheckedKeys(list)
                    } else {
                        this.$refs.tree1.setCheckedKeys(list)
                    }
                });
            },
            setCheckedKeys() {
                //console.log(this.editForm.sysResourcesIds);
                this.nextTick(this.editForm.sysResourcesIds,0);
                //  this.$refs.tree1.setCheckedKeys(this.editForm.sysResourcesIds);
            },
            showAddRole() {
                this.addVisible = true;
                this.addForm = {};
                this.addForm.sysResourcesIds = [];
                this.editForm.sysResourcesIds = [];
                this.nextTick(this.addForm.sysResourcesIds, 1);
                // this.$refs.tree.setCheckedKeys(this.addForm.sysResourcesIds);
                this.resetForm('addForm');
            },
            // 弹出修改框
            showEditRole(editForm) {
                this.searchResource();
                this.editVisible = true;
                this.searchRoleInfo(editForm.sysRoleId);
                this.resetForm('editForm');
//                    this.$nextTick(() => {
//                    });
            },
            //修改请求
            editRole: function (editForm) {
                let all = [];
                let half = [];
                let total = [];
                all = this.$refs.tree1.getCheckedKeys();
                half = this.$refs.tree1.getHalfCheckedKeys();
                total = all.concat(half);
                this.editForm.sysResourcesIds = total;

              const exportPermissionId = 151;
              const dataPermissionsStartId = 153;
              const dataPermissionsEndId = 166;

              // 如果选中了“导出文件下载”
              if (total.includes(exportPermissionId)) {
                // 判断是否选择了数据权限子项（id=153~166）
                const hasDataPermissions = total.some((permissionId) => {
                  return permissionId >= dataPermissionsStartId && permissionId <= dataPermissionsEndId;
                });

                // 如果未选中任何数据权限子项
                if (!hasDataPermissions) {
                  this.$message.warning("请选择导出下载功能的数据权限");
                  return;
                }
              }

                this.$refs[editForm].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/sys/sysRole/updateSysRole`, JSON.stringify(this.editForm))
                            .then(function (res) {
                                if (res.data.resStatus == 0) {
                                    this.$message.success("修改成功！");
                                    this.editVisible = false;
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('修改失败!' + res.data.resText);
                                }
                            })
                    } else {
                        return false;
                    }
                });
            },
            // 新增
            addRole(formName) {
                this.searchResource();
                this.addForm.sysResourcesIds = this.$refs.tree.getCheckedKeys();
                this.$refs[formName].validate((valid) => {
                    if (valid) {
                      const exportPermissionId = 151;
                      const dataPermissionsStartId = 153;
                      const dataPermissionsEndId = 166;

                      // 如果选中了"导出文件下载"
                      if (this.addForm.sysResourcesIds.includes(exportPermissionId)) {
                        // 判断是否选择了数据权限子项（id=153~166）
                        const hasDataPermissions = this.addForm.sysResourcesIds.some((permissionId) => {
                          return permissionId >= dataPermissionsStartId && permissionId <= dataPermissionsEndId;
                        });

                        // 如果未选择任何数据权限子项
                        if (!hasDataPermissions) {
                          this.$message.warning("请选择导出下载功能的数据权限");
                          return;
                        }
                      }
                        this.$http.post(`${this.proxyUrl}/sys/sysRole/addSysRole`, JSON.stringify(this.addForm))
                            .then(function (res) {
                                console.log(res);
                                if (res.data.resStatus == 0) {
                                    this.$message.success("添加成功！");
                                    this.addVisible = false;
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('添加失败!' + res.data.resText);
                                }
                            })
                    } else {
                        return false;
                    }
                });
            },
            //删除请求
            delRole: function (role) {
                this.queryOrDelForm.sysRoleId = role.sysRoleId;
                this.$confirm('确定删除此角色?')
                    .then(_ => {
                        this.$http.post(`${this.proxyUrl}/sys/sysRole/deleteSysRole`, this.queryOrDelForm, {emulateJSON: true})
                            .then(function (res) {
                                console.log(res)
                                if (res.data.resStatus == 0) {
                                    this.$message.success("删除成功！");
                                    this.search(this.searchForm);
                                } else {
                                    this.$message.error('删除失败!' + res.data.resText);
                                }
                            })
                    })
            },
            resetForm(formName) {
                if (this.$refs[formName] !== undefined) {
                    this.$refs[formName].resetFields();
                }
            },
            tableheaderClassName({row, rowIndex}) {
                return "table-head-th";
            },
            handleSizeChange(val) {
                this.searchForm.pageSize = val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum = val;
                this.search(this.searchForm);
            },

        },
        created() {
        },
        components: {}
    }


</script>
<style scoped>
    .user-title {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }

    .user-line {
        margin-top: 3%;
        background-color: blue;;
    }

    .user-search {
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }

    .el-table .table-head-th {
        background-color: #F5F5F5;
    }

    .el-table .cell {
        white-space: nowrap
    }

    .el-tooltip__popper {
        width: 300px;

    }

    .el-tooltip__popper .light {
        width: 300px;

    }
</style>
