<template scope="scope">
  <div name>
    <!--中央平台审核分类优化-增强群发彩印改成企业通知-->
    <h1 class="userenhanceMassPrint-title">企业通知</h1>
    <div class="app-search">
      <el-form :inline="true" label-width="70px" label-position="right" class="demo-form-inline">
        <el-row>
          <el-col :span="8">
            <el-form-item label="企业编号">
              <el-input v-model="searchReq.corpId" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业名称">
              <el-input v-model="searchReq.corpName" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="彩印主题">
              <el-input v-model="searchReq.subject" size="small" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="省份">
              <el-select
                v-model="searchReq.provinceId"
                clearable
                placeholder="请选择"
                size="small"
                @change="querySearchRegionList"
                class="app-input"
              >
                <el-option
                  v-for="item in provinceList"
                  :key="item.provinceName"
                  :label="item.provinceName"
                  :value="item.provinceCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="地市">
              <el-select
                v-model="searchReq.cityId"
                clearable
                placeholder="请选择"
                size="small"
                class="app-input"
              >
                <el-option
                  v-for="item in city"
                  :key="item.regionCode"
                  :label="item.regionName"
                  :value="item.regionCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="彩印ID">
              <el-input v-model="searchReq.uuid" size="small" clearable></el-input>
            </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label="彩印内容">
              <el-input v-model="searchReq.text" size="small" clearable></el-input>
            </el-form-item>
          </el-col>

        </el-row>

        <el-form-item label="提交时间">
          <div class="block">
            <el-date-picker
              v-model="searchReq.timearr"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              size="small"
            ></el-date-picker>
          </div>
        </el-form-item>
          <el-form-item label="内容编号">
            <el-input v-model="searchReq.contentID" size="small" clearable></el-input>
          </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="searchReq.p = 1;search()" size="small">查询</el-button>
        </el-form-item>
      </el-form>

      <el-form :inline="true" class="demo-form-inline">
        <el-form-item>
          <el-button
            type="primary"
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="passVisible=true;passType=2;"
          >批量通过</el-button>
          <el-button
            type="primary"
            :type="typeoff"
            :disabled="clickoff"
            size="small"
            @click="rejectVisible=true;rejectType=2;"
          >批量驳回</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        class="app-tab"
        @selection-change="handleSelectionChange"
        :header-cell-class-name="tableheaderClassName"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="uuid" label="彩印ID" width="200"></el-table-column>
        <el-table-column prop="text" label="彩印内容" width="200"></el-table-column>

        <!--中央平台审核分类优化-增彩内容为内容-->
        <el-table-column label="内容" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="toDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
        <el-table-column label="敏感词"
                         width="200">
          <template slot-scope="scope">
            <div  v-html="scope.row.sensitiveWords"></div>
          </template>
        </el-table-column>
        <el-table-column prop="corpName" label="企业名称" width="200"></el-table-column>
         <el-table-column label="企业资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)" :style="hasCorpImage(scope.row.corpImage)?'':'color:#808080'">详情</el-button>
          </template>
        </el-table-column>

        <!--中央平台审核分类优化-新增其他资质-->
        <el-table-column label="其他资质" width="100">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="showOtherImage(scope.row.otherImage)" :style="hasOtherImage(scope.row.otherImage)?'':'color: #808080'">详情</el-button>
          </template>
        </el-table-column>

        <el-table-column prop="insertTime" label="提交时间" width="200"></el-table-column>

        <!--中央平台审核分类优化-新增彩印类型-->
        <el-table-column prop="caiyinType" label="彩印类型" width="100"></el-table-column>
        <el-table-column
        prop="contentID"
        label="内容编号"
        width="200">
         </el-table-column>
        <el-table-column
                prop="templateIds"
                label="异网模板编号"
                width="350">
        </el-table-column>
        <!--中央平台审核分类优化-删除企业编号-->
        <!--<el-table-column prop="corpId" label="企业编号" width="200"></el-table-column>-->

        <!--中央平台审核分类优化-修改主题名称为主题-->
        <el-table-column prop="subject" label="主题" width="240"></el-table-column>

        <el-table-column prop="province" label="省份" width="150"></el-table-column>
        <el-table-column prop="city" label="地市" width="150"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="passVisible=true;rowData=scope.row;passType=1;"
            >通过</el-button>
            <el-button
              @click="rejectVisible=true;rowData=scope.row;rejectReq.svCause='';rejectType=1;"
              type="text"
              size="small"
            >驳回</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="block app-pageganit">
        <el-pagination
          v-show="pageTotal"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchReq.p"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal"
          style="text-align: right;"
        ></el-pagination>
      </div>

      <el-dialog
        width="30%"
        title="通过"
        :visible.sync="passVisible"
        :close-on-click-modal="false"
        append-to-body
      >
        <div>是否通过该内容？</div>
        <div slot="footer" style="text-align: right;">
          <el-button @click="passVisible = false" size="small">取 消</el-button>
          <el-button type="primary" size="small" @click="passVisible = false;passCheck(rowData)">确 定</el-button>
        </div>
      </el-dialog>

      <el-dialog
        width="30%"
        title="驳回"
        :visible.sync="rejectVisible"
        :close-on-click-modal="false"
        append-to-body
      >
        <el-form label-width="80px" justify="center">
          <el-form-item label="驳回原因">
            <el-radio v-model="radio" label="1">手动输入</el-radio>
            <el-radio v-model="radio" label="2">系统预设</el-radio>
          </el-form-item>
          <el-form-item label v-show="radio==1">
            <!--驳回原因字数无限制-->
            <el-input
              v-model="rejectReq.reason"
              type="textarea"
              :rows="2"
              placeholder
              style="width: 200px;"
            ></el-input>
            <p v-if="rejectReq.reason.length > 1024" style="color: red">不能超过1024个字</p>
          </el-form-item>
          <el-form-item label v-show="radio==2">
            <el-select v-model="rejectReq.reason" clearable placeholder="请选择" size="small">
              <el-option
                v-for="item in refuse"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" style="text-align: right;">
          <el-button @click="rejectVisible = false" size="small">取 消</el-button>
          <el-button
            type="primary"
            @click="rejectVisible = false;rejectCheck(rowData)"
            size="small"
            :disabled="rejectReq.reason.length > 1024"
          >确 定</el-button>
        </div>
      </el-dialog>
        <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
        <img style="width: 100%;" :src="corpImage"  alt>
      </el-dialog>

      <!--中央平台审核分类优化-新增其他资质模态框-->
      <el-dialog title="其他资质" class="zzWrap" width="30%" :visible.sync="otherImageVisible">
        <ul class="contentlist" v-for="(item, index) in otherImage" :key="index">
          <li >
            <a :href="item" target="_blank">其他资质{{index+1}}</a>
          </li>
        </ul>
      </el-dialog>

    </div>
    <div class="user-line"></div>
  </div>
</template>
<script>
import moment from "moment";
import {dealSensitiveWord} from "../../../util/core";

export default {
  data() {
    return {
      imgHeight: "300px",
      imgArr: [],
      carouselIndex: "",
      tableLoading: false,
      zzVisible: false,
      searchReq: {
         contentID:"",//内容编号
        // serviceId: "01136,01144,02114,05106",
        corpId: "",
        corpName: "",
        subject: "",
        uuid: "",
        provinceId: "",
        cityId: "",
        timearr: [],
        pz: 10,
        p: 1,
        status:4
      },
      typeoff: "info",
      clickoff: true,
      //省份
      provinceList: JSON.parse(sessionStorage.getItem("provinceList")),
      //地市
      city: [],
      //数据表
      tableData: [],
      //操作列表
      rowData: "",
      pageTotal: 0, //总条数
      passVisible: false,
      rejectVisible: false,
      radio: "1",
      //通过请求参数
      passReq: {
        ids: [],
        type: 2,
        // 中央平台审核分类优化——用作区分01150、05300通过
        serviceIds:[],
        corpInfoId:-1,
        submitter:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        reviewer:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        isReAudit:"1"
      },
      //中央平台审核分类优化-增加批量通过请求参数变量(用于01150，05300)
      newPassBatch1:{
        type:1,
        corpInfoIds:[],
        reviewer:
                sessionStorage.getItem("userInfo") &&
                JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        isReAudit:"1"
      },
      // 中央平台审核分类优化-增加批量通过请求参数变量(用于01150，05300以外的serviceId)
      newPassBatch2:{
        type:2,
        ids:[],
        reviewer:
                sessionStorage.getItem("userInfo") &&
                JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        isReAudit:"1"
      },
      passType:0,
      // 中央平台审核分类优化-新增彩印类型初始化变量
      caiyinType:[
        {
          id:"01136",
          name:"企业彩印-增彩",
        },
        {
          id:"01144",
          name:"省内版企业彩印-增彩",
        },
        {
          id:"02114",
          name:"新媒彩印-增彩",
        },
        {
          id:"05106",
          name:"数媒-增彩",
        },
        {
          id:"05299",
          name:"二级企业-增彩群发",
        },
        {
          id:"05300",
          name:"二级企业-屏显群发",
        },
        {
          id:"01150",
          name:"二级企业-挂机短信群发",
        },
        {
          id:"01123",
          name:"二级企业-挂机彩漫群发",
        }
      ],

      //驳回请求参数
      rejectReq: {
        ids: [],
        type: 1,
        reason: "", //驳回原因
        submitter:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName,
        reviewer:
          sessionStorage.getItem("userInfo") &&
          JSON.parse(sessionStorage.getItem("userInfo")).sysUserName, //操作者
        // 中央平台审核分类优化——用作01150、05300驳回
        serviceIds:[],
        corpInfoId:-1,
        rejectReason:"",
        isReAudit:"1"
      },
      // 中央平台审核分类优化——增加批量驳回请求变量参数
      newRejBatch1:{
        type:2,
        rejectReason:"",
        reviewer:
                sessionStorage.getItem("userInfo") &&
                JSON.parse(sessionStorage.getItem("userInfo")).sysUserName, //操作者
        corpInfoIds:[],
        isReAudit:"1"
      },
      newRejBatch2:{
        type:1,
        reason:"",
        reviewer:
                sessionStorage.getItem("userInfo") &&
                JSON.parse(sessionStorage.getItem("userInfo")).sysUserName, //操作者
        ids:[],
        isReAudit:"1"
      },
      phonesVisible: false,
      phonesData: [],
      corpImageVisible: false,
      corpImage: "", //企业资质

      // 中央平台审核分类优化-修改其他资质初始化变量
      otherImageVisible: false,
      otherImage: [], //其他资质

      //系统驳回原因
      refuse: [],
      clickPassFlag: false,
	  clickRejectFlag: false
    };
  },
  created() {
    // this.formatData();
    this.search();
    this.refuse=JSON.parse(sessionStorage.getItem("refuseList"))
  },
  watch: {
    "rejectReq.ids"() {
      if (this.rejectReq.ids.length) {
        this.clickoff = false;
        this.typeoff = "primary";
      } else {
        this.clickoff = true;
        this.typeoff = "info";
      }
    },
    radio() {
      if (this.radio == 2 || this.radio == 1) {
        this.rejectReq.reason = "";
      }
    },
    rejectVisible() {
      if (!this.rejectVisible) {
        this.rejectReq.reason = "";
      }
    }
  },
  methods: {
    searchBtn() {
      this.searchReq.p = 1;
      this.search()
    },
    //查询请求
    search: function() {
      this.tableLoading = true;
      if (this.searchReq.timearr.length > 0) {
        this.searchReq.start = this.searchReq.timearr[0];
        this.searchReq.end = this.searchReq.timearr[1];
      } else {
        this.searchReq.start = "";
        this.searchReq.end = "";
      }
      const { timearr, ...searchReq } = this.searchReq;
      this.$http
        .post(
          `${this.proxyUrl}/entContent/corp/hangup/supper/query`,
          JSON.stringify(searchReq),
          {
            headers: {
              "X-Requested-With": "XMLHttpRequest",
              contentType: "application/json",
              charset: "utf-8"
            },
            emulateJSON: true,
            timeout: 5000
          }
        )
        .then(res => {
          this.tableLoading = false;
          var res = res.data;
          if (res.code == 0) {
            // 测试数据
            const testData = [
              // {
              //   id: 1,
              //   uuid: "20190219160831302698",
              //   serviceId: "23423423e",
              //   corpId: "11121313",
              //   corpName: "xxxsdf中国",
              //   subject: "求人须求大丈夫休",
              //   status: 0,
              //   isDelete: 0,
              //   provinceId: null,
              //   province: null,
              //   cityId: null,
              //   city: null,
              //   insertTime: 1550563712000,
              //   updateTime: 1550563712000,
              //   corpImage: "/data/xxsss/jz.jpg",
              //   frames: [
              //     {
              //       id: 24,
              //       seqNo: 1,
              //       text: "中国人民解放军s",
              //       image: "/data/uploadFile/image/200007/1552026296389.jpg",
              //       txtLocalPath:
              //         "02102_3_2019030814275932489/20190219164448/1.txt",
              //       imageLocalPath:
              //         "http://pic29.photophoto.cn/20131008/0020033001098472_b.jpg",
              //       video: "",
              //       videoLocalPath: "http://www.w3school.com.cn/i/movie.ogg"
              //     },
              //     {
              //       id: 25,
              //       seqNo: 2,
              //       text: "中国人",
              //       image: null,
              //       txtLocalPath:
              //         "02102_3_2019030814275932489/20190219164448/2.txt",
              //       imageLocalPath:
              //         "http://pic29.photophoto.cn/20131008/0020033001098472_b.jpg",
              //       video: "",
              //       videoLocalPath: "http://www.w3school.com.cn/i/movie.ogg"
              //     }
              //   ]
              // }
            ];
            this.tableData = res.data || testData;
            console.log(this.tableData)
            this.tableData.forEach(val => {
              let sensitiveWords = "";
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"03");
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"02");
              sensitiveWords = dealSensitiveWord(sensitiveWords,val,"01");

              val.sensitiveWords = sensitiveWords;
            })
            this.formatData();
            this.pageTotal = res.count;


          }
        });
    },
    formatData() {
      this.tableData.forEach(function(val, index) {
        val.insertTime =
          val.insertTime != null &&
          moment(val.insertTime).format("YYYY-MM-DD HH:mm:ss");

        // 中央平台审核分类优化——增加彩印类型判断
        switch (val.serviceId) {
          case "01136":
            val.caiyinType = "企业彩印-增彩";
            break;
          case "01144":
            val.caiyinType = "省内版企业彩印-增彩";
            break;
          case "02114":
            val.caiyinType = "新媒彩印-增彩";
            break;
          case "05106":
            val.caiyinType = "数媒-增彩";
            break;
          case "05299":
            val.caiyinType = "二级企业-增彩群发";
            break;
          case "05300":
            val.caiyinType = "二级企业-屏显群发";
            break;
          case "01150":
            val.caiyinType = "二级企业-挂机短信群发";
            break;
          case "01123":
            val.caiyinType = "二级企业-挂机彩漫群发";
            break;
          default:
            val.caiyinType = "";
            break;
        }
      });
    },

    //查询地市
    querySearchRegionList() {
      var queryRegion = {
        provinceCode: this.searchReq.provinceId
      };
      this.$http
        .post(`${this.proxyUrl}/param/regionMgt/getRegion`, queryRegion, {
          emulateJSON: true
        })
        .then(function(res) {
          this.city = res.data;
          this.searchReq.cityId = "";
        });
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    //多选框
    handleSelectionChange(val) {
      this.rejectReq.ids = [];
      this.passReq.ids = [];
      this.rejectReq.serviceIds = [];
      this.passReq.serviceIds = [];
      // 中央平台审核分类优化——多选框判断是否有属于01150、05300的值
      for (var i = 0; i < val.length; i++) {
        this.rejectReq.ids.push(val[i].id);
        this.passReq.ids.push(val[i].id);
        this.rejectReq.serviceIds.push(val[i].serviceId);
        this.passReq.serviceIds.push(val[i].serviceId);
      }
      console.log(this.rejectReq.serviceIds);
    },
    handleSizeChange(val) {
      this.searchReq.p = 1;
      //每页条数
      this.searchReq.pz = val;
      this.search();
    },
    handleCurrentChange(val) {
      //当前页
      this.searchReq.p = val;
      this.search();
    },
    //判断是批量还是单操作，发相应请求
    passCheck(val) {
      if(this.clickPassFlag) {
        return
      }
      this.clickPassFlag = true;
      //1为单，2为多
      if (this.passType == 1) {
        this.pass(val);
      } else if (this.passType == 2) {
        this.passlist();
      }
    },
    //判断是批量但是单操作，发相应请求
    rejectCheck(val) {
      if(this.clickRejectFlag) {
        return
      }
      this.clickRejectFlag = true;
      //1为单，2为多
      if (this.rejectType == 1) {
        this.reject(val);
      } else if (this.rejectType == 2) {
        this.rejectlist();
      }
    },
    //通过请求---单
    pass: function(val) {
      // 中央平台审核分类优化-判断serviceId是否为01150、05300
      // 如果是则走/enContent/audit/approve
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (val.serviceId === "01150" || val.serviceId ==="05300"){
        this.passReq.corpInfoId = val.id;
        this.passReq.type = 1;
        this.$http
          .post(
            `${this.proxyUrl}/entContent/audit/approve`,
             JSON.stringify(this.passReq)
          )
          .then(function(res) {
            loading.close();
            this.clickPassFlag = false;
            if (res.data.code == "0") {
              this.$message.success("通过成功");
              this.search();
              this.passReq.corpInfoId = -1;
            } else {
              this.$message("通过失败");
              this.passReq.corpInfoId = -1;
            }
          });
      }else {
        this.passReq.ids = [val.id];
        this.passReq.type = 2;
        this.$http
          .post(
            `${this.proxyUrl}/entContent/corp/hangup/content/pass`,
            JSON.stringify(this.passReq)
          )
          .then(function(res) {
            loading.close();
            this.clickPassFlag = false;
            if (res.data.code == "0") {
              this.$message.success("通过成功");
              this.search();
              this.passReq.ids = [];
            } else {
              this.$message("通过失败");
              this.passReq.ids = [];
            }
          });
      }
    },
    //通过请求---多
    passlist: function() {
      if (!this.passReq.ids.length < 0) {
        this.$message.error("请选择批量通过的内容");
        this.clickPassFlag = false;
        return false;
      }
      // 中央平台审核分类优化-判断serviceId是否为01150、05300
      // 如果是则走entContent/audit/approve/batch
      this.newPassBatch1.corpInfoIds = [];
      this.newPassBatch2.ids = [];
      for (var i=0;i<this.passReq.ids.length;i++) {
        if (this.passReq.serviceIds[i] ==="01150" || this.passReq.serviceIds[i] ==="05300"){
          this.newPassBatch1.corpInfoIds.push(this.passReq.ids[i]);
        }else {
          this.newPassBatch2.ids.push(this.passReq.ids[i]);
        }
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.newPassBatch1.corpInfoIds.length>0) {
        this.$http
          .post(
              `${this.proxyUrl}/entContent/audit/approve/batch`,
              JSON.stringify(this.newPassBatch1)
          )
          .then(function (res) {
            loading.close();
            this.clickPassFlag = false;
            if (res.data.code == "0") {
              let reslist = res.data.data;
              let count = 0;
              reslist.forEach(list => {
                if (list.success) {
                  count++;
                }
              });
              this.$message.success(
                      `审核通过成功记录${count}条，失败记录${reslist.length -
                      count}条，详细情况请查询企业明细`
              );
              this.search();
              this.newPassBatch1.corpInfoIds = [];
            } else {
              this.$message("通过失败");
              this.newPassBatch1.corpInfoIds = [];
            }
          });
      }
      if(this.newPassBatch2.ids.length>0){
        this.$http
          .post(
              `${this.proxyUrl}/entContent/corp/hangup/content/pass`,
              JSON.stringify(this.newPassBatch2)
          )
          .then(function(res) {
            loading.close();
            this.clickPassFlag = false;
            if (res.data.code == "0") {
              let reslist = res.data.data;
              let count = 0;
              reslist.forEach(list => {
                if (list.success) {
                  count++;
                }
              });
              this.$message.success(
                      `审核通过成功记录${count}条，失败记录${reslist.length -
                      count}条，详细情况请查询企业明细`
              );
              this.search();
              this.newPassBatch2.ids = [];
            } else {
              this.$message("通过失败");
              this.newPassBatch2.ids = [];
            }
          });
      }
      this.passReq.ids = [];
      this.passReq.serviceIds = [];
    },
    //驳回请求---单
    reject: function(val) {
      if (!this.rejectReq.reason) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        this.clickRejectFlag = false;
        return false;
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      // 中央平台审核分类优化-判断serviceId是否为01150、05300
      // 如果是则走/enContent/audit/reject
      if (val.serviceId === "01150" || val.serviceId ==="05300"){
        this.rejectReq.corpInfoId = val.id;
        this.rejectReq.type = 1;
        this.rejectReq.rejectReason = this.rejectReq.reason;
        this.$http
          .post(
              `${this.proxyUrl}/entContent/audit/reject`,
              JSON.stringify(this.rejectReq)
          )
          .then(function(res) {
            loading.close();
            this.clickRejectFlag = false;
            if (res.data.code == "0") {
              this.$message.success("驳回成功");
              this.rejectReq.corpInfoId = -1;
              this.search();
            } else {
              this.$message("驳回失败");
              this.rejectReq.corpInfoId = -1;
            }
          });
      }else{
        this.rejectReq.ids = [val.id];
        this.$http
          .post(
            `${this.proxyUrl}/entContent/corp/hangup/content/reject`,
            JSON.stringify(this.rejectReq)
          )
          .then(function(res) {
            loading.close();
            this.clickRejectFlag = false;
            if (res.data.code == "0") {
              this.$message.success("驳回成功");
              this.rejectReq.ids = [];
              this.search();
            } else {
              this.$message("驳回失败");
              this.rejectReq.ids = [];
            }
          });
      }
    },
    //驳回请求---多
    rejectlist: function() {
      if (!this.rejectReq.reason) {
        this.$message.error("请填写驳回原因");
        this.rejectVisible = true;
        this.clickRejectFlag = false;
        return false;
      }
      if (!this.rejectReq.ids.length > 0) {
        this.$message.error("请选择批量驳回的内容");
        this.clickRejectFlag = false;
        return false;
      }
      // 中央平台审核分类优化-判断serviceId是否为01150、05300
      // 如果是则走/enContent/audit/reject/batch
      this.newRejBatch1.corpInfoIds = [];
      this.newRejBatch2.ids = [];
      this.newRejBatch1.rejectReason = this.rejectReq.reason;
      this.newRejBatch2.reason = this.rejectReq.reason;
      for(var i=0;i<this.rejectReq.ids.length;i++){
        if(this.rejectReq.serviceIds[i] =="01150" || this.rejectReq.serviceIds[i] =="05300"){
          this.newRejBatch1.corpInfoIds.push(this.rejectReq.ids[i]);
        }else {
          this.newRejBatch2.ids.push(this.rejectReq.ids[i]);
        }
      }
      const loading = this.$loading({
        lock: true,
        text: 'Loading',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.newRejBatch1.corpInfoIds.length>0) {
        this.$http
          .post(
            `${this.proxyUrl}/entContent/audit/reject/batch`,
            JSON.stringify(this.newRejBatch1)
          ).then(function(res) {
            loading.close();
            this.clickRejectFlag = false;
            if (res.data.code == "0") {
              let reslist = res.data.data;
              let count = 0;
              reslist.forEach(list => {
                if (list.success == true) {
                  count++;
                }
              });
              this.$message.success(
                      `驳回成功记录${count}条，失败记录${reslist.length -
                      count}条，详细情况请查询企业明细`
              );
              this.search();
              this.newRejBatch1.corpInfoIds = [];
            } else {
              this.$message("驳回失败");
              this.newRejBatch1.corpInfoIds = [];
            }
          });
      }
      if (this.newRejBatch2.ids.length>0){
        this.$http
          .post(
            `${this.proxyUrl}/entContent/corp/hangup/content/reject`,
            JSON.stringify(this.newRejBatch2)
          ).then(function(res) {
            loading.close();
            this.clickRejectFlag = false;
            if (res.data.code == "0") {
              let reslist = res.data.data;
              let count = 0;
              reslist.forEach(list => {
                if (list.success) {
                  count++;
                }
              });
              this.$message.success(
                      `驳回成功记录${count}条，失败记录${reslist.length -
                      count}条，详细情况请查询企业明细`
              );
              this.search();
              this.newRejBatch2.ids = [];
            } else {
              this.$message("驳回失败");
              this.newRejBatch2.ids = [];
            }
          });
      }
      this.rejectReq.ids = [];
      this.rejectReq.serviceIds = [];
    },
    // 跳转详情页
    toDetail(row) {
      sessionStorage.setItem("massDetail", row && JSON.stringify(row));
      this.$router.push("../contentAudit/massDetail");
    },
    showCorpImage(corpImage) {
      console.log(corpImage);
      this.corpImage = corpImage;
      this.corpImageVisible = true;
    },
    // 中央平台审核分类优化-新增其他资质跳转函数
    showOtherImage(otherImage) {
      console.log(otherImage);
      this.otherImage = otherImage;
      this.otherImageVisible = true;
    },
    // 判断企业资质和其他资质是否有值，没有则字体颜色变成灰色
    hasCorpImage(corpImage){
      return !(corpImage == null || corpImage == "");
    },
    hasOtherImage(otherImage){
      return !(otherImage == null || otherImage.length == 0);
    }
  }
};
</script>

<style scoped>
.cardPrint >>> .el-tabs__header {
  margin-left: 24px;
}
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
  margin-top: 3%;
  background-color: blue;
}
.user-search {
  width: 94%;
  margin: 0 auto;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table {
  margin-left: 3%;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th {
  background-color: #f5f5f5;
}

.zzItemWrap {
  margin: 8px 0;
}

.zzItemLeft {
  float: left;
  width: 70px;
}

.zzItemRight >>> .el-carousel__item {
  display: flex;
  align-items: center;
  justify-content: center;
}

.carouselIndex {
  margin-left: 70px;
  text-align: center;
}

.zzWrap >>> .el-dialog__body {
  text-align: center;
}

</style>
