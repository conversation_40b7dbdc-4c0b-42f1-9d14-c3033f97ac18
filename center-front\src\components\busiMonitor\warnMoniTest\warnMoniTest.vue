<template>
    <div>
        <h1 class="user-title">推送时延预警</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <el-form :inline="true" :model="form" size="small" class="demo-form-inline">
            <el-row>
                <el-col :span="21">
                    <el-form-item label="时间">
                        <el-select v-model="form.datetype" style="width: 70px">
                            <el-option label="天" value="date"></el-option>
                            <el-option label="月" value="month"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-date-picker placeholder="开始日期" v-model="form.startDate"
                                        style="width: 150px" :picker-options="pickerOptions" :type="form.datetype"/>
                        至
                        <el-date-picker placeholder="结束日期" v-model="form.endDate"
                                        style="width: 150px" :picker-options="pickerOptions" :type="form.datetype"/>
                    </el-form-item>
                    <el-form-item label="省份">
                        <el-select v-model="form.provinceId" clearable>
                            <el-option v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search(1)">查询</el-button>
                    </el-form-item>
                </el-col>
                <!-- <el-col :span="3">
                    <el-form-item style="float:right">
                        <el-button type="primary" plain @click="download">导出excel</el-button>
                    </el-form-item>
                </el-col> -->
            </el-row>
        </el-form>
        <div>
            <el-table :data="tableData" border max-height=500 v-loading="tableLoading" class="app-tab02">
                <el-table-column prop="createTime" label="日期"  width="200"/>
                <el-table-column prop="provinceName" label="省份" width="100"/>
                <el-table-column prop="statsEriod" label="统计周期" width="100"/>
                <el-table-column prop="timeQuantum" label="统计时间段" width="140"/>
                <el-table-column prop="cycleAverageDelay" label="周期平均时延" width="140"/>
                <el-table-column prop="timeDelayThreshold" label="时延阀值" width="100"/>
                <el-table-column prop="timeDelayWarningState" label="时延警示状态" width="100"/>
                <el-table-column label="操作" fixed="right" min-width="160px">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="sendSms(scope.row)">下发短信</el-button>
                        <el-button type="text" size="small" @click="sendEmail(scope.row)">下发邮件</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="block app-pageganit">
                <el-pagination @size-change="handleSizeChange" @current-change="search"
                            :current-page="form.pageNo"
                            :page-sizes="[100, 200, 300, 400]" :page-size="form.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="form.total">
                </el-pagination>
            </div>
        </div> </div>
    </div>
</template>

<script src='./warnMoniTest.js'></script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 3%;
    }
</style>