<template>
    <div class="enterprise">
        <div class="user-titler">{{$route.name}}</div>
        <div class="contentbox">
            <!--查询条件-->
            <div style="margin-top: 15px;">
                <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" label-width="75px">
                    <el-form-item label="企业名称">
                        <el-input  v-model="searchForm.companyName" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="创建时间">
                        <el-date-picker
                                v-model="searchForm.startTime"
                                type="daterange"
                                value-format="yyyy-MM-dd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search(1)">查询</el-button>
                    </el-form-item>
                    <br>
                    <el-form-item style="margin-left: 10px;">
                        <el-button type="primary" @click="addList">新增</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="exportecl" plain>导出excel</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!--表格-->
            <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                <el-table-column prop="companyNo" label="企业编号" />
                <el-table-column prop="companyName" label="企业名称" />
                <el-table-column prop="companyIp" label="企业IP" />
                <el-table-column prop="apiName" label="API接口名称" />
                <el-table-column prop="companySecretKey" label="密钥" />
                <el-table-column prop="createTime" label="接入时间" />
                <el-table-column prop="sysRoleName" label="操作">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="editbtn(scope.row)">编辑</el-button>
                        <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                            <p style="margin: 10px;text-align:center">确定删除此企业?</p>
                            <div style="margin: 10px;text-align:center">
                                <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                            </div>
                            <div slot="reference">
                                <el-button  type="text" size="small" >删除</el-button>
                            </div>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
            <!--分页-->
            <div class="block app-pageganit" v-show="total">
                <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="total"  style="text-align: right;">
                </el-pagination>
            </div>
        </div>
        <!--新增-->
        <div>
            <el-dialog title="新增接入企业" :visible.sync="addVisible"   :close-on-click-modal="false">
                <addenterprise :addVisible="addVisible" @addList="addList"></addenterprise>
            </el-dialog>
        </div>
        <!--编辑-->
        <div>
            <el-dialog title="编辑接入企业" :visible.sync="editVisible"   :close-on-click-modal="false">
                <editenterprise :editrow="editrow" :editVisible="editVisible" @editbtn="editbtn"></editenterprise>
            </el-dialog>
        </div>
    </div>
</template>

<script>
    import addenterprise from './addenterprise';
    import editenterprise from './editenterprise';
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    import {dowandFile} from '@/util/core.js';
    export default {
        name: 'enterprise',
        data(){
            return{
                addVisible:false,
                editVisible:false,
                //查询form对象定义
                searchForm: {
                    companyName:'', //公司名称
                    startDate:'',//开始时间,
                    endDate:'', //结束时间
                    startTime:[],//时间
                    pageNo: 1, //页数
                    pageSize: 10,//每页条数
                },
                tableData:[],
                currentPage: 1,
                total:0,
                editrow:''
            }
        },
        components: {
            addenterprise,
            editenterprise
        },
        created(){
            this.search();
        },
        methods:{
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search();
            },
            handleCurrentChange(val) {
                this.searchForm.pageNo=val;
                this.search();
            },
            //查询请求
            search(pg) {
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                postHeader('queryCompany', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.apiCompanies;
                        vm.total = data.data.total;
                    }
                })
            },
            //编辑
            editbtn(row){
                let vm = this;
                vm.editVisible = !vm.editVisible;
                if(row){
                    this.editrow = row;
                }
                if(!vm.editVisible){
                    this.search()
                }
            },
            //删除
            deletebtn(row) {
                let vm = this;
                row.show = false;
                postHeader('deleteApiCompany', JSON.stringify({id:row.id})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("删除成功");
                        this.search();
                    }else{
                        vm.$message.error("删除失败");
                    }
                })
            },
            //新增
            addList(){
                let vm = this;
                vm.addVisible = !vm.addVisible;
                if(!vm.addVisible){
                    this.search()
                }
            },
            //导出
            exportecl(){
                let vm = this;
                if(this.searchForm.startTime){
                    this.searchForm.startDate = this.searchForm.startTime[0];
                    this.searchForm.endDate = this.searchForm.startTime[1];
                }else{
                    this.searchForm.startDate = '';
                    this.searchForm.endDate = '';
                }
                postDownloadHeader('exportApiCompany', JSON.stringify(this.searchForm)).then(res=>{
                    dowandFile(res.data,'API企业接入管理.xlsx');
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .user-titler{
        font-size: 20px;
        padding-left: 24px;
        height: 56px;
        line-height: 56px;
        font-family: PingFangSC-Medium;
        color: #333333;
        letter-spacing: -0.57px;
        border-bottom: 1px solid #D9D9D9;
    }
    .contentbox{
        margin:0 15px;
    }
    .el-table{
        margin-left: 0;
        margin-top: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
