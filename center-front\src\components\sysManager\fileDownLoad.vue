<template>
  <div>
    <h1 class="user-title">导出文件下载</h1>
    <div class="user-line"></div>
    <div class="app-search">
      <el-form :model="searchForm" :inline="true" class="demo-form-inline">
        <el-form-item label="文件名">
          <el-input  v-model="searchForm.fileName" placeholder="" size="small" class="app-input"></el-input>
        </el-form-item>
        <el-form-item label="文件类型">
          <el-select v-model="searchForm.taskType" multiple collapse-tags clearable placeholder="请选择" size="small" class="app-input" @change="handleSelectAll">
            <el-option  value="全选" label="全选" style="font-weight:bold;" ></el-option>
            <el-option
                v-for="item in resultList"
                :key="item.key"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <el-form :model="searchForm" :inline="true" class="demo-form-inline">

        <el-form-item label="导出时间">
          <div class="block">
            <el-date-picker
                v-model="searchForm.createTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyyMMddHHmmss" size="small">
            </el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
        </el-form-item>
<!--        <el-form-item>-->
<!--          <el-button type="primary" @click="exportExcel(searchForm)" size="small">导出excel</el-button>-->
<!--        </el-form-item>-->
      </el-form>

    </div>
    <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" border
              class="app-tab" :header-cell-class-name="tableheaderClassName"
              v-loading="dataHiddent"
              element-loading-text="数据量比较大!正拼命加载中……">
      <!--<el-table-column type="index" label="序号" width="50px"/>-->
      <el-table-column prop="taskID" label="文件编号" width="100"/>
      <el-table-column prop="fileName" label="文件名" width="100"/>
      <el-table-column prop="remark" label="备注" width="180"/>
      <el-table-column label="文件类型" width="180">
        <template slot-scope="{row}">{{row.taskType | queryTaskTypeName}}</template>
      </el-table-column>
      <el-table-column prop="exportTime" label="导出时间" width="160" :show-overflow-tooltip="true"/>
      <el-table-column label="状态" width="150" :show-overflow-tooltip="true">
        <template slot-scope="{row}">{{row.status | queryStatusName}}</template>
      </el-table-column>
      <el-table-column  label="操作下载" fixed="right">
        <template slot-scope="scope">
          <el-button v-show="scope.row.status == 2" type="text" size="small" @click="download(scope.row.filePath)" >下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div>
      <el-dialog title="操作详情" :visible.sync="outerVisible" :close-on-click-modal="false">
        <el-form :model="showForm" class="demo-form-inline" label-width="25%"  style="width: 80%;">
          <el-form-item label="操作者 : ">{{showForm.sysUseName}}</el-form-item>
          <el-form-item label="操作模块">
            {{showForm.sysUseModule}}
          </el-form-item>
          <el-form-item label="操作时间">
            {{showForm.sysUseTime}}
          </el-form-item>
          <el-form-item label="操作对象">
            {{showForm.sysUseObject}}
          </el-form-item>
          <el-form-item label="操作内容">
            {{showForm.sysUseContent}}
          </el-form-item>
          <el-form-item label="操作结果">
            {{showForm.sysUseResult}}
          </el-form-item>
          <el-form-item label="IP">
            {{showForm.sysUseIp}}
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>

    <div class="block app-pageganit">
      <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"  style="text-align: right;">
      </el-pagination>
    </div>

  </div>




</template>
<script>
import {post,postDownload} from './../../servers/httpServer.js';
import {dowandFile, formDate} from './../../util/core.js';
import axios from '../../../node_modules/axios/dist/axios'
import store from "../../store/store";
export default {
  name: 'UserList',
  data() {
    return {
      isSelectAll: false,
      //查询form对象定义
      searchForm: {
        taskID: '',
        taskType: [],
        fileName: '',
        exportStartTime: '',
        exportEndTime: '',
        createTime:'',
        pageSize:10,
        pageNum :1// 查询的页码
      },
      fileName: '',
      searchForemRsp: [],
      resultList:[
      ],
      tableData:[],
      currentPage: 1,
      total:0,

      dataHiddent:false,
      outerVisible: false,
      showForm:'',
      sysUesTypeList:[],
      moduleList:[]
    }
  },

  mounted(){
//            this.slideData(this.proxyUrl);
    isSelectAll: false;
    this.getRoleResources(); // 调用获取角色关联资源接口
  },
  filters:{
    queryTaskTypeName(value) {
      let status = '';
      if (value === 1) {
        status = '个人彩印-审核明细';
      } else if (value === 2) {
        status = '运营数据-呼叫推送统计';
      } else if (value === 3) {
        status = '初审审核记录-名片文本审核明细';
      } else if (value === 4) {
        status = '初审审核记录-名片挂彩审核明细';
      } else if (value === 5) {
        status = '初审审核记录-热线彩印审核明细';
      } else if (value === 6) {
        status = '初审审核记录-广告彩印审核明细';
      } else if (value === 7) {
        status = '初审审核记录-企业通知审核明细';
      } else if (value === 8) {
        status = '初审审核记录-热线彩印固定模板审核明细';
      } else if (value === 9) {
        status = '复审审核记录-名片文本审核明细';
      } else if (value === 10) {
        status = '复审审核记录-名片挂彩审核明细';
      } else if (value === 11) {
        status = '复审审核记录-热线彩印审核明细';
      } else if (value === 12) {
        status = '复审审核记录-广告彩印审核明细';
      } else if (value === 13) {
        status = '复审审核记录-企业通知审核明细';
      } else if (value === 14) {
        status = '复审审核记录-热线彩印固定模板审核明细';
      } else if (value === 15) {
        status = "初审审核记录-行业名片号-热线彩印审核明细"
      } else if (value === 16) {
        status = "行业名片号-热线彩印"
      }
      return status;
    },
    queryStatusName(value){
      var status = '';
      if (value == 0){
        status = '待处理';
      }else if (value == 1){
        status = '处理中';
      }else if(value == 2){
        status = '导出成功'
      }else {
        status = '导出失败'
      }
      return status;
    }
  },
  methods: {
    getRoleResources:function() {
      this.resultList=[];
      const token = sessionStorage.getItem('TOKEN');

      axios.get(`${this.proxyUrl}/sys/sysRole/getRoleResources`,{
        headers:{
          Token: token
        }
      }).then((res) => {
        const data = res.data;
        let taskTypes = [];
        data.data.list.forEach((role, index) => {
          if(role.id === 152){
            const children = (role && role.children) || [];
            children.forEach((item) => {
              let value = '';
              let label = '';
              switch (item.id) {
                case 153:
                  value = '1';
                  label = '个人彩印-审核明细';
                  break;
                case 154:
                  value = '2';
                  label = '运营数据-呼叫推送统计';
                  break;
                case 155:
                  value = '3';
                  label = '初审审核记录-名片文本审核明细';
                  break;
                case 156:
                  value = '4';
                  label = '初审审核记录-名片挂彩审核明细';
                  break;
                case 157:
                  value = '5';
                  label = '初审审核记录-热线彩印审核明细';
                  break;
                case 158:
                  value = '6';
                  label = '初审审核记录-广告彩印审核明细';
                  break;
                case 159:
                  value = '7';
                  label = '初审审核记录-企业通知审核明细';
                  break;
                case 160:
                  value = '8';
                  label = '初审审核记录-热线彩印固定模板审核明细';
                  break;

                case 161:
                  value = '9';
                  label = '复审审核记录-名片文本审核明细';
                  break;
                case 162:
                  value = '10';
                  label = '复审审核记录-名片挂彩审核明细';
                  break;
                case 163:
                  value = '11';
                  label = '复审审核记录-热线彩印审核明细';
                  break;
                case 164:
                  value = '12';
                  label = '复审审核记录-广告彩印审核明细';
                  break;
                case 165:
                  value = '13';
                  label = '复审审核记录-企业通知审核明细';
                  break;
                case 166:
                  value = '14';
                  label = '复审审核记录-热线彩印固定模板审核明细';
                  break;
                case 172:
                  value = '15';
                  label = '初审审核记录-行业名片号-热线彩印审核明细';
                case 174:
                  value = '16';
                  label = '行业名片号-热线彩印';
                break;

                  // 添加其他映射规则...
              }
              taskTypes.push({ key: item.id, value: value, label: label });
            });
          }
        });
        this.resultList = taskTypes;
        this.firstGetModulelList(this.searchForm);
      }).catch((error) => {
        console.error('获取角色关联资源信息出错：', error);
      });
    },
    //查询导出任务列表
    firstGetModulelList:function(searchForm){
      this.tableData = [];
      if(this.resultList == null || this.resultList.length == 0 ){
        return;
      }
      if(searchForm.taskType == null || searchForm.taskType.length == 0){
        const allValues = this.resultList.map((item) => item.value);
        searchForm.taskType = allValues;
      }
      axios.post(`${this.proxyUrl}/entContent/fileService/queryExportTask`,searchForm,{emulateJSON:true})
          .then((res)=>{
            this.searchForm.taskType = [];
            this.total=res.data.totalCount;
            this.tableData=res.data.exportTaskList;
          })
          .catch((error) => {
            console.error('查询导出任务列表出错：', error);
          });
    },
    //查询导出任务列表
    getModulelList:function(searchForm){
      this.tableData = [];
      if(this.resultList == null || this.resultList.length == 0){
        return;
      }
      if(searchForm.taskType == null || searchForm.taskType.length == 0){
        const allValues = this.resultList.map((item) => item.value);
        searchForm.taskType = allValues;
      }
      axios.post(`${this.proxyUrl}/entContent/fileService/queryExportTask`,searchForm,{emulateJSON:true})
          .then((res)=>{
            this.total=res.data.totalCount;
            this.tableData=res.data.exportTaskList;
          })
          .catch((error) => {
            console.error('查询导出任务列表出错：', error);
          });
    },
    download:function (filePath) {
      window.location.href=filePath
    },

    //查询请求
    search:function(searchForm){
      if(searchForm.createTime != '' && searchForm.createTime != null){
        searchForm.exportStartTime=searchForm.createTime[0];
        searchForm.exportEndTime=searchForm.createTime[1];
      }else {
        searchForm.exportStartTime=null;
        searchForm.exportEndTime=null;
      }
      if(searchForm.taskType == null || searchForm.taskType.length == 0){
        this.firstGetModulelList(searchForm)
      }else {
        this.getModulelList(searchForm)
      }
    },
    handleSelectAll(value) {

      if (value.includes('全选')) {
        this.isSelectAll = !this.isSelectAll;
        if(!this.isSelectAll){
          this.searchForm.taskType = []; // 取消选中所有选项
        }else {
          const allValues = this.resultList.map((item) => item.value); // 获取所有选项的 value
          this.searchForm.taskType = allValues; // 选中所有选项
        }
      }
    },

    //分页
    handleSizeChange(val) {
      this.searchForm.pageSize=val;
      this.search(this.searchForm);
    },
    handleCurrentChange(val) {
      this.searchForm.pageNum=val;
      this.search(this.searchForm);
    },
    tableheaderClassName({ row, rowIndex }) {
      return "table-head-th";
    },
    // 关闭弹出框
    handleClose(done) {
      done();
    }

  },
  created() {
  },
  components: {}
}


</script>
<style>
.user-title{
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line{
  margin-top: 3%;
  background-color: blue;;
}
.user-search{
  width: 100%;
  margin-top: 3%;
  margin-left: 3%;
}
.el-table .table-head-th{
  background-color: #F5F5F5;
}
</style>
