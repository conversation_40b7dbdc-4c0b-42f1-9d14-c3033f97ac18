<template>
    <div class="innerbox">
        <div class="effect">
            <div>
                <!--查询条件-->
                <div>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" label-width="60px">
                        <el-form-item label="号码">
                            <el-input  v-model="searchForm.phoneNumber" placeholder="" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="search(1)">查询</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <!--表格-->
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                    <el-table-column prop="phoneNumber" label="号码" />
                    <el-table-column prop="classBtype" label="号码描述" />
                    <el-table-column prop="provinceName" label="省份" />
                    <el-table-column prop="countyName" label="地市"/>
                    <el-table-column prop="categoryName" label="分类"/>
                    <el-table-column prop="standardTypeName" label="标准类型"/>
                    <el-table-column prop="markType" label="标记类型"/>
                    <el-table-column prop="sourceName" label="号码来源"/>
                    <el-table-column prop="markTimes" label="标记次数"/>
                    <el-table-column prop="createTime" label="生效时间"/>
                    <el-table-column prop="numType" label="所属号码库"/>
                    <el-table-column prop="deleteTime" label="被删除时间"/>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total>20">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="50"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import {postHeader} from '../../../servers/httpServer.js';
    export default {
        name: 'effect',
        data(){
            return{
                //查询form对象定义
                searchForm: {
                    numType:2, //号码库
                    phoneNumber:'',//号码
                    pageSize:50,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                provinceList:[{ provinceCode: '', provinceName: '全部'},
                    { provinceCode: '01', provinceName: '北京'},
                    { provinceCode: '02', provinceName: '天津'},
                    { provinceCode: '03', provinceName: '河北'},
                    { provinceCode: '04', provinceName: '山西'},
                    { provinceCode: '05', provinceName: '内蒙古'},
                    { provinceCode: '06', provinceName: '辽宁'},
                    { provinceCode: '07', provinceName: '吉林'},
                    { provinceCode: '08', provinceName: '黑龙江'},
                    { provinceCode: '09', provinceName: '上海'},
                    { provinceCode: '10', provinceName: '江苏'},
                    { provinceCode: '11', provinceName: '浙江'},
                    { provinceCode: '12', provinceName: '安徽'},
                    { provinceCode: '13', provinceName: '福建'},
                    { provinceCode: '14', provinceName: '江西'},
                    { provinceCode: '15', provinceName: '山东'},
                    { provinceCode: '16', provinceName: '河南'},
                    { provinceCode: '17', provinceName: '湖北'},
                    { provinceCode: '18', provinceName: '湖南'},
                    { provinceCode: '19', provinceName: '广东'},
                    { provinceCode: '20', provinceName: '海南'},
                    { provinceCode: '21', provinceName: '广西'},
                    { provinceCode: '22', provinceName: '重庆'},
                    { provinceCode: '23', provinceName: '四川'},
                    { provinceCode: '24', provinceName: '贵州'},
                    { provinceCode: '25', provinceName: '云南'},
                    { provinceCode: '26', provinceName: '陕西'},
                    { provinceCode: '27', provinceName: '甘肃'},
                    { provinceCode: '28', provinceName: '青海'},
                    { provinceCode: '29', provinceName: '宁夏'},
                    { provinceCode: '30', provinceName: '新疆'},
                    { provinceCode: '31', provinceName: '西藏'}
                ],
                tableData:[],//表数据
                currentPage: 1,
                total:0
            }
        },
        components: {

        },
        created(){
//            this.search();
        },
        methods:{
            //每页条数
            handleSizeChange(val) {
                this.searchForm.pageSize = val;
                this.search();
            },
            //当前页面
            handleCurrentChange(val) {
                this.searchForm.pageNo = val;
                this.search();
            },
            //查询请求
            search: function(pg) {
                let vm = this;
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                if(!this.searchForm.phoneNumber){
                    vm.$message.error("请输入号码查询");
                    return;
                }
                postHeader('queryNumInfo', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.queryNumInfoList;
                        vm.total = data.data.total;
                    }
                })
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
        }
    }
</script>

<style scoped>
    .innerbox{
        margin-bottom: 20px;
    }
    .el-table{
        margin: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
