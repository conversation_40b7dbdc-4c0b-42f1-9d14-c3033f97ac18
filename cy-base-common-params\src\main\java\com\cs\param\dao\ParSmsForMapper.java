package com.cs.param.dao;

import java.sql.SQLException;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.cs.param.common.ParSmsForCommon;
import com.cs.param.model.ParSmsForModel;

@Repository
public interface ParSmsForMapper {

	int updateParSmsForByPK(ParSmsForCommon common) throws SQLException;

	int deleteParSmsForByPK(ParSmsForCommon common) throws SQLException;

	int openOrCloseStatusById(ParSmsForCommon common) throws SQLException;

	ParSmsForModel queryStatusById(ParSmsForCommon common) throws SQLException;

	List<ParSmsForModel> queryPageInfo(ParSmsForCommon common) throws SQLException;

	Integer queryPageCount(ParSmsForCommon common) throws SQLException;

}
