<template>
    <div>
        <h1 class="user-title">推送规则同步查询</h1>
        <div class="user-line"></div>
        <div class="app-search">
            <el-form :model="searchForm" :rules="rules" ref="searchForm" :inline="true" class="demo-ruleForm">
                <el-form-item label="操作对象">
                    <el-input  v-model="searchForm.optObject" placeholder="操作对象"  size="small" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="号码" prop="phone">
                    <el-input  v-model="searchForm.phone" placeholder="号码"  size="small" :maxlength="11" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="创建时间">
                    <div class="block">
                        <el-date-picker
                                v-model="searchForm.createTime"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']"
                                format="yyyy-MM-dd HH:mm:ss"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                style="width:355px"
                                size="small">
                        </el-date-picker>
                    </div>
                </el-form-item>
            </el-form>
            <el-form :inline="true" class="demo-form-inline">
                <el-form-item label="操作类型">
                    <el-select v-model="searchForm.optType" clearable placeholder="请选择"  size="small" class="app-input">
                        <el-option
                                v-for="item in optTypeList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="状态">
                    <el-select v-model="searchForm.status" clearable placeholder="请选择"  size="small" class="app-input">
                        <el-option
                                v-for="item in statusList"
                                :key="item.key"
                                :label="item.value"
                                :value="item.key">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item>
                    <el-button type="primary" @click="search('searchForm')" size="small">查询</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-table  ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                   :header-cell-class-name="tableheaderClassName">
            <el-table-column prop="partitionCode" label="分区编号" width="120"/>
            <el-table-column prop="optObject" label="操作对象"  width="120"/>
            <el-table-column prop="optType" label="操作类型" :formatter="formatterType"  width="120"/>
            <el-table-column prop="phone" label="号码" width="150"/>
            <el-table-column prop="status" label="状态" :formatter="formatterStatus"  width="80"/>
            <el-table-column prop="reqUrl" label="请求URL"  width="200"  :show-overflow-tooltip="true"/>
            <el-table-column prop="handleNum" label="处理次数"  width="100"/>
            <el-table-column prop="createTime" label="创建时间"  width="180"/>
            <el-table-column prop="lastTime" label="最后处理时间"  width="180"/>
            <el-table-column prop="details" label="详情"   fixed="right" width="100">
                <template slot-scope="scope">
                    <el-button type="text" @click="dialogFormVisible = true;details=scope.row.details" size="mini">查看</el-button>
                </template>
            </el-table-column>
        </el-table>

        <div class="block app-pageganit">
        <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage"
                :page-sizes="[10, 20, 30, 50]"
                :page-size="10"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total" style="text-align: right;">
        </el-pagination>
        </div>

        <el-dialog title="详情" :visible.sync="dialogFormVisible" center>
            <span>{{details}}</span>
        </el-dialog>

    </div>


</template>
<script>
    export default {
        name: 'UserList',
        data() {
            return {
                dialogFormVisible: false,
                details:'',
                tableData:[],
                searchForm:{
                    phone:'',
                    status:'',
                    optObject:'',
                    optType:'',
                    createTime:'',
                    startTime:'',
                    endTime:'',
                    pageSize:10,
                    pageNum:1,
                },
                optTypeList:[
                    {
                        key:"",
                        value:"全部"
                    },
                    {
                        key:"0",
                        value:"新增"
                    },
                    {
                        key:"1",
                        value:"编辑"
                    },
                    {
                        key:"2",
                        value:"删除"
                    }
                ],
                statusList:[
                    {
                        key:"",
                        value:"全部"
                    },
                    {
                        key:"1",
                        value:"成功"
                    },
                    {
                        key:"2",
                        value:"失败"
                    }
                ],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
            // this.search();
        },
        methods: {
            formatterType(row, column) {
                switch(row.optType){
                    case '0':
                        return '新增';
                        break;
                    case '1':
                        return '编辑';
                        break;
                    case '2':
                        return '删除';
                        break;
                }
            },
            formatterStatus(row, column) {
                switch(row.status){
                    case '1':
                        return '成功';
                        break;
                    case '2':
                        return '失败';
                        break;
                }
            },
            //查询请求
            search(searchForm){
                this.searchForm.startTime="";
                this.searchForm.endTime="";
                if(this.searchForm.createTime != null && this.searchForm.createTime != ''){
                    console.log(this.searchForm.createTime);
                    this.searchForm.startTime=this.searchForm.createTime[0];
                    this.searchForm.endTime=this.searchForm.createTime[1];
                }
                // this.$refs[searchForm].validate((valid) => {
                //     if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/pushRules/getPushRulesPage`,this.searchForm,{emulateJSON:true})
                            .then(function(res){
                                this.currentPage=res.data.pageNum;
                                this.total=res.data.pageTotal;
                                this.tableData=res.data.datas;
                            })
                //     }
                // });

            },

            //分页
            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            },
            // 关闭弹出框
            handleClose(done) {
                this.$confirm('确认关闭？')
                        .then(_ => {
                    done();
            })
            .catch(_ => {});
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 100%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
