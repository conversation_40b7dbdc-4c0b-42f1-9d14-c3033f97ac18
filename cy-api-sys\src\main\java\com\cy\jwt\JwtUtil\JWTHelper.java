package com.cy.jwt.JwtUtil;

import com.cy.model.SysUserModel;

public class JW<PERSON>elper {
	
	
	public final static ThreadLocal<SysUserModel> CURRENT_USER = new ThreadLocal<SysUserModel>();
	
	public final static ThreadLocal<String> TOKEN = new ThreadLocal<String>();
	
	
	public static SysUserModel getSysUser(){
		return CURRENT_USER.get();
	}

	
	public static String getToken(){
        return TOKEN.get();
    }
	
}
