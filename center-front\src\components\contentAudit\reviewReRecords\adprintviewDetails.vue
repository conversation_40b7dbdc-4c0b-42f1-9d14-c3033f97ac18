<template scope="scope">
    <div>
        <div class="user-titler">广告彩印审核记录</div>
        <!--企业彩印-->
        <div class="app-search">
            <el-form :inline="true" class="demo-form-inline" label-width="70px">
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="类型">
                            <el-select
                                    v-model="searchReq.adType"
                                    class="inputWidth"
                                    placeholder="请选择"
                                    size="small"
                                    clearable
                            >
                                <el-option label="屏显" value="0"></el-option>
                                <el-option label="挂机" value="1"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="彩印ID">
                            <el-input v-model="searchReq.uuid" class="inputWidth" size="small" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="活动名称">
                            <el-input v-model="searchReq.subject" class="inputWidth" size="small" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="审核意见">
                            <el-select
                                    v-model="searchReq.status"
                                    class="inputWidth"
                                    placeholder="请选择"
                                    size="small"
                                    clearable
                            >
                                <el-option label="驳回" value="1"></el-option>
                                <el-option label="已通过" value="2"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="企业名称">
                            <el-input v-model="searchReq.submitter" class="inputWidth" size="small"
                                      clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="提交时间">
                            <div class="block">
                                <el-date-picker
                                        v-model="searchReq.timearr"
                                        type="datetimerange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :default-time="['00:00:00', '23:59:59']"
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        size="small"
                                ></el-date-picker>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="审核人">
                            <el-input v-model="searchReq.reviewer" class="inputWidth" size="small" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="审核时间">
                            <div class="block">
                                <el-date-picker
                                        v-model="searchReq.timearr1"
                                        type="datetimerange"
                                        range-separator="至"
                                        start-placeholder="开始日期"
                                        end-placeholder="结束日期"
                                        :default-time="['00:00:00', '23:59:59']"
                                        format="yyyy-MM-dd HH:mm:ss"
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        size="small"
                                ></el-date-picker>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="6">
                        <el-form-item label="屏显编号">
                            <el-input v-model="searchReq.cyContentID" size="small" class="inputWidth" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="挂机编号">
                            <el-input v-model="searchReq.cmContentID" size="small" class="inputWidth" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item>
                            <el-button type="primary" @click="searchBtn" size="small">查询</el-button>
                          <el-button type="primary" @click="propVisible=true" size="small">导出CSV</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <div>
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    :header-cell-class-name="tableheaderClassName"
            >
                <el-table-column prop="uuid" label="彩印ID" width="140"></el-table-column>
                <el-table-column prop="subject" label="活动名称" width="120"></el-table-column>
                <el-table-column label="彩印内容" width="100">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="toDetail(scope.row)">详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="reason" label="驳回原因" width="100"></el-table-column>
                <el-table-column prop="submitter" label="企业名称" width="100"></el-table-column>
                <el-table-column label="企业资质" width="100">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="showCorpImage(scope.row.corpImage)">详情</el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="submitDate" label="提交时间" width="200"></el-table-column>
                <el-table-column prop="cyContentID" label="屏显编号" width="200"></el-table-column>
                <el-table-column prop="cmContentID" label="挂机编号" width="200"></el-table-column>
                <el-table-column prop="reviewer" label="审核人" width="100"></el-table-column>
                <el-table-column prop="reviewDate" label="审核时间" width="200"></el-table-column>
                <el-table-column prop="adType" label="类型" width="100"></el-table-column>
                <el-table-column prop="status" label="审核意见" width="100"></el-table-column>
            </el-table>
            <!-- 分页 -->
            <div class="block app-pageganit">
                <el-pagination
                        v-show="pageTotal"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="searchReq.p"
                        :page-sizes="[10, 20, 30, 50]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="pageTotal"
                        style="text-align: right;"
                ></el-pagination>
            </div>
            <el-dialog title="企业资质" class="zzWrap" width="30%" :visible.sync="corpImageVisible">
                <img style="width: 100%;" :src="corpImage" alt>
            </el-dialog>
        </div>

      <el-dialog
          @open="exportClick"
          title="导出"
          :visible.sync="propVisible"
          :close-on-click-modal="false"
          width="45%">
        <el-form label-width="80px" justify="center" :model="addReq" :rules="rules" ref="addReqForm">
          <el-form-item label="文件名" prop="fileName">
            <el-input v-model="addReq.fileName" type="input" size="small"
                      placeholder="请输入文件名，不能包含特殊字符：\/:*?&quot;<>|，最多64字"
                      style="width: 90%;"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="addReq.remark" type="input" size="small" placeholder="请输入备注，长度不能超过256"
                      style="width: 90%;"></el-input>
          </el-form-item>
        </el-form>
        <div style=" margin-left: 80px; color: red;">
          导出后请到系统管理-导出文件下载对应文件
        </div>

        <div slot="footer" class="dialog-footer" style="text-align: center;">
          <el-button type="primary" @click="confirmExport">确定</el-button>
          <el-button @click="cancelExport">取消</el-button>
        </div>
      </el-dialog>
    </div>
</template>
<script>
import moment from 'moment';
import {postDownload} from './../../../servers/httpServer.js';
import {dowandFile} from './../../../util/core.js';
import axios from '../../../../node_modules/axios/dist/axios';

export default {
    data() {
        return {
          propVisible: false,
          addReq:{
            fileName: '',
            remark: ''
          },
          rules:{
            fileName: [
              { required: true, message: '请输入文件名', trigger: 'blur' },
              { pattern: /^[^\\/:*?"<>|]+$/, message: '不能包含特殊字符：\\\/:*?"<>|', trigger: 'blur' },
              { max: 64, message: '文件名不能超过64个字符',trigger: 'blur' }
            ],
            remark: [
              { max: 256, message: '备注不能超过256个字符', trigger: 'blur' }
            ]
          },
            tableLoading: false,
            corpImageVisible: false,
            corpImage: "", //企业资质
            pageTotal: 0, //总条数
            //查询条件
            searchReq: {
                cyContentID:"",//屏显内容编号
                cmContentID:"",//挂机内容编号
                adType: "",
                uuid: "", //内容id
                subject: "",
                status: "", //  #默认0 0未审核，1审核通过，2审核不通过，3,撤销';
                submitter: "", //企业名称
                timearr: [], //提交时间
                submitStart: "",
                submitEnd: "",
                reviewer: "", //审核人
                timearr1: [], //审核时间
                reviewerStart: "",
                reviewerEnd: "",
                pz: 10,
                p: 1,
                isReAudit:"1"
            },
            //数据表
            tableData: []
        };
    },
    created() {
        // this.formatData();
        this.search();
    },
    methods: {
      exportClick(){
        this.$refs.addReqForm.resetFields();
      },
      confirmExport() {
        this.$refs.addReqForm.validate(valid => {
          if (valid) {
            this.propVisible = !this.propVisible;

            if (this.searchReq.timearr) {
              this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
              this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
            } else {
              this.searchReq.submitStart = "";
              this.searchReq.submitEnd = "";
            }
            //审核时间
            if (this.searchReq.timearr1) {
              this.searchReq.reviewerStart = this.searchReq.timearr1[0] ? moment(new Date(this.searchReq.timearr1[0])).format('YYYYMMDDHHmmss') : '';
              this.searchReq.reviewerEnd = this.searchReq.timearr1[1] ? moment(new Date(this.searchReq.timearr1[1])).format('YYYYMMDDHHmmss') : '';
            } else {
              this.searchReq.reviewerStart = "";
              this.searchReq.reviewerEnd = "";
            }
            const {timearr, timearr1, ...searchReq} = this.searchReq;

            const vm = this;
            var req = {
              fileName: this.addReq.fileName,
              remark: this.addReq.remark,
              taskType: 12,
              params: JSON.stringify(this.searchReq)
            }
            axios.post(`${this.proxyUrl}/entContent/fileService/createExportTask`, req).then(function (res) {

              let data = res.data;
              if (data.code == 0) {
                vm.$message.success("系统将生成文件名为" + vm.addReq.fileName + "的文件");
              } else {
                vm.$message.error(data.msg);
              }
            });
          } else {
            return false;
          }
        });
      },
      cancelExport() {
        this.propVisible = !this.propVisible;
        this.$refs.addReqForm.resetFields();
      },
        showCorpImage(corpImage) {
            console.log(corpImage);
            this.corpImage = corpImage;
            this.corpImageVisible = true;
        },
        searchBtn() {
            this.searchReq.p = 1;
            this.search()
        },
        //查询请求
        search: function () {
            this.tableLoading = true;
            //提交时间
            if (this.searchReq.timearr) {
                this.searchReq.submitStart = this.searchReq.timearr[0] ? moment(new Date(this.searchReq.timearr[0])).format('YYYYMMDDHHmmss') : '';
                this.searchReq.submitEnd = this.searchReq.timearr[1] ? moment(new Date(this.searchReq.timearr[1])).format('YYYYMMDDHHmmss') : '';
            } else {
                this.searchReq.submitStart = "";
                this.searchReq.submitEnd = "";
            }
            //审核时间
            if (this.searchReq.timearr1) {
                this.searchReq.reviewerStart = this.searchReq.timearr1[0] ? moment(new Date(this.searchReq.timearr1[0])).format('YYYYMMDDHHmmss') : '';
                this.searchReq.reviewerEnd = this.searchReq.timearr1[1] ? moment(new Date(this.searchReq.timearr1[1])).format('YYYYMMDDHHmmss') : '';
            } else {
                this.searchReq.reviewerStart = "";
                this.searchReq.reviewerEnd = "";
            }
            const {timearr, timearr1, ...searchReq} = this.searchReq;
            this.$http
                .post(
                    `${this.proxyUrl}/entContent/corp/ad/hangup/review`,
                    JSON.stringify(searchReq),
                    {
                        headers: {
                            "X-Requested-With": "XMLHttpRequest",
                            contentType: "application/json",
                            charset: "utf-8"
                        },
                        emulateJSON: true,
                        timeout: 5000
                    }
                )
                .then(res => {
                    this.tableLoading = false;
                    var res = res.data;
                    if (res.code == 0) {
                        this.tableData = res.data;
                        this.formatData();
                        this.pageTotal = res.count;
                    } else {
                        this.tableData = [];
                        this.formatData();
                        this.pageTotal = 0;
                    }
                });
        },
        formatData() {
            this.tableData.forEach(function (val, index) {
                switch (val.status) {
                    case 0:
                        val.status = "未审核";
                        break;
                    case 1:
                        val.status = "驳回";
                        break;
                    case 2:
                        val.status = "已通过";
                        break;
                    case 3:
                        val.status = "已撤销";
                        break;
                  case 4:
                    val.status = "初审通过";
                    break;
                  case 5:
                    val.status = "初审驳回";
                    break;

                    default:
                        break;
                }
                switch (val.adType) {
                    case 0:
                        val.adType = "屏显";
                        break;
                    case 1:
                        val.adType = "挂机";
                        break;
                    default:
                        break;
                }
                val.submitDate = (val.submitDate != null) && moment(val.submitDate).format("YYYY-MM-DD HH:mm:ss");
                val.reviewDate = (val.reviewDate != null) && moment(val.reviewDate).format("YYYY-MM-DD HH:mm:ss");
            });
        },
        handleSizeChange(val) {
            this.searchReq.p = 1;
            //每页条数
            this.searchReq.pz = val;
            this.search();
        },
        handleCurrentChange(val) {
            //当前页
            this.searchReq.p = val;
            this.search();
        },
        tableheaderClassName({row, rowIndex}) {
            return "table-head-th";
        },
        toDetail(row) {
            const {adType} = row;
            sessionStorage.setItem('adType', '1')
            sessionStorage.setItem('aduuid', row.uuid)
            if (adType == '屏显') {
                sessionStorage.setItem('adDetail', row && JSON.stringify(row))
                this.$router.push({path: '/contentAudit/screenShow'})
            } else if (adType == '挂机') {
                sessionStorage.setItem('adDetail', row && JSON.stringify(row))
                sessionStorage.setItem('hangUpType', '1')
                this.$router.push({path: '/contentAudit/hangUp'})
            }
        },
    }
};
</script>
<style scoped>
.inputWidth {
    width: 160px !important;
}

.user-titler {
    font-size: 20px;
    padding-left: 24px;
    height: 56px;
    line-height: 56px;
    font-family: PingFangSC-Medium;
    color: #333333;
    letter-spacing: -0.57px;
    border-bottom: 1px solid #d9d9d9;
}

.content-title {
    margin-top: 20px;
    margin-left: 20px;
    background-color: white;
}

.content-line {
    margin-top: 20px;
}

.user-search {
    width: 94%;
    margin: 0 auto;
    margin-top: 20px;
    margin-left: 20px;
}

.el-table {
    margin-left: 3%;
    margin-top: 3%;
    border: 1px solid #ecebe9;
}
</style>
<style>
.el-table .table-head-th {
    background-color: #f5f5f5;
}
</style>
