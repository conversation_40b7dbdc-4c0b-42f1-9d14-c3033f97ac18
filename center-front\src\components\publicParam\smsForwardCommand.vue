<template>
            <div>
                <h1 class="user-title">转发短信</h1>
                <div class="user-line"></div>
                <div class="app-search">
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline">
                        <el-form-item label="模板ID">
                            <el-input  v-model="searchForm.orderNo" placeholder="" size="small" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item label="模板内容">
                            <el-input   v-model="searchForm.content" placeholder="" size="small" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item label="状态">
                            <el-select v-model="searchForm.status" clearable placeholder="请选择" size="small" class="app-input">
                                <el-option
                                        v-for="item in statusList"
                                        :key="item.key"
                                        :label="item.value"
                                        :value="item.key">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="search(searchForm)" size="small">查询</el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <el-table ref="multipleTable" :data="tableData" border tooltip-effect="dark" class="app-tab"
                          :header-cell-class-name="tableheaderClassName">
                    <el-table-column prop="orderNo" label="模板ID" width="100"/>
                    <!--<el-table-column prop="orderType" label="指令类型" />-->
                    <el-table-column prop="order" label="指令" width="120"/>
                    <el-table-column prop="partition" label="判断用户是否属于分区" :formatter="formatYes" width="100"/>
                    <el-table-column prop="continued" label="是否继续进行指令处理" :formatter="formatYes" width="100"/>
                    <el-table-column prop="partitionCode" label="分区ID" width="100"/>
                    <el-table-column prop="partitionName" label="分区名称" width="160"/>
                    <el-table-column prop="partitionType" label="分区类型" width="120"/>
                    <el-table-column prop="status" label="状态" :formatter="formatStatus" width="100"/>
                    <el-table-column prop="content" label="模板内容" width="300" :show-overflow-tooltip="true"/>
                    <!--<el-table-column prop="exaStatus" label="审核状态"/>-->
                    <el-table-column prop="remarks" label="备注" width="400"/>
                    <!--<el-table-column prop="oper" label="操作" width="200px">-->
                        <!--<template slot-scope="scope">-->
                            <!--<el-button type="text" size="small" @click="switchStatus(scope.row.id)" v-show="scope.row.status==0">启用</el-button>-->
                            <!--<el-button type="text" size="small" @click="switchStatus(scope.row.id)" v-show="scope.row.status==1">禁用</el-button>-->
                            <!--<el-button type="text" size="small" @click="showEditSms(scope.row)" >编辑</el-button>-->
                            <!--<el-button type="text" size="small" @click="delSms(scope.row)" >删除</el-button>-->
                        <!--</template>-->
                    <!--</el-table-column>-->
                </el-table>

                <div class="block app-pageganit">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="10"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>

                <div>
                    <el-dialog title="编辑系统用户" :visible.sync="editVisible" :before-close="handleClose">
                        <el-form :model="editForm" :rules="rules" ref="editForm" class="demo-form-inline" label-width="25%"  style="width: 80%">
                            <el-form-item label="指令编号 : " prop="sysUserName">
                                {{editForm.orderNo}}
                            </el-form-item>
                            <el-form-item label="指令 : " prop="sysStaffName">
                                {{editForm.order}}
                            </el-form-item>
                            <el-form-item label="指令类型 : " prop="sysUserCompany">
                                {{editForm.orderType}}
                            </el-form-item>
                            <el-form-item label="指令内容 : " prop="content">
                                <el-input v-model="editForm.content" type="textarea"></el-input>
                            </el-form-item>
                            <el-form-item label="描述 : ">
                                <el-input v-model="editForm.remarks" ></el-input>
                            </el-form-item>
                        </el-form>
                        <div slot="footer" class="dialog-footer" style="text-align: right;">
                            <el-button @click="editVisible = false">取 消</el-button>
                            <el-button type="primary" @click="editSms('editForm')">确 定</el-button>
                        </div>
                    </el-dialog>
                </div>

                <el-dialog
                        title="提示"
                        :visible.sync="propVisible"
                        width="30%"
                        :before-close="handleCloseConfirm">
                    <span>{{propMsg}}</span>
  <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="propVisible = false">确 定</el-button>
  </span>
                </el-dialog>

            </div>
</template>
<script>
    export default {
        data() {
            return {
                activeName:'smsForwardCommand',
                editVisible: false,
                propVisible:false,
                propMsg:'',
                searchForm: {
                    orderNo:'',
                    orderType:'',
                    provinceCode:'',
                    status:'',//0：关闭，1开启
                    content:'',
                    pageSize:10,// 每页显示条数
                    pageNum :1
                },
                //编辑form对象定义
                editForm: {
                    id:12312,
                    orderNo:'',
                    orderType:'',
                    order:'',
                    content:'',
                    remarks:''
                },
                //查询或删除form
                queryOrDelForm:{
                    id:'',
                },

                statusList:[
                    {
                        key:'',
                        value:'全部'
                    },
                    {
                        key:'0',
                        value:'禁用'
                    },{
                        key:'1',
                        value:'启用'
                    }
                ],
                provinceList:[],
                rules: {
                    content: [
                        { required: true, message: '请输入短信模板内容', trigger: 'blur' },
                    ]
                },
                tableData:[],
                currentPage: 1,
                total:0
            }
        },

        mounted(){
            this.getProvinceList();
            this.search();
//            this.slideData(this.proxyUrl);
        },
        methods: {
            //开关开启关闭
            switchStatus(id){
                this.queryOrDelForm.id = id;
                this.$http.post(`${this.proxyUrl}/param/smsMgt/openOrCloseSmsFor`,this.queryOrDelForm,{emulateJSON:true}).then(function(res){
                    if(res.data.resStatus == 0){
                        this.propMsg = '修改成功！';
                        this.propVisible=true;
                        this.editVisible = false;
                        this.search(this.searchForm);
                    }else{
                        this.propMsg = '修改失败!'+ res.data.resText;;
                        this.propVisible=true;
                    }
                })
            },
            formatStatus: function (row, column, cellValue) {
                if (cellValue === "1"){
                    return '启用';
                }else if (cellValue === "0"){
                    return '禁用';
                }
            },
            formatYes: function (row, column, cellValue) {
                if (cellValue === "1"){
                    return '是';
                }else if (cellValue === "0"){
                    return '否';
                }
            },
            //获取省份list
            getProvinceList:function(){
                this.$http.get(`${this.proxyUrl}/param/regionMgt/getProvince`).then(function(res){
                    this.provinceList=res.data;
                })
            },
            //查询列表请求
            search:function(searchForm){
                this.$http.post(`${this.proxyUrl}/param/smsMgt/getSmsForPage`,searchForm,{emulateJSON:true}).then(function(res){
                    this.currentPage=res.data.pageNum;
                    this.total=res.data.pageTotal;
                    this.tableData=res.data.datas;
                })
            },
            // 弹出修改框
            showEditSms(editForm){
                this.editForm = Object.assign({},editForm);
                this.editVisible = true;
            },
            //修改请求
            editSms(editForm){
                this.$refs[editForm].validate((valid) => {
                    if (valid) {
                        this.$http.post(`${this.proxyUrl}/param/smsMgt/updateParSmsFor`,this.editForm,{emulateJSON:true}).then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '修改成功！';
                                this.propVisible=true;
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '修改失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
                    } else {
                        return false;
            }
            });
            },
            //删除请求
            delSms(role){
                this.queryOrDelForm.id = role.id;
                this.$http.post(`${this.proxyUrl}/param/smsMgt/deleteParSmsFor`,this.queryOrDelForm,{emulateJSON:true})
                        .then(function(res){
                            if(res.data.resStatus == 0){
                                this.propMsg = '删除成功！';
                                this.propVisible=true;
                                this.editVisible = false;
                                this.search(this.searchForm);
                            }else{
                                this.propMsg = '删除失败!'+ res.data.resText;;
                                this.propVisible=true;
                            }
                        })
            },

            handleSizeChange(val) {
                this.searchForm.pageSize=val;
                this.search(this.searchForm);
            },
            handleCurrentChange(val) {
                this.searchForm.pageNum=val;
                this.search(this.searchForm);
            },
            handleClick(activeName){
                this.$router.push(activeName);
            },

            // 关闭弹出框
            handleClose(done) {
                done();
            },
            // 关闭提示框
            handleCloseConfirm(done) {
                done();
            },
            tableheaderClassName({ row, rowIndex }) {
                return "table-head-th";
            }

        },
        created() {
        },
        components: {}
    }


</script>
<style>
    .def-tab {
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .el-tabs__item{
        font-size: 24px;
        -webkit-margin-before: 0.67em;
        -webkit-margin-after: 0.67em;
        -webkit-margin-start: 0px;
        -webkit-margin-end: 0px;
        font-weight: bold;
    }

    .user-title{
        margin-top: 8px;
        margin-left: 16px;
        background-color: white;
    }
    .user-line{
        margin-top: 8px;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 3%;
        margin-left: 3%;
    }
    .el-table .table-head-th{
        background-color: #F5F5F5;
    }
</style>
