<template>
    <div class="editprovincial">
        <div class="content">
            <el-form :model="addForm" ref="addForm" :rules="rules" class="demo-form-inline app-form-item" label-width="35%" size="small" style="width: 80%">
                <el-form-item label="省份：" prop="provinceId">
                    <span>{{addForm.provinceName}}</span>
                    <!--<el-select v-model="addForm.provinceId" placeholder="请选择" class="app-input">-->
                        <!--<el-option-->
                                <!--v-for="item in provinceList"-->
                                <!--:key="item.provinceCode"-->
                                <!--:label="item.provinceName"-->
                                <!--:value="item.provinceCode">-->
                        <!--</el-option>-->
                    <!--</el-select>-->
                </el-form-item>
                <el-form-item label="标记次数：" prop="markTimes">
                    <el-input v-model="addForm.markTimes" class="app-input"></el-input>
                </el-form-item>
                <el-form-item label="分类：" prop="categoryid">
                    <el-checkbox-group v-model="category">
                        <el-checkbox :label="1" key="1">诈骗</el-checkbox>
                        <el-checkbox :label="2" key="2">黄页</el-checkbox>
                        <el-checkbox :label="3" key="3">标记</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="标准类型：" prop="sysUserName">
                    <el-checkbox-group v-model="standardType">
                        <el-checkbox v-for="item in StandardType" :label="item.standardTypeId" :key="item.standardTypeId">{{item.standardTypeName}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="号码来源：" prop="categoryid">
                    <el-checkbox-group v-model="source">
                        <el-checkbox v-for="item in sourceList" :label="item.sourceId" :key="item.sourceId">{{item.sourceName}}</el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
            </el-form>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="submit" size="small">取 消</el-button>
            <el-button type="primary" @click="addProvincial" size="small">确 定</el-button>
        </div>
    </div>
</template>

<script>
    import {postHeader,postDownloadHeader} from '@/servers/httpServer.js';
    export default {
        name: 'editprovincial',
        data(){
            return{
                name:'',
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                addForm:{
                    provinceId:'',//身份id
                    provinceName:'',
                    categoryId:'',//分类id
                    standardTypeId:'',//标准类型id
                    sourceId:'',//来源ID
                    markTimes:'',//标记次数
                },
                rules: {
//                    provinceId: [
//                        { required: true, message: '请选择省份', trigger: 'change' }
//                    ]
                },
                searchForm:{
                    pageSize:10000,
                    pageNo:1
                },
                category:[],
                standardType:[],
                source:[],
                StandardType:[],//标准类型
                sourceList :[],//号码来源
            }
        },
        components: {},
        props:['editrow','editVisible'],
        created(){
            this.qusourceList();
            this.searchStand();
            this.searchID();
        },
        watch:{
            editVisible(){
                if(!this.editVisible){

                }
            }
        },
        methods:{
            submit(){
                this.$emit('editbtn');
            },
            //查看号码来源
            qusourceList(){
                let vm = this;
                postHeader('querySource',JSON.stringify({categoryId:0})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.sourceList = data.data.sourceList;
                    }
                })
            },
            //查询标准类型
            searchStand() {
                let vm = this;
                postHeader('queryStandardType',JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.StandardType = data.data.standardTypeList;
                    }
                })
            },
            //根据ID查询详情
            searchID() {
                let vm = this;
                postHeader('getProvRuleById',JSON.stringify({id:this.editrow.id})).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        this.addForm = data.data.provinceRule;
                        if(this.addForm.categoryId){
                            this.category = this.addForm.categoryId.split(',');
                            this.category = this.category.map(item=>{
                                return Number(item);
                            })
                        }
                        if(this.addForm.standardTypeId){
                            this.standardType = this.addForm.standardTypeId.split(',');
                            this.standardType = this.standardType.map(item=>{
                                return Number(item);
                            })
                        }
                        if(this.addForm.sourceId){
                            this.source = this.addForm.sourceId.split(',');
                            this.source = this.source.map(item=>{
                                return Number(item);
                            })
                        }
                    }
                })
            },
            //编辑
            addProvincial(){
                let vm = this;
                if(this.category){
                    this.addForm.categoryId = this.category.join(',');
                }
                if(this.standardType){
                    this.addForm.standardTypeId = this.standardType.join(',');
                }
                if(this.source){
                    this.addForm.sourceId = this.source.join(',');
                }
                if(this.addForm.categoryId.indexOf('3') != -1){
                    vm.$message.error("填写标记次数");
                    return;
                }
                postHeader('updateProvRule',JSON.stringify(this.addForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.$message.success("编辑成功");
                    }else{
                        vm.$message.error("编辑失败");
                    }
                    this.submit();
                })
            },
        }
    }
</script>

<style scoped>
    .content{
        width: 640px;
        margin: 20px auto;
    }
    .content1{
        text-align: center;
    }
    .el-checkbox,.el-checkbox+.el-checkbox{
        margin-left: 0px !important;
        margin-right: 20px;
    }
</style>
