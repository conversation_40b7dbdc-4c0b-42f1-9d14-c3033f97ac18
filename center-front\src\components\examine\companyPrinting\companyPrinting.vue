<template>
  <div>
    <h1 class="user-title">彩印盒</h1>
    <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="彩印盒ID" >
        <el-input v-model="print.svNumber"  placeholder="彩印盒ID" size="small" :maxlength="32" style="width:170px;" clearable></el-input>
      </el-form-item>
      <el-form-item label="彩印盒名称" >
        <el-input v-model="print.svName" placeholder="彩印盒名称" size="small" :maxlength="50" style="width:170px;" clearable></el-input>
      </el-form-item>
        <el-form-item label="提交时间">
            <el-date-picker v-model="dateTime"
                type="datetimerange"
                range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              style="width:355px" size="small"
              />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit(print)" size="small">查询</el-button>
        </el-form-item>
    </el-form>
      </div>
<div>
    <el-table
    v-loading="tableLoading"
    :data="tableData"
    border tooltip-effect="dark"
    class="app-tab"
    :header-cell-class-name="tableheaderClassName">
    <el-table-column
      prop="svNumber"
      label="彩印盒ID"
      width="240">
    </el-table-column>
    <el-table-column
      prop="svName"
      label="彩印盒名称"
      width="200">
    </el-table-column>
    <el-table-column
            label="彩印盒内容"
            width="200">
      <template slot-scope="scope">
        <el-button type="text" @click="detailVisible=true;rowData=scope.row;">内容详情</el-button>
      </template>
    </el-table-column>
    <el-table-column
      prop="groupName"
      label="内容分类"
      width="200">
    </el-table-column>
    <el-table-column
      prop="labelName"
      label="内容标签"
      width="200"
      :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column
      prop="submitTime"
      label=" 提交时间"
      width="200">
    </el-table-column>
    <el-table-column
      prop="svStatusName"
      label="审核意见"
      width="120">
    </el-table-column>
    <el-table-column
      prop="assessor"
      label="审核人"
      width="200">
    </el-table-column>
    <el-table-column
      prop="svCause"
      label="驳回原因"
      width="200"
      :show-overflow-tooltip="true">
    </el-table-column>
    <el-table-column
      prop="svSssesstime"
      label="审核时间"
      width="200">
    </el-table-column>
    <el-table-column
      fixed="right"
      label="操作"
      width="120">
      <template slot-scope="scope">
        <el-button type="text" @click="openModify(scope.row)">编辑</el-button>
        <el-button type="text" @click="delVisible=true;delRequest.svId=scope.row.svId;" >删除</el-button>
      </template>
    </el-table-column>
  </el-table>

  <el-dialog
      width="30%"
      title="删除"
      :visible.sync="delVisible"
      :close-on-click-modal="false"
      append-to-body>
    <span style="font-size:20px;">确认删除彩印盒？</span>
    <div slot="footer" class="dialog-footer" style="text-align: right;">
      <el-button @click="delVisible = false" size="small">取 消</el-button>
      <el-button type="primary" @click="delVisible = false;deltr();" size="small">确 定</el-button>
    </div>
  </el-dialog>

<!-- 弹窗 -->    
      <el-dialog
          width="40%"
          title="内容详情"
          :visible.sync="detailVisible"
          :close-on-click-modal="false"
          append-to-body>
        <el-row>
          <el-col :span="12">彩印ID</el-col>
          <el-col :span="12">内容</el-col>
        </el-row>
        <div style="height:300px;overflow:auto;">
          <el-row v-if="rowData.content1">
            <el-col :span="12"><div v-html="rowData.svNumber+'1'"></div></el-col>
            <el-col :span="12"><div v-html="highLight(rowData.sensitiveWords,rowData.content1)"></div></el-col>
          </el-row>

         <el-row v-if="rowData.content2">
            <el-col :span="12"><div v-html="rowData.svNumber+'2'"></div></el-col>
            <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content2)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content3">
            <el-col :span="12"><div v-html="rowData.svNumber+'3'"></div></el-col>
            <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content3)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content4">
            <el-col :span="12"><div v-html="rowData.svNumber+'4'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content4)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content5">
            <el-col :span="12"><div v-html="rowData.svNumber+'5'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content5)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content6">
            <el-col :span="12"><div v-html="rowData.svNumber+'6'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content6)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content7">
            <el-col :span="12"><div v-html="rowData.svNumber+'7'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content7)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content8">
            <el-col :span="12"><div v-html="rowData.svNumber+'8'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content8)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content9">
            <el-col :span="12"><div v-html="rowData.svNumber+'9'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content9)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content10">
            <el-col :span="12"><div v-html="rowData.svNumber+'10'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content10)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content11">
            <el-col :span="12"><div v-html="rowData.svNumber+'11'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content11)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content12">
            <el-col :span="12"><div v-html="rowData.svNumber+'12'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content12)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content13">
            <el-col :span="12"><div v-html="rowData.svNumber+'13'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content13)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content14">
            <el-col :span="12"><div v-html="rowData.svNumber+'14'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content14)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content15">
            <el-col :span="12"><div v-html="rowData.svNumber+'15'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content15)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content16">
            <el-col :span="12"><div v-html="rowData.svNumber+'16'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content16)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content17">
            <el-col :span="12"><div v-html="rowData.svNumber+'17'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content17)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content18">
            <el-col :span="12"><div v-html="rowData.svNumber+'18'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content18)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content19">
            <el-col :span="12"><div v-html="rowData.svNumber+'19'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content19)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content20">
            <el-col :span="12"><div v-html="rowData.svNumber+'20'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content20)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content21">
            <el-col :span="12"><div v-html="rowData.svNumber+'21'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content21)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content22">
            <el-col :span="12"><div v-html="rowData.svNumber+'22'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content22)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content23">
            <el-col :span="12"><div v-html="rowData.svNumber+'23'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content23)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content24">
            <el-col :span="12"><div v-html="rowData.svNumber+'24'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content24)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content25">
            <el-col :span="12"><div v-html="rowData.svNumber+'25'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content25)"></div></el-col>
          </el-row>
           <el-row v-if="rowData.content26">
            <el-col :span="12"><div v-html="rowData.svNumber+'26'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content26)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content27">
            <el-col :span="12"><div v-html="rowData.svNumber+'27'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content27)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content28">
            <el-col :span="12"><div v-html="rowData.svNumber+'28'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content28)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content29">
            <el-col :span="12"><div v-html="rowData.svNumber+'29'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content29)"></div></el-col>
          </el-row>
          <el-row v-if="rowData.content30">
             <el-col :span="12"><div v-html="rowData.svNumber+'30'"></div></el-col>
             <el-col :span="12"><div  v-html="highLight(rowData.sensitiveWords,rowData.content30)"></div></el-col>
          </el-row>
        </div>
        <div slot="footer" style="text-align: right;">
          <el-button type="primary" @click="detailVisible = false">确 定</el-button>
        </div>
      </el-dialog>

  <div class="block app-pageganit">
  <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableData.pageNum"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="10"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageTotal">
  </el-pagination>
  </div>


<!-- 编辑 -->
    <el-dialog title="编辑彩印盒内容" :visible.sync="updateVisible" :close-on-click-modal="false" top="1vh">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label=" 彩印盒ID" :label-width="formLabelWidth">
          {{updateRequest.svNumber}}
        </el-form-item>
        <el-form-item label=" 彩印盒名称" :label-width="formLabelWidth">
          <el-input  v-model="updateRequest.svName" :disabled="true"></el-input>
        </el-form-item>

        <el-form-item label="内容分类" :label-width="formLabelWidth">
          <el-select v-model="updateRequest.groupId" placeholder="请选择" size="small">
            <el-option
                v-for="item in groupData"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容标签" :label-width="formLabelWidth">
            <el-button type="info" @click="visible = true" size="small">添加标签</el-button>
            &nbsp;{{ckLabelNames}}
             
            <el-dialog title="添加标签" :visible.sync="visible" :close-on-click-modal="false"  append-to-body>
              <div style="height:300px;overflow:auto;">
                <el-form class="demo-form-inline" label-width="160px" justify="center">
                  <el-form-item>
                    <el-checkbox-group v-model="ckLabelIdArray">
                      <el-checkbox  @change="labelChange(item.liName)" v-for="item in labelData" :label="item.liId" :key="item.liName" style="display:inline-block;margin-left:30px;">{{item.liName}} </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-form>
              </div>
              <div slot="footer" class="dialog-footer" style="text-align: right;">
                <span>{{"已选"+ckLabelIdArray.length+"个标签"}}</span>
                <el-button @click="visible = false" size="small">取消</el-button>
                <el-button type="primary" @click="subCheckLabel();visible = false" size="small">确认</el-button>
              </div>
            </el-dialog>   
        </el-form-item>
        <div style="height:180px;overflow:auto;">
          <el-form-item label="彩印内容1" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent1" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容2" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent2" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容3" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent3" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容4" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent4" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容5" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent5" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容6" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent6" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容7" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent7" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容8" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent8" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容9" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent9" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容10" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent10" clearable :maxlength="50"></el-input>
          </el-form-item>

          <el-form-item label="彩印内容11" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent11" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容12" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent12" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容13" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent13" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容14" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent14" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容15" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent15" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容16" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent16" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容17" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent17" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容18" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent18" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容19" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent19" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容20" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent20" clearable :maxlength="50"></el-input>
          </el-form-item>

          <el-form-item label="彩印内容21" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent21" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容22" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent22" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容23" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent23" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容24" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent24" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容25" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent25" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容26" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent26" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容27" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent27" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容28" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent28" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容29" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent29" clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容30" :label-width="formLabelWidth">
              <el-input type="text" v-model="updateRequest.csPkgContent30" clearable :maxlength="50"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="updateVisible = false;">关闭</el-button>
          <el-button @click="updatetr();" type="primary">保存</el-button>
      </div>  
    </el-dialog>
  </div>
  </div>

</template>
<script>
import {formDate} from './../../../util/core.js';
  export default {
    name: 'UserList',
    data() {
      return {
        tableLoading: false,
        pageTotal:0,
        detailVisible:false,
        updateVisible: false,
        visible:false,
        delVisible:false,
        updateData:[],
        rowData: [],
        // tableData: [{
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '文本彩印',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }, {
        //   date: '彩印盒',
        //   name: '王小虎',
        //   province: '上海',
        //   city: '普陀区',
        //   address: '上海市普陀区金沙江路 1518 弄',
        //   zip: 200333
        // }],
        formLabelWidth:'200px',
        groupData: [], //内容分类变量
        labelData: [], //内容标签变量
        ckLabelNames:'',
        ckLabelNameArray: [],
        ckLabelIdArray:[],
        tableData:[],
        value6: '',
        dateTime:[],
        print:{
            svNumber:"",
            svName:"",
            startTime:"",
            endTime:"",
            pageNum:1,
            pageSize:10
        },
        updateRequest: {
          svId: "",
          svNumber:"",
          svName:"",
          csPkgContent1:"",
          csPkgContent2:"",
          csPkgContent3:"",
          csPkgContent4:"",
          csPkgContent5:"",
          csPkgContent6:"",
          csPkgContent7:"",
          csPkgContent8:"",
          csPkgContent9:"",
          csPkgContent10:"",
          csPkgContent11:"",
          csPkgContent12:"",
          csPkgContent13:"",
          csPkgContent14:"",
          csPkgContent15:"",
          csPkgContent16:"",
          csPkgContent17:"",
          csPkgContent18:"",
          csPkgContent19:"",
          csPkgContent20:"",
          csPkgContent21:"",
          csPkgContent22:"",
          csPkgContent23:"",
          csPkgContent24:"",
          csPkgContent25:"",
          csPkgContent26:"",
          csPkgContent27:"",
          csPkgContent28:"",
          csPkgContent29:"",
          csPkgContent30:"",
          groupId:"",
          labelId:""
        },
        delRequest: {
          svId: ""
        }

      }
    },
    mounted() {
      this.groupDatas();
      this.labelDatas();
    },
    methods: {
      highLight(sensitiveWords,svCard){
        if(!sensitiveWords || !svCard){
          return svCard;
        }else{
          var specalKey=sensitiveWords.replace(new RegExp(',','g'),'|');
          var scCard=svCard.replace(new RegExp(specalKey,'g'),`<span style="color:red">$&</span>`);
          return scCard;
        }
      },
      groupDatas: function() {
        this.$http
          .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
            emulateJSON: true
          })
          .then(function(res) {
            this.groupData = res.data;
          });
      },
      //内容标签选项请求
      labelDatas: function() {
        this.$http
          .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
            emulateJSON: true
          })
          .then(function(res) {
            this.labelData = res.data;
          });
      },
      labelChange: function(labelName) {
        let checkedBoolean=false;
        for(let i=0;i<this.ckLabelNameArray.length;i++){
          if(labelName===this.ckLabelNameArray[i]){
            this.ckLabelNameArray.splice(i,1);
            checkedBoolean=true;
          }
        }
        if(!checkedBoolean){
          this.ckLabelNameArray.push(labelName);
        }
      },
      subCheckLabel:function(){
        this.ckLabelNames = this.ckLabelNameArray.join(',');
        this.updateRequest.labelId = this.ckLabelIdArray.join(',');
      },
      openModify: function(row) {
          this.updateVisible = true;
          this.ckLabelNames = row.labelName;
          this.ckLabelNameArray = row.labelName.split(",");
          this.ckLabelIdArray = row.svPkgLabelId.split(",");
          for (let i=0;i<this.ckLabelIdArray.length;i++){
              this.ckLabelIdArray[i] = parseInt(this.ckLabelIdArray[i]);
          }
          this.updateRequest.svId = row.svId;
          this.updateRequest.svNumber = row.svNumber;
          this.updateRequest.svName = row.svName;
          this.updateRequest.groupId = row.svPkgGroupId;
          this.updateRequest.labelId = row.svPkgLabelId;
          this.updateRequest.csPkgContent1 = row.content1;
          this.updateRequest.csPkgContent2 = row.content2;
          this.updateRequest.csPkgContent3 = row.content3;
          this.updateRequest.csPkgContent4 = row.content4;
          this.updateRequest.csPkgContent5 = row.content5;
          this.updateRequest.csPkgContent6 = row.content6;
          this.updateRequest.csPkgContent7 = row.content7;
          this.updateRequest.csPkgContent8 = row.content8;
          this.updateRequest.csPkgContent9 = row.content9;
          this.updateRequest.csPkgContent10 = row.content10;
          this.updateRequest.csPkgContent11 = row.content11;
          this.updateRequest.csPkgContent12 = row.content12;
          this.updateRequest.csPkgContent13 = row.content13;
          this.updateRequest.csPkgContent14 = row.content14;
          this.updateRequest.csPkgContent15 = row.content15;
          this.updateRequest.csPkgContent16 = row.content16;
          this.updateRequest.csPkgContent17 = row.content17;
          this.updateRequest.csPkgContent18 = row.content18;
          this.updateRequest.csPkgContent19 = row.content19;
          this.updateRequest.csPkgContent20 = row.content20;
          this.updateRequest.csPkgContent21 = row.content21;
          this.updateRequest.csPkgContent22 = row.content22;
          this.updateRequest.csPkgContent23 = row.content23;
          this.updateRequest.csPkgContent24 = row.content24;
          this.updateRequest.csPkgContent25 = row.content25;
          this.updateRequest.csPkgContent26 = row.content26;
          this.updateRequest.csPkgContent27 = row.content27;
          this.updateRequest.csPkgContent28 = row.content28;
          this.updateRequest.csPkgContent29 = row.content29;
          this.updateRequest.csPkgContent30 = row.content30;
      },
      handleSizeChange(val) {
        this.print.pageSize = val;
        if(this.dateTime && this.dateTime.length>0){
          this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          this.print.startTime='';
          this.print.endTime='';
        }
        this.tableLoading=true;
        this.$http
          .post(`${this.proxyUrl}/content/auditBox/getMyTaskPkg`, JSON.stringify(this.print))
          .then(function(res) {
            this.tableData = res.data.datas;
            this.tableLoading=false;
          });
      },
      handleCurrentChange(val) {
        this.print.pageNum = val;
        if(this.dateTime && this.dateTime.length>0){
          this.print.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          this.print.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          this.print.startTime='';
          this.print.endTime='';
        }
        this.tableLoading=true;
        this.$http
          .post(`${this.proxyUrl}/content/auditBox/getMyTaskPkg`, JSON.stringify(this.print))
          .then(function(res) {
            this.tableData = res.data.datas;
            this.tableLoading=false;
          });
      },
      onSubmit(val) {
        const h = this.$createElement;
        if(this.dateTime && this.dateTime.length>0){
          val.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
          val.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
        }else{
          val.startTime='';
          val.endTime='';
        }
        this.tableLoading=true;
        this.$http.post(`${this.proxyUrl}/content/auditBox/getMyTaskPkg`, val).then((response) => {
              this.tableData=response.data.datas;
              this.pageTotal=response.data.pageTotal;
              this.tableLoading=false;
          }, (response) => {
               this.$notify.error({
                title: '错误',
                message: '查询异常'
              });
               this.tableLoading=false;
          });
      },
      updatetr: function() {
        if(!this.updateRequest.groupId){
          this.$message("彩印盒分类不能为空");
          return;
        }
        if(!this.updateRequest.labelId || !this.updateRequest.labelId.trim()){
          this.$message("彩印盒标签不能为空");
          return;
        }
        if(!this.updateRequest.csPkgContent1 || !this.updateRequest.csPkgContent1.trim()){
          this.$message("彩印内容1不能为空");
          return;
        }
        if(!this.updateRequest.csPkgContent2 || !this.updateRequest.csPkgContent2.trim()){
          this.$message("彩印内容2不能为空");
          return;
        }
        if(this.updateRequest.csPkgContent1){
        this.updateRequest.csPkgContent1 = this.updateRequest.csPkgContent1.trim();
      }
      if(this.updateRequest.csPkgContent2){
        this.updateRequest.csPkgContent2 = this.updateRequest.csPkgContent2.trim();
      }
      if(this.updateRequest.csPkgContent3){
        this.updateRequest.csPkgContent3 = this.updateRequest.csPkgContent3.trim();
      }
      if(this.updateRequest.csPkgContent4){
        this.updateRequest.csPkgContent4 = this.updateRequest.csPkgContent4.trim();
      }
      if(this.updateRequest.csPkgContent5){
        this.updateRequest.csPkgContent5 = this.updateRequest.csPkgContent5.trim();
      }
      if(this.updateRequest.csPkgContent6){
        this.updateRequest.csPkgContent6 = this.updateRequest.csPkgContent6.trim();
      }
      if(this.updateRequest.csPkgContent7){
        this.updateRequest.csPkgContent7 = this.updateRequest.csPkgContent7.trim();
      }
      if(this.updateRequest.csPkgContent8){
        this.updateRequest.csPkgContent8 = this.updateRequest.csPkgContent8.trim();
      }
      if(this.updateRequest.csPkgContent9){
        this.updateRequest.csPkgContent9 = this.updateRequest.csPkgContent9.trim();
      }
      if(this.updateRequest.csPkgContent10){
        this.updateRequest.csPkgContent10 = this.updateRequest.csPkgContent10.trim();
      }
      if(this.updateRequest.csPkgContent11){
        this.updateRequest.csPkgContent11 = this.updateRequest.csPkgContent11.trim();
      }
      if(this.updateRequest.csPkgContent12){
        this.updateRequest.csPkgContent12 = this.updateRequest.csPkgContent12.trim();
      }
      if(this.updateRequest.csPkgContent13){
        this.updateRequest.csPkgContent13 = this.updateRequest.csPkgContent13.trim();
      }
      if(this.updateRequest.csPkgContent14){
        this.updateRequest.csPkgContent14 = this.updateRequest.csPkgContent14.trim();
      }
      if(this.updateRequest.csPkgContent15){
        this.updateRequest.csPkgContent15 = this.updateRequest.csPkgContent15.trim();
      }
      if(this.updateRequest.csPkgContent16){
        this.updateRequest.csPkgContent16 = this.updateRequest.csPkgContent16.trim();
      }
      if(this.updateRequest.csPkgContent17){
        this.updateRequest.csPkgContent17 = this.updateRequest.csPkgContent17.trim();
      }
      if(this.updateRequest.csPkgContent18){
        this.updateRequest.csPkgContent18 = this.updateRequest.csPkgContent18.trim();
      }
      if(this.updateRequest.csPkgContent19){
        this.updateRequest.csPkgContent19 = this.updateRequest.csPkgContent19.trim();
      }
      if(this.updateRequest.csPkgContent20){
        this.updateRequest.csPkgContent20 = this.updateRequest.csPkgContent20.trim();
      }
      if(this.updateRequest.csPkgContent21){
        this.updateRequest.csPkgContent21 = this.updateRequest.csPkgContent21.trim();
      }
      if(this.updateRequest.csPkgContent22){
        this.updateRequest.csPkgContent22 = this.updateRequest.csPkgContent22.trim();
      }
      if(this.updateRequest.csPkgContent23){
        this.updateRequest.csPkgContent23 = this.updateRequest.csPkgContent23.trim();
      }
      if(this.updateRequest.csPkgContent24){
        this.updateRequest.csPkgContent24 = this.updateRequest.csPkgContent24.trim();
      }
      if(this.updateRequest.csPkgContent25){
        this.updateRequest.csPkgContent25 = this.updateRequest.csPkgContent25.trim();
      }
      if(this.updateRequest.csPkgContent26){
        this.updateRequest.csPkgContent26 = this.updateRequest.csPkgContent26.trim();
      }
      if(this.updateRequest.csPkgContent27){
        this.updateRequest.csPkgContent27 = this.updateRequest.csPkgContent27.trim();
      }
      if(this.updateRequest.csPkgContent28){
        this.updateRequest.csPkgContent28 = this.updateRequest.csPkgContent28.trim();
      }
      if(this.updateRequest.csPkgContent29){
        this.updateRequest.csPkgContent29 = this.updateRequest.csPkgContent29.trim();
      }
      if(this.updateRequest.csPkgContent30){
        this.updateRequest.csPkgContent30 = this.updateRequest.csPkgContent30.trim();
      }

        this.updateVisible = false;
        this.tableLoading=true;
        this.$http.post(`${this.proxyUrl}/content/auditBox/modMyAuditPkg`, this.updateRequest, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success('修改成功');
            this.tableLoading=false;
            this.onSubmit(this.print);
          } else if (res.data.resStatus == "1") {
            this.$message.error("修改失败 "+res.data.resText);
            this.tableLoading=false;
          }
        });
      },
      deltr: function() {
      this.$http
        .post(`${this.proxyUrl}/content/auditBox/delMyAuditPkg`, this.delRequest, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.onSubmit(this.print);
          } else if (res.data.resStatus == "1") {
            this.$message.error("删除失败 "+res.data.resText);
          }
        });
    },
    tableheaderClassName({ row, rowIndex }) {
        return "table-head-th";
    }
    },
    created() {
    },
    components: {}
  }


</script>
<style>
.user-title{
    margin-top: 3%;
    margin-left: 3%;
    background-color: white;
  }
.user-line{
  margin-top: 3%;
  background-color: blue;;
}

  .user-search{
    width: 100%;
   margin-top: 3%;
    margin-left: 3%;
  }
  #printingtable{
    margin-top: 3%;
  }
  .el-pagination{
    margin-left:270px;
  }
  .el-table {
  margin: 0 auto;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
    background-color: #F5F5F5;
}
</style>
