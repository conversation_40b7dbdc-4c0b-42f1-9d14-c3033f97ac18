package com.cy.audit.model;

import java.util.List;

public class AuditDataResult {
    private String dataId; // 数据ID
    private String dataType; // 数据类型
    private String data; // 审核内容
    private String notifyName; // 回调名称，需要在主管理系统配置
    private String status; // 任务状态，详见表
    private String label; // 审核结果标签，REJECT(违规)、REVIEW(疑似)、正常(NORMAL)；文化机审：REJECT(违规)、正常(NORMAL)
    private List<ResultItem> resultItems; // 审核结果明细，仅当status=PROCESSING/SUCCESS时存在，文化机审无此数据
    private String code; // 错误码，仅当status=FAILED时存在，详见异常信息
    private String message; // 错误信息，仅当status=FAILED时存在，详见异常信息
    private String startTime; // 审核开始时间，示例：2022-07-19 04:03:43
    private String finishTime; // 审核完成时间，示例：2022-07-19 04:06:43
    private String taskId; // 百度机审任务ID
    private String preset; // 送审数据指定的模版
    private String labelPrompt; // 人审风险标签提示语
    private String auditChannel; // 机审渠道，baidu：百度，biz：文化
    private String description; // 描述

    public AuditDataResult() {

    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getNotifyName() {
        return notifyName;
    }

    public void setNotifyName(String notifyName) {
        this.notifyName = notifyName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<ResultItem> getResultItems() {
        return resultItems;
    }

    public void setResultItems(List<ResultItem> resultItems) {
        this.resultItems = resultItems;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(String finishTime) {
        this.finishTime = finishTime;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getPreset() {
        return preset;
    }

    public void setPreset(String preset) {
        this.preset = preset;
    }

    public String getLabelPrompt() {
        return labelPrompt;
    }

    public void setLabelPrompt(String labelPrompt) {
        this.labelPrompt = labelPrompt;
    }

    public String getAuditChannel() {
        return auditChannel;
    }

    public void setAuditChannel(String auditChannel) {
        this.auditChannel = auditChannel;
    }

    @Override
    public String toString() {
        return "AuditDataResult{" +
                "dataId='" + dataId + '\'' +
                ", dataType='" + dataType + '\'' +
                ", data='" + data + '\'' +
                ", notifyName='" + notifyName + '\'' +
                ", status='" + status + '\'' +
                ", label='" + label + '\'' +
                ", resultItems=" + resultItems +
                ", code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", startTime='" + startTime + '\'' +
                ", finishTime='" + finishTime + '\'' +
                ", taskId='" + taskId + '\'' +
                ", preset='" + preset + '\'' +
                ", labelPrompt='" + labelPrompt + '\'' +
                ", auditChannel='" + auditChannel + '\'' +
                '}';
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
