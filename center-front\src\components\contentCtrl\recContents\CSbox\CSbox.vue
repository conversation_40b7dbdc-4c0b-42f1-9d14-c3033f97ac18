<template scope="scope">
    <div v-loading="loading">
        <h1 class="user-title">彩印盒</h1>
        <div class="user-line"></div>
        <div class="app-search">
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item label="彩印盒名称">
              <el-input v-model="searchReq.csPackageName" size="small" clearable class="app-input"></el-input>
            </el-form-item>
            <el-form-item label="彩印盒ID">
              <el-input v-model="searchReq.csPackageNo" size="small" clearable class="app-input"></el-input>
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="searchReq.csPackageStatus" placeholder="请选择" size="small" clearable class="app-input">
                <el-option
                    v-for="item in statusData"
                    :key="item.csStatusNo"
                    :label="item.csStatusName"
                    :value="item.csStatusNo">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="提交时间" style="margin-left:14px;">
                <div class="block">
                      <el-date-picker v-model="dateTime"
                          type="datetimerange"
                          range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        :default-time="['00:00:00', '23:59:59']"
                        style="width:355px"
                        size="small"
                        />
                </div>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search(searchReq)" size="small" class="app-bnt">查询</el-button>
            </el-form-item>
          </el-form>
          <el-form :inline="true" class="demo-form-inline">
            <el-form-item>
              <el-button type="primary" @click="locationHref('/creatBox')" size="small">新建彩印盒</el-button>
              <el-button type="primary" @click="importTextCY()" size="small">导入彩印盒</el-button>
              <el-button type="primary" plain @click="exportPkg()" size="small">导出excel</el-button>
              <!-- <el-button type="info" @click="delAny" size="small">批量删除</el-button> -->
            </el-form-item>
          </el-form>
        </div>
        <div>
            <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    border
                    class="app-tab"
                    @selection-change="handleSelectionChange"
                    :header-cell-class-name="tableheaderClassName">
                <el-table-column
                    type="selection"
                    width="55">
                </el-table-column>
                <el-table-column
                        prop="csPkgNumber"
                        label="彩印盒ID"
                        width="240">
                </el-table-column>
                <el-table-column
                        prop="csPkgName"
                        label="彩印盒名称"
                        width="150">
                </el-table-column>
                <el-table-column
                        prop="csPkgGroupName"
                        label="内容分类"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="csPkgLabelName"
                        label="内容标签"
                        width="200"
                        :show-overflow-tooltip="true">
                </el-table-column>
                <el-table-column
                        label="彩印盒内容"
                        width="120">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click="detailVisible=true;rowData=scope.row;">内容详情</el-button>
                  </template>
                </el-table-column>
                 <el-table-column
                        prop="csStatusName"
                        label="状态"
                        width="100">
                </el-table-column>
                <el-table-column
                        prop="csSubmitTime"
                        label="提交时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="assessor"
                        label="审核人"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="assessTime"
                        label="通过时间"
                        width="200">
                </el-table-column>
                <el-table-column
                        prop="useNumber"
                        label="使用人数"
                        width="100">
                </el-table-column>
                <el-table-column
                    fixed="right"
                    label="操作"
                    width="120">
                  <template slot-scope="scope">
                    <el-button v-show="(scope.row.csPkgStatus==1 & scope.row.tempId!=5)" @click="openModify(scope.row)" type="text" size="small">编辑</el-button>
                    <el-button v-show="(scope.row.tempId!=5)" @click="delVisible=true;delRequest.pkgIds.length = 0;delRequest.pkgIds[0] = scope.row.csPkgId;" type="text" size="small">删除</el-button>
                  </template>
                </el-table-column>
            </el-table>
            <!-- 弹窗 -->
             <el-dialog
                width="40%"
                title="内容详情"
                :visible.sync="detailVisible"
                :close-on-click-modal="false"
                append-to-body>
                <el-row>
                  <el-col :span="12">彩印ID</el-col>
                  <el-col :span="12">内容</el-col>
                </el-row>
                <div style="height:300px;overflow:auto;">
                  <el-row v-if="rowData.csPkgContent1">
                    <el-col :span="12"><div v-html="rowData.csPkgNumber+'1'"></div></el-col>
                    <el-col :span="12"><div v-html="rowData.csPkgContent1"></div></el-col>
                  </el-row>

                   <el-row v-if="rowData.csPkgContent2">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'2'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.csPkgContent2"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent3">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'3'"></div></el-col>
                      <el-col :span="12"><div  v-html="rowData.csPkgContent3"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent4">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'4'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent4"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent5">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'5'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent5"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.csPkgContent6">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'6'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent6"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent7">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'7'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent7"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent8">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'8'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent8"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent9">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'9'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent9"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent10">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'10'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent10"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.csPkgContent11">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'11'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent11"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent12">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'12'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent12"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent13">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'13'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent13"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent14">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'14'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent14"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent15">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'15'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent15"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.csPkgContent16">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'16'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent16"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent17">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'17'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent17"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent18">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'18'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent18"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent19">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'19'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent19"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent20">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'20'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent20"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent21">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'21'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent21"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent22">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'22'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent22"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent23">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'23'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent23"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent24">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'24'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent24"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent25">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'25'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent25"></div></el-col>
                    </el-row>
                     <el-row v-if="rowData.csPkgContent26">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'26'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent26"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent27">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'27'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent27"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent28">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'28'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent28"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent29">
                      <el-col :span="12"><div v-html="rowData.csPkgNumber+'29'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent29"></div></el-col>
                    </el-row>
                    <el-row v-if="rowData.csPkgContent30">
                       <el-col :span="12"><div v-html="rowData.csPkgNumber+'30'"></div></el-col>
                       <el-col :span="12"><div  v-html="rowData.csPkgContent30"></div></el-col>
                    </el-row>
                  </div>
          <div slot="footer" style="text-align: right;">
            <!-- <el-button @click="detailVisible = false">取 消</el-button> -->
            <el-button type="primary" @click="detailVisible = false">确 定</el-button>
          </div>
        </el-dialog>
        <el-dialog
            width="30%"
            title="删除"
            :visible.sync="delVisible"
            :close-on-click-modal="false"
            append-to-body>
          <span style="font-size:20px;">内容删除后，用户当前设置将失效，请确认？</span>
          <div slot="footer" class="dialog-footer" style="text-align: right;">
            <el-button @click="delVisible = false" size="small">取 消</el-button>
            <el-button type="primary" @click="delVisible = false;deltr();" size="small">确 定</el-button>
          </div>
        </el-dialog>

            <!-- 分页 -->
            <div class="block app-pageganit">
            <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="tableData.pageNum"
                    :page-sizes="[10, 20, 30, 50]"
                    :page-size="10"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="pageTotal"  style="text-align: right;">
            </el-pagination>
        </div>
        </div>
      <el-dialog title="导入彩印盒" :visible.sync="importCYHiddent" :close-on-click-modal="false" width="50%">
          <el-form :inline="true"  class="demo-form-inline">
              <el-form-item label="彩印盒文件">
                  <el-input v-model="fileName" disabled="" size="small"></el-input>
              </el-form-item>

              <el-form-item>
                <el-upload
                        class="upload-demo"
                        ref="upload"
                        action=""
                        :auto-upload='false'
                        :on-change="handleChange"
                        :on-remove="handleRemove"
                        accept=".xls, .xlsx"
                         :show-file-list='false'>
                      <el-button  type="primary" size="small">上传excel表</el-button>
                  </el-upload>
              </el-form-item>
              <el-form-item>
                  <a href="javaScript:;"  @click="getTemp()">下载模板文件</a>
              </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer" style="text-align: right;">
              <el-button @click="clearUpload();"  size="small">取 消</el-button>
              <el-button type="primary" @click="submitForm()"  size="small">确 定</el-button>
          </div>
      </el-dialog>




        <!-- 编辑 -->
    <el-dialog title="编辑彩印盒内容" :visible.sync="updateVisible" :close-on-click-modal="false" top="1vh">
      <el-form :inline="true" class="demo-form-inline">
        <el-form-item label=" 彩印盒ID" :label-width="formLabelWidth">
          {{updateRequest.csPackageNo}}
        </el-form-item>
        <el-form-item label=" 彩印盒名称" :label-width="formLabelWidth">
          <el-input  v-model="updateRequest.csPackageName" size="small" :disabled="true"></el-input>
        </el-form-item>

        <el-form-item label="内容分类" :label-width="formLabelWidth">
          <el-select v-model="updateRequest.csPkgGroupId" placeholder="请选择" size="small">
            <el-option
                v-for="item in groupData"
                :key="item.groupId"
                :label="item.groupName"
                :value="item.groupId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="内容标签" :label-width="formLabelWidth">
            <el-button type="info" @click="visible = true" size="small">添加标签</el-button>
            &nbsp;{{ckLabelNames}}
             
            <el-dialog title="添加标签" :visible.sync="visible" :close-on-click-modal="false"  append-to-body>
              <div style="height:300px;overflow:auto;">
                <el-form class="demo-form-inline" label-width="160px" justify="center">
                  <el-form-item>
                    <el-checkbox-group v-model="ckLabelIdArray">
                      <el-checkbox  @change="labelChange(item.liName)" v-for="item in labelData" :label="item.liId" :key="item.liName" style="display:inline-block;margin-left:30px;">{{item.liName}} </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-form>
              </div>
              <div slot="footer" class="dialog-footer" style="text-align: right;">
                <span>{{"已选"+ckLabelIdArray.length+"个标签"}}</span>
                <el-button @click="visible = false" size="small">取消</el-button>
                <el-button type="primary" @click="subCheckLabel();visible = false" size="small">确认</el-button>
              </div>
            </el-dialog>   
        </el-form-item>
        <div style="height:180px;overflow:auto;">
          <el-form-item label="彩印内容1" :label-width="formLabelWidth">
			  <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent1' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容2" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent2' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容3" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent3' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容4" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent4' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容5" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent5' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容6" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent6' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容7" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent7' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容8" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent8' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容9" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent9' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容10" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent10' clearable :maxlength="50"></el-input>
          </el-form-item>

          <el-form-item label="彩印内容11" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent11' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容12" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent12' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容13" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent13' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容14" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent14' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容15" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent15' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容16" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent16' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容17" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent17' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容18" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent18' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容19" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent19' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容20" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent20' clearable :maxlength="50"></el-input>
          </el-form-item>

          <el-form-item label="彩印内容21" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent21' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容22" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent22' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容23" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent23' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容24" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent24' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容25" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent25' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容26" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent26' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容27" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent27' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容28" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent28' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容29" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent29' clearable :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="彩印内容30" :label-width="formLabelWidth">
              <el-input type="textarea" style="margin-bottom:5px;" v-model='updateRequest.csPkgContent30' clearable :maxlength="50"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: right;">
          <el-button @click="updateVisible = false;" size="small" >关闭</el-button>
          <el-button @click="updatetr();" type="primary" size="small" >保存</el-button>
      </div>  
    </el-dialog>
    </div>

  
</template>
<script>
import {postDownload,post} from './../../../../servers/httpServer.js';
 import {dowandFile,formDate} from './../../../../util/core.js';
export default {
  name: "CSbox",
  data() {
    return {
      loading:false,
      tableLoading: false,
      pageTotal: 0,
      statusData: [{csStatusNo:1,csStatusName:'已上架'},{csStatusNo:4,csStatusName:'已下架'}], //状态下拉框选项
      rowData: [],
      checked: [],
      importCYHiddent:false,
      file:"",//上传文件流
      fileName:'',//文件名
      detailVisible: false,
      updateVisible: false,
      visible:false,
      delVisible: false,
      //表格数据
      tableData: [],
      multipleSelection: [],
      dateTime:[],
      //请求数据
      //查询
      searchReq: {
        csPackageName: "",
        csPackageId: "",
        csPackageNo: "",
        csPackageStatus: "",
        startTime: "",
        endTime: "",
        pageSize: 10,
        pageNum: 1
      },
      //导出
      exportReq: {
        csPackageName: "",
        csPackageId: "",
        csPackageNo: "",
        csPackageStatus: "",
        startTime: "",
        endTime: "",
        pageSize: 0,
        pageNum: 1
      },
      //编辑
      formLabelWidth:'200px',
      groupData: [], //内容分类变量
      labelData: [], //内容标签变量
      ckLabelNames:'',
      ckLabelNameArray: [],
      ckLabelIdArray:[],
      updateRequest: {
        csPackageId:"",
        csPackageNo:"",
        csPackageName:"",
        csPkgGroupId:"",
        csPkgLabelId:"",
        csPkgContent1:"",
        csPkgContent2:"",
        csPkgContent3:"",
        csPkgContent4:"",
        csPkgContent5:"",
        csPkgContent6:"",
        csPkgContent7:"",
        csPkgContent8:"",
        csPkgContent9:"",
        csPkgContent10:"",
        csPkgContent11:"",
        csPkgContent12:"",
        csPkgContent13:"",
        csPkgContent14:"",
        csPkgContent15:"",
        csPkgContent16:"",
        csPkgContent17:"",
        csPkgContent18:"",
        csPkgContent19:"",
        csPkgContent20:"",
        csPkgContent21:"",
        csPkgContent22:"",
        csPkgContent23:"",
        csPkgContent24:"",
        csPkgContent25:"",
        csPkgContent26:"",
        csPkgContent27:"",
        csPkgContent28:"",
        csPkgContent29:"",
        csPkgContent30:""
      },
      delRequest: {
        pkgIds: []
      },
      //下拉栏数据
      sildeData: []
    };
  },
  mounted() {
    // this.statusSilde();
    this.groupDatas();
    this.labelDatas();
  },
  methods: {
    clearUpload(){
      this.importCYHiddent = false;
      this.$refs.upload.clearFiles();
      this.file='';
    },
    check(vm) {
     if (!vm.searchReq.startTime) {
        //vm.$message.error("开始日期不能为空");
        return true;
      }
      if (!vm.searchReq.endTime) {
        //vm.$message.error("结束时间不能为空");
        return true;
      }
      if (vm.searchReq.startTime > vm.searchReq.endTime) {
        vm.$message.error("开始时间不能晚于结束时间");
        return false;
      }
      return true;
    },
    //状态选项请求
    // statusSilde: function() {
    //   this.$http
    //     .get(`${this.proxyUrl}/content/csText/getCsStatus`, { emulateJSON: true })
    //     .then(function(res) {
    //       if (res.data.resStatus == "0") {
    //         for(let i=0;i<res.data.datas.length;i++){
    //           if(res.data.datas[i].csStatusName=="待上架"){
    //             res.data.datas.splice(i,1);
    //           }
    //         }
    //         this.statusData = res.data.datas;
    //       } else if (res.data.resStatus == "1") {
    //         console.log("请求失败");
    //       }
    //     });
    // },
    //表格多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.delRequest.pkgIds.length = 0;
      for (var i = 0; i < val.length; i++) {
        this.delRequest.pkgIds.push(val[i].csPkgId);
      }
    },
    //获取文件信息
    handleChange(file,fileList){
      if(this.file){
        this.$message.warning('只能上传一个文件');
        fileList.length = 1;
        return;
      }
      this.file=file.raw;
      this.fileName=file.name;
    },
    handleRemove(file,fileList){
       this.file='';
      this.fileName='';
    },
    //导入彩印盒文件
    importTextCY(){
      this.importCYHiddent=true;
      this.fileName='';
      if(this.$refs.upload){
        this.$refs.upload.clearFiles();
      }
    },
    //提交批量用户信息
    submitForm(){
      if(!this.file){
        this.$message({
          message:'请上传excel文件',
          type:'warning'
        })
        return;
      }
      let formData=new FormData();
      formData.append('file',this.file);
      post(`/content/csPackage/uploadPackage`,formData).then(res=>{
        if(res.data.resStatus == "0"){
          this.$message({
            message:'提交成功',
            type:'success'
          })
          this.importCYHiddent=false;
        }else{
          this.$message.error(res.data.resText);
        }
      })
    },
    //下载模板文件
    getTemp(){
      window.open(`${this.proxyUrl}/content/csPackage/getTemplate`);
    },
    //请求--------------------------
    //查询请求
    search: function(searchReq) {
      if(this.dateTime && this.dateTime.length>0){
        searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        searchReq.startTime='';
        searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/getCsPackage`, searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          // if(res.data.datas.length==0){
          //   this.$message('查无数据');
          //   this.pageTotal = res.data.pageTotal;
          //   this.tableLoading=false;
          //   return false;
          // }
          this.tableLoading = false;
          this.tableData = res.data.datas;
          this.pageTotal = res.data.pageTotal;
        });
    },
    //导出excel
    exportPkg(){
      this.loading = true;
      if(!this.check(this)){
        return false;
      }
      if(this.searchReq.startTime==null){
        this.searchReq.startTime='';
      }
      if(this.searchReq.endTime==null){
        this.searchReq.endTime='';
      }
      //this.tableLoading=true;
      this.exportReq.csPackageName = this.searchReq.csPackageName;
      this.exportReq.csPackageId = this.searchReq.csPackageId;
      this.exportReq.csPackageStatus = this.searchReq.csPackageStatus;
      this.exportReq.startTime = this.searchReq.startTime;
      this.exportReq.endTime = this.searchReq.endTime;
      this.exportReq.csPackageNo = this.searchReq.csPackageNo;
      postDownload(`/content/csPackage/exportCsPkg`,this.exportReq).then(res=>{
                    dowandFile(res.data,'彩印盒.xlsx');
                    this.loading = false;
       })
    },
    groupDatas: function() {
        this.$http
          .post(`${this.proxyUrl}/content/csGroup/getCsGroup`, this.request, {
            emulateJSON: true
          })
          .then(function(res) {
            this.groupData = res.data;
          });
      },
      //内容标签选项请求
      labelDatas: function() {
        this.$http
          .post(`${this.proxyUrl}/content/csLabel/getCsLabel`, this.request, {
            emulateJSON: true
          })
          .then(function(res) {
            this.labelData = res.data;
          });
      },
      labelChange: function(labelName) {
        let checkedBoolean=false;
        for(let i=0;i<this.ckLabelNameArray.length;i++){
          if(labelName===this.ckLabelNameArray[i]){
            this.ckLabelNameArray.splice(i,1);
            checkedBoolean=true;
          }
        }
        if(!checkedBoolean){
          this.ckLabelNameArray.push(labelName);
        }
      },
      subCheckLabel:function(){
        this.ckLabelNames = this.ckLabelNameArray.join(',');
        this.updateRequest.csPkgLabelId = this.ckLabelIdArray.join(',');
      },
    //编辑
    openModify: function(row) {
      this.updateVisible = true;
      this.ckLabelNames = row.csPkgLabelName;

      if(row.csPkgLabelName != null && row.csPkgLabelName != ''){
        this.ckLabelNameArray = row.csPkgLabelName.split(",");
      }

      if(row.csPkgLabelId != null && row.csPkgLabelId != ''){
        this.ckLabelIdArray = row.csPkgLabelId.split(",");
      }
      
      for (let i=0;i<this.ckLabelIdArray.length;i++){
          this.ckLabelIdArray[i] = parseInt(this.ckLabelIdArray[i]);
      }
      this.updateRequest.csPackageId = row.csPkgId;
      this.updateRequest.csPackageNo = row.csPkgNumber;
      this.updateRequest.csPackageName = row.csPkgName;
      this.updateRequest.csPkgGroupId = row.csPkgGroupId;
      this.updateRequest.csPkgLabelId = row.csPkgLabelId;
      this.updateRequest.csPkgContent1 = row.csPkgContent1;
      this.updateRequest.csPkgContent2 = row.csPkgContent2;
      this.updateRequest.csPkgContent3 = row.csPkgContent3;
      this.updateRequest.csPkgContent4 = row.csPkgContent4;
      this.updateRequest.csPkgContent5 = row.csPkgContent5;
      this.updateRequest.csPkgContent6 = row.csPkgContent6;
      this.updateRequest.csPkgContent7 = row.csPkgContent7;
      this.updateRequest.csPkgContent8 = row.csPkgContent8;
      this.updateRequest.csPkgContent9 = row.csPkgContent9;
      this.updateRequest.csPkgContent10 = row.csPkgContent10;
      this.updateRequest.csPkgContent11 = row.csPkgContent11;
      this.updateRequest.csPkgContent12 = row.csPkgContent12;
      this.updateRequest.csPkgContent13 = row.csPkgContent13;
      this.updateRequest.csPkgContent14 = row.csPkgContent14;
      this.updateRequest.csPkgContent15 = row.csPkgContent15;
      this.updateRequest.csPkgContent16 = row.csPkgContent16;
      this.updateRequest.csPkgContent17 = row.csPkgContent17;
      this.updateRequest.csPkgContent18 = row.csPkgContent18;
      this.updateRequest.csPkgContent19 = row.csPkgContent19;
      this.updateRequest.csPkgContent20 = row.csPkgContent20;
      this.updateRequest.csPkgContent21 = row.csPkgContent22;
      this.updateRequest.csPkgContent22 = row.csPkgContent22;
      this.updateRequest.csPkgContent23 = row.csPkgContent23;
      this.updateRequest.csPkgContent24 = row.csPkgContent24;
      this.updateRequest.csPkgContent25 = row.csPkgContent25;
      this.updateRequest.csPkgContent26 = row.csPkgContent26;
      this.updateRequest.csPkgContent27 = row.csPkgContent27;
      this.updateRequest.csPkgContent28 = row.csPkgContent28;
      this.updateRequest.csPkgContent29 = row.csPkgContent29;
      this.updateRequest.csPkgContent30 = row.csPkgContent30;
    },
    //编辑请求
    updatetr : function() {
      if(!this.updateRequest.csPkgGroupId){
        this.$message("彩印盒分类不能为空");
        return;
      }
      if(!this.updateRequest.csPkgLabelId || !this.updateRequest.csPkgLabelId.trim()){
        this.$message("彩印盒标签不能为空");
        return;
      }
      if(!this.updateRequest.csPkgContent1 || !this.updateRequest.csPkgContent1.trim()){
        this.$message("彩印内容1不能为空");
        return;
      }
      if(!this.updateRequest.csPkgContent2 || !this.updateRequest.csPkgContent2.trim()){
        this.$message("彩印内容2不能为空");
        return;
      }
      if(this.updateRequest.csPkgContent1){
        this.updateRequest.csPkgContent1 = this.updateRequest.csPkgContent1.trim();
      }
      if(this.updateRequest.csPkgContent2){
        this.updateRequest.csPkgContent2 = this.updateRequest.csPkgContent2.trim();
      }
      if(this.updateRequest.csPkgContent3){
        this.updateRequest.csPkgContent3 = this.updateRequest.csPkgContent3.trim();
      }
      if(this.updateRequest.csPkgContent4){
        this.updateRequest.csPkgContent4 = this.updateRequest.csPkgContent4.trim();
      }
      if(this.updateRequest.csPkgContent5){
        this.updateRequest.csPkgContent5 = this.updateRequest.csPkgContent5.trim();
      }
      if(this.updateRequest.csPkgContent6){
        this.updateRequest.csPkgContent6 = this.updateRequest.csPkgContent6.trim();
      }
      if(this.updateRequest.csPkgContent7){
        this.updateRequest.csPkgContent7 = this.updateRequest.csPkgContent7.trim();
      }
      if(this.updateRequest.csPkgContent8){
        this.updateRequest.csPkgContent8 = this.updateRequest.csPkgContent8.trim();
      }
      if(this.updateRequest.csPkgContent9){
        this.updateRequest.csPkgContent9 = this.updateRequest.csPkgContent9.trim();
      }
      if(this.updateRequest.csPkgContent10){
        this.updateRequest.csPkgContent10 = this.updateRequest.csPkgContent10.trim();
      }
      if(this.updateRequest.csPkgContent11){
        this.updateRequest.csPkgContent11 = this.updateRequest.csPkgContent11.trim();
      }
      if(this.updateRequest.csPkgContent12){
        this.updateRequest.csPkgContent12 = this.updateRequest.csPkgContent12.trim();
      }
      if(this.updateRequest.csPkgContent13){
        this.updateRequest.csPkgContent13 = this.updateRequest.csPkgContent13.trim();
      }
      if(this.updateRequest.csPkgContent14){
        this.updateRequest.csPkgContent14 = this.updateRequest.csPkgContent14.trim();
      }
      if(this.updateRequest.csPkgContent15){
        this.updateRequest.csPkgContent15 = this.updateRequest.csPkgContent15.trim();
      }
      if(this.updateRequest.csPkgContent16){
        this.updateRequest.csPkgContent16 = this.updateRequest.csPkgContent16.trim();
      }
      if(this.updateRequest.csPkgContent17){
        this.updateRequest.csPkgContent17 = this.updateRequest.csPkgContent17.trim();
      }
      if(this.updateRequest.csPkgContent18){
        this.updateRequest.csPkgContent18 = this.updateRequest.csPkgContent18.trim();
      }
      if(this.updateRequest.csPkgContent19){
        this.updateRequest.csPkgContent19 = this.updateRequest.csPkgContent19.trim();
      }
      if(this.updateRequest.csPkgContent20){
        this.updateRequest.csPkgContent20 = this.updateRequest.csPkgContent20.trim();
      }
      if(this.updateRequest.csPkgContent21){
        this.updateRequest.csPkgContent21 = this.updateRequest.csPkgContent21.trim();
      }
      if(this.updateRequest.csPkgContent22){
        this.updateRequest.csPkgContent22 = this.updateRequest.csPkgContent22.trim();
      }
      if(this.updateRequest.csPkgContent23){
        this.updateRequest.csPkgContent23 = this.updateRequest.csPkgContent23.trim();
      }
      if(this.updateRequest.csPkgContent24){
        this.updateRequest.csPkgContent24 = this.updateRequest.csPkgContent24.trim();
      }
      if(this.updateRequest.csPkgContent25){
        this.updateRequest.csPkgContent25 = this.updateRequest.csPkgContent25.trim();
      }
      if(this.updateRequest.csPkgContent26){
        this.updateRequest.csPkgContent26 = this.updateRequest.csPkgContent26.trim();
      }
      if(this.updateRequest.csPkgContent27){
        this.updateRequest.csPkgContent27 = this.updateRequest.csPkgContent27.trim();
      }
      if(this.updateRequest.csPkgContent28){
        this.updateRequest.csPkgContent28 = this.updateRequest.csPkgContent28.trim();
      }
      if(this.updateRequest.csPkgContent29){
        this.updateRequest.csPkgContent29 = this.updateRequest.csPkgContent29.trim();
      }
      if(this.updateRequest.csPkgContent30){
        this.updateRequest.csPkgContent30 = this.updateRequest.csPkgContent30.trim();
      }
      
      this.updateVisible = false;
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/modCsPackage`, this.updateRequest, {
          emulateJSON: true
        })
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success('编辑成功');
            this.search(this.searchReq);
          } else if (res.data.resStatus == "1") {
            this.$message.error('编辑失败');
          }
        });
    },

    //单行删除
    deltr: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/delCsPackage`, JSON.stringify(this.delRequest))
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.search(this.searchReq);
            this.delRequest.pkgIds.length = 0;
          } else if (res.data.resStatus == "1") {
            this.$message.error(res.data.resText);
            this.delRequest.pkgIds.length = 0;
          }
        });
    },
    //批量删除
    delAny: function() {
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/delCsPackage`, JSON.stringify(this.delRequest))
        .then(function(res) {
          if (res.data.resStatus == "0") {
            this.$message.success('删除成功');
            this.search(this.searchReq);
            this.delRequest.pkgIds.length = 0;
          } else if (res.data.resStatus == "1") {
            this.$message.error(res.data.resText);
            this.delRequest.pkgIds.length = 0;
          }
        });
    },
    handleSizeChange(val) {
      this.searchReq.pageSize = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/getCsPackage`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
    handleCurrentChange(val) {
      this.searchReq.pageNum = val;
      if(this.dateTime && this.dateTime.length>0){
        this.searchReq.startTime=formDate(new Date(this.dateTime[0]),'yyyy-MM-dd hh:mm:ss');
        this.searchReq.endTime=formDate(new Date(this.dateTime[1]),'yyyy-MM-dd hh:mm:ss');
      }else{
        this.searchReq.startTime='';
        this.searchReq.endTime='';
      }
      this.tableLoading = true;
      this.$http
        .post(`${this.proxyUrl}/content/csPackage/getCsPackage`, this.searchReq, {
          emulateJSON: true
        })
        .then(function(res) {
          this.tableData = res.data.datas;
          this.tableLoading = false;
        });
    },
      tableheaderClassName({ row, rowIndex }) {
          return "table-head-th";
      },
    //路由跳转
    locationHref(href) {
      let vm = this;
      vm.$router.push({ path: href });
    }
  }
};
</script>
<style>
.user-title {
  margin-top: 3%;
  margin-left: 3%;
  background-color: white;
}
.user-line {
   margin-top: 3%;
   background-color: blue;;
}
.el-table {
  margin: 0 auto;
  margin-top: 3%;
  border: 1px solid #ecebe9;
}
.el-table .table-head-th{
    background-color: #F5F5F5;
}
</style>
