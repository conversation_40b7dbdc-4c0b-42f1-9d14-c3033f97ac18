
package com.cy.model;

public class SysTaskModel {
	private static final long serialVersionUID = 1L;
	private Integer sysTaskId;
	private String sysTaskName;
	private String sysTaskState;
	private String sysTriggerPeople;
	private String sysTiggerTime;
	private String sysStartTime;
	private String sysEndTime;
	private String sysTaskProgress;
	private String sysRemainingTime;
	private String sysTaskDesc;
    private Integer contentNoPushStatus;
    private Integer diyStatus;
	private String sysFileName;

	public String getSysFileName() {
		return sysFileName;
	}

	public void setSysFileName(String sysFileName) {
		this.sysFileName = sysFileName;
	}
	public Integer getSysTaskId() {
		return sysTaskId;
	}

	public void setSysTaskId(Integer sysTaskId) {
		this.sysTaskId = sysTaskId;
	}

	public String getSysTaskName() {
		return sysTaskName;
	}

	public void setSysTaskName(String sysTaskName) {
		this.sysTaskName = sysTaskName;
	}

	public String getSysTaskState() {
		return sysTaskState;
	}

	public void setSysTaskState(String sysTaskState) {
		this.sysTaskState = sysTaskState;
	}

	public String getSysTriggerPeople() {
		return sysTriggerPeople;
	}

	public void setSysTriggerPeople(String sysTriggerPeople) {
		this.sysTriggerPeople = sysTriggerPeople;
	}

	public String getSysTiggerTime() {
		return sysTiggerTime;
	}

	public void setSysTiggerTime(String sysTiggerTime) {
		this.sysTiggerTime = sysTiggerTime;
	}

	public String getSysStartTime() {
		return sysStartTime;
	}

	public void setSysStartTime(String sysStartTime) {
		this.sysStartTime = sysStartTime;
	}

	public String getSysTaskProgress() {
		return sysTaskProgress;
	}

	public void setSysTaskProgress(String sysTaskProgress) {
		this.sysTaskProgress = sysTaskProgress;
	}

	public String getSysTaskDesc() {
		return sysTaskDesc;
	}

	public void setSysTaskDesc(String sysTaskDesc) {
		this.sysTaskDesc = sysTaskDesc;
	}

	public String getSysRemainingTime() {
		return sysRemainingTime;
	}

	public void setSysRemainingTime(String sysRemainingTime) {
		this.sysRemainingTime = sysRemainingTime;
	}

	public String getSysEndTime() {
		return sysEndTime;
	}

	public void setSysEndTime(String sysEndTime) {
		this.sysEndTime = sysEndTime;
	}

    public void setContentNoPushStatus(Integer contentStatus) {
        this.contentNoPushStatus = contentStatus;
    }

    public Integer getContentNoPushStatus() {
        return contentNoPushStatus;
    }

    public void setDiyStatus(Integer diyStatus) {
        this.diyStatus = diyStatus;
    }

    public Integer getDiyStatus() {
        return diyStatus;
    }
}
