package com.cy.content.model;

import java.io.Serializable;

public class CsOffModel  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 0用户DIY,1个人彩印，2彩印盒
	 */
	private int moldId;
	
	private String moldName;
	
	private String pkId;
	/**
	 * 内容ID
	 */
	private String csId;
	/**
	 * 内容
	 */
	private String csContent;
	/**
	 * 内容状态：上架
	 */
	private int csType;
	/**
	 * 审批人
	 */
	private String assessor;
	/**
	 *审批时间
	 */
	private String assessorTime;
	/**
	 * 提交时间
	 */
	private String submitTime;
	
	private String submitUser;
	/**
	 * 使用人数
	 */
	private int usedCount;
	
	private String content1, content2, content3, content4, content5, content6, content7, content8, content9, content10,
	    content11, content12, content13, content14, content15, content16, content17, content18, content19,content20,
	    content21, content22, content23, content24, content25, content26, content27, content28,content29, content30;
	
	
	public String getSubmitUser() {
	    return submitUser;
	}
	public void setSubmitUser(String submitUser) {
	    this.submitUser = submitUser;
	}
	public String getSubmitTime() {
	    return submitTime;
	}
	public void setSubmitTime(String submitTime) {
	    this.submitTime = submitTime;
	}
	public String getContent1() {
	    return content1;
	}
	public void setContent1(String content1) {
	    this.content1 = content1;
	}
	public String getContent2() {
	    return content2;
	}
	public void setContent2(String content2) {
	    this.content2 = content2;
	}
	public String getContent3() {
	    return content3;
	}
	public void setContent3(String content3) {
	    this.content3 = content3;
	}
	public String getContent4() {
	    return content4;
	}
	public void setContent4(String content4) {
	    this.content4 = content4;
	}
	public String getContent5() {
	    return content5;
	}
	public void setContent5(String content5) {
	    this.content5 = content5;
	}
	public String getContent6() {
	    return content6;
	}
	public void setContent6(String content6) {
	    this.content6 = content6;
	}
	public String getContent7() {
	    return content7;
	}
	public void setContent7(String content7) {
	    this.content7 = content7;
	}
	public String getContent8() {
	    return content8;
	}
	public void setContent8(String content8) {
	    this.content8 = content8;
	}
	public String getContent9() {
	    return content9;
	}
	public void setContent9(String content9) {
	    this.content9 = content9;
	}
	public String getContent10() {
	    return content10;
	}
	public void setContent10(String content10) {
	    this.content10 = content10;
	}
	public String getContent11() {
	    return content11;
	}
	public void setContent11(String content11) {
	    this.content11 = content11;
	}
	public String getContent12() {
	    return content12;
	}
	public void setContent12(String content12) {
	    this.content12 = content12;
	}
	public String getContent13() {
	    return content13;
	}
	public void setContent13(String content13) {
	    this.content13 = content13;
	}
	public String getContent14() {
	    return content14;
	}
	public void setContent14(String content14) {
	    this.content14 = content14;
	}
	public String getContent15() {
	    return content15;
	}
	public void setContent15(String content15) {
	    this.content15 = content15;
	}
	public String getContent16() {
	    return content16;
	}
	public void setContent16(String content16) {
	    this.content16 = content16;
	}
	public String getContent17() {
	    return content17;
	}
	public void setContent17(String content17) {
	    this.content17 = content17;
	}
	public String getContent18() {
	    return content18;
	}
	public void setContent18(String content18) {
	    this.content18 = content18;
	}
	public String getContent19() {
	    return content19;
	}
	public void setContent19(String content19) {
	    this.content19 = content19;
	}
	public String getContent20() {
	    return content20;
	}
	public void setContent20(String content20) {
	    this.content20 = content20;
	}
	public String getContent21() {
	    return content21;
	}
	public void setContent21(String content21) {
	    this.content21 = content21;
	}
	public String getContent22() {
	    return content22;
	}
	public void setContent22(String content22) {
	    this.content22 = content22;
	}
	public String getContent23() {
	    return content23;
	}
	public void setContent23(String content23) {
	    this.content23 = content23;
	}
	public String getContent24() {
	    return content24;
	}
	public void setContent24(String content24) {
	    this.content24 = content24;
	}
	public String getContent25() {
	    return content25;
	}
	public void setContent25(String content25) {
	    this.content25 = content25;
	}
	public String getContent26() {
	    return content26;
	}
	public void setContent26(String content26) {
	    this.content26 = content26;
	}
	public String getContent27() {
	    return content27;
	}
	public void setContent27(String content27) {
	    this.content27 = content27;
	}
	public String getContent28() {
	    return content28;
	}
	public void setContent28(String content28) {
	    this.content28 = content28;
	}
	public String getContent29() {
	    return content29;
	}
	public void setContent29(String content29) {
	    this.content29 = content29;
	}
	public String getContent30() {
	    return content30;
	}
	public void setContent30(String content30) {
	    this.content30 = content30;
	}
	public String getPkId() {
	    return pkId;
	}
	public void setPkId(String pkId) {
	    this.pkId = pkId;
	}
	public int getMoldId() {
	    return moldId;
	}
	public void setMoldId(int moldId) {
	    this.moldId = moldId;
	}
	public String getMoldName() {
	    return moldName;
	}
	public void setMoldName(String moldName) {
	    this.moldName = moldName;
	}
	public String getCsId() {
		return csId;
	}
	public void setCsId(String csId) {
		this.csId = csId;
	}
	public String getCsContent() {
		return csContent;
	}
	public void setCsContent(String csContent) {
		this.csContent = csContent;
	}
	public int getCsType() {
		return csType;
	}
	public void setCsType(int csType) {
		this.csType = csType;
	}
	public String getAssessor() {
		return assessor;
	}
	public void setAssessor(String assessor) {
		this.assessor = assessor;
	}
	public String getAssessorTime() {
		return assessorTime;
	}
	public void setAssessorTime(String assessorTime) {
		this.assessorTime = assessorTime;
	}
	public int getUsedCount() {
		return usedCount;
	}
	public void setUsedCount(int usedCount) {
		this.usedCount = usedCount;
	}
	
}
