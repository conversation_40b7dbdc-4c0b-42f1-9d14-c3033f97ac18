<template>
  <div>
    <h1 class="user-title">携转号码统计</h1>
    <div class="user-line"></div>
  <div class="app-search">
    <el-form :inline="true" class="demo-form-inline">
      <el-form-item label="手机号">
        <el-input v-model="searchReq.phoneNumber" size="small"></el-input>
      </el-form-item>
       <el-form-item>
           <el-button type="primary" @click="pageQuery()" size="small" class="app-bnt">查询</el-button>
       </el-form-item>
       <el-form-item>
           <el-button type="primary" plain @click="exportText()" size="small"  class="app-bnt">导出excel</el-button>
       </el-form-item>
      </el-form>
  </div>
  <div>
              <el-table
                      v-loading="tableLoading"
                      :data="tableData"
                      border
                      class="app-tab"
                      @selection-change="handleSelectionChange"
                      :header-cell-class-name="tableheaderClassName">
                  <el-table-column
                      type="selection"
                      width="55">
                  </el-table-column>
                  <el-table-column
                          prop="batchId"
                          label="批量统计ID"
                          width="240">
                  </el-table-column>
                  <el-table-column
                          prop="phoneNumber"
                          label="手机号"
                          width="200">
                  </el-table-column>
                  <el-table-column
                          prop="turnIn"
                          label="携入运营商"
                          width="200"
                          :show-overflow-tooltip="true">
                  </el-table-column>
                  <el-table-column
                          prop="turnOut"
                          label="携出运营商"
                          width="400"
                          :show-overflow-tooltip="true">
                  </el-table-column>
                  <el-table-column
                          prop="turnTime"
                          label="携转时间"
                          width="200">
                  </el-table-column>
              </el-table>

              <!-- 分页 -->
              <div class="block app-pageganit">
              <el-pagination
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      :current-page="searchReq.pageNum"
                      :page-sizes="[10, 20, 30, 50]"
                      :page-size="10"
                      layout="total, sizes, prev, pager, next, jumper"
                      :total="pageTotal"  style="text-align: right;">
              </el-pagination>
          </div>
        </div>
  </div>
</template>
<script >
import {postDownload,post} from './../../servers/httpServer.js';
import {dowandFile,formDate} from './../../util/core.js';
  export default {
    name: 'carrayTurnList',
    data() {
      return {
         row:{},
         delVisible:false,
         batchIds:[],
         pageTotal:0,
          tableData: [],
          searchReq:{
            phoneNumber:'',
            pageNum:1,
            pageSize:10
          },
          exportReq: {
            batchIds: [],
            pageSize: 10,
            pageNum: 1
          }
      }
    },
     methods: {
    //导出功能
        exportText: function(row) {
         this.loading = true;
           if (this.exportReq.batchIds.length == 0) {
                  this.$message.error('请选择要导出的批次');
                  return ;
            }
            postDownload(`/user/batchCarryTurn/carrayTurnExport`,this.exportReq).then(res=>{
                                dowandFile(res.data,'携转信息结果.xlsx');
                                this.loading = false;
                   })
        },
   //表格多选框
         handleSelectionChange(val) {
           this.multipleSelection = val;
           this.exportReq.batchIds.length = 0;
           for (var i = 0; i < val.length; i++) {
             this.exportReq.batchIds.push(val[i].batchId);
           }
         },
        //查询请求
        pageQuery:function(){
         this.tableLoading = true;
          this.$http
            .post(`${this.proxyUrl}/user/batchCarryTurn/carrayTurnList`,this.searchReq,{emulateJSON:true})
            .then(function(res){
              this.tableData=res.data.datas;
              this.pageTotal=res.data.pageTotal;
               this.tableLoading = false;
            })
        },
        //分页
        handleSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.searchReq.pageSize=val;
            this.$http
                .post(`${this.proxyUrl}/user/batchCarryTurn/carrayTurnList`,this.searchReq,{emulateJSON:true})
                .then(function(res){
                this.tableData=res.data.datas;
            })
        },
        handleCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.searchReq.pageNum=val;
            this.$http
              .post(`${this.proxyUrl}/user/batchCarryTurn/carrayTurnList`,this.searchReq,{emulateJSON:true})
              .then(function(res){
              this.tableData=res.data.datas;
            })
        },
        handleCloseConfirm(done) {
            done();
        },
      mounted() {
        //  this.pageQuery();
      },
      }
  }


</script>
<style scoped>
.user-title{
      padding: 10px 0px 0px 0px;
  }
.user-line{
  width:100%;
  margin:0 auto;
  margin-top: 3%;
  border-bottom: 1px solid #DDDFE6;
}
.user-search{
    width: 100%;
}
.user-push-serch {
    margin-top: 10px;
    padding-left:24px !important;
}
.el-table{
  border:1px solid #ECEBE9;
  margin: 0 auto;
  width:99%;
}
.el-th {
  border-right: 1px solid #DDDFE6 !important;
}
</style>
