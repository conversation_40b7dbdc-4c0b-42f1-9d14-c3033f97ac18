<template>
    <div class="innerbox">
        <div class="effect">
            <div>
                <!--查询条件-->
                <div>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" label-width="60px">
                        <el-form-item label="号码">
                            <el-input  v-model="searchForm.phoneNumber" placeholder="" class="app-input"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="search(1)">查询</el-button>
                        </el-form-item>
                    </el-form>
                    <el-form :model="searchForm" :inline="true" class="demo-form-inline app-form-item" size="small" style="margin-left: 25px;">
                        <el-form-item>
                            <el-button type="primary" size="small" @click="addList">新增</el-button>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="addall">有限期限设置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <!--表格-->
                <el-table :data="tableData" border :header-cell-class-name="tableheaderClassNameZ">
                    <el-table-column prop="phoneNumber" label="号码" />
                    <el-table-column prop="createTime" label="入库时间"/>
                    <el-table-column prop="expireTime" label="到期时间"/>
                    <el-table-column prop="operatorName" label="操作人员" />
                    <el-table-column label="操作">
                        <template slot-scope="scope">
                            <el-popover trigger="click" placement="top" style="display:inline-block;" v-model="scope.row.show">
                                <p style="margin: 10px;text-align:center">确定删除此号码?</p>
                                <div style="margin: 10px;text-align:center">
                                    <el-button size="small" @click="scope.row.show = false">取消</el-button>
                                    <el-button class="el-button--primary" @click="deletebtn(scope.row)" size="small">删除</el-button>
                                </div>
                                <div slot="reference">
                                    <el-button  type="text" size="small" >移入已删除</el-button>
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table>
                <!--分页-->
                <div class="block app-pageganit" v-show="total>20">
                    <el-pagination
                            @size-change="handleSizeChange"
                            @current-change="handleCurrentChange"
                            :current-page="currentPage"
                            :page-sizes="[10, 20, 30, 50]"
                            :page-size="50"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="total"  style="text-align: right;">
                    </el-pagination>
                </div>
            </div>
            <!--新增-->
            <div>
                <el-dialog title="新增白名单" :visible.sync="addVisible"   :close-on-click-modal="false">
                    <addwhite :addVisible="addVisible"  @addList="addList"></addwhite>
                </el-dialog>
            </div>
            <!--有限期限设置-->
            <div>
                <el-dialog title="有效期限设置" :visible.sync="alladdVisible" width="40%"  :close-on-click-modal="false">
                    <el-form :model="addForm" :rules="rules" ref="addForm" class="demo-form-inline" size="small" label-width="50%">
                        <el-form-item label="请选择有效期限" prop="dayTime">
                                <el-input v-model.number="addForm.dayTime" style="width: 30%;"></el-input>天
                        </el-form-item>
                    </el-form>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="alladdVisible = false;resetForm('addForm')" size="small">取 消</el-button>
                        <el-button type="primary" @click="setWhiteNumLimitTime" size="small">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
    import addwhite from './addwhite';
    import leadingin from './leadingin';
    import {postHeader} from '../../../servers/httpServer.js';
    export default {
        name: 'effect',
        data(){
            return{
                value6: '',
                addVisible:false,//新增
                alladdVisible:false,//批量新增
                //查询form对象定义
                searchForm: {
                    numType:3, //号码库
                    phoneNumber:'',//号码
                    pageSize:50,// 每页显示条数
                    pageNo:1 // 查询的页码
                },
                tableData:[],//表数据
                currentPage: 1,
                total:0,
                addForm:{
                    dayTime:'',//天
                    operatorName:JSON.parse(sessionStorage.getItem('userInfo')).sysUserName,//操作者姓名
                },
                rules: {
                    dayTime: [
                        {required: true, message: '请输入有效期限', trigger: 'blur' },
                        { type: 'number', message: '必须为数字值',trigger: 'blur'}
                    ],
                }
            }
        },
        components: {
            addwhite,
            leadingin
        },
        created(){
//            this.search();
        },
        methods:{
            //每页条数
            handleSizeChange(val) {
                this.searchForm.pageSize = val;
                this.search();
            },
            //当前页面
            handleCurrentChange(val) {
                this.searchForm.pageNo = val;
                this.search();
            },
            //查询请求
            search: function(pg) {
                let vm = this;
                if(pg){
                    this.searchForm.pageNo = pg;
                }
                if(!this.searchForm.phoneNumber){
                    vm.$message.error("请输入号码查询");
                    return;
                }
                postHeader('queryNumInfo', JSON.stringify(this.searchForm)).then(res=>{
                    let data = res.data;
                    if(data.code==0){
                        vm.tableData = data.data.queryNumInfoList;
                        vm.total = data.data.total;
                    }
                })
            },
            //新增按钮
            addList(){
                let vm = this;
                vm.addVisible=!vm.addVisible;
            },
            //批量新增
            addall(){
                let vm = this;
                vm.alladdVisible = !vm.alladdVisible
                vm.addForm.dayTime = '';
                vm.resetForm('addForm')
            },
            deletebtn(row) {
                let vm = this;
                let parms = {
                    type:2,
                    categoryId:4,
                    phoneNumber:row.phoneNumber
                }
                postHeader('deleteNumber', JSON.stringify(parms)).then(res=>{
                    let data = res.data;
                if(data.code==0){
                    vm.$message.success("删除成功");
                    this.search(1);
                }else{
                    vm.$message.error("删除失败");
                }
            })
                row.show = false;
            },
            //有效期限设置
            setWhiteNumLimitTime(){
                let vm = this;
                this.$refs['addForm'].validate((valid) => {
                    if (valid) {
                        postHeader('setWhiteNumLimitTime', JSON.stringify(this.addForm)).then(res=>{
                            let data = res.data;
                            if(data.code==0){
                                vm.$message.success("设置成功");
                            }else{
                                vm.$message.error("设置失败");
                            }
                            this.alladdVisible = false;
                        })
                    }else{
                        return false;
                    }
                });
            },
            tableheaderClassNameZ({ row, rowIndex }) {
                return "table-head-thz";
            },
            //重置
            resetForm(formName) {
                if(this.$refs[formName]){
                    this.$refs[formName].resetFields();
                }
            }
        }
    }
</script>

<style scoped>
    .innerbox{
        margin-bottom: 20px;
    }
    .el-table{
        margin: 0;
    }
</style>
<style>
    .el-table .table-head-thz{
        background-color: #F5F5F5;
    }
</style>
