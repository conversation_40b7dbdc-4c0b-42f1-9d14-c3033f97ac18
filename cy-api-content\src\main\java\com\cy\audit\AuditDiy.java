package com.cy.audit;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;

import com.cy.audit.model.AuditTextModel;
import org.springframework.web.bind.annotation.RequestParam;
public interface AuditDiy {
    /**
     * 添加用户diy审核
     * 
     * @param auditTextModel
     * @return
     */
    @RequestMapping(value = "/auditDiy/addAuditDiy")
    String addAuditDiy(AuditTextModel auditTextModel);

    /**
     * 批量提交需要审核的diy，内容相同调用此方法，敏感词只检测一次
     * 
     * @param List<AuditTextModel>
     * @return
     */
    @RequestMapping(value = "/auditDiy/batchAuditDiy")
    void batchAuditDiy(List<AuditTextModel> auditTextModel);
    
    /**
     * 批量提交需要审核的diy，内容不相同调用此方法，每条内容敏感词都检测一次
     * 
     * @param List<AuditTextModel>
     * @return
     */
    @RequestMapping(value = "/auditDiy/batchAuditDiy2")
    void batchAuditDiy2(List<AuditTextModel> auditTextModel);
	
	    /**
     * 判断用户是否具备个人彩印配置权益
     * @param submitUser 用户手机号码
     * @param type 1 DIY（DIY彩印） 2 text（文本彩印） 3 grgc （个人挂彩）
     */
    @RequestMapping("/auditDiy/hasPersonalCyConfigRights")
    boolean hasPersonalCyConfigRights(@RequestParam("submitUser")String submitUser, @RequestParam("type") Integer type, @RequestParam("provinceCode") String provinceCode);

}
