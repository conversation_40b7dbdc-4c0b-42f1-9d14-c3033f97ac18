<template>
    <div class="fun_page">
        <h1 class="user-title">区域维度</h1>
        <div class="user-line"></div>
        <div class="app-search">
        <el-form :inline="true" :model="form" size="small" class="demo-form-inline">
            <el-row>
                <el-col :span="21">
                    <el-form-item label="呼叫类型：">
                        <el-select v-model="form.callType" class="app-input app-bottom">
                            <el-option label="主叫" value="1"/>
                            <el-option label="被叫" value="2"/>
                        </el-select>
                    </el-form-item>
                    <!--<el-form-item label="发送方式：">-->
                        <!--<el-select v-model="form.sendType" class="app-input app-bottom">        -->
                            <!--<el-option label="方式一" value="1"/>-->
                            <!--<el-option label="方式二" value="2"/>-->
                        <!--</el-select>-->
                    <!--</el-form-item>-->
                    <el-form-item label="省份：">
                        <el-select v-model="form.provinceId" clearable @change="selectProvince()" class="app-input app-bottom">
                            <el-option v-for="item in provinceList"
                                :key="item.provinceCode"
                                :label="item.provinceName"
                                :value="item.provinceCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="城市：">
                        <el-select v-model="form.cityId" clearable class="app-input app-bottom">
                            <el-option v-for="item in regionList"
                                       :key="item.regionCode"
                                       :label="item.regionName"
                                       :value="item.regionCode">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发送结果：">
                        <el-select v-model="form.sendResult" class="app-input app-bottom">
                            <el-option label="成功" value="1"/>
                            <el-option label="失败" value="2"/>
                        </el-select>
                    </el-form-item>
                    <!--<el-form-item label="标准类型：">-->
                        <!--<el-select v-model="form.standardType" class="app-input app-bottom">-->
                            <!--<el-option label="广告营销" value="1"/>-->
                            <!--<el-option label="其他方式" value="2"/>-->
                        <!--</el-select>-->
                    <!--</el-form-item>-->

                    <el-form-item label="提醒日期：">
                        <el-date-picker type="daterange" placeholder="选择日期" v-model="form.date"
                                        style="width: 215px;" :picker-options="pickerOptions" class="app-bottom"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="search">查询</el-button>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain @click="download">导出excel</el-button>
                    </el-form-item>
                </el-col>

            </el-row>
        </el-form>
        <div>
            <el-table :data="tableData" border style="width: 100%" max-height=500 v-loading="tableLoading">
                <el-table-column prop="remindDate" label="提醒日期" width="120"/>
                <el-table-column prop="province" label="省份" width="120"/>
                <el-table-column prop="city" label="地市" width="120"/>
                <el-table-column prop="category" label="分类" width="120"/>
                <el-table-column prop="type" label="标准类型" width="120"/>
                <el-table-column prop="callType" label="呼叫类型" width="120"/>
                <el-table-column prop="sendType" label="发送方式" width="120"/>
                <el-table-column prop="sendResult" label="发送结果" width="120"/>
                <el-table-column prop="remindCount" label="提醒次数" width="120"/>
                <el-table-column prop="remindUserCount" label="提醒人数" width="120"/>
                <el-table-column prop="remindMobileCount" label="提醒号码数" width="120"/>
            </el-table>
            <div class="block app-pageganit">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="form.page.pageNo"
                           :page-sizes="[100, 200, 300, 400]" :page-size="form.page.pageSize"
                           layout="total, sizes, prev, pager, next, jumper" :total="form.page.total">
            </el-pagination>
            </div>
        </div></div>
    </div>
</template>

<script>
    import {formatDate} from '../../../static/js/date.js'
    import axios from '../../../node_modules/axios/dist/axios'
    import {dowandFile} from './../../util/core.js'

    export default {
        name: 'procMoni',
        data() {
            return {
                form: {
                    callType:'',
                    sendType:'',
                    standardType:'',
                    sendResult:'',
                    date: '',
                    startDate:'',
                    endDate:'',
                    locationId:'',
                    provinceId:'',
                    cityId:'',
                    page:{
                        pageNo:1,
                        pageSize:100,
                        total:0
                    }
                },
                //省份列表
                provinceList:JSON.parse(sessionStorage.getItem('provinceList')),
                regionList:new Array(),//城市列表
                tableData: [],
                tableLoading:false,
                pickerOptions:{
                    disabledDate:function (today) {
                        return today.getTime()>Date.now();
                    }
                }
            }
        },
        methods: {
            check(vm){
                if (vm.form.provinceId == '' || vm.form.provinceId == undefined) {
                    vm.$message.error("请选择省份");
                    return false;
                }
                return true;
            },
            search() {
                const vm = this;
                vm.tableLoading=true;
                vm.form.startDate = vm.form.date[0];
                vm.form.endDate = vm.form.date[1];
                var params = {
                    startDate:vm.form.date[0],
                    endDate:vm.form.date[1],
                    callType:vm.form.callType,
                    sendType:vm.form.sendType,
                    provinceId:vm.form.provinceId,
                    cityId:vm.form.cityId,
                    type:vm.form.standardType,
                    sendResult:vm.form.sendResult,
                    pageNo:vm.form.page.pageNo,
                    pageSize:vm.form.page.pageSize,
                }
                //从服务器获取数据
                axios
                        .post(`${this.proxyUrl}/oper/sop`,params,{

                            headers: {
                                'CY-operation': 'queryRemindDataByArea'
                            }
                        })
                        .then(function(response) {
                            vm.tableData = response.data.data.areaDimensionInfoList;
                            vm.form.page.total = response.data.data.total;
                            vm.chartLoading=false;
                            console.log(response);
                        })
                        .catch(function (error) {
                            console.log(error);
                        }).finally(function () {
                            vm.tableLoading=false;
                        });
            },
            handleSizeChange(val) {
                this.form.page.pageSize = val;
                this.search();
            },
            handleCurrentChange(val) {
                this.form.page.pageNo=val;
                this.search();
            },
            download() {
                const vm = this;
                if(!vm.check(vm)){
                    return false;
                }
                var params = {
                    startDate:vm.form.date[0],
                    endDate:vm.form.date[1],
                    callType:vm.form.callType,
                    sendType:vm.form.sendType,
                    provinceId:vm.form.provinceId,
                    cityId:vm.form.cityId,
                    type:vm.form.standardType,
                    sendResult:vm.form.sendResult,
                }
                //发送下载请求
                axios.post(`${this.proxyUrl}/oper/sop`,params,{
                    headers: {
                        'CY-operation': 'exportAreaDimension'
                    },
                    responseType:'blob'
                })
                .then(function(response) {
                    dowandFile(response.data,'区域维度.xlsx');
                })
                .catch(function (error) {
                    console.log(error);
                })
            },
            //根据省份查询城市列表
            selectProvince(){
                this.$http.post(`${this.proxyUrl}/param/regionMgt/getRegion`,{provinceCode:this.form.provinceId},{emulateJSON:true})
                            .then((res)=>{
                                this.regionList=res.data;
                })
            },
        }
    }
</script>
<style scoped>
    .user-title{
        margin-top: 3%;
        margin-left: 3%;
        background-color: white;
    }
    .user-line{
        margin-top: 3%;
        background-color: blue;;
    }
    .user-search{
        width: 96%;
        margin-top: 10px;
        margin-left: 2%;
    }
</style>
